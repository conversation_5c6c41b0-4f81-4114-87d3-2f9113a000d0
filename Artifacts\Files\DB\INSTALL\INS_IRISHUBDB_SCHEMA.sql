USE [master]
GO
/****** Object:  Database [MA_SUP_TJUHOR_IRISHUBDB]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE DATABASE [MA_SUP_TJUHOR_IRISHUBDB]
GO
USE [MA_SUP_TJUHOR_IRISHUBDB]
GO
/****** Object:  UserDefinedTableType [dbo].[udt700WebConsoleTransactedItems]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE TYPE [dbo].[udt700WebConsoleTransactedItems] AS TABLE(
	[ItemID] [bigint] NULL,
	[RFID] [varchar](50) NULL,
	[LocationID] [int] NULL,
	[TagType] [varchar](100) NULL
)
GO
/****** Object:  UserDefinedTableType [dbo].[udt701WebConsoleTransactedItemDetails]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE TYPE [dbo].[udt701WebConsoleTransactedItemDetails] AS TABLE(
	[ItemID] [bigint] NULL,
	[RFID] [varchar](50) NULL,
	[LocationID] [int] NULL,
	[ProductID] [int] NULL,
	[UseCycle] [int] NULL,
	[Qty] [decimal](10, 2) NULL,
	[UOMCode] [varchar](50) NULL,
	[TagType] [varchar](100) NULL,
	[MA_ScheduleID] [varchar](15) NULL
)
GO
/****** Object:  UserDefinedTableType [dbo].[udt702WebConsoleTags]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE TYPE [dbo].[udt702WebConsoleTags] AS TABLE(
	[RFID] [varchar](50) NULL,
	[LocationID] [varchar](100) NULL
)
GO
/****** Object:  UserDefinedTableType [dbo].[udt703WebConsoleItemsData]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE TYPE [dbo].[udt703WebConsoleItemsData] AS TABLE(
	[ItemID] [int] NULL,
	[Qty] [int] NULL
)
GO
/****** Object:  UserDefinedTableType [dbo].[udt704WebConsoleScanData]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE TYPE [dbo].[udt704WebConsoleScanData] AS TABLE(
	[BarcodeNo] [varchar](100) NULL,
	[Qty] [int] NULL
)
GO
/****** Object:  UserDefinedTableType [dbo].[udt705WebConsoleSPActivityLog]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE TYPE [dbo].[udt705WebConsoleSPActivityLog] AS TABLE(
	[Property] [varchar](100) NULL,
	[Value] [varchar](max) NULL
)
GO
/****** Object:  UserDefinedTableType [dbo].[udt706WebConsoleModules]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE TYPE [dbo].[udt706WebConsoleModules] AS TABLE(
	[ModuleName] [varchar](50) NULL
)
GO
/****** Object:  UserDefinedTableType [dbo].[udt707WebConsoleUnCheckedModules]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE TYPE [dbo].[udt707WebConsoleUnCheckedModules] AS TABLE(
	[UnCheckedModuleNames] [varchar](50) NULL
)
GO
/****** Object:  UserDefinedTableType [dbo].[udt708WebConsoleClusters]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE TYPE [dbo].[udt708WebConsoleClusters] AS TABLE(
	[Cluster] [varchar](30) NULL,
	[Cabinet] [varchar](30) NULL
)
GO
/****** Object:  UserDefinedTableType [dbo].[udt709WebConsoleReports]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE TYPE [dbo].[udt709WebConsoleReports] AS TABLE(
	[ReportID] [int] NULL,
	[ReportName] [varchar](30) NULL,
	[Checked] [int] NULL
)
GO
/****** Object:  UserDefinedTableType [dbo].[udt710WebConsoleSPParam]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE TYPE [dbo].[udt710WebConsoleSPParam] AS TABLE(
	[Property] [varchar](50) NULL,
	[Value] [varchar](255) NULL
)
GO
/****** Object:  UserDefinedTableType [dbo].[udtSPParam]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE TYPE [dbo].[udtSPParam] AS TABLE(
	[Property] [varchar](50) NULL,
	[Value] [varchar](255) NULL
)
GO
/****** Object:  Synonym [dbo].[s00sysObjects]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s00sysObjects] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[sysobjects]
GO
/****** Object:  Synonym [dbo].[s01BGInfoTable]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01BGInfoTable] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[BGInfoTable]
GO
/****** Object:  Synonym [dbo].[s01P380VL0138]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01P380VL0138] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[P380VL0138]
GO
/****** Object:  Synonym [dbo].[s01qryArchiveItems]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryArchiveItems] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryArchiveItems]
GO
/****** Object:  Synonym [dbo].[s01qryArchiveItemUsage]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryArchiveItemUsage] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryArchiveItemUsage]
GO
/****** Object:  Synonym [dbo].[s01qryAssetLocation]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryAssetLocation] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryAssetLocation]
GO
/****** Object:  Synonym [dbo].[s01qryAssociateItemPatient]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryAssociateItemPatient] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryAssociateItemPatient]
GO
/****** Object:  Synonym [dbo].[s01qryAssociatePatient]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryAssociatePatient] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryAssociatePatient]
GO
/****** Object:  Synonym [dbo].[s01qryAssociatePatientLite]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryAssociatePatientLite] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryAssociatePatientLite]
GO
/****** Object:  Synonym [dbo].[s01qryCabinetAccess]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryCabinetAccess] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryCabinetAccess]
GO
/****** Object:  Synonym [dbo].[s01qryCabinetLog]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryCabinetLog] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryCabinetLog]
GO
/****** Object:  Synonym [dbo].[s01qryCabinets]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryCabinets] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryCabinets]
GO
/****** Object:  Synonym [dbo].[s01qryCabinetUsers]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryCabinetUsers] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryCabinetUsers]
GO
/****** Object:  Synonym [dbo].[s01qryCernerInterface]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryCernerInterface] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryCernerInterface]
GO
/****** Object:  Synonym [dbo].[s01qryClusterLocation]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryClusterLocation] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryClusterLocation]
GO
/****** Object:  Synonym [dbo].[s01qryClusterParLevel]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryClusterParLevel] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryClusterParLevel]
GO
/****** Object:  Synonym [dbo].[s01qryCompliancePacemaker]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryCompliancePacemaker] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryCompliancePacemaker]
GO
/****** Object:  Synonym [dbo].[s01qryCustomItemStatus]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryCustomItemStatus] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryCustomItemStatus]
GO
/****** Object:  Synonym [dbo].[s01qryDailyInventoryUsageRate]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryDailyInventoryUsageRate] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryDailyInventoryUsageRate]
GO
/****** Object:  Synonym [dbo].[s01qryDailyStockedItems]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryDailyStockedItems] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryDailyStockedItems]
GO
/****** Object:  Synonym [dbo].[s01qryDailyUsedItemReport]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryDailyUsedItemReport] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryDailyUsedItemReport]
GO
/****** Object:  Synonym [dbo].[s01qryEpicExport]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryEpicExport] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryEpicExport]
GO
/****** Object:  Synonym [dbo].[s01qryExceptionNotesChoice]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryExceptionNotesChoice] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryExceptionNotesChoice]
GO
/****** Object:  Synonym [dbo].[s01qryFixParlevel]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryFixParlevel] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryFixParlevel]
GO
/****** Object:  Synonym [dbo].[s01qryGender]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryGender] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryGender]
GO
/****** Object:  Synonym [dbo].[s01qryInvCountParLevel]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryInvCountParLevel] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryInvCountParLevel]
GO
/****** Object:  Synonym [dbo].[s01qryInventory]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryInventory] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryInventory]
GO
/****** Object:  Synonym [dbo].[s01qryInventoryBelowPar]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryInventoryBelowPar] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryInventoryBelowPar]
GO
/****** Object:  Synonym [dbo].[s01qryInventoryByCluster]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryInventoryByCluster] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryInventoryByCluster]
GO
/****** Object:  Synonym [dbo].[s01qryInventoryCount]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryInventoryCount] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryInventoryCount]
GO
/****** Object:  Synonym [dbo].[s01qryInventoryCountInCabinet]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryInventoryCountInCabinet] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryInventoryCountInCabinet]
GO
/****** Object:  Synonym [dbo].[s01qryInventoryCountInCabinetReport]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryInventoryCountInCabinetReport] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryInventoryCountInCabinetReport]
GO
/****** Object:  Synonym [dbo].[s01qryInventoryCountItemUsedNoPatientReport]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryInventoryCountItemUsedNoPatientReport] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryInventoryCountItemUsedNoPatientReport]
GO
/****** Object:  Synonym [dbo].[s01qryInventoryCountNewItem]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryInventoryCountNewItem] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryInventoryCountNewItem]
GO
/****** Object:  Synonym [dbo].[s01qryInventoryCountNewItemReport]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryInventoryCountNewItemReport] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryInventoryCountNewItemReport]
GO
/****** Object:  Synonym [dbo].[s01qryInventoryInCabinet]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryInventoryInCabinet] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryInventoryInCabinet]
GO
/****** Object:  Synonym [dbo].[s01qryInventoryLevels]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryInventoryLevels] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryInventoryLevels]
GO
/****** Object:  Synonym [dbo].[s01qryInventoryLevelsByCost]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryInventoryLevelsByCost] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryInventoryLevelsByCost]
GO
/****** Object:  Synonym [dbo].[s01qryInventoryOnShelf]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryInventoryOnShelf] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryInventoryOnShelf]
GO
/****** Object:  Synonym [dbo].[s01qryInventoryQty]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryInventoryQty] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryInventoryQty]
GO
/****** Object:  Synonym [dbo].[s01qryInventoryReorder]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryInventoryReorder] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryInventoryReorder]
GO
/****** Object:  Synonym [dbo].[s01qryInventorySummary]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryInventorySummary] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryInventorySummary]
GO
/****** Object:  Synonym [dbo].[s01qryInventoryTotal]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryInventoryTotal] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryInventoryTotal]
GO
/****** Object:  Synonym [dbo].[s01qryInventoryUpdate]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryInventoryUpdate] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryInventoryUpdate]
GO
/****** Object:  Synonym [dbo].[s01qryInventoryUsageRate]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryInventoryUsageRate] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryInventoryUsageRate]
GO
/****** Object:  Synonym [dbo].[s01qryItemHistory]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryItemHistory] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryItemHistory]
GO
/****** Object:  Synonym [dbo].[s01qryItemInventory]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryItemInventory] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryItemInventory]
GO
/****** Object:  Synonym [dbo].[s01qryItemInventoryDisplay]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryItemInventoryDisplay] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryItemInventoryDisplay]
GO
/****** Object:  Synonym [dbo].[s01qryItemInventoryLocation]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryItemInventoryLocation] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryItemInventoryLocation]
GO
/****** Object:  Synonym [dbo].[s01qryItemInventorySpecs]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryItemInventorySpecs] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryItemInventorySpecs]
GO
/****** Object:  Synonym [dbo].[s01qryItemInventorySpecsImport]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryItemInventorySpecsImport] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryItemInventorySpecsImport]
GO
/****** Object:  Synonym [dbo].[s01qryItemInventoryUsageCount]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryItemInventoryUsageCount] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryItemInventoryUsageCount]
GO
/****** Object:  Synonym [dbo].[s01qryItemInventoryWastedCount]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryItemInventoryWastedCount] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryItemInventoryWastedCount]
GO
/****** Object:  Synonym [dbo].[s01qryItems]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryItems] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryItems]
GO
/****** Object:  Synonym [dbo].[s01qryItemsAll]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryItemsAll] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryItemsAll]
GO
/****** Object:  Synonym [dbo].[s01qryItemsDeleted]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryItemsDeleted] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryItemsDeleted]
GO
/****** Object:  Synonym [dbo].[s01qryItemsDisplay]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryItemsDisplay] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryItemsDisplay]
GO
/****** Object:  Synonym [dbo].[s01qryItemsDisplayLite]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryItemsDisplayLite] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryItemsDisplayLite]
GO
/****** Object:  Synonym [dbo].[s01qryItemStatus]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryItemStatus] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryItemStatus]
GO
/****** Object:  Synonym [dbo].[s01qryItemUsage]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryItemUsage] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryItemUsage]
GO
/****** Object:  Synonym [dbo].[s01qryItemUsageBarcode]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryItemUsageBarcode] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryItemUsageBarcode]
GO
/****** Object:  Synonym [dbo].[s01qryItemUsageCredit]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryItemUsageCredit] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryItemUsageCredit]
GO
/****** Object:  Synonym [dbo].[s01qryItemUsageList]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryItemUsageList] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryItemUsageList]
GO
/****** Object:  Synonym [dbo].[s01qryItemUsageOrder]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryItemUsageOrder] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryItemUsageOrder]
GO
/****** Object:  Synonym [dbo].[s01qryLawsonItemsUsed]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryLawsonItemsUsed] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryLawsonItemsUsed]
GO
/****** Object:  Synonym [dbo].[s01qryLawsonReorder]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryLawsonReorder] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryLawsonReorder]
GO
/****** Object:  Synonym [dbo].[s01qryLawsonReorderQty]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryLawsonReorderQty] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryLawsonReorderQty]
GO
/****** Object:  Synonym [dbo].[s01qryLocation]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryLocation] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryLocation]
GO
/****** Object:  Synonym [dbo].[s01qryMaterialMessages]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryMaterialMessages] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryMaterialMessages]
GO
/****** Object:  Synonym [dbo].[s01qryMaterialMsgs]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryMaterialMsgs] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryMaterialMsgs]
GO
/****** Object:  Synonym [dbo].[s01qryMissingPhysicians]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryMissingPhysicians] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryMissingPhysicians]
GO
/****** Object:  Synonym [dbo].[s01qryMMIncompleteOrder]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryMMIncompleteOrder] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryMMIncompleteOrder]
GO
/****** Object:  Synonym [dbo].[s01qryMMIncompleteOrderByCluster]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryMMIncompleteOrderByCluster] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryMMIncompleteOrderByCluster]
GO
/****** Object:  Synonym [dbo].[s01qryMMReorder]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryMMReorder] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryMMReorder]
GO
/****** Object:  Synonym [dbo].[s01qryNewItem]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryNewItem] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryNewItem]
GO
/****** Object:  Synonym [dbo].[s01qryNoTransactions]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryNoTransactions] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryNoTransactions]
GO
/****** Object:  Synonym [dbo].[s01qryOperation]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryOperation] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryOperation]
GO
/****** Object:  Synonym [dbo].[s01qryOrderLocation]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryOrderLocation] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryOrderLocation]
GO
/****** Object:  Synonym [dbo].[s01qryOverrideNotesChoice]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryOverrideNotesChoice] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryOverrideNotesChoice]
GO
/****** Object:  Synonym [dbo].[s01qryParCheck]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryParCheck] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryParCheck]
GO
/****** Object:  Synonym [dbo].[s01qryParlevel]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryParlevel] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryParlevel]
GO
/****** Object:  Synonym [dbo].[s01qryParlevelReorder]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryParlevelReorder] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryParlevelReorder]
GO
/****** Object:  Synonym [dbo].[s01qryParlevelReorderByCluster]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryParlevelReorderByCluster] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryParlevelReorderByCluster]
GO
/****** Object:  Synonym [dbo].[s01qryParlevelReorderByDept]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryParlevelReorderByDept] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryParlevelReorderByDept]
GO
/****** Object:  Synonym [dbo].[s01qryParLevelReport]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryParLevelReport] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryParLevelReport]
GO
/****** Object:  Synonym [dbo].[s01qryParLevels]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryParLevels] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryParLevels]
GO
/****** Object:  Synonym [dbo].[s01qryPatientBillingByItem]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryPatientBillingByItem] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryPatientBillingByItem]
GO
/****** Object:  Synonym [dbo].[s01qryPatientBillingByItemUsage]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryPatientBillingByItemUsage] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryPatientBillingByItemUsage]
GO
/****** Object:  Synonym [dbo].[s01qryPatientCharge]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryPatientCharge] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryPatientCharge]
GO
/****** Object:  Synonym [dbo].[s01qryPatientCredit]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryPatientCredit] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryPatientCredit]
GO
/****** Object:  Synonym [dbo].[s01qryPatientDefault]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryPatientDefault] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryPatientDefault]
GO
/****** Object:  Synonym [dbo].[s01qryPatientDisplay]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryPatientDisplay] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryPatientDisplay]
GO
/****** Object:  Synonym [dbo].[s01qryPatientDisplayADT]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryPatientDisplayADT] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryPatientDisplayADT]
GO
/****** Object:  Synonym [dbo].[s01qryPatientDisplaySCH]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryPatientDisplaySCH] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryPatientDisplaySCH]
GO
/****** Object:  Synonym [dbo].[s01qryPatientDisplaySelected]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryPatientDisplaySelected] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryPatientDisplaySelected]
GO
/****** Object:  Synonym [dbo].[s01qryPatientInfo]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryPatientInfo] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryPatientInfo]
GO
/****** Object:  Synonym [dbo].[s01qryPatientLocation]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryPatientLocation] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryPatientLocation]
GO
/****** Object:  Synonym [dbo].[s01qryPatientLookup]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryPatientLookup] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryPatientLookup]
GO
/****** Object:  Synonym [dbo].[s01qryPatientMsgs]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryPatientMsgs] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryPatientMsgs]
GO
/****** Object:  Synonym [dbo].[s01qryPatients]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryPatients] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryPatients]
GO
/****** Object:  Synonym [dbo].[s01qryPatientSchedules]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryPatientSchedules] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryPatientSchedules]
GO
/****** Object:  Synonym [dbo].[s01qryPatientSchedulesActive]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryPatientSchedulesActive] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryPatientSchedulesActive]
GO
/****** Object:  Synonym [dbo].[s01qryPatientSchedulesChoice]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryPatientSchedulesChoice] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryPatientSchedulesChoice]
GO
/****** Object:  Synonym [dbo].[s01qryPatientSchedulesDisplay]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryPatientSchedulesDisplay] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryPatientSchedulesDisplay]
GO
/****** Object:  Synonym [dbo].[s01qryPatientsDisplay]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryPatientsDisplay] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryPatientsDisplay]
GO
/****** Object:  Synonym [dbo].[s01qryPatientSelectedToday]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryPatientSelectedToday] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryPatientSelectedToday]
GO
/****** Object:  Synonym [dbo].[s01qryPatientSelection]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryPatientSelection] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryPatientSelection]
GO
/****** Object:  Synonym [dbo].[s01qryPatientSelectionSearch]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryPatientSelectionSearch] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryPatientSelectionSearch]
GO
/****** Object:  Synonym [dbo].[s01qryPatientTrackHistory]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryPatientTrackHistory] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryPatientTrackHistory]
GO
/****** Object:  Synonym [dbo].[s01qryPatientUsageHistory]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryPatientUsageHistory] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryPatientUsageHistory]
GO
/****** Object:  Synonym [dbo].[s01qryPatientVisits]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryPatientVisits] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryPatientVisits]
GO
/****** Object:  Synonym [dbo].[s01qryPatientVisitsChoice]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryPatientVisitsChoice] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryPatientVisitsChoice]
GO
/****** Object:  Synonym [dbo].[s01qryPharmacyBulkOpenBottle]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryPharmacyBulkOpenBottle] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryPharmacyBulkOpenBottle]
GO
/****** Object:  Synonym [dbo].[s01qryPhysicianLocation]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryPhysicianLocation] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryPhysicianLocation]
GO
/****** Object:  Synonym [dbo].[s01qryPhysicians]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryPhysicians] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryPhysicians]
GO
/****** Object:  Synonym [dbo].[s01qryPhysiciansDisplay]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryPhysiciansDisplay] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryPhysiciansDisplay]
GO
/****** Object:  Synonym [dbo].[s01qryPrintTags]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryPrintTags] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryPrintTags]
GO
/****** Object:  Synonym [dbo].[s01qryPrintUsage]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryPrintUsage] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryPrintUsage]
GO
/****** Object:  Synonym [dbo].[s01qryProductCatalog]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryProductCatalog] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryProductCatalog]
GO
/****** Object:  Synonym [dbo].[s01qryProductCategoryAdvSearchChoice]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryProductCategoryAdvSearchChoice] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryProductCategoryAdvSearchChoice]
GO
/****** Object:  Synonym [dbo].[s01qryProductDirectory]    Script Date: 11/21/2024 2:16:26 AM ******/
CREATE SYNONYM [dbo].[s01qryProductDirectory] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryProductDirectory]
GO
/****** Object:  Synonym [dbo].[s01qryProductInventory]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01qryProductInventory] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryProductInventory]
GO
/****** Object:  Synonym [dbo].[s01qryProductInventoryByStatus]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01qryProductInventoryByStatus] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryProductInventoryByStatus]
GO
/****** Object:  Synonym [dbo].[s01qryProductInventoryLevels]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01qryProductInventoryLevels] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryProductInventoryLevels]
GO
/****** Object:  Synonym [dbo].[s01qryProductKitDetails]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01qryProductKitDetails] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryProductKitDetails]
GO
/****** Object:  Synonym [dbo].[s01qryProductKits]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01qryProductKits] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryProductKits]
GO
/****** Object:  Synonym [dbo].[s01qryProductMaster]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01qryProductMaster] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryProductMaster]
GO
/****** Object:  Synonym [dbo].[s01qryProducts]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01qryProducts] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryProducts]
GO
/****** Object:  Synonym [dbo].[s01qryProductsAdvSearchChoice]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01qryProductsAdvSearchChoice] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryProductsAdvSearchChoice]
GO
/****** Object:  Synonym [dbo].[s01qryProductsDisplay]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01qryProductsDisplay] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryProductsDisplay]
GO
/****** Object:  Synonym [dbo].[s01qryProductsOrderKit]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01qryProductsOrderKit] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryProductsOrderKit]
GO
/****** Object:  Synonym [dbo].[s01qryProductsSizeAdvSearchChoice]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01qryProductsSizeAdvSearchChoice] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryProductsSizeAdvSearchChoice]
GO
/****** Object:  Synonym [dbo].[s01qryProductsUsed]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01qryProductsUsed] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryProductsUsed]
GO
/****** Object:  Synonym [dbo].[s01qryProductUsedInventoryByStatus]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01qryProductUsedInventoryByStatus] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryProductUsedInventoryByStatus]
GO
/****** Object:  Synonym [dbo].[s01qryProtocolsPhysician]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01qryProtocolsPhysician] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryProtocolsPhysician]
GO
/****** Object:  Synonym [dbo].[s01qryRandNumber]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01qryRandNumber] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryRandNumber]
GO
/****** Object:  Synonym [dbo].[s01qryReceiveTissueLocation]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01qryReceiveTissueLocation] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryReceiveTissueLocation]
GO
/****** Object:  Synonym [dbo].[s01qryReceiveTissueLocationCathLab]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01qryReceiveTissueLocationCathLab] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryReceiveTissueLocationCathLab]
GO
/****** Object:  Synonym [dbo].[s01qryReceiveTissueLocationMainOR]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01qryReceiveTissueLocationMainOR] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryReceiveTissueLocationMainOR]
GO
/****** Object:  Synonym [dbo].[s01qryReorderCalculation]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01qryReorderCalculation] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryReorderCalculation]
GO
/****** Object:  Synonym [dbo].[s01qryReorderCalculationALL]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01qryReorderCalculationALL] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryReorderCalculationALL]
GO
/****** Object:  Synonym [dbo].[s01qryReorderCalculationByDept]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01qryReorderCalculationByDept] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryReorderCalculationByDept]
GO
/****** Object:  Synonym [dbo].[s01qryReorderCalculationCheck]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01qryReorderCalculationCheck] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryReorderCalculationCheck]
GO
/****** Object:  Synonym [dbo].[s01qryReorderCalculationCheck2]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01qryReorderCalculationCheck2] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryReorderCalculationCheck2]
GO
/****** Object:  Synonym [dbo].[s01qryReorderCalculationCheckByCluster]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01qryReorderCalculationCheckByCluster] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryReorderCalculationCheckByCluster]
GO
/****** Object:  Synonym [dbo].[s01qryReorderCalculationCheckByDept]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01qryReorderCalculationCheckByDept] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryReorderCalculationCheckByDept]
GO
/****** Object:  Synonym [dbo].[s01qryReorderInventory]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01qryReorderInventory] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryReorderInventory]
GO
/****** Object:  Synonym [dbo].[s01qryReorderInventory2]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01qryReorderInventory2] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryReorderInventory2]
GO
/****** Object:  Synonym [dbo].[s01qryReportAgedInventory]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01qryReportAgedInventory] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryReportAgedInventory]
GO
/****** Object:  Synonym [dbo].[s01qryReportBasicReorder]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01qryReportBasicReorder] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryReportBasicReorder]
GO
/****** Object:  Synonym [dbo].[s01qryReportBasicReorder4]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01qryReportBasicReorder4] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryReportBasicReorder4]
GO
/****** Object:  Synonym [dbo].[s01qryReportCabinetInventory]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01qryReportCabinetInventory] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryReportCabinetInventory]
GO
/****** Object:  Synonym [dbo].[s01qryReportConsignmentRule]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01qryReportConsignmentRule] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryReportConsignmentRule]
GO
/****** Object:  Synonym [dbo].[s01qryReportDailyInventory]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01qryReportDailyInventory] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryReportDailyInventory]
GO
/****** Object:  Synonym [dbo].[s01qryReportDailyStockedItems]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01qryReportDailyStockedItems] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryReportDailyStockedItems]
GO
/****** Object:  Synonym [dbo].[s01qryReportExpiredItemsByMonth]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01qryReportExpiredItemsByMonth] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryReportExpiredItemsByMonth]
GO
/****** Object:  Synonym [dbo].[s01qryReportHomograftInventory]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01qryReportHomograftInventory] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryReportHomograftInventory]
GO
/****** Object:  Synonym [dbo].[s01qryReportInventory]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01qryReportInventory] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryReportInventory]
GO
/****** Object:  Synonym [dbo].[s01qryReportInventoryByDepartment]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01qryReportInventoryByDepartment] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryReportInventoryByDepartment]
GO
/****** Object:  Synonym [dbo].[s01qryReportInventoryCost]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01qryReportInventoryCost] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryReportInventoryCost]
GO
/****** Object:  Synonym [dbo].[s01qryReportInventoryCost1]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01qryReportInventoryCost1] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryReportInventoryCost1]
GO
/****** Object:  Synonym [dbo].[s01qryReportInventoryCost11]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01qryReportInventoryCost11] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryReportInventoryCost11]
GO
/****** Object:  Synonym [dbo].[s01qryReportInventoryCost12]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01qryReportInventoryCost12] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryReportInventoryCost12]
GO
/****** Object:  Synonym [dbo].[s01qryReportInventoryCost2]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01qryReportInventoryCost2] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryReportInventoryCost2]
GO
/****** Object:  Synonym [dbo].[s01qryReportInventoryEach]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01qryReportInventoryEach] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryReportInventoryEach]
GO
/****** Object:  Synonym [dbo].[s01qryReportInventoryInCabinet]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01qryReportInventoryInCabinet] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryReportInventoryInCabinet]
GO
/****** Object:  Synonym [dbo].[s01qryReportInventorySummary]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01qryReportInventorySummary] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryReportInventorySummary]
GO
/****** Object:  Synonym [dbo].[s01qryReportItemHistory]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01qryReportItemHistory] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryReportItemHistory]
GO
/****** Object:  Synonym [dbo].[s01qryReportItems]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01qryReportItems] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryReportItems]
GO
/****** Object:  Synonym [dbo].[s01qryReportItemTransfer]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01qryReportItemTransfer] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryReportItemTransfer]
GO
/****** Object:  Synonym [dbo].[s01qryReportList]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01qryReportList] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryReportList]
GO
/****** Object:  Synonym [dbo].[s01qryReportOrderFulfillment]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01qryReportOrderFulfillment] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryReportOrderFulfillment]
GO
/****** Object:  Synonym [dbo].[s01qryReportOrderHistory]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01qryReportOrderHistory] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryReportOrderHistory]
GO
/****** Object:  Synonym [dbo].[s01qryReportOutstandingOrder]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01qryReportOutstandingOrder] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryReportOutstandingOrder]
GO
/****** Object:  Synonym [dbo].[s01qryReportProductList]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01qryReportProductList] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryReportProductList]
GO
/****** Object:  Synonym [dbo].[s01qryReportProductReconciliation]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01qryReportProductReconciliation] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryReportProductReconciliation]
GO
/****** Object:  Synonym [dbo].[s01qryReportProductsWithoutPars]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01qryReportProductsWithoutPars] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryReportProductsWithoutPars]
GO
/****** Object:  Synonym [dbo].[s01qryReportReorder]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01qryReportReorder] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryReportReorder]
GO
/****** Object:  Synonym [dbo].[s01qryReportRestock]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01qryReportRestock] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryReportRestock]
GO
/****** Object:  Synonym [dbo].[s01qryReportRestockInventory]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01qryReportRestockInventory] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryReportRestockInventory]
GO
/****** Object:  Synonym [dbo].[s01qryReportSpecimenManagement]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01qryReportSpecimenManagement] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryReportSpecimenManagement]
GO
/****** Object:  Synonym [dbo].[s01qryReportTissueBankTracking]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01qryReportTissueBankTracking] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryReportTissueBankTracking]
GO
/****** Object:  Synonym [dbo].[s01qryReportTransactionTracking]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01qryReportTransactionTracking] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryReportTransactionTracking]
GO
/****** Object:  Synonym [dbo].[s01qryReportUsage]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01qryReportUsage] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryReportUsage]
GO
/****** Object:  Synonym [dbo].[s01qryReportUsage_Basic_ReoReport]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01qryReportUsage_Basic_ReoReport] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryReportUsage_Basic_ReoReport]
GO
/****** Object:  Synonym [dbo].[s01qryReportUsageDeleted]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01qryReportUsageDeleted] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryReportUsageDeleted]
GO
/****** Object:  Synonym [dbo].[s01qryReportUsageExc]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01qryReportUsageExc] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryReportUsageExc]
GO
/****** Object:  Synonym [dbo].[s01qryReportUsageHistory]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01qryReportUsageHistory] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryReportUsageHistory]
GO
/****** Object:  Synonym [dbo].[s01qryReportUsageImplant]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01qryReportUsageImplant] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryReportUsageImplant]
GO
/****** Object:  Synonym [dbo].[s01qryReportUsageVendorCard]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01qryReportUsageVendorCard] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryReportUsageVendorCard]
GO
/****** Object:  Synonym [dbo].[s01qryReportUsageVendorCardEPIC]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01qryReportUsageVendorCardEPIC] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryReportUsageVendorCardEPIC]
GO
/****** Object:  Synonym [dbo].[s01qryStockInventory]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01qryStockInventory] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryStockInventory]
GO
/****** Object:  Synonym [dbo].[s01qrySupplyAlertsZeroTransactionsLogin]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01qrySupplyAlertsZeroTransactionsLogin] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qrySupplyAlertsZeroTransactionsLogin]
GO
/****** Object:  Synonym [dbo].[s01qrySysProcedures]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01qrySysProcedures] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qrySysProcedures]
GO
/****** Object:  Synonym [dbo].[s01qrySysTables]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01qrySysTables] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qrySysTables]
GO
/****** Object:  Synonym [dbo].[s01qrySystemAccess]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01qrySystemAccess] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qrySystemAccess]
GO
/****** Object:  Synonym [dbo].[s01qrySysViews]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01qrySysViews] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qrySysViews]
GO
/****** Object:  Synonym [dbo].[s01qryTissueReceipts]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01qryTissueReceipts] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryTissueReceipts]
GO
/****** Object:  Synonym [dbo].[s01qryTissueStorageMethod]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01qryTissueStorageMethod] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryTissueStorageMethod]
GO
/****** Object:  Synonym [dbo].[s01qryTransferItemInventory]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01qryTransferItemInventory] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryTransferItemInventory]
GO
/****** Object:  Synonym [dbo].[s01qryUserLogReport]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01qryUserLogReport] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryUserLogReport]
GO
/****** Object:  Synonym [dbo].[s01qryUsers]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01qryUsers] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryUsers]
GO
/****** Object:  Synonym [dbo].[s01qryUsersAuthentication]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01qryUsersAuthentication] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryUsersAuthentication]
GO
/****** Object:  Synonym [dbo].[s01qryUsersDisplay]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01qryUsersDisplay] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryUsersDisplay]
GO
/****** Object:  Synonym [dbo].[s01qryUsersLog]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01qryUsersLog] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryUsersLog]
GO
/****** Object:  Synonym [dbo].[s01qryVerifyProductImport]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01qryVerifyProductImport] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryVerifyProductImport]
GO
/****** Object:  Synonym [dbo].[s01qryVirtualClusterLocation]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01qryVirtualClusterLocation] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryVirtualClusterLocation]
GO
/****** Object:  Synonym [dbo].[s01qryVirtualClusterLocationCathLab]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01qryVirtualClusterLocationCathLab] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryVirtualClusterLocationCathLab]
GO
/****** Object:  Synonym [dbo].[s01qryVirtualClusterLocationMainOR]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01qryVirtualClusterLocationMainOR] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[qryVirtualClusterLocationMainOR]
GO
/****** Object:  Synonym [dbo].[s01sp_add_all_users_to_all_cluster]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01sp_add_all_users_to_all_cluster] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[sp_add_all_users_to_all_cluster]
GO
/****** Object:  Synonym [dbo].[s01sp_add_all_users_to_cluster]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01sp_add_all_users_to_cluster] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[sp_add_all_users_to_cluster]
GO
/****** Object:  Synonym [dbo].[s01sp_add_user_access]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01sp_add_user_access] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[sp_add_user_access]
GO
/****** Object:  Synonym [dbo].[s01sp_check_inventory]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01sp_check_inventory] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[sp_check_inventory]
GO
/****** Object:  Synonym [dbo].[s01sp_create_db_maintenance_plan]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01sp_create_db_maintenance_plan] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[sp_create_db_maintenance_plan]
GO
/****** Object:  Synonym [dbo].[s01sp_create_maintenance_plan]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01sp_create_maintenance_plan] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[sp_create_maintenance_plan]
GO
/****** Object:  Synonym [dbo].[s01sp_CreateDailyReports]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01sp_CreateDailyReports] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[sp_CreateDailyReports]
GO
/****** Object:  Synonym [dbo].[s01sp_duplicate_ItemInventory_Resolver]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01sp_duplicate_ItemInventory_Resolver] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[sp_duplicate_ItemInventory_Resolver]
GO
/****** Object:  Synonym [dbo].[s01sp_duplicate_patients_Resolver]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01sp_duplicate_patients_Resolver] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[sp_duplicate_patients_Resolver]
GO
/****** Object:  Synonym [dbo].[s01sp_duplicate_Products_Resolver]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01sp_duplicate_Products_Resolver] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[sp_duplicate_Products_Resolver]
GO
/****** Object:  Synonym [dbo].[s01sp_duplicate_users_Resolver]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01sp_duplicate_users_Resolver] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[sp_duplicate_users_Resolver]
GO
/****** Object:  Synonym [dbo].[s01sp_duplicate_Visit_Resolver]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01sp_duplicate_Visit_Resolver] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[sp_duplicate_Visit_Resolver]
GO
/****** Object:  Synonym [dbo].[s01sp_Item_Conflict_Resolver]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01sp_Item_Conflict_Resolver] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[sp_Item_Conflict_Resolver]
GO
/****** Object:  Synonym [dbo].[s01sp_process_event]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01sp_process_event] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[sp_process_event]
GO
/****** Object:  Synonym [dbo].[s01sp_process_missing_chargecode]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01sp_process_missing_chargecode] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[sp_process_missing_chargecode]
GO
/****** Object:  Synonym [dbo].[s01sp_process_new_products]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01sp_process_new_products] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[sp_process_new_products]
GO
/****** Object:  Synonym [dbo].[s01sp_process_patient_billing]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01sp_process_patient_billing] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[sp_process_patient_billing]
GO
/****** Object:  Synonym [dbo].[s01sp_process_patient_credit]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01sp_process_patient_credit] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[sp_process_patient_credit]
GO
/****** Object:  Synonym [dbo].[s01sp_send_cdosysmail]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01sp_send_cdosysmail] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[sp_send_cdosysmail]
GO
/****** Object:  Synonym [dbo].[s01sp_send_dbstats]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01sp_send_dbstats] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[sp_send_dbstats]
GO
/****** Object:  Synonym [dbo].[s01sp_send_JMail]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01sp_send_JMail] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[sp_send_JMail]
GO
/****** Object:  Synonym [dbo].[s01sp_send_mail]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01sp_send_mail] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[sp_send_mail]
GO
/****** Object:  Synonym [dbo].[s01sp_unapproved_msgs_alert]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01sp_unapproved_msgs_alert] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[sp_unapproved_msgs_alert]
GO
/****** Object:  Synonym [dbo].[s01spArchiveItemDetails]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01spArchiveItemDetails] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[spArchiveItemDetails]
GO
/****** Object:  Synonym [dbo].[s01spArchiveItems]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01spArchiveItems] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[spArchiveItems]
GO
/****** Object:  Synonym [dbo].[s01spArchivePatientDetails]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01spArchivePatientDetails] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[spArchivePatientDetails]
GO
/****** Object:  Synonym [dbo].[s01spCerner_ItemTransaction]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01spCerner_ItemTransaction] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[spCerner_ItemTransaction]
GO
/****** Object:  Synonym [dbo].[s01spCerner_RegisterDevice]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01spCerner_RegisterDevice] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[spCerner_RegisterDevice]
GO
/****** Object:  Synonym [dbo].[s01spCerner_ReprocessItemTransaction]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01spCerner_ReprocessItemTransaction] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[spCerner_ReprocessItemTransaction]
GO
/****** Object:  Synonym [dbo].[s01spCerner_UnRegisterDevice]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01spCerner_UnRegisterDevice] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[spCerner_UnRegisterDevice]
GO
/****** Object:  Synonym [dbo].[s01spCheckForDeadTags]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01spCheckForDeadTags] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[spCheckForDeadTags]
GO
/****** Object:  Synonym [dbo].[s01spCreateDatabaseBackup]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01spCreateDatabaseBackup] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[spCreateDatabaseBackup]
GO
/****** Object:  Synonym [dbo].[s01spDailyUsedItems]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01spDailyUsedItems] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[spDailyUsedItems]
GO
/****** Object:  Synonym [dbo].[s01spFulFillOrders]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01spFulFillOrders] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[spFulFillOrders]
GO
/****** Object:  Synonym [dbo].[s01spInventoryLevel]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01spInventoryLevel] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[spInventoryLevel]
GO
/****** Object:  Synonym [dbo].[s01spInventoryWastageSummary]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01spInventoryWastageSummary] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[spInventoryWastageSummary]
GO
/****** Object:  Synonym [dbo].[s01spItemsExpired]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01spItemsExpired] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[spItemsExpired]
GO
/****** Object:  Synonym [dbo].[s01spPatientBilling]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01spPatientBilling] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[spPatientBilling]
GO
/****** Object:  Synonym [dbo].[s01spProcedureAnalysis]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01spProcedureAnalysis] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[spProcedureAnalysis]
GO
/****** Object:  Synonym [dbo].[s01spProcessExpirationAlerts]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01spProcessExpirationAlerts] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[spProcessExpirationAlerts]
GO
/****** Object:  Synonym [dbo].[s01spProcessLeadExpirationAlerts]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01spProcessLeadExpirationAlerts] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[spProcessLeadExpirationAlerts]
GO
/****** Object:  Synonym [dbo].[s01spReorderItems]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01spReorderItems] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[spReorderItems]
GO
/****** Object:  Synonym [dbo].[s01spTableMaitenance]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01spTableMaitenance] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[spTableMaitenance]
GO
/****** Object:  Synonym [dbo].[s01spWeeklyPatientCount]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01spWeeklyPatientCount] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[spWeeklyPatientCount]
GO
/****** Object:  Synonym [dbo].[s01tblAllergies]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblAllergies] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblAllergies]
GO
/****** Object:  Synonym [dbo].[s01tblAlphaBoxLog]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblAlphaBoxLog] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblAlphaBoxLog]
GO
/****** Object:  Synonym [dbo].[s01tblAppEventLog]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblAppEventLog] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblAppEventLog]
GO
/****** Object:  Synonym [dbo].[s01tblAppEvents]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblAppEvents] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblAppEvents]
GO
/****** Object:  Synonym [dbo].[s01tblApprovalStatusTypes]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblApprovalStatusTypes] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblApprovalStatusTypes]
GO
/****** Object:  Synonym [dbo].[s01tblAssetItems]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblAssetItems] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblAssetItems]
GO
/****** Object:  Synonym [dbo].[s01tblAssetProducts]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblAssetProducts] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblAssetProducts]
GO
/****** Object:  Synonym [dbo].[s01tblAssetType]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblAssetType] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblAssetType]
GO
/****** Object:  Synonym [dbo].[s01tblBadMaterialMessages]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblBadMaterialMessages] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblBadMaterialMessages]
GO
/****** Object:  Synonym [dbo].[s01tblBarcodeMaskItem]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblBarcodeMaskItem] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblBarcodeMaskItem]
GO
/****** Object:  Synonym [dbo].[s01tblBarcodeMaskProduct]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblBarcodeMaskProduct] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblBarcodeMaskProduct]
GO
/****** Object:  Synonym [dbo].[s01tblBarcodeRuleItem]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblBarcodeRuleItem] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblBarcodeRuleItem]
GO
/****** Object:  Synonym [dbo].[s01tblBarcodeRuleProduct]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblBarcodeRuleProduct] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblBarcodeRuleProduct]
GO
/****** Object:  Synonym [dbo].[s01tblBillingStatus]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblBillingStatus] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblBillingStatus]
GO
/****** Object:  Synonym [dbo].[s01tblBillingVerificationReport]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblBillingVerificationReport] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblBillingVerificationReport]
GO
/****** Object:  Synonym [dbo].[s01tblBillOnlyItemHistory]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblBillOnlyItemHistory] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblBillOnlyItemHistory]
GO
/****** Object:  Synonym [dbo].[s01tblBillOnlyItems]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblBillOnlyItems] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblBillOnlyItems]
GO
/****** Object:  Synonym [dbo].[s01tblCabinetLog]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblCabinetLog] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblCabinetLog]
GO
/****** Object:  Synonym [dbo].[s01tblCabinets]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblCabinets] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblCabinets]
GO
/****** Object:  Synonym [dbo].[s01tblCabinetType]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblCabinetType] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblCabinetType]
GO
/****** Object:  Synonym [dbo].[s01tblCaseStatus]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblCaseStatus] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblCaseStatus]
GO
/****** Object:  Synonym [dbo].[s01tblCernerInterfaceLog]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblCernerInterfaceLog] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblCernerInterfaceLog]
GO
/****** Object:  Synonym [dbo].[s01tblChargeCodes]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblChargeCodes] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblChargeCodes]
GO
/****** Object:  Synonym [dbo].[s01tblClusterLawson]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblClusterLawson] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblClusterLawson]
GO
/****** Object:  Synonym [dbo].[s01tblClusterParLevel]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblClusterParLevel] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblClusterParLevel]
GO
/****** Object:  Synonym [dbo].[s01tblClusters]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblClusters] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblClusters]
GO
/****** Object:  Synonym [dbo].[s01tblCompartmentAccess]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblCompartmentAccess] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblCompartmentAccess]
GO
/****** Object:  Synonym [dbo].[s01tblCompartments]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblCompartments] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblCompartments]
GO
/****** Object:  Synonym [dbo].[s01tblConsignmentProducts]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblConsignmentProducts] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblConsignmentProducts]
GO
/****** Object:  Synonym [dbo].[s01tblConsignmentRule]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblConsignmentRule] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblConsignmentRule]
GO
/****** Object:  Synonym [dbo].[s01tblDailyUsedItems]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblDailyUsedItems] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblDailyUsedItems]
GO
/****** Object:  Synonym [dbo].[s01tblDetailDesc]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblDetailDesc] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblDetailDesc]
GO
/****** Object:  Synonym [dbo].[s01tblDevices]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblDevices] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblDevices]
GO
/****** Object:  Synonym [dbo].[s01tblDocumentExtension]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblDocumentExtension] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblDocumentExtension]
GO
/****** Object:  Synonym [dbo].[s01tblEntryType]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblEntryType] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblEntryType]
GO
/****** Object:  Synonym [dbo].[s01tblErrorLog]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblErrorLog] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblErrorLog]
GO
/****** Object:  Synonym [dbo].[s01tblEventLog]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblEventLog] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblEventLog]
GO
/****** Object:  Synonym [dbo].[s01tblEventType]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblEventType] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblEventType]
GO
/****** Object:  Synonym [dbo].[s01tblException]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblException] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblException]
GO
/****** Object:  Synonym [dbo].[s01tblExceptionNotes]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblExceptionNotes] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblExceptionNotes]
GO
/****** Object:  Synonym [dbo].[s01tblExceptionNotesMap]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblExceptionNotesMap] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblExceptionNotesMap]
GO
/****** Object:  Synonym [dbo].[s01tblExpiredItems]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblExpiredItems] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblExpiredItems]
GO
/****** Object:  Synonym [dbo].[s01tblFacilities]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblFacilities] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblFacilities]
GO
/****** Object:  Synonym [dbo].[s01tblFulFill]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblFulFill] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblFulFill]
GO
/****** Object:  Synonym [dbo].[s01tblHTMLExpirationAlert]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblHTMLExpirationAlert] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblHTMLExpirationAlert]
GO
/****** Object:  Synonym [dbo].[s01tblHTMLLeadExpirationAlert]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblHTMLLeadExpirationAlert] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblHTMLLeadExpirationAlert]
GO
/****** Object:  Synonym [dbo].[s01tblInventoryCheck]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblInventoryCheck] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblInventoryCheck]
GO
/****** Object:  Synonym [dbo].[s01tblInventoryLevels]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblInventoryLevels] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblInventoryLevels]
GO
/****** Object:  Synonym [dbo].[s01tblInventoryWastageSummary]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblInventoryWastageSummary] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblInventoryWastageSummary]
GO
/****** Object:  Synonym [dbo].[s01tblItemEvent]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblItemEvent] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblItemEvent]
GO
/****** Object:  Synonym [dbo].[s01tblItemHistory]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblItemHistory] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblItemHistory]
GO
/****** Object:  Synonym [dbo].[s01tblItemHistoryExtn]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblItemHistoryExtn] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblItemHistoryExtn]
GO
/****** Object:  Synonym [dbo].[s01tblItemInventory]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblItemInventory] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblItemInventory]
GO
/****** Object:  Synonym [dbo].[s01tblItemInventoryDeleted]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblItemInventoryDeleted] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblItemInventoryDeleted]
GO
/****** Object:  Synonym [dbo].[s01tblItemInventorySpecs]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblItemInventorySpecs] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblItemInventorySpecs]
GO
/****** Object:  Synonym [dbo].[s01tblItemInventorySpecsImport]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblItemInventorySpecsImport] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblItemInventorySpecsImport]
GO
/****** Object:  Synonym [dbo].[s01tblItems]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblItems] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblItems]
GO
/****** Object:  Synonym [dbo].[s01tblItemsDeleted]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblItemsDeleted] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblItemsDeleted]
GO
/****** Object:  Synonym [dbo].[s01tblItemsExt]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblItemsExt] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblItemsExt]
GO
/****** Object:  Synonym [dbo].[s01tblItemsLoaned]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblItemsLoaned] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblItemsLoaned]
GO
/****** Object:  Synonym [dbo].[s01tblItemSpecs]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblItemSpecs] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblItemSpecs]
GO
/****** Object:  Synonym [dbo].[s01tblItemStatus]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblItemStatus] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblItemStatus]
GO
/****** Object:  Synonym [dbo].[s01tblItemsUnassociated]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblItemsUnassociated] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblItemsUnassociated]
GO
/****** Object:  Synonym [dbo].[s01tblItemUsage]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblItemUsage] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblItemUsage]
GO
/****** Object:  Synonym [dbo].[s01tblItemUsageCredit]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblItemUsageCredit] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblItemUsageCredit]
GO
/****** Object:  Synonym [dbo].[s01tblItemUsageExt]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblItemUsageExt] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblItemUsageExt]
GO
/****** Object:  Synonym [dbo].[s01tblItemUsageNO]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblItemUsageNO] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblItemUsageNO]
GO
/****** Object:  Synonym [dbo].[s01tblItemUsageOrder]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblItemUsageOrder] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblItemUsageOrder]
GO
/****** Object:  Synonym [dbo].[s01tblItemUsageTest]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblItemUsageTest] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblItemUsageTest]
GO
/****** Object:  Synonym [dbo].[s01tblItemWastedCount]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblItemWastedCount] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblItemWastedCount]
GO
/****** Object:  Synonym [dbo].[s01tblLawsonLocation]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblLawsonLocation] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblLawsonLocation]
GO
/****** Object:  Synonym [dbo].[s01tblLocationCity]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblLocationCity] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblLocationCity]
GO
/****** Object:  Synonym [dbo].[s01tblLocationCountry]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblLocationCountry] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblLocationCountry]
GO
/****** Object:  Synonym [dbo].[s01tblLocationDetail]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblLocationDetail] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblLocationDetail]
GO
/****** Object:  Synonym [dbo].[s01tblLocationHospital]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblLocationHospital] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblLocationHospital]
GO
/****** Object:  Synonym [dbo].[s01tblLocationMap]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblLocationMap] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblLocationMap]
GO
/****** Object:  Synonym [dbo].[s01tblLocationRoom]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblLocationRoom] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblLocationRoom]
GO
/****** Object:  Synonym [dbo].[s01tblLocationState]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblLocationState] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblLocationState]
GO
/****** Object:  Synonym [dbo].[s01tblLocationTracking]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblLocationTracking] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblLocationTracking]
GO
/****** Object:  Synonym [dbo].[s01tblLocationTrackingHistory]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblLocationTrackingHistory] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblLocationTrackingHistory]
GO
/****** Object:  Synonym [dbo].[s01tblLocationType]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblLocationType] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblLocationType]
GO
/****** Object:  Synonym [dbo].[s01tblManufacturer]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblManufacturer] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblManufacturer]
GO
/****** Object:  Synonym [dbo].[s01tblMaterialMessages]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblMaterialMessages] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblMaterialMessages]
GO
/****** Object:  Synonym [dbo].[s01tblMaterialMessagesAudit]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblMaterialMessagesAudit] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblMaterialMessagesAudit]
GO
/****** Object:  Synonym [dbo].[s01tblMaterialMessagesExtn]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblMaterialMessagesExtn] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblMaterialMessagesExtn]
GO
/****** Object:  Synonym [dbo].[s01tblMaterialMessagesPO]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblMaterialMessagesPO] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblMaterialMessagesPO]
GO
/****** Object:  Synonym [dbo].[s01tblMaterialMsgItems]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblMaterialMsgItems] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblMaterialMsgItems]
GO
/****** Object:  Synonym [dbo].[s01tblMergeItemInventory]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblMergeItemInventory] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblMergeItemInventory]
GO
/****** Object:  Synonym [dbo].[s01tblMergeParlevel]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblMergeParlevel] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblMergeParlevel]
GO
/****** Object:  Synonym [dbo].[s01tblMergeProduct]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblMergeProduct] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblMergeProduct]
GO
/****** Object:  Synonym [dbo].[s01tblMergeProductDetail]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblMergeProductDetail] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblMergeProductDetail]
GO
/****** Object:  Synonym [dbo].[s01tblOrders]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblOrders] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblOrders]
GO
/****** Object:  Synonym [dbo].[s01tblOrdersLocation]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblOrdersLocation] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblOrdersLocation]
GO
/****** Object:  Synonym [dbo].[s01tblOrdersPO]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblOrdersPO] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblOrdersPO]
GO
/****** Object:  Synonym [dbo].[s01tblOrdersPOHistory]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblOrdersPOHistory] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblOrdersPOHistory]
GO
/****** Object:  Synonym [dbo].[s01tblOrderStatusType]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblOrderStatusType] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblOrderStatusType]
GO
/****** Object:  Synonym [dbo].[s01tblOverride]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblOverride] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblOverride]
GO
/****** Object:  Synonym [dbo].[s01tblOverrideNotes]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblOverrideNotes] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblOverrideNotes]
GO
/****** Object:  Synonym [dbo].[s01tblParLevel]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblParLevel] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblParLevel]
GO
/****** Object:  Synonym [dbo].[s01tblParlevelAudit]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblParlevelAudit] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblParlevelAudit]
GO
/****** Object:  Synonym [dbo].[s01tblParLevelDeleted]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblParLevelDeleted] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblParLevelDeleted]
GO
/****** Object:  Synonym [dbo].[s01tblParlevelHistory]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblParlevelHistory] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblParlevelHistory]
GO
/****** Object:  Synonym [dbo].[s01tblParLevelLog]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblParLevelLog] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblParLevelLog]
GO
/****** Object:  Synonym [dbo].[s01tblParLevelPLS]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblParLevelPLS] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblParLevelPLS]
GO
/****** Object:  Synonym [dbo].[s01tblParLevelPLSLog]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblParLevelPLSLog] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblParLevelPLSLog]
GO
/****** Object:  Synonym [dbo].[s01tblParLevelSite]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblParLevelSite] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblParLevelSite]
GO
/****** Object:  Synonym [dbo].[s01tblPatientAllergies]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblPatientAllergies] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblPatientAllergies]
GO
/****** Object:  Synonym [dbo].[s01tblPatientAmbulatoryStatus]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblPatientAmbulatoryStatus] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblPatientAmbulatoryStatus]
GO
/****** Object:  Synonym [dbo].[s01tblPatientBilling]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblPatientBilling] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblPatientBilling]
GO
/****** Object:  Synonym [dbo].[s01tblPatientClass]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblPatientClass] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblPatientClass]
GO
/****** Object:  Synonym [dbo].[s01tblPatientMessageHistory]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblPatientMessageHistory] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblPatientMessageHistory]
GO
/****** Object:  Synonym [dbo].[s01tblPatientMessages]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblPatientMessages] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblPatientMessages]
GO
/****** Object:  Synonym [dbo].[s01tblPatientProcedures]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblPatientProcedures] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblPatientProcedures]
GO
/****** Object:  Synonym [dbo].[s01tblPatients]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblPatients] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblPatients]
GO
/****** Object:  Synonym [dbo].[s01tblPatientSchedules]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblPatientSchedules] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblPatientSchedules]
GO
/****** Object:  Synonym [dbo].[s01tblPatientService]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblPatientService] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblPatientService]
GO
/****** Object:  Synonym [dbo].[s01tblPatientTrackHistory]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblPatientTrackHistory] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblPatientTrackHistory]
GO
/****** Object:  Synonym [dbo].[s01tblPatientTrackStatus]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblPatientTrackStatus] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblPatientTrackStatus]
GO
/****** Object:  Synonym [dbo].[s01tblPatientVisits]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblPatientVisits] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblPatientVisits]
GO
/****** Object:  Synonym [dbo].[s01tblPhysicians]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblPhysicians] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblPhysicians]
GO
/****** Object:  Synonym [dbo].[s01tblPrescriptionItems]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblPrescriptionItems] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblPrescriptionItems]
GO
/****** Object:  Synonym [dbo].[s01tblPrescriptions]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblPrescriptions] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblPrescriptions]
GO
/****** Object:  Synonym [dbo].[s01tblProcedureAnalysis]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblProcedureAnalysis] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblProcedureAnalysis]
GO
/****** Object:  Synonym [dbo].[s01tblProcedures]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblProcedures] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblProcedures]
GO
/****** Object:  Synonym [dbo].[s01tblProductCategory]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblProductCategory] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblProductCategory]
GO
/****** Object:  Synonym [dbo].[s01tblProductCheck]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblProductCheck] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblProductCheck]
GO
/****** Object:  Synonym [dbo].[s01tblProductDetail]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblProductDetail] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblProductDetail]
GO
/****** Object:  Synonym [dbo].[s01tblProductDocument]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblProductDocument] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblProductDocument]
GO
/****** Object:  Synonym [dbo].[s01tblProductDocumentHistory]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblProductDocumentHistory] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblProductDocumentHistory]
GO
/****** Object:  Synonym [dbo].[s01tblProductDocumentTypes]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblProductDocumentTypes] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblProductDocumentTypes]
GO
/****** Object:  Synonym [dbo].[s01tblProductExpireDetail]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblProductExpireDetail] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblProductExpireDetail]
GO
/****** Object:  Synonym [dbo].[s01tblProductGroup]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblProductGroup] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblProductGroup]
GO
/****** Object:  Synonym [dbo].[s01tblProductImport]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblProductImport] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblProductImport]
GO
/****** Object:  Synonym [dbo].[s01tblProductKits]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblProductKits] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblProductKits]
GO
/****** Object:  Synonym [dbo].[s01tblProductOrderTracker]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblProductOrderTracker] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblProductOrderTracker]
GO
/****** Object:  Synonym [dbo].[s01tblProducts]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblProducts] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblProducts]
GO
/****** Object:  Synonym [dbo].[s01tblProductsAudit]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblProductsAudit] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblProductsAudit]
GO
/****** Object:  Synonym [dbo].[s01tblProductsBackup]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblProductsBackup] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblProductsBackup]
GO
/****** Object:  Synonym [dbo].[s01tblProductsCrossWalk]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblProductsCrossWalk] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblProductsCrossWalk]
GO
/****** Object:  Synonym [dbo].[s01tblProductsDeleted]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblProductsDeleted] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblProductsDeleted]
GO
/****** Object:  Synonym [dbo].[s01tblProductsInfor]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblProductsInfor] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblProductsInfor]
GO
/****** Object:  Synonym [dbo].[s01tblProductsLog]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblProductsLog] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblProductsLog]
GO
/****** Object:  Synonym [dbo].[s01tblProductsMaster]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblProductsMaster] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblProductsMaster]
GO
/****** Object:  Synonym [dbo].[s01tblProductsOrderKit]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblProductsOrderKit] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblProductsOrderKit]
GO
/****** Object:  Synonym [dbo].[s01tblProductsSupplier]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblProductsSupplier] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblProductsSupplier]
GO
/****** Object:  Synonym [dbo].[s01tblProductSubCategory]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblProductSubCategory] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblProductSubCategory]
GO
/****** Object:  Synonym [dbo].[s01tblProductTypes]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblProductTypes] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblProductTypes]
GO
/****** Object:  Synonym [dbo].[s01tblProductUnit]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblProductUnit] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblProductUnit]
GO
/****** Object:  Synonym [dbo].[s01tblProtocols]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblProtocols] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblProtocols]
GO
/****** Object:  Synonym [dbo].[s01tblProtocolsPhysician]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblProtocolsPhysician] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblProtocolsPhysician]
GO
/****** Object:  Synonym [dbo].[s01tblReorderedItems]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblReorderedItems] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblReorderedItems]
GO
/****** Object:  Synonym [dbo].[s01tblReorderTypes]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblReorderTypes] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblReorderTypes]
GO
/****** Object:  Synonym [dbo].[s01tblReportCategory]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblReportCategory] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblReportCategory]
GO
/****** Object:  Synonym [dbo].[s01tblReportFields]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblReportFields] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblReportFields]
GO
/****** Object:  Synonym [dbo].[s01tblReportFilter]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblReportFilter] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblReportFilter]
GO
/****** Object:  Synonym [dbo].[s01tblReportLayout]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblReportLayout] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblReportLayout]
GO
/****** Object:  Synonym [dbo].[s01tblReportList]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblReportList] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblReportList]
GO
/****** Object:  Synonym [dbo].[s01tblReports]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblReports] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblReports]
GO
/****** Object:  Synonym [dbo].[s01tblReportStyle]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblReportStyle] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblReportStyle]
GO
/****** Object:  Synonym [dbo].[s01tblReportText]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblReportText] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblReportText]
GO
/****** Object:  Synonym [dbo].[s01tblReportType]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblReportType] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblReportType]
GO
/****** Object:  Synonym [dbo].[s01tblResearchItems]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblResearchItems] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblResearchItems]
GO
/****** Object:  Synonym [dbo].[s01tblSettingsCommon]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblSettingsCommon] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblSettingsCommon]
GO
/****** Object:  Synonym [dbo].[s01tblSettingsUnique]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblSettingsUnique] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblSettingsUnique]
GO
/****** Object:  Synonym [dbo].[s01tblSolutions]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblSolutions] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblSolutions]
GO
/****** Object:  Synonym [dbo].[s01tblSolutionsDetailDeleted]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblSolutionsDetailDeleted] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblSolutionsDetailDeleted]
GO
/****** Object:  Synonym [dbo].[s01tblSolutionsDetailHistory]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblSolutionsDetailHistory] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblSolutionsDetailHistory]
GO
/****** Object:  Synonym [dbo].[s01tblSolutionsDetails]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblSolutionsDetails] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblSolutionsDetails]
GO
/****** Object:  Synonym [dbo].[s01tblStaff]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblStaff] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblStaff]
GO
/****** Object:  Synonym [dbo].[s01tblStaffTypes]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblStaffTypes] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblStaffTypes]
GO
/****** Object:  Synonym [dbo].[s01tblStandardTimeZone]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblStandardTimeZone] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblStandardTimeZone]
GO
/****** Object:  Synonym [dbo].[s01tblStatusTypes]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblStatusTypes] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblStatusTypes]
GO
/****** Object:  Synonym [dbo].[s01tblSupplierDocument]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblSupplierDocument] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblSupplierDocument]
GO
/****** Object:  Synonym [dbo].[s01tblSupplierDocumentHistory]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblSupplierDocumentHistory] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblSupplierDocumentHistory]
GO
/****** Object:  Synonym [dbo].[s01tblSuppliers]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblSuppliers] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblSuppliers]
GO
/****** Object:  Synonym [dbo].[s01tblTissueDetails]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblTissueDetails] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblTissueDetails]
GO
/****** Object:  Synonym [dbo].[s01tblTissueEPIC]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblTissueEPIC] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblTissueEPIC]
GO
/****** Object:  Synonym [dbo].[s01tblTissueFormStatus]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblTissueFormStatus] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblTissueFormStatus]
GO
/****** Object:  Synonym [dbo].[s01tblTissueReceipts]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblTissueReceipts] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblTissueReceipts]
GO
/****** Object:  Synonym [dbo].[s01tblTissueStorageMethod]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblTissueStorageMethod] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblTissueStorageMethod]
GO
/****** Object:  Synonym [dbo].[s01tblTissueVendor]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblTissueVendor] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblTissueVendor]
GO
/****** Object:  Synonym [dbo].[s01tblTrackStatus]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblTrackStatus] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblTrackStatus]
GO
/****** Object:  Synonym [dbo].[s01tblUOM]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblUOM] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblUOM]
GO
/****** Object:  Synonym [dbo].[s01tblUpdateBarcodeItemInventoryLog]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblUpdateBarcodeItemInventoryLog] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblUpdateBarcodeItemInventoryLog]
GO
/****** Object:  Synonym [dbo].[s01tblUsageType]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblUsageType] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblUsageType]
GO
/****** Object:  Synonym [dbo].[s01tblUserAlertPrefs]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblUserAlertPrefs] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblUserAlertPrefs]
GO
/****** Object:  Synonym [dbo].[s01tblUserAlerts]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblUserAlerts] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblUserAlerts]
GO
/****** Object:  Synonym [dbo].[s01tblUserGroup]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblUserGroup] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblUserGroup]
GO
/****** Object:  Synonym [dbo].[s01tblUsers]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblUsers] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblUsers]
GO
/****** Object:  Synonym [dbo].[s01tblUsersAudit]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblUsersAudit] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblUsersAudit]
GO
/****** Object:  Synonym [dbo].[s01tblUsersHistory]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblUsersHistory] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblUsersHistory]
GO
/****** Object:  Synonym [dbo].[s01tblUsersLog]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblUsersLog] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblUsersLog]
GO
/****** Object:  Synonym [dbo].[s01tblVendorCard]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblVendorCard] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblVendorCard]
GO
/****** Object:  Synonym [dbo].[s01tblWeeklyPatientCount]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tblWeeklyPatientCount] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tblWeeklyPatientCount]
GO
/****** Object:  Synonym [dbo].[s01tmpPatientImport]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01tmpPatientImport] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[tmpPatientImport]
GO
/****** Object:  Synonym [dbo].[s01udf_RemoveNonPRINTableCharacters]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01udf_RemoveNonPRINTableCharacters] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[udf_RemoveNonPRINTableCharacters]
GO
/****** Object:  Synonym [dbo].[s01udf_return_month]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01udf_return_month] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[udf_return_month]
GO
/****** Object:  Synonym [dbo].[s01udf_StripSpecialCharacters]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01udf_StripSpecialCharacters] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[udf_StripSpecialCharacters]
GO
/****** Object:  Synonym [dbo].[s01udfRandomName]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01udfRandomName] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[udfRandomName]
GO
/****** Object:  Synonym [dbo].[s01usp_AutoscanReconcile]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01usp_AutoscanReconcile] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[usp_AutoscanReconcile]
GO
/****** Object:  Synonym [dbo].[s01usp_ItemUsageVisitIDCorrection]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01usp_ItemUsageVisitIDCorrection] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[usp_ItemUsageVisitIDCorrection]
GO
/****** Object:  Synonym [dbo].[s01usp_LogItemTransaction]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01usp_LogItemTransaction] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[usp_LogItemTransaction]
GO
/****** Object:  Synonym [dbo].[s01usp_LogItemTransactionBatch_Billing]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01usp_LogItemTransactionBatch_Billing] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[usp_LogItemTransactionBatch_Billing]
GO
/****** Object:  Synonym [dbo].[s01usp_LogItemTransactionBatch_Reorder]    Script Date: 11/21/2024 2:16:27 AM ******/
CREATE SYNONYM [dbo].[s01usp_LogItemTransactionBatch_Reorder] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[usp_LogItemTransactionBatch_Reorder]
GO
/****** Object:  Synonym [dbo].[s01usp_LogItemTransactionBatch_Reorder_BU]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01usp_LogItemTransactionBatch_Reorder_BU] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[usp_LogItemTransactionBatch_Reorder_BU]
GO
/****** Object:  Synonym [dbo].[s01usp_LogItemTransactionBatch_Reorder2]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01usp_LogItemTransactionBatch_Reorder2] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[usp_LogItemTransactionBatch_Reorder2]
GO
/****** Object:  Synonym [dbo].[s01usp_LogItemTransactionBatch_ReorderPS]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01usp_LogItemTransactionBatch_ReorderPS] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[usp_LogItemTransactionBatch_ReorderPS]
GO
/****** Object:  Synonym [dbo].[s01usp_LogItemTransactionBatch_Usage]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01usp_LogItemTransactionBatch_Usage] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[usp_LogItemTransactionBatch_Usage]
GO
/****** Object:  Synonym [dbo].[s01usp_LogItemTransactionBatch_Usage_BU]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01usp_LogItemTransactionBatch_Usage_BU] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[usp_LogItemTransactionBatch_Usage_BU]
GO
/****** Object:  Synonym [dbo].[s01usp_ReportBasicReorder4]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01usp_ReportBasicReorder4] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[usp_ReportBasicReorder4]
GO
/****** Object:  Synonym [dbo].[s01usp_ReportConsignmentRule]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01usp_ReportConsignmentRule] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[usp_ReportConsignmentRule]
GO
/****** Object:  Synonym [dbo].[s01usp_ReportExpiredItems]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01usp_ReportExpiredItems] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[usp_ReportExpiredItems]
GO
/****** Object:  Synonym [dbo].[s01usp_ReportInventory]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01usp_ReportInventory] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[usp_ReportInventory]
GO
/****** Object:  Synonym [dbo].[s01usp_ReportInventoryCost2]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01usp_ReportInventoryCost2] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[usp_ReportInventoryCost2]
GO
/****** Object:  Synonym [dbo].[s01usp_ReportInventoryEach]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01usp_ReportInventoryEach] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[usp_ReportInventoryEach]
GO
/****** Object:  Synonym [dbo].[s01usp_ReportInventorySummary]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01usp_ReportInventorySummary] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[usp_ReportInventorySummary]
GO
/****** Object:  Synonym [dbo].[s01usp_ReportItemTransfer]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01usp_ReportItemTransfer] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[usp_ReportItemTransfer]
GO
/****** Object:  Synonym [dbo].[s01usp_ReportMisplacedItems]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01usp_ReportMisplacedItems] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[usp_ReportMisplacedItems]
GO
/****** Object:  Synonym [dbo].[s01usp_ReportMissingProductData]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01usp_ReportMissingProductData] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[usp_ReportMissingProductData]
GO
/****** Object:  Synonym [dbo].[s01usp_ReportOrderHistory]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01usp_ReportOrderHistory] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[usp_ReportOrderHistory]
GO
/****** Object:  Synonym [dbo].[s01usp_ReportOutstandingOrder]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01usp_ReportOutstandingOrder] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[usp_ReportOutstandingOrder]
GO
/****** Object:  Synonym [dbo].[s01usp_ReportProductList]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01usp_ReportProductList] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[usp_ReportProductList]
GO
/****** Object:  Synonym [dbo].[s01usp_ReportReceiveOrderHistory]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01usp_ReportReceiveOrderHistory] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[usp_ReportReceiveOrderHistory]
GO
/****** Object:  Synonym [dbo].[s01usp_ReportReorder]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01usp_ReportReorder] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[usp_ReportReorder]
GO
/****** Object:  Synonym [dbo].[s01usp_ReportReorderItems]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01usp_ReportReorderItems] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[usp_ReportReorderItems]
GO
/****** Object:  Synonym [dbo].[s01usp_ReportRestock]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01usp_ReportRestock] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[usp_ReportRestock]
GO
/****** Object:  Synonym [dbo].[s01usp_ReportRestockInventory]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01usp_ReportRestockInventory] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[usp_ReportRestockInventory]
GO
/****** Object:  Synonym [dbo].[s01usp_ReportRestockItemByCluster]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01usp_ReportRestockItemByCluster] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[usp_ReportRestockItemByCluster]
GO
/****** Object:  Synonym [dbo].[s01usp_ReportRFOut]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01usp_ReportRFOut] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[usp_ReportRFOut]
GO
/****** Object:  Synonym [dbo].[s01usp_ReportStockedItem]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01usp_ReportStockedItem] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[usp_ReportStockedItem]
GO
/****** Object:  Synonym [dbo].[s01usp_ReportUsage]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01usp_ReportUsage] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[usp_ReportUsage]
GO
/****** Object:  Synonym [dbo].[s01usp_ReportUsageExc]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01usp_ReportUsageExc] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[usp_ReportUsageExc]
GO
/****** Object:  Synonym [dbo].[s01usp_ReportUsageHistory]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01usp_ReportUsageHistory] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[usp_ReportUsageHistory]
GO
/****** Object:  Synonym [dbo].[s01usp_ReportUsageImplant]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01usp_ReportUsageImplant] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[usp_ReportUsageImplant]
GO
/****** Object:  Synonym [dbo].[s01usp_ReportUsageVendorCard]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01usp_ReportUsageVendorCard] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[usp_ReportUsageVendorCard]
GO
/****** Object:  Synonym [dbo].[s01usp_ReportUsageVendorCardEPIC]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01usp_ReportUsageVendorCardEPIC] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[usp_ReportUsageVendorCardEPIC]
GO
/****** Object:  Synonym [dbo].[s01usp_UpdateBarcodeItemInventory]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01usp_UpdateBarcodeItemInventory] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[usp_UpdateBarcodeItemInventory]
GO
/****** Object:  Synonym [dbo].[s01usp_UpdateCaseStatus]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01usp_UpdateCaseStatus] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[usp_UpdateCaseStatus]
GO
/****** Object:  Synonym [dbo].[s01usp001GetExpiryDateStatistics]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01usp001GetExpiryDateStatistics] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[usp001GetExpiryDateStatistics]
GO
/****** Object:  Synonym [dbo].[s01usp002GetRefPriceStatistics]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01usp002GetRefPriceStatistics] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[usp002GetRefPriceStatistics]
GO
/****** Object:  Synonym [dbo].[s01usp003GetIncorrectDescriptionStatistics]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01usp003GetIncorrectDescriptionStatistics] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[usp003GetIncorrectDescriptionStatistics]
GO
/****** Object:  Synonym [dbo].[s01usp004GetDuplicateProductsStatistics]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01usp004GetDuplicateProductsStatistics] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[usp004GetDuplicateProductsStatistics]
GO
/****** Object:  Synonym [dbo].[s01usp005GetProductOwnershipStatistics]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01usp005GetProductOwnershipStatistics] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[usp005GetProductOwnershipStatistics]
GO
/****** Object:  Synonym [dbo].[s01usp006GetManufacturerDataFromSite]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01usp006GetManufacturerDataFromSite] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[usp006GetManufacturerDataFromSite]
GO
/****** Object:  Synonym [dbo].[s01usp007GetConsignmentDataFromSite]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01usp007GetConsignmentDataFromSite] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[usp007GetConsignmentDataFromSite]
GO
/****** Object:  Synonym [dbo].[s01uspActiveDirectoryUserLogin]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspActiveDirectoryUserLogin] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspActiveDirectoryUserLogin]
GO
/****** Object:  Synonym [dbo].[s01uspAddPurchaseOrder]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspAddPurchaseOrder] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspAddPurchaseOrder]
GO
/****** Object:  Synonym [dbo].[s01uspAlertReturnModeViolation]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspAlertReturnModeViolation] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspAlertReturnModeViolation]
GO
/****** Object:  Synonym [dbo].[s01uspAssociateUsageLocation]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspAssociateUsageLocation] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspAssociateUsageLocation]
GO
/****** Object:  Synonym [dbo].[s01uspAuditCreateTable]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspAuditCreateTable] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspAuditCreateTable]
GO
/****** Object:  Synonym [dbo].[s01uspAuditDeploy]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspAuditDeploy] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspAuditDeploy]
GO
/****** Object:  Synonym [dbo].[s01uspAuditDeployTriggers]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspAuditDeployTriggers] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspAuditDeployTriggers]
GO
/****** Object:  Synonym [dbo].[s01uspAuditView]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspAuditView] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspAuditView]
GO
/****** Object:  Synonym [dbo].[s01uspAutoPopulatetblItemInventory]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspAutoPopulatetblItemInventory] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspAutoPopulatetblItemInventory]
GO
/****** Object:  Synonym [dbo].[s01uspAutoTransferBarcodeItems]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspAutoTransferBarcodeItems] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspAutoTransferBarcodeItems]
GO
/****** Object:  Synonym [dbo].[s01uspAutoTransferItem]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspAutoTransferItem] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspAutoTransferItem]
GO
/****** Object:  Synonym [dbo].[s01uspAutoTransferItemPush]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspAutoTransferItemPush] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspAutoTransferItemPush]
GO
/****** Object:  Synonym [dbo].[s01uspCleanupManufacturer]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspCleanupManufacturer] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspCleanupManufacturer]
GO
/****** Object:  Synonym [dbo].[s01uspCleanupSuppliers]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspCleanupSuppliers] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspCleanupSuppliers]
GO
/****** Object:  Synonym [dbo].[s01uspCorrectItemDomain]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspCorrectItemDomain] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspCorrectItemDomain]
GO
/****** Object:  Synonym [dbo].[s01uspCorrectSupllierID]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspCorrectSupllierID] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspCorrectSupllierID]
GO
/****** Object:  Synonym [dbo].[s01uspCreateCabinetDBMaINTenancePlan]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspCreateCabinetDBMaINTenancePlan] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspCreateCabinetDBMaINTenancePlan]
GO
/****** Object:  Synonym [dbo].[s01uspCreateFulfillOrdersJob]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspCreateFulfillOrdersJob] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspCreateFulfillOrdersJob]
GO
/****** Object:  Synonym [dbo].[s01uspCreateJobChangeItemStatusForManualReturns]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspCreateJobChangeItemStatusForManualReturns] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspCreateJobChangeItemStatusForManualReturns]
GO
/****** Object:  Synonym [dbo].[s01uspCreateJobZeroTransactionAlert]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspCreateJobZeroTransactionAlert] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspCreateJobZeroTransactionAlert]
GO
/****** Object:  Synonym [dbo].[s01uspCreateMissingPhysiciansAlertJob]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspCreateMissingPhysiciansAlertJob] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspCreateMissingPhysiciansAlertJob]
GO
/****** Object:  Synonym [dbo].[s01uspCreateNewProductsAlertJob]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspCreateNewProductsAlertJob] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspCreateNewProductsAlertJob]
GO
/****** Object:  Synonym [dbo].[s01uspCreateNoTransactionAlertJob]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspCreateNoTransactionAlertJob] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspCreateNoTransactionAlertJob]
GO
/****** Object:  Synonym [dbo].[s01uspCreateReorderItemsJob1]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspCreateReorderItemsJob1] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspCreateReorderItemsJob1]
GO
/****** Object:  Synonym [dbo].[s01uspCreateReturnedItemAlertJob]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspCreateReturnedItemAlertJob] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspCreateReturnedItemAlertJob]
GO
/****** Object:  Synonym [dbo].[s01uspCreateRFIDToPeoplesoftReorderItemsJob]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspCreateRFIDToPeoplesoftReorderItemsJob] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspCreateRFIDToPeoplesoftReorderItemsJob]
GO
/****** Object:  Synonym [dbo].[s01uspCreateTableMaintenanceJob]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspCreateTableMaintenanceJob] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspCreateTableMaintenanceJob]
GO
/****** Object:  Synonym [dbo].[s01uspCreateTestItems]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspCreateTestItems] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspCreateTestItems]
GO
/****** Object:  Synonym [dbo].[s01uspDailyStockedInvReport]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspDailyStockedInvReport] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspDailyStockedInvReport]
GO
/****** Object:  Synonym [dbo].[s01uspDefragIndexes]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspDefragIndexes] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspDefragIndexes]
GO
/****** Object:  Synonym [dbo].[s01uspDeleteDuplicateUsage]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspDeleteDuplicateUsage] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspDeleteDuplicateUsage]
GO
/****** Object:  Synonym [dbo].[s01uspDeleteIntegrateErrorMessages]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspDeleteIntegrateErrorMessages] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspDeleteIntegrateErrorMessages]
GO
/****** Object:  Synonym [dbo].[s01uspDeleteMissingItems]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspDeleteMissingItems] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspDeleteMissingItems]
GO
/****** Object:  Synonym [dbo].[s01uspDeleteOrderItem]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspDeleteOrderItem] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspDeleteOrderItem]
GO
/****** Object:  Synonym [dbo].[s01uspDeleteSystemUsageHistory]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspDeleteSystemUsageHistory] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspDeleteSystemUsageHistory]
GO
/****** Object:  Synonym [dbo].[s01uspDuplicateCategoryResolver]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspDuplicateCategoryResolver] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspDuplicateCategoryResolver]
GO
/****** Object:  Synonym [dbo].[s01uspDuplicateProductResolver]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspDuplicateProductResolver] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspDuplicateProductResolver]
GO
/****** Object:  Synonym [dbo].[s01uspDuplicateSchedulesResolver]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspDuplicateSchedulesResolver] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspDuplicateSchedulesResolver]
GO
/****** Object:  Synonym [dbo].[s01uspErrorHandler]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspErrorHandler] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspErrorHandler]
GO
/****** Object:  Synonym [dbo].[s01uspExpirationAlertJob]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspExpirationAlertJob] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspExpirationAlertJob]
GO
/****** Object:  Synonym [dbo].[s01uspExpirationAlertLeadTimeJob]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspExpirationAlertLeadTimeJob] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspExpirationAlertLeadTimeJob]
GO
/****** Object:  Synonym [dbo].[s01uspExportDataQualityStatisticsJob]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspExportDataQualityStatisticsJob] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspExportDataQualityStatisticsJob]
GO
/****** Object:  Synonym [dbo].[s01uspFetchProductDocumentExtn]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspFetchProductDocumentExtn] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspFetchProductDocumentExtn]
GO
/****** Object:  Synonym [dbo].[s01uspFetchProductDocuments]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspFetchProductDocuments] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspFetchProductDocuments]
GO
/****** Object:  Synonym [dbo].[s01uspFetchPurchaseOrder]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspFetchPurchaseOrder] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspFetchPurchaseOrder]
GO
/****** Object:  Synonym [dbo].[s01uspFetchSolution]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspFetchSolution] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspFetchSolution]
GO
/****** Object:  Synonym [dbo].[s01uspFetchSolutionDetails]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspFetchSolutionDetails] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspFetchSolutionDetails]
GO
/****** Object:  Synonym [dbo].[s01uspFixItemUsageInconsistencies]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspFixItemUsageInconsistencies] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspFixItemUsageInconsistencies]
GO
/****** Object:  Synonym [dbo].[s01uspFulFillOrders]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspFulFillOrders] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspFulFillOrders]
GO
/****** Object:  Synonym [dbo].[s01uspFulfillOrdersManually]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspFulfillOrdersManually] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspFulfillOrdersManually]
GO
/****** Object:  Synonym [dbo].[s01uspGenerateOrders]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspGenerateOrders] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspGenerateOrders]
GO
/****** Object:  Synonym [dbo].[s01uspGenerateOrdersJob]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspGenerateOrdersJob] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspGenerateOrdersJob]
GO
/****** Object:  Synonym [dbo].[s01uspGenerateOrdersOnUsed]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspGenerateOrdersOnUsed] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspGenerateOrdersOnUsed]
GO
/****** Object:  Synonym [dbo].[s01uspGetCapitateInfo]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspGetCapitateInfo] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspGetCapitateInfo]
GO
/****** Object:  Synonym [dbo].[s01uspGetClusterInfo]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspGetClusterInfo] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspGetClusterInfo]
GO
/****** Object:  Synonym [dbo].[s01uspGetDataStatistics]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspGetDataStatistics] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspGetDataStatistics]
GO
/****** Object:  Synonym [dbo].[s01uspGrantAccessToClusters]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspGrantAccessToClusters] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspGrantAccessToClusters]
GO
/****** Object:  Synonym [dbo].[s01uspHcadReturnValue]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspHcadReturnValue] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspHcadReturnValue]
GO
/****** Object:  Synonym [dbo].[s01uspImportProductData]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspImportProductData] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspImportProductData]
GO
/****** Object:  Synonym [dbo].[s01uspInsertMissingUsage]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspInsertMissingUsage] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspInsertMissingUsage]
GO
/****** Object:  Synonym [dbo].[s01uspInventoryLevelsByMonth]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspInventoryLevelsByMonth] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspInventoryLevelsByMonth]
GO
/****** Object:  Synonym [dbo].[s01uspLawsonItemsUsed]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspLawsonItemsUsed] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspLawsonItemsUsed]
GO
/****** Object:  Synonym [dbo].[s01uspLogError]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspLogError] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspLogError]
GO
/****** Object:  Synonym [dbo].[s01uspMailProductsMissingLawsonNumber]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspMailProductsMissingLawsonNumber] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspMailProductsMissingLawsonNumber]
GO
/****** Object:  Synonym [dbo].[s01uspMAtoPMMReorderItems]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspMAtoPMMReorderItems] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspMAtoPMMReorderItems]
GO
/****** Object:  Synonym [dbo].[s01uspMergeProduct]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspMergeProduct] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspMergeProduct]
GO
/****** Object:  Synonym [dbo].[s01uspMergeProductByMMID]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspMergeProductByMMID] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspMergeProductByMMID]
GO
/****** Object:  Synonym [dbo].[s01uspMergeProductRestore]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspMergeProductRestore] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspMergeProductRestore]
GO
/****** Object:  Synonym [dbo].[s01uspMergeProductReview]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspMergeProductReview] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspMergeProductReview]
GO
/****** Object:  Synonym [dbo].[s01uspMergeUser]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspMergeUser] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspMergeUser]
GO
/****** Object:  Synonym [dbo].[s01uspMissingPhysiciansAlert]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspMissingPhysiciansAlert] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspMissingPhysiciansAlert]
GO
/****** Object:  Synonym [dbo].[s01uspNewProductsAlerts]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspNewProductsAlerts] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspNewProductsAlerts]
GO
/****** Object:  Synonym [dbo].[s01uspNoTransactionAlert]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspNoTransactionAlert] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspNoTransactionAlert]
GO
/****** Object:  Synonym [dbo].[s01uspPatientCount]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspPatientCount] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspPatientCount]
GO
/****** Object:  Synonym [dbo].[s01uspPlaceApprovedOrders]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspPlaceApprovedOrders] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspPlaceApprovedOrders]
GO
/****** Object:  Synonym [dbo].[s01uspPlaceApprovedOrdersJob]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspPlaceApprovedOrdersJob] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspPlaceApprovedOrdersJob]
GO
/****** Object:  Synonym [dbo].[s01uspProcessCapitatedItems]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspProcessCapitatedItems] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspProcessCapitatedItems]
GO
/****** Object:  Synonym [dbo].[s01uspProcessFDAData]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspProcessFDAData] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspProcessFDAData]
GO
/****** Object:  Synonym [dbo].[s01uspProcessOrderError]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspProcessOrderError] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspProcessOrderError]
GO
/****** Object:  Synonym [dbo].[s01uspProcessOrderFulfillmentJob]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspProcessOrderFulfillmentJob] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspProcessOrderFulfillmentJob]
GO
/****** Object:  Synonym [dbo].[s01uspProcessOrders]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspProcessOrders] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspProcessOrders]
GO
/****** Object:  Synonym [dbo].[s01uspProcessOrdersJeff]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspProcessOrdersJeff] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspProcessOrdersJeff]
GO
/****** Object:  Synonym [dbo].[s01uspProcessOrdersJob]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspProcessOrdersJob] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspProcessOrdersJob]
GO
/****** Object:  Synonym [dbo].[s01uspProcessTissueVerification]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspProcessTissueVerification] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspProcessTissueVerification]
GO
/****** Object:  Synonym [dbo].[s01uspProductInventoryLevelsByCategory]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspProductInventoryLevelsByCategory] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspProductInventoryLevelsByCategory]
GO
/****** Object:  Synonym [dbo].[s01uspProductUtilizationReport]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspProductUtilizationReport] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspProductUtilizationReport]
GO
/****** Object:  Synonym [dbo].[s01uspProtocolBerlex]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspProtocolBerlex] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspProtocolBerlex]
GO
/****** Object:  Synonym [dbo].[s01uspProtocolPhysician]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspProtocolPhysician] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspProtocolPhysician]
GO
/****** Object:  Synonym [dbo].[s01uspReconcileBarcodeInventory]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspReconcileBarcodeInventory] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspReconcileBarcodeInventory]
GO
/****** Object:  Synonym [dbo].[s01uspReconcileRFIDItems]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspReconcileRFIDItems] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspReconcileRFIDItems]
GO
/****** Object:  Synonym [dbo].[s01uspReorderItems]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspReorderItems] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspReorderItems]
GO
/****** Object:  Synonym [dbo].[s01uspReorderItemsByCluster]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspReorderItemsByCluster] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspReorderItemsByCluster]
GO
/****** Object:  Synonym [dbo].[s01uspReorderItemsByDept]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspReorderItemsByDept] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspReorderItemsByDept]
GO
/****** Object:  Synonym [dbo].[s01uspReorderItemsJeff]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspReorderItemsJeff] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspReorderItemsJeff]
GO
/****** Object:  Synonym [dbo].[s01uspReorderItemsJeff_3]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspReorderItemsJeff_3] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspReorderItemsJeff_3]
GO
/****** Object:  Synonym [dbo].[s01uspReorderItemsJeff_BU]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspReorderItemsJeff_BU] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspReorderItemsJeff_BU]
GO
/****** Object:  Synonym [dbo].[s01uspReorderItemsPalos]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspReorderItemsPalos] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspReorderItemsPalos]
GO
/****** Object:  Synonym [dbo].[s01uspReorderItemsPalosBU]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspReorderItemsPalosBU] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspReorderItemsPalosBU]
GO
/****** Object:  Synonym [dbo].[s01uspReorderProcessing]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspReorderProcessing] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspReorderProcessing]
GO
/****** Object:  Synonym [dbo].[s01uspReportExpiredItems]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspReportExpiredItems] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspReportExpiredItems]
GO
/****** Object:  Synonym [dbo].[s01uspReportExpiredItemsByMonth]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspReportExpiredItemsByMonth] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspReportExpiredItemsByMonth]
GO
/****** Object:  Synonym [dbo].[s01uspReportExpiringItems]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspReportExpiringItems] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspReportExpiringItems]
GO
/****** Object:  Synonym [dbo].[s01uspReportMostUsed]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspReportMostUsed] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspReportMostUsed]
GO
/****** Object:  Synonym [dbo].[s01uspReportParlevel]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspReportParlevel] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspReportParlevel]
GO
/****** Object:  Synonym [dbo].[s01uspReportProductReconciliation]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspReportProductReconciliation] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspReportProductReconciliation]
GO
/****** Object:  Synonym [dbo].[s01uspReportProductsWithoutPars]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspReportProductsWithoutPars] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspReportProductsWithoutPars]
GO
/****** Object:  Synonym [dbo].[s01uspReportSpecimenManagement]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspReportSpecimenManagement] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspReportSpecimenManagement]
GO
/****** Object:  Synonym [dbo].[s01uspReportTaggedItems]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspReportTaggedItems] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspReportTaggedItems]
GO
/****** Object:  Synonym [dbo].[s01uspReportTissueBankTracking]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspReportTissueBankTracking] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspReportTissueBankTracking]
GO
/****** Object:  Synonym [dbo].[s01uspReportTissueBankTrackingDT]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspReportTissueBankTrackingDT] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspReportTissueBankTrackingDT]
GO
/****** Object:  Synonym [dbo].[s01uspReportTransactionTracking]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspReportTransactionTracking] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspReportTransactionTracking]
GO
/****** Object:  Synonym [dbo].[s01uspReportUnUsed]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspReportUnUsed] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspReportUnUsed]
GO
/****** Object:  Synonym [dbo].[s01uspReturnedItemAlert]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspReturnedItemAlert] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspReturnedItemAlert]
GO
/****** Object:  Synonym [dbo].[s01uspRFIDToLawsonReorderItems]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspRFIDToLawsonReorderItems] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspRFIDToLawsonReorderItems]
GO
/****** Object:  Synonym [dbo].[s01uspRFIDToPeopleSoftReorderItems]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspRFIDToPeopleSoftReorderItems] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspRFIDToPeopleSoftReorderItems]
GO
/****** Object:  Synonym [dbo].[s01uspRFIDToPeopleSoftReorderItemsCHA]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspRFIDToPeopleSoftReorderItemsCHA] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspRFIDToPeopleSoftReorderItemsCHA]
GO
/****** Object:  Synonym [dbo].[s01uspRISInterfaceExport]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspRISInterfaceExport] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspRISInterfaceExport]
GO
/****** Object:  Synonym [dbo].[s01uspRunPatientBillingJob]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspRunPatientBillingJob] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspRunPatientBillingJob]
GO
/****** Object:  Synonym [dbo].[s01uspSearchPurchaseOrder]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspSearchPurchaseOrder] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspSearchPurchaseOrder]
GO
/****** Object:  Synonym [dbo].[s01uspSettingsDuplicateResolver]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspSettingsDuplicateResolver] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspSettingsDuplicateResolver]
GO
/****** Object:  Synonym [dbo].[s01uspSetTriggersStatus]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspSetTriggersStatus] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspSetTriggersStatus]
GO
/****** Object:  Synonym [dbo].[s01uspSetupNewSubscription]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspSetupNewSubscription] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspSetupNewSubscription]
GO
/****** Object:  Synonym [dbo].[s01uspSupplyAlertZeroTransactionLogins]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspSupplyAlertZeroTransactionLogins] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspSupplyAlertZeroTransactionLogins]
GO
/****** Object:  Synonym [dbo].[s01uspSynonyms]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspSynonyms] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspSynonyms]
GO
/****** Object:  Synonym [dbo].[s01uspTableMaintenance]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspTableMaintenance] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspTableMaintenance]
GO
/****** Object:  Synonym [dbo].[s01uspTaggedInventoryReport]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspTaggedInventoryReport] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspTaggedInventoryReport]
GO
/****** Object:  Synonym [dbo].[s01uspTestOrderInterface]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspTestOrderInterface] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspTestOrderInterface]
GO
/****** Object:  Synonym [dbo].[s01uspTestPars]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspTestPars] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspTestPars]
GO
/****** Object:  Synonym [dbo].[s01uspTissueReconstitution]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspTissueReconstitution] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspTissueReconstitution]
GO
/****** Object:  Synonym [dbo].[s01uspUpdateAdmitDate]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspUpdateAdmitDate] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspUpdateAdmitDate]
GO
/****** Object:  Synonym [dbo].[s01uspUpdateBarcodeItemInventory]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspUpdateBarcodeItemInventory] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspUpdateBarcodeItemInventory]
GO
/****** Object:  Synonym [dbo].[s01uspUploadProductDocument]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspUploadProductDocument] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspUploadProductDocument]
GO
/****** Object:  Synonym [dbo].[s01uspUserLogReport]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspUserLogReport] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspUserLogReport]
GO
/****** Object:  Synonym [dbo].[s01uspWeeklyUsedItemsReport]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s01uspWeeklyUsedItemsReport] FOR [MA_SUP_TJUHORWD_IRISDB].[dbo].[uspWeeklyUsedItemsReport]
GO
/****** Object:  Synonym [dbo].[s03tblOutboundDFTMsgs]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s03tblOutboundDFTMsgs] FOR [MA_SUP_TJUHOR_INTERFACE].[dbo].[tblOutboundDFTMsgs]
GO
/****** Object:  Synonym [dbo].[s03tblOutboundDFTUsage]    Script Date: 11/21/2024 2:16:28 AM ******/
CREATE SYNONYM [dbo].[s03tblOutboundDFTUsage] FOR [MA_SUP_TJUHOR_INTERFACE].[dbo].[tblOutboundDFTUsage]
GO
/****** Object:  UserDefinedFunction [dbo].[udf700WebConsoleSUPFetchSettings]    Script Date: 11/21/2024 2:16:28 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE  FUNCTION [dbo].[udf700WebConsoleSUPFetchSettings]
( 
	@DeviceID INT
    , @SettingName VARCHAR(200)
) 
RETURNS VARCHAR(MAX)
WITH EXECUTE AS CALLER
AS
BEGIN
	DECLARE @SettingValue VARCHAR(200)
    IF @SettingValue IS NULL
		SELECT @SettingValue =  [Setting Value] FROM s01tblSettingsCommon  WHERE [Setting Name] = @SettingName --AND ClusterID = @DeviceID
	IF @SettingValue IS NULL
		SELECT @SettingValue =  [Setting Value] FROM s01tblSettingsUnique  WHERE [Setting Name] = @SettingName --AND ClusterID IS NULL

	--IF @SettingValue IS NULL
	--	SELECT @SettingValue =  [Setting Value] FROM dbo.s01tblSettingsUnique  WHERE [Setting Name] = @SettingName AND ClusterID = @DeviceID
	--IF @SettingValue IS NULL
	--	SELECT @SettingValue =  [Setting Value] FROM dbo.s01tblSettingsUnique  WHERE [Setting Name] = @SettingName AND ClusterID IS NULL
	RETURN @SettingValue
END
GO
/****** Object:  View [dbo].[qryHUBSUPFetchItemUsageList]    Script Date: 11/21/2024 2:16:28 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE VIEW [dbo].[qryHUBSUPFetchItemUsageList]
AS
SELECT [Description 1] AS Description
	,CASE 
		WHEN I.ItemStatusID = 5
			THEN ISNULL( [CAT NO], 'N/A')+ ' (WASTED)'
		ELSE
			ISNULL( [CAT NO], 'N/A')
	END AS CatNo
	,CONVERT (VARCHAR,U.DateUsed,120) AS DateUsed 
	,U.DateUsed AS REAL, ISNULL(U.Qty,1) AS Qty
	,PT.[Patient Name] AS PatientName
	,U.ItemUsageID, U.UsageTypeID, I.RFID
	,CASE 
		WHEN P.RFID = 'Y' 
			THEN 'I' 
		WHEN P.ProductTypeID = 5
			THEN 'T'
		ELSE '-'
	END AS ProductType
	,[Serial No] AS SerialNo, [Lot No] AS LotNo
	--,CASE 
	--	WHEN D.Processed = 'P' 
	--		THEN 'Sent to EMR' 
	--	ELSE 'Not Sent' 
	--END AS EpicStatus
	, U.MAScheduleID, U.MAPatientID, SessionID
FROM (SELECT UsageItemID, UsageTypeID, ItemUsageID, DateUsed, Qty, MAScheduleID, MAVisitID, MAPatientID, SessionID 
		FROM dbo.s01tblItemUsage
		WHERE DateUsed > GETDATE() - 60 
		and UsageTypeID = 1) U 

LEFT OUTER JOIN (SELECT MAScheduleID, MAVisitID, MAPatientID, [Patient Name]
					FROM dbo.s01qryPatientSchedules
					WHERE ProcedureDate > GETDATE() -60 ) PT ON U.MAScheduleID = PT.MAScheduleID -- isnull(U.MAScheduleID,U.MAVisitID) = isnull(PT.MAScheduleID,PT.MAScheduleID)
LEFT OUTER JOIN (SELECT ItemID, ProductID, ItemStatusID, [Serial No], [Lot No], RFID
					FROM dbo.s01tblItems) I ON UsageItemID = I.ItemID
INNER JOIN (SELECT [ProductID], [Cat No], [Description 1], RFID, ProductTypeID
				FROM dbo.s01tblProducts) P ON P.ProductID = I.ProductID

--LEFT OUTER JOIN (  SELECT ItemUsageID, Processed
--		FROM INTERFACE..tblOutboundDFTMsgs
--		WHERE EnteredDate>GETDATE()-60) D ON D.ItemUsageID = U.ItemUsageID --[Interface].dbo.tblOutboundDFTMsgs D ON D.ItemUsageID = U.ItemUsageID
UNION ALL
SELECT [Description 1] AS Description
	, CASE 
		WHEN U.OverrideID = 5
			THEN ISNULL( p.[CAT NO], 'N/A')+ ' (WASTED)'
		ELSE
			ISNULL( P.[CAT NO], 'N/A')
	END AS CatNo  
	, CONVERT (VARCHAR,U.DateUsed,120) AS DateUsed, U.DateUsed AS REAL
	, ISNULL(U.Qty,1) AS Qty
	--, @PatientName AS PatientName
	,PT.[Patient Name] AS PatientName
	, U.ItemUsageID, U.UsageTypeID, BR.[Barcode No] RFID
	, Case 
		When ProdUnitID = 4
			THEN 'B'
		ELSE '-'
	END AS ProductType, NULL  SerialNo, NULL LotNo
	--, CASE 
	--	WHEN D.Processed = 'P' 
	--		THEN 'Sent to EMR' 
	--		ELSE 'Not Sent' 
	--END AS EpicStatus
	, U.MAScheduleID, U.MAPatientID, SessionID
FROM (SELECT ItemUsageID, UsageItemID, UsageTypeID, MAScheduleID, MAVisitID, SessionID
			, MAPatientID, DateUsed, OverrideID,Qty 
		FROM dbo.s01tblItemUsage
		WHERE DateUsed > GETDATE() - 60
		AND UsageTypeID = 2 ) U

LEFT OUTER JOIN (SELECT MAScheduleID, MAVisitID, MAPatientID, [Patient Name]
					FROM dbo.s01qryPatientSchedules
					WHERE ProcedureDate > GETDATE() -60 ) PT ON U.MAScheduleID = PT.MAScheduleID --isnull(U.MAScheduleID,U.MAVisitID) = isnull(PT.MAScheduleID,PT.MAScheduleID)
LEFT OUTER JOIN (SELECT ItemInventoryID, ProductID
					FROM dbo.s01tblItemInventory) I ON U.UsageItemID = I.ItemInventoryID
INNER JOIN  (SELECT ProductID, [Cat No], [Description 1], ProdUnitID
				FROM dbo.s01tblProducts) P ON P.ProductID = I.ProductID 
LEFT OUTER JOIN (SELECT [Barcode No],[Cat No]
					FROM dbo.s01tblBarcodeRuleProduct) BR ON P.[Cat no] = BR.[Cat No]

--LEFT OUTER JOIN ( SELECT ItemUsageID, Processed
--					FROM INTERFACE..tblOutboundDFTMsgs
--					WHERE EnteredDate>GETDATE()-60) D ON D.ItemUsageID = U.ItemUsageID

WHERE P.[Cat No]<> '-'
GO
/****** Object:  View [dbo].[qryHUBFetchUsageItemList]    Script Date: 11/21/2024 2:16:28 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE VIEW [dbo].[qryHUBFetchUsageItemList]
AS
SELECT DISTINCT 
    IU.Description, 
    IU.CatNo,
    CONVERT(VARCHAR, CONVERT(DATETIME, IU.DateUsed, 120), 22) AS DateUsed,
    IU.REAL,
    IU.Qty,
    IU.PatientName,
    IU.ItemUsageID,
    IU.UsageTypeID,
    IUE.Capitated,
    IU.RFID,
    IU.ProductType,
    IU.SerialNo,
    IU.LotNo,
    i.ItemStatusID,
    IU.MAScheduleID
FROM 
    qryHUBSUPFetchItemUsageList IU
LEFT JOIN 
    s01tblItemUsageExt IUE ON IU.ItemUsageID = IUE.ItemUsageID
INNER JOIN 
    s01qryItemUsage i ON IU.ItemUsageID = i.ItemUsageID
-- WHERE IU.MAScheduleID = @MAScheduleID 
-- AND i.ItemStatusID = 4
GO
/****** Object:  View [dbo].[qry700WebConsoleSUPUsersValidate]    Script Date: 11/21/2024 2:16:28 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE VIEW [dbo].[qry700WebConsoleSUPUsersValidate]
AS
	SELECT U.[UserID], U.[Employee ID], CASE WHEN ([Picture] IS NULL OR Picture = '') THEN [RFID] ELSE Picture END AS RFID,
		U.[PIN], U.[Title], U.[First Name], U.[Middle Name], U.[Last Name], U.[Suffix], 
		U.[Company], U.[Department], U.[Office], U.[Job Title], U.[Office Phone No], U.[Mobile Phone No], U.[Pager No], 
		U.[Fax No], U.[Office Street], U.[Office City], U.[Office State], U.[Office ZIP], U.[Office Country], U.[Username], 
		U.[Password], U.[GroupID], U.[SupplierID], U.[Email], U.[Picture], U.[Home Street], U.[Home City], U.[Home State], 
		U.[Home ZIP], U.[Home Country], U.[Home Phone No], U.[Entered By], U.[Computer Name], U.[Domain], U.[Entered Date], 
		U.[UserStatusID], U.[rowguid], U.[User Group], U.[User Name]
		--,U.TwoFactorEnabled as [2FactorAuthStatus]
		, C.CompartmentID, C.ClusterID
	FROM s01qryUsers U 
	INNER JOIN s01tblCompartmentAccess C ON U.UserID = C.UserID	
	WHERE U.UserStatusID = 1
GO
/****** Object:  View [dbo].[qry701WebConsoleSUPPatientDisplayADT]    Script Date: 11/21/2024 2:16:28 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE VIEW [dbo].[qry701WebConsoleSUPPatientDisplayADT]
AS
SELECT MaScheduleID, ProcedureCode, ScheduleStatus, VisitNumber
FROM s01qryPatientSchedules 
WHERE UPPER(ISNULL(ScheduleStatus,'ACTIVE'))='ACTIVE'
GO
/****** Object:  View [dbo].[qry703WebConsoleProductCatalog]    Script Date: 11/21/2024 2:16:28 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

/******************************************************************************
Name                       : qryProductCatalog
Version                    : 4.0.43
Purpose                    : 
Author                     : 
Date Created               : 
Tables Affected            : s01tblItemInventory,s01tblProducts
Application Affected       : 
-----------------------------------------------------------------------------------------------------
Release Notes         :
********************************************************************************/
CREATE VIEW [dbo].[qry703WebConsoleProductCatalog]
AS
SELECT 	LEFT(s01tblProducts.[Description 1], 45) + '||11' AS Field1, 'Cat No : ' + s01tblProducts.[Cat No] + '|||||0' AS Field3,
	'Qty : ' + CAST(s01tblItemInventory.Qty AS nvarchar) + '|||||0' AS Field4, s01tblProducts.Manufacturer + N'|||||0' AS Field5,
	s01tblItemInventory.UOMCode + '|||||0' AS Field6, s01tblProducts.[Cat No] AS FieldID1, s01tblItemInventory.ItemInventoryID, s01tblProducts.[Cat No],
	s01tblProducts.[Description 1], s01tblProducts.UOMCode, s01tblProducts.[Ref Price], s01tblProducts.ProdUnitID, s01tblItemInventory.Qty,
	s01tblProducts.PCDM,s01tblItemInventory.LocationID,s01tblItemInventory.LocationTypeID,BP.[Barcode No]
FROM 	s01tblItemInventory INNER JOIN
     	s01tblProducts ON s01tblItemInventory.ProductID = s01tblProducts.ProductID
		inner join s01tblbarcoderuleproduct BP on s01tblProducts.[Cat No]=BP.[Cat No]
GO
/****** Object:  View [dbo].[qry704WebConsoleSUPItemUsageList]    Script Date: 11/21/2024 2:16:28 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE VIEW [dbo].[qry704WebConsoleSUPItemUsageList]
AS
SELECT [Description 1] AS Description
	,CASE 
		WHEN I.ItemStatusID = 5
			THEN ISNULL( [CAT NO], 'N/A')+ ' (WASTED)'
		ELSE
			ISNULL( [CAT NO], 'N/A')
	END AS CatNo
	,CONVERT (VARCHAR,U.DateUsed,120) AS DateUsed 
	,U.DateUsed AS REAL, ISNULL(U.Qty,1) AS Qty
	,PT.[Patient Name] AS PatientName
	,U.ItemUsageID, U.UsageTypeID, I.RFID
	,CASE 
		WHEN P.RFID = 'Y' 
			THEN 'I' 
		WHEN P.ProductTypeID = 5
			THEN 'T'
		ELSE '-'
	END AS ProductType
	,[Serial No] AS SerialNo, [Lot No] AS LotNo
	--,CASE 
	--	WHEN D.Processed = 'P' 
	--		THEN 'Sent to EMR' 
	--	ELSE 'Not Sent' 
	--END AS EpicStatus
	, U.MAScheduleID, U.MAPatientID, SessionID
FROM (SELECT UsageItemID, UsageTypeID, ItemUsageID, DateUsed, Qty, MAScheduleID, MAVisitID, MAPatientID, SessionID 
		FROM s01tblItemUsage
		WHERE DateUsed > GETDATE() - 60 
		and UsageTypeID = 1) U 

LEFT OUTER JOIN (SELECT MAScheduleID, MAVisitID, MAPatientID, [Patient Name]
					FROM s01qryPatientSchedules
					WHERE ProcedureDate > GETDATE() -60 ) PT ON U.MAScheduleID = PT.MAScheduleID -- isnull(U.MAScheduleID,U.MAVisitID) = isnull(PT.MAScheduleID,PT.MAScheduleID)
LEFT OUTER JOIN (SELECT ItemID, ProductID, ItemStatusID, [Serial No], [Lot No], RFID
					FROM s01tblItems) I ON UsageItemID = I.ItemID
INNER JOIN (SELECT [ProductID], [Cat No], [Description 1], RFID, ProductTypeID
				FROM s01tblProducts) P ON P.ProductID = I.ProductID

--LEFT OUTER JOIN (  SELECT ItemUsageID, Processed
--		FROM INTERFACE..tblOutboundDFTMsgs
--		WHERE EnteredDate>GETDATE()-60) D ON D.ItemUsageID = U.ItemUsageID --[Interface].dbo.tblOutboundDFTMsgs D ON D.ItemUsageID = U.ItemUsageID
UNION ALL
SELECT [Description 1] AS Description
	, CASE 
		WHEN U.OverrideID = 5
			THEN ISNULL( p.[CAT NO], 'N/A')+ ' (WASTED)'
		ELSE
			ISNULL( P.[CAT NO], 'N/A')
	END AS CatNo  
	, CONVERT (VARCHAR,U.DateUsed,120) AS DateUsed, U.DateUsed AS REAL
	, ISNULL(U.Qty,1) AS Qty
	--, @PatientName AS PatientName
	,PT.[Patient Name] AS PatientName
	, U.ItemUsageID, U.UsageTypeID, BR.[Barcode No] RFID
	, Case 
		When ProdUnitID = 4
			THEN 'B'
		ELSE '-'
	END AS ProductType, NULL  SerialNo, NULL LotNo
	--, CASE 
	--	WHEN D.Processed = 'P' 
	--		THEN 'Sent to EMR' 
	--		ELSE 'Not Sent' 
	--END AS EpicStatus
	, U.MAScheduleID, U.MAPatientID, SessionID
FROM (SELECT ItemUsageID, UsageItemID, UsageTypeID, MAScheduleID, MAVisitID, SessionID
			, MAPatientID, DateUsed, OverrideID,Qty 
		FROM s01tblItemUsage
		WHERE DateUsed > GETDATE() - 60
		AND UsageTypeID = 2 ) U

LEFT OUTER JOIN (SELECT MAScheduleID, MAVisitID, MAPatientID, [Patient Name]
					FROM s01qryPatientSchedules
					WHERE ProcedureDate > GETDATE() -60 ) PT ON U.MAScheduleID = PT.MAScheduleID --isnull(U.MAScheduleID,U.MAVisitID) = isnull(PT.MAScheduleID,PT.MAScheduleID)
LEFT OUTER JOIN (SELECT ItemInventoryID, ProductID
					FROM s01tblItemInventory) I ON U.UsageItemID = I.ItemInventoryID
INNER JOIN  (SELECT ProductID, [Cat No], [Description 1], ProdUnitID
				FROM s01tblProducts) P ON P.ProductID = I.ProductID 
LEFT OUTER JOIN (SELECT [Barcode No],[Cat No]
					FROM s01tblBarcodeRuleProduct) BR ON P.[Cat no] = BR.[Cat No]

--LEFT OUTER JOIN ( SELECT ItemUsageID, Processed
--					FROM INTERFACE..tblOutboundDFTMsgs
--					WHERE EnteredDate>GETDATE()-60) D ON D.ItemUsageID = U.ItemUsageID

WHERE P.[Cat No]<> '-'
GO
/****** Object:  Table [dbo].[TBL_COUNTRIES]    Script Date: 11/21/2024 2:16:28 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[TBL_COUNTRIES](
	[COUNTRY_ID] [int] IDENTITY(1,1) NOT NULL,
	[COUNTRY_CODE] [varchar](5) NOT NULL,
	[COUNTRY_NAME] [varchar](50) NOT NULL,
PRIMARY KEY CLUSTERED 
(
	[COUNTRY_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[TBL_STATES]    Script Date: 11/21/2024 2:16:28 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[TBL_STATES](
	[STATE_ID] [bigint] IDENTITY(1,1) NOT NULL,
	[STATE_CODE] [varchar](5) NOT NULL,
	[STATE_NAME] [varchar](50) NOT NULL,
	[COUNTRY_ID] [int] NOT NULL,
PRIMARY KEY CLUSTERED 
(
	[STATE_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[tblAppEvents]    Script Date: 11/21/2024 2:16:28 AM ******/
SET ANSI_NULLS OFF
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[tblAppEvents](
	[EventID] [int] NOT NULL,
	[Event] [varchar](255) NULL,
	[EventDesc] [varchar](255) NULL,
	[SPName] [varchar](255) NULL,
	[SPDesc] [varchar](255) NULL,
	[LogMsg] [varchar](512) NULL,
	[EnteredDate] [datetime] NULL,
	[AppID] [int] NULL,
PRIMARY KEY CLUSTERED 
(
	[EventID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[tblDeviceRegistration]    Script Date: 11/21/2024 2:16:28 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[tblDeviceRegistration](
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[DeviceID] [nvarchar](50) NULL,
	[Location ID] [int] NULL,
	[Registration Date] [datetime] NULL,
	[Registered By] [nvarchar](50) NULL,
	[Entered Date] [datetime] NULL,
	[SerialNumber] [nvarchar](50) NULL,
	[DeviceName] [nvarchar](50) NULL,
	[McAddress] [nvarchar](50) NULL,
	[Status] [nvarchar](50) NULL,
	[Device IPAddress] [varchar](50) NULL,
	[Location] [varchar](100) NULL,
 CONSTRAINT [PK_tblDeviceRegistration] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[tblModules]    Script Date: 11/21/2024 2:16:28 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[tblModules](
	[MODULE_ID] [int] IDENTITY(1,1) NOT NULL,
	[MODULE_NAME] [nvarchar](255) NOT NULL,
	[DESCRIPTION] [nvarchar](255) NULL,
	[LINK] [nvarchar](500) NOT NULL,
	[PARENT_MODULE_ID] [int] NULL,
	[PROJECT_ID] [int] NULL,
	[ORDER_ID] [int] NULL,
	[MODULE_ICON] [nvarchar](255) NULL,
	[ISENABLED] [bit] NULL,
PRIMARY KEY CLUSTERED 
(
	[MODULE_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[tblReportCategory]    Script Date: 11/21/2024 2:16:28 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[tblReportCategory](
	[CategoryID] [tinyint] NOT NULL,
	[Category] [varchar](50) NULL,
 CONSTRAINT [PK_tblReportCategory] PRIMARY KEY CLUSTERED 
(
	[CategoryID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[tblReportFields]    Script Date: 11/21/2024 2:16:28 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[tblReportFields](
	[FieldID] [int] IDENTITY(1,1) NOT NULL,
	[ReportID] [int] NOT NULL,
	[FieldName] [varchar](50) NULL,
	[Title] [varchar](50) NULL,
	[Format] [varchar](50) NULL,
	[Alignment] [varchar](50) NULL,
	[Sorting] [varchar](50) NULL,
	[TotalColumn] [tinyint] NULL,
 CONSTRAINT [PK_tblReportFields] PRIMARY KEY CLUSTERED 
(
	[FieldID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[tblReportFilter]    Script Date: 11/21/2024 2:16:28 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[tblReportFilter](
	[FilterID] [int] IDENTITY(1,1) NOT NULL,
	[ReportID] [int] NOT NULL,
	[FilterMode] [tinyint] NULL,
	[TopFilter] [tinyint] NULL,
	[NoRecords] [int] NULL,
	[NoCategory] [varchar](50) NULL,
	[FilterScript] [varchar](1000) NULL,
 CONSTRAINT [PK_tblReportFilter] PRIMARY KEY CLUSTERED 
(
	[FilterID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[tblReportLayout]    Script Date: 11/21/2024 2:16:28 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[tblReportLayout](
	[ReportID] [int] NOT NULL,
	[PaperSize] [varchar](50) NULL,
	[MarginTop] [float] NULL,
	[MarginBottom] [decimal](3, 1) NULL,
	[MarginLeft] [decimal](3, 1) NULL,
	[MarginRight] [decimal](3, 1) NULL,
	[MarginHeader] [decimal](3, 1) NULL,
	[MarginFooter] [decimal](3, 1) NULL,
	[MarginGuide] [tinyint] NULL,
	[TableMarginLeft] [decimal](3, 1) NULL,
	[Orientation] [varchar](50) NULL,
	[BorderPosition] [varchar](50) NULL,
	[BorderStyle] [varchar](50) NULL,
	[BorderWidth] [tinyint] NULL,
	[BorderColor] [varchar](50) NULL,
	[ShowPicture] [tinyint] NULL,
	[PicFile] [varchar](255) NULL,
	[PicScaleX] [decimal](3, 0) NULL,
	[PicScaleY] [decimal](3, 0) NULL,
	[PicX] [smallint] NULL,
	[PicY] [smallint] NULL,
	[ShowRowNo] [tinyint] NULL,
	[ShowBottomSum] [tinyint] NULL,
 CONSTRAINT [PK_tblReportLayout] PRIMARY KEY CLUSTERED 
(
	[ReportID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[tblReportList]    Script Date: 11/21/2024 2:16:28 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[tblReportList](
	[ReportID] [int] IDENTITY(1,1) NOT NULL,
	[TypeID] [tinyint] NOT NULL,
	[CategoryID] [tinyint] NULL,
	[Title] [varchar](50) NULL,
	[Description] [varchar](255) NULL,
	[Datasource] [varchar](50) NULL,
	[CustomQuery] [tinyint] NULL,
	[SQLQuery] [varchar](2000) NULL,
	[GroupField] [tinyint] NULL,
	[ButtonID] [tinyint] NULL,
	[IconID] [tinyint] NULL,
	[OwnerID] [int] NULL,
	[CreatorID] [int] NULL,
	[ColWidth] [varchar](255) NULL,
	[Shared] [bit] NULL,
	[Shortcut] [bit] NULL,
	[Aggregate] [tinyint] NULL,
	[TotalRow] [tinyint] NULL,
	[AggFunction] [varchar](50) NULL,
	[AggField] [varchar](50) NULL,
	[PivotField] [varchar](50) NULL,
	[Computer Name] [varchar](50) NULL,
	[Domain] [varchar](50) NULL,
	[Entered Date] [datetime] NULL,
 CONSTRAINT [PK_tblReportList] PRIMARY KEY CLUSTERED 
(
	[ReportID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[tblReportRBAC]    Script Date: 11/21/2024 2:16:28 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[tblReportRBAC](
	[ReportRBACID] [int] IDENTITY(1,1) NOT NULL,
	[ReportID] [int] NULL,
	[UserID] [int] NULL,
	[Status] [bit] NULL,
	[rowguid] [uniqueidentifier] NULL,
PRIMARY KEY CLUSTERED 
(
	[ReportRBACID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[tblReportStatus]    Script Date: 11/21/2024 2:16:28 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[tblReportStatus](
	[ID] [int] NOT NULL,
	[Status] [varchar](50) NOT NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[tblReportStyle]    Script Date: 11/21/2024 2:16:28 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[tblReportStyle](
	[StyleID] [int] IDENTITY(1,1) NOT NULL,
	[ReportID] [int] NOT NULL,
	[Section] [varchar](50) NULL,
	[Font] [varchar](50) NULL,
	[BackColor] [varchar](50) NULL,
	[Alignment] [varchar](50) NULL,
	[Height] [tinyint] NULL,
	[Indent] [tinyint] NULL,
	[BeforeSpacing] [tinyint] NULL,
	[TopBorder] [varchar](50) NULL,
	[BottomBorder] [varchar](50) NULL,
	[BorderColor] [varchar](50) NULL,
	[AltColor] [varchar](50) NULL,
	[SideBorder] [tinyint] NULL,
 CONSTRAINT [PK_tblReportStyle] PRIMARY KEY CLUSTERED 
(
	[StyleID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[tblReportText]    Script Date: 11/21/2024 2:16:28 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[tblReportText](
	[TextID] [int] IDENTITY(1,1) NOT NULL,
	[ReportID] [int] NOT NULL,
	[Section] [varchar](50) NULL,
	[ReportTitle] [varchar](255) NULL,
	[TitleAlignment] [varchar](50) NULL,
	[TextLeft] [varchar](255) NULL,
	[TextCenter] [varchar](255) NULL,
	[TextRight] [varchar](255) NULL,
 CONSTRAINT [PK_tblReportText] PRIMARY KEY CLUSTERED 
(
	[TextID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[tblReportType]    Script Date: 11/21/2024 2:16:28 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[tblReportType](
	[TypeID] [tinyint] NOT NULL,
	[Report Type] [varchar](50) NULL,
 CONSTRAINT [PK_tblReportType] PRIMARY KEY CLUSTERED 
(
	[TypeID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[tblStandardTimeZone]    Script Date: 11/21/2024 2:16:28 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[tblStandardTimeZone](
	[StandardTimeZoneID] [int] IDENTITY(1,1) NOT NULL,
	[TimezoneDescription] [varchar](500) NULL,
	[Abbreviation] [varchar](10) NULL,
	[StandardTimeZone] [varchar](500) NULL,
	[UTCStandard] [varchar](100) NULL,
	[Country] [varchar](100) NULL,
	[EnteredDate] [datetime] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[tblUserBookMarks]    Script Date: 11/21/2024 2:16:28 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[tblUserBookMarks](
	[BookmarkID] [int] IDENTITY(1,1) NOT NULL,
	[UserID] [int] NULL,
	[Bookmark] [nvarchar](50) NULL,
	[BookmarkURL] [nvarchar](50) NULL,
	[DateAdded] [datetime] NULL,
 CONSTRAINT [PK_tbluserbookmarks] PRIMARY KEY CLUSTERED 
(
	[BookmarkID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[tblUserRBAC]    Script Date: 11/21/2024 2:16:28 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[tblUserRBAC](
	[UserRBACID] [int] IDENTITY(1,1) NOT NULL,
	[UserGroupID] [int] NULL,
	[ModuleID] [int] NULL,
	[Status] [bit] NULL,
	[rowguid] [uniqueidentifier] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[tblUsersRecentHistory]    Script Date: 11/21/2024 2:16:28 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[tblUsersRecentHistory](
	[ID] [int] IDENTITY(1,1) NOT NULL,
	[UserID] [int] NULL,
	[ModuleName] [varchar](50) NULL,
	[ModuleLink] [varchar](50) NULL,
	[LastActivityDate] [datetime] NULL,
 CONSTRAINT [PK_tblusersrecenthistory] PRIMARY KEY CLUSTERED 
(
	[ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
ALTER TABLE [dbo].[tblAppEvents] ADD  DEFAULT (getdate()) FOR [EnteredDate]
GO
ALTER TABLE [dbo].[tblDeviceRegistration] ADD  DEFAULT (getutcdate()) FOR [Entered Date]
GO
ALTER TABLE [dbo].[tblReportList] ADD  CONSTRAINT [DF_tblReportList_TypeID]  DEFAULT ((1)) FOR [TypeID]
GO
ALTER TABLE [dbo].[tblReportList] ADD  CONSTRAINT [DF_tblReportList_CategoryID]  DEFAULT ((0)) FOR [CategoryID]
GO
ALTER TABLE [dbo].[tblReportList] ADD  CONSTRAINT [DF_tblReportList_Description]  DEFAULT ('') FOR [Description]
GO
ALTER TABLE [dbo].[tblReportList] ADD  CONSTRAINT [DF_tblReportList_Datasource]  DEFAULT ('') FOR [Datasource]
GO
ALTER TABLE [dbo].[tblReportList] ADD  CONSTRAINT [DF_tblReportList_CustomQuery]  DEFAULT ((0)) FOR [CustomQuery]
GO
ALTER TABLE [dbo].[tblReportList] ADD  CONSTRAINT [DF_tblReportList_SQLQuery]  DEFAULT ('') FOR [SQLQuery]
GO
ALTER TABLE [dbo].[tblReportList] ADD  CONSTRAINT [DF_tblReportList_GroupField]  DEFAULT ((0)) FOR [GroupField]
GO
ALTER TABLE [dbo].[tblReportList] ADD  CONSTRAINT [DF_tblReportList_ButtonID]  DEFAULT ((1)) FOR [ButtonID]
GO
ALTER TABLE [dbo].[tblReportList] ADD  CONSTRAINT [DF_tblReportList_IconID]  DEFAULT ((9)) FOR [IconID]
GO
ALTER TABLE [dbo].[tblReportList] ADD  CONSTRAINT [DF_tblReportList_ColWidth]  DEFAULT ('') FOR [ColWidth]
GO
ALTER TABLE [dbo].[tblReportList] ADD  CONSTRAINT [DF_tblReportList_Shared]  DEFAULT ((1)) FOR [Shared]
GO
ALTER TABLE [dbo].[tblReportList] ADD  CONSTRAINT [DF_tblReportList_MyReport]  DEFAULT ((1)) FOR [Shortcut]
GO
ALTER TABLE [dbo].[tblReportList] ADD  CONSTRAINT [DF_tblReportList_Aggregate]  DEFAULT ((0)) FOR [Aggregate]
GO
ALTER TABLE [dbo].[tblReportList] ADD  CONSTRAINT [DF_tblReportList_Total]  DEFAULT ((0)) FOR [TotalRow]
GO
ALTER TABLE [dbo].[tblReportList] ADD  CONSTRAINT [DF_tblReportList_AggFunction]  DEFAULT ('') FOR [AggFunction]
GO
ALTER TABLE [dbo].[tblReportList] ADD  CONSTRAINT [DF_tblReportList_AggField]  DEFAULT ('') FOR [AggField]
GO
ALTER TABLE [dbo].[tblReportList] ADD  CONSTRAINT [DF_tblReportList_PivotField]  DEFAULT ('') FOR [PivotField]
GO
ALTER TABLE [dbo].[tblReportList] ADD  CONSTRAINT [DF_tblReportList_EnteredDate]  DEFAULT (getdate()) FOR [Entered Date]
GO
ALTER TABLE [dbo].[tblReportRBAC] ADD  DEFAULT (newid()) FOR [rowguid]
GO
ALTER TABLE [dbo].[tblStandardTimeZone] ADD  DEFAULT (getutcdate()) FOR [EnteredDate]
GO
ALTER TABLE [dbo].[tblUserRBAC] ADD  DEFAULT (newid()) FOR [rowguid]
GO
ALTER TABLE [dbo].[TBL_STATES]  WITH NOCHECK ADD FOREIGN KEY([COUNTRY_ID])
REFERENCES [dbo].[TBL_COUNTRIES] ([COUNTRY_ID])
GO
ALTER TABLE [dbo].[TBL_STATES]  WITH NOCHECK ADD FOREIGN KEY([COUNTRY_ID])
REFERENCES [dbo].[TBL_COUNTRIES] ([COUNTRY_ID])
GO
ALTER TABLE [dbo].[TBL_STATES]  WITH NOCHECK ADD FOREIGN KEY([COUNTRY_ID])
REFERENCES [dbo].[TBL_COUNTRIES] ([COUNTRY_ID])
GO
ALTER TABLE [dbo].[TBL_STATES]  WITH NOCHECK ADD FOREIGN KEY([COUNTRY_ID])
REFERENCES [dbo].[TBL_COUNTRIES] ([COUNTRY_ID])
GO
ALTER TABLE [dbo].[TBL_STATES]  WITH NOCHECK ADD FOREIGN KEY([COUNTRY_ID])
REFERENCES [dbo].[TBL_COUNTRIES] ([COUNTRY_ID])
GO
ALTER TABLE [dbo].[TBL_STATES]  WITH NOCHECK ADD FOREIGN KEY([COUNTRY_ID])
REFERENCES [dbo].[TBL_COUNTRIES] ([COUNTRY_ID])
GO
ALTER TABLE [dbo].[TBL_STATES]  WITH NOCHECK ADD FOREIGN KEY([COUNTRY_ID])
REFERENCES [dbo].[TBL_COUNTRIES] ([COUNTRY_ID])
GO
ALTER TABLE [dbo].[TBL_STATES]  WITH NOCHECK ADD FOREIGN KEY([COUNTRY_ID])
REFERENCES [dbo].[TBL_COUNTRIES] ([COUNTRY_ID])
GO
ALTER TABLE [dbo].[TBL_STATES]  WITH NOCHECK ADD FOREIGN KEY([COUNTRY_ID])
REFERENCES [dbo].[TBL_COUNTRIES] ([COUNTRY_ID])
GO
/****** Object:  StoredProcedure [dbo].[usp0010GetClusterId]    Script Date: 11/21/2024 2:16:28 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE procedure [dbo].[usp0010GetClusterId]
AS
BEGIN
	 DECLARE @CompartmentID INT
     SET @CompartmentID= (SELECT [Setting Value] FROM s01tblSettingsCommon WHERE [Setting Name]='HubConsoleComprtmentID')
     SELECT  ClusterID FROM s01qryCabinets where compartmentid=@CompartmentID;
	
END
GO
/****** Object:  StoredProcedure [dbo].[usp008isDeviceConnected]    Script Date: 11/21/2024 2:16:28 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE PROCEDURE [dbo].[usp008isDeviceConnected]
    @ClinetIP VARCHAR(MAX)
AS
BEGIN
    
    -- Check if a device with the given IP address and 'Active' status exists
    IF EXISTS (SELECT 1 FROM tblDeviceRegistration WHERE [Device IPAddress] = @ClinetIP AND Status = 'Active')
    BEGIN
        SELECT 'Device is Connected'
    END
    ELSE
    BEGIN
        SELECT 'Device is not Connected'
    END
END
GO
/****** Object:  StoredProcedure [dbo].[usp704WebConsoleProcessUserLogs]    Script Date: 11/21/2024 2:16:28 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE Procedure [dbo].[usp704WebConsoleProcessUserLogs]
	@Event VARCHAR(255)
	, @udtSystemData udtSPParam READONLY
	, @udtEventData udtSPParam READONLY
	, @UserSessionID VARCHAR (50) = NULL
	--, @UsersID VARCHAR (50)
AS
	SET NOCOUNT ON;

	DECLARE @UserID INT, @CurrentUtcTime DATETIME, @ClusterName VARCHAR(100)
		   ,@DeviceID INT, @ComputerName VARCHAR(100), @Domain VARCHAR(50)
	DECLARE @EventType VARCHAR(30), @PIN VARCHAR(20)

	SET XACT_ABORT ON;
	BEGIN TRY
		BEGIN TRANSACTION

			SELECT @UserID = Value FROM @udtEventData WHERE Property = 'UserID'
			SELECT @DeviceID = Value FROM @udtSystemData WHERE Property = 'DeviceID'
			SELECT @ComputerName = Value FROM @udtSystemData WHERE Property = 'ComputerName'
			SELECT @Domain = Value FROM @udtSystemData WHERE Property = 'Domain'
			SELECT @EventType = Value FROM @udtEventData WHERE Property = 'EventType'
			SELECT @PIN = Value FROM @udtEventData WHERE Property = 'Pwd' 
			SELECT @DeviceID = Value FROM @udtSystemData WHERE Property = 'DeviceID'
			SELECT @ClusterName = [Cluster Name] FROM  dbo.qryCabinets WHERE ClusterID = @DeviceID
			IF @UserID is null
				SELECT @UserID = UserID FROM qry700WebConsoleSUPUsersValidate WHERE RFID = @PIN AND RFID <> ''
			IF @UserID is null
				SELECT @UserID = UserID FROM  qry700WebConsoleSUPUsersValidate WHERE PIN = @PIN AND PIN <> ''
			IF (SELECT [Setting Value] FROM s01tblSettingsCommon WHERE [Setting Name] = 'ENABLE_UTC_TIMEZONE') = 'TRUE'
			BEGIN
				SELECT @CurrentUTCTime = GETUTCDATE()
			END
			ELSE
			BEGIN
				SELECT @CurrentUTCTime = GETDATE()
			END
			IF @Event = 'Login'
			BEGIN
				INSERT INTO [MA_SUP_TJUHOR_IRISUPPLYDB]..s01tblUsersLog( UserID, [Login Time], [Computer Name], Domain, [Log Date], SessionID)
				VALUES( @UserID, @CurrentUtcTime, @ClusterName, @ComputerName, @CurrentUtcTime, @UserSessionID)
				INSERT INTO [MA_SUP_TJUHOR_IRISUPPLYDB]..tblSupplyUsersLog( UserID, [Login Time], [Computer Name], Domain, [Log Date], SessionID, ClusterID )
				VALUES( @UserID, @CurrentUtcTime, @ComputerName, @Domain, @CurrentUtcTime, @UserSessionID, @DeviceID)
			END
			ELSE IF @Event = 'Logout'
			BEGIN
				UPDATE [MA_SUP_TJUHOR_IRISUPPLYDB]..s01tblUsersLog
				SET [Logout Time] = @CurrentUtcTime
				WHERE UserID = @UserID 
					AND SessionID = @UserSessionID 
				UPDATE [MA_SUP_TJUHOR_IRISUPPLYDB]..tblSupplyUsersLog 
				SET [Logout Time] = @CurrentUtcTime
					, LogoutType = @EventType
				WHERE UserID = @UserID 
					AND SessionID = @UserSessionID 
			END
		COMMIT TRANSACTION
		END TRY
		BEGIN CATCH
			IF (XACT_STATE()) <> 0
				ROLLBACK TRANSACTION;
			EXECUTE uspLogError;
		END CATCH;
	SET NOCOUNT OFF
GO
/****** Object:  StoredProcedure [dbo].[usp705WebConsoleAppCabEventLogs]    Script Date: 11/21/2024 2:16:28 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE Procedure [dbo].[usp705WebConsoleAppCabEventLogs]
	@Event VARCHAR(255)
	, @udtSystemData udtSPParam READONLY
	, @udtEventData udtSPParam READONLY
	, @Logs VARCHAR (50) = ''
	, @UserSessionID VARCHAR (50) = NULL
AS
	SET NOCOUNT ON;

		--Edted by subramanya to handle type mismatch exception
		--SET XACT_ABORT ON;
		BEGIN TRY
		--BEGIN TRANSACTION

		DECLARE @UserID INT, @EntryTypeID INT, @EventID INT, @LoginSessionID BIGINT, @SessionID BIGINT, @LogMsg VARCHAR(255) 
		DECLARE @LoginType VARCHAR(100), @Msg VARCHAR(500), @LoginEventID INT, @ClusterName VARCHAR(100)
		DECLARE @EventType VARCHAR(50), @UserName VARCHAR(200), @RFID VARCHAR(50), @DeviceID INT, @ComputerName VARCHAR(100), @Domain VARCHAR(50)
		DECLARE @CurrentUtcTime DATETIME,@OverrideNotes VARCHAR(50),@Reason VARCHAR(50)
		DECLARE @DoorStatus VARCHAR(50), @LockStatus VARCHAR(50), @SwitchStatus VARCHAR(50), @ButtonClick VARCHAR(50), @PIN VARCHAR(20)

		SELECT @DeviceID = Value FROM @udtSystemData WHERE Property = 'DeviceID'
		SELECT @EntryTypeID = Value FROM @udtSystemData WHERE Property = 'EntryTypeID'
		SELECT @ComputerName = Value FROM @udtSystemData WHERE Property = 'ComputerName'
		SELECT @Domain = Value FROM @udtSystemData WHERE Property = 'Domain'
		SELECT @UserID = Value FROM @udtEventData WHERE Property = 'UserID'
		SELECT @SessionID = Value FROM @udtEventData WHERE Property = 'SessionID'
		SELECT @EventType = Value FROM @udtEventData WHERE Property = 'EventType'
		SELECT @OverrideNotes = Value FROM @udtEventData WHERE Property = 'EventInput2'
		SELECT @Reason = Value FROM @udtEventData WHERE Property = 'EventInput' 
		
		--H/w logs
		SELECT @DoorStatus = Value FROM @udtEventData WHERE Property = 'DoorStatus'
		SELECT @LockStatus = Value FROM @udtEventData WHERE Property = 'LockStatus'
		SELECT @SwitchStatus = Value FROM @udtEventData WHERE Property = 'SwitchStatus'
		SELECT @ButtonClick = Value FROM @udtEventData WHERE Property = 'ButtonClick' 

		SELECT @LogMsg = LogMsg, @EventID = EventID FROM tblAppEvents WHERE Event = @Event 
		SELECT @LoginEventID = EventID FROM tblAppEvents WHERE Event = 'LogIn'
		SELECT @ClusterName = [Cluster Name] FROM  dbo.qryCabinets WHERE ClusterID = @DeviceID
		SELECT @PIN = Value FROM @udtEventData WHERE Property = 'Pwd' 	

		SELECT @UserName = [User Name], @RFID = RFID, @UserID = UserID FROM qry700WebConsoleSUPUsersValidate WHERE RFID = @PIN AND RFID <> ''

		IF @UserName IS NULL
			SELECT @UserName = [User Name], @RFID = PIN, @UserID = UserID FROM  qry700WebConsoleSUPUsersValidate WHERE PIN = @PIN AND PIN <> ''

		IF @UserName IS NULL
			SELECT TOP 1 @RFID = RFID, @UserName = [User Name] FROM qry700WebConsoleSUPUsersValidate WHERE ClusterID = @DeviceID AND UserID = @UserID

		IF @RFID IS NULL
				SET @RFID = @PIN

		IF (SELECT [Setting Value] FROM s01tblSettingsCommon WHERE [Setting Name] = 'ENABLE_UTC_TIMEZONE') = 'TRUE'
		BEGIN
			SELECT @CurrentUTCTime = GETUTCDATE()
		END
		ELSE
		BEGIN
			SELECT @CurrentUTCTime = GETDATE()
		END

		IF @Event in ('Login','LogOut')
		BEGIN

			--INSERT INTO dbo.tblCabinetLog( UserID, [User Name], RFID, EventID, [Cluster Name], [Computer Name], [Domain], [Log Date], SessionID  )
			--SELECT  @UserID, @UserName, @RFID, @EventID, @ClusterName, @ComputerName, @Domain, @CurrentUtcTime, @SessionID
					
			INSERT INTO s01tblAppEventLog( UserID, [User Name], RFID, EventID, [Cluster Name], [Computer Name], [Domain], [Log Date], LogMsg, SessionID
									   )
		    SELECT  @UserID, @UserName, @RFID, @EventID, @ClusterName, @ComputerName, @Domain, @CurrentUtcTime, @Logs, @UserSessionID
					
		END

		ELSE IF (@Event NOT IN ('AppStartReason','AppOnLoad','IntializeCluster','LoadCulture', 'Login', 'ButtonClick') 
				   AND @Reason NOT IN ('btnProduct Search')
				   AND @OverrideNotes NOT IN ('ProductInventory') )
		BEGIN

			--INSERT INTO dbo.tblCabinetLog( UserID, [User Name], RFID, EventID, [Cluster Name], [Computer Name], [Domain], [Log Date], SessionID  )
			--SELECT  @UserID, @UserName, @RFID, @EventID, @ClusterName, @ComputerName, @Domain, @CurrentUtcTime, @SessionID
					
			INSERT INTO s01tblAppEventLog( UserID, [User Name], RFID, EventID, [Cluster Name], [Computer Name], [Domain], [Log Date], LogMsg, SessionID
									   )
		    SELECT  @UserID, @UserName, @RFID, @EventID, @ClusterName, @ComputerName, @Domain, @CurrentUtcTime
					, CASE
						WHEN @Event='AppShutdown' AND  @EventType = 'AddReason' THEN  'Added App shut down Reason Notes'
						WHEN @Event='AppShutdown' AND  @EventType = 'SelectReason' THEN  'Selected App shut down Reason and the application exited successfully'
					ELSE 
						@LogMsg
					END , @SessionID
					
		END

		--ELSE IF ( @Event= 'ButtonClick' )
		--BEGIN

			--INSERT INTO tblAppEventLog( UserID, [User Name], RFID, EventID, EntryTypeID, [Cluster Name], [Computer Name], [Domain], [Log Date], LogMsg, SessionID,OverrideNotes
			--						   , [Door Status],[Lock Status],[Switch Status],[Button Events] )
		 --   SELECT  @UserID, @UserName, @RFID, @EventID, @EntryTypeID, @ClusterName, @ComputerName, @Domain, @CurrentUtcTime, @LogMsg, @SessionID
			--		, @OverrideNotes, @DoorStatus, @LockStatus, @SwitchStatus, @ButtonClick

		--END

		ELSE
		BEGIN
			
			--INSERT INTO dbo.tblCabinetLog ([UserID], [User Name], [EventID], [Cluster Name], [Computer Name], [Domain], [Log Date], SessionID)
			--VALUES ( 0, 'System, System', @EventID, @ClusterName, @ComputerName, @Domain, @CurrentUtcTime, @SessionID)

			INSERT INTO s01tblAppEventLog( [UserID], [User Name], EventID,  [Cluster Name], [Computer Name], [Domain], [Log Date], LogMsg, SessionID  )
			VALUES ( 0, 'System, System', @EventID, @ClusterName, @ComputerName, @Domain, @CurrentUtcTime
				, CASE
					WHEN @EventType = 'AddReason' 
						THEN  'Added App Start Reason Notes'
					WHEN @EventType = 'SelectReason'
						THEN 'Selected App start Reason Notes'
					WHEN @EventType = 'CancelReason'
						THEN 'No Reason Added/Selected'
					ELSE 
						@LogMsg
			 	END
				
			, @SessionID  )

		END

		

		--COMMIT TRANSACTION
		END TRY
		BEGIN CATCH
			--IF (XACT_STATE()) <> 0
				--ROLLBACK TRANSACTION;
				SET @Msg = 'Error in inserting Logs';
			EXECUTE uspLogError;
		END CATCH;
	SET NOCOUNT OFF
GO
/****** Object:  StoredProcedure [dbo].[usp706WebConsoleGetProductName]    Script Date: 11/21/2024 2:16:28 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE Procedure [dbo].[usp706WebConsoleGetProductName]
as
begin
    select [Setting Value] from s01tblSettingsCommon where [Setting Name]='SiteName'
end
GO
/****** Object:  StoredProcedure [dbo].[usp709WebConsoleWebEventLog]    Script Date: 11/21/2024 2:16:28 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE Procedure [dbo].[usp709WebConsoleWebEventLog]
@Event varchar(50)
,@EventData [dbo].[udt705WebConsoleSPActivityLog] readonly
AS
begin
DECLARE @UserID INT,  @EventID INT, @LoginSessionID BIGINT, @SessionID varchar(max), @LogMsg VARCHAR(255) ,@OverrideNotes varchar(max)
        DECLARE @LoginType VARCHAR(100), @Msg VARCHAR(500), @LoginEventID INT, @ClusterName VARCHAR(100), @CompartmentName VARCHAR(100)
        DECLARE @EventType VARCHAR(50), @UserName VARCHAR(200), @RFID VARCHAR(50), @DeviceID INT, @ComputerName VARCHAR(100), @Domain VARCHAR(50),@Location varchar(100),@Room varchar(100)



 

 

        SELECT @DeviceID = Value FROM @EventData WHERE Property = 'DeviceID'

        SELECT @ComputerName = Value FROM @EventData WHERE Property = 'ComputerName'
        SELECT @Domain = Value FROM @EventData WHERE Property = 'Domain'
        SELECT @UserID = Value FROM @EventData WHERE Property = 'UserID'
        SELECT @SessionID = Value FROM @EventData WHERE Property = 'SessionID'
        SELECT @EventType = Value FROM @EventData WHERE Property = 'EventType'
        SELECT @OverrideNotes = Value FROM @EventData WHERE Property = 'OverrideNotes'
        SELECT @Location= Value FROM @EventData WHERE Property = 'Location'
        SELECT @Room = Value FROM @EventData WHERE Property = 'Room'


        SELECT @LogMsg = LogMsg, @EventID = EventID FROM MA_SUP_TJUHOR_IRISUPPLYDB..tblAppEvents WHERE Event = @Event 
        --SELECT @LoginEventID = EventID FROM tblAppEvents WHERE Event = 'LogIn'
        --SELECT @ClusterName = Value FROM @EventData WHERE Property = 'ClusterID'
        SELECT @ClusterName = [Cluster Name] FROM s01qryCabinets WHERE ClusterID = @DeviceID
        select @CompartmentName=CompartmentID FROM s01qryCabinets WHERE ClusterID = @DeviceID

        SELECT @RFID = Value FROM @EventData WHERE Property = 'RFID'
        SELECT @UserName =Value FROM @EventData WHERE Property = 'UserName'
      
        BEGIN
			INSERT INTO MA_SUP_TJUHOR_IRISUPPLYDB..tblAppEventLog( UserID, [User Name], RFID, EventID, [Cluster Name],[Compartment Name],[Computer Name], [Domain], [Log Date],LogMsg,SessionID
			--,WorkflowInfo,location,Room 
			)
            SELECT  @UserID, @UserName, @RFID, @EventID, @ClusterName,@CompartmentName, @ComputerName, @Domain, getutcdate(),@LogMsg,@SessionID
                    --, @OverrideNotes,@Location,@Room
        END


end
GO
/****** Object:  StoredProcedure [dbo].[usp710WebConsoleLoadOverrideReasons]    Script Date: 11/21/2024 2:16:28 AM ******/
SET ANSI_NULLS OFF
GO
SET QUOTED_IDENTIFIER OFF
GO


/****************************************************************************************************
Name				: uspLoadOverrideReasons
Version				: *******
Purpose				: To Load Override reason list
Author				: Rashmita
Date Created 		: 3Oth Nov 2018
Application Affected: IriSupply
-----------------------------------------------------------------------------------------------------
Release Notes		:
------------------------------------------------------------------------------------------------------
--SP CALL
DECLARE @p2 dbo.udtSPParam
DECLARE @p3 dbo.udtSPParam
INSERT INTO @p2 VALUES(N'DeviceID',4)
INSERT INTO @p2 VALUES(N'EntryTypeID',1)
INSERT INTO @p2 values(N'ComputerName',N'TestMachine')
INSERT INTO @p2 values(N'Domain',N'TestDomain')
INSERT INTO @p3 VALUES(N'UserID', 2)
INSERT INTO @p3 VALUES(N'EventInput', 'Return')--Stock Item, Remove Item, Wste Item, Waste
INSERT INTO @p3 VALUES(N'EventOutPut', 'OverrideReasons')
INSERT INTO @p3 VALUES(N'SessionID', N'20180910183204970')

EXEC uspLoadOverrideReasons @Event='LoadOverrideReason',@udtSystemData=@p2, @udtEventData=@p3

--Validate Data-
SELECT TOP 1 * FROM tblCabinetLog ORDER BY CabinetLogID DESC
SELECT TOP 1 * FROM tblAppEventLog ORDER BY AppEventLogID DESC
*****************************************************************************************************/
CREATE Procedure [dbo].[usp710WebConsoleLoadOverrideReasons]
	@Event VARCHAR(255)
	, @udtSystemData udtSPParam READONLY
	, @udtEventData udtSPParam READONLY
AS
	SET NOCOUNT ON;
		DECLARE @TransactionType VARCHAR(100), @DataSetName VARCHAR(100), @Msg VARCHAR(200), @EventType VARCHAR(15)
		DECLARE @OverrideNotes VARCHAR (100), @OverrideID INT, @ExceptionID INT

		--Edited by subramanya to handle type mismatch exception
		SET XACT_ABORT ON;
		BEGIN TRY
		BEGIN TRANSACTION

		SELECT @TransactionType = Value FROM @udtEventData WHERE Property = 'EventInput'
		
		SELECT @EventType = Value FROM @udtEventData WHERE Property = 'EventType'
		SELECT @DataSetName = Value FROM @udtEventData WHERE Property = 'EventOutPut'

		SELECT @OverrideID = OverrideID from s01tblOverride WHERE OverrideDesc = CASE 
																				   WHEN @TransactionType = 'Waste Item' 
																						THEN 'Waste' 
																				   WHEN @TransactionType = 'Return' 
																						THEN 'Return Item'
																				   WHEN @TransactionType = 'Override' 
																						THEN 'Manual Override'
																				   ELSE @TransactionType 
																		      END
		
		SELECT @ExceptionID = ExcID from s01tblException WHERE ExceptionDesc = CASE 
																				WHEN @TransactionType = 'Waste' 
																					THEN 'Waste Item Usage' 
																				WHEN @TransactionType = 'Return' 
																					THEN 'Return RFID item to inventory'
																				WHEN @TransactionType = 'Return' 
																					THEN 'Return RFID item to inventory'
																			    ELSE @TransactionType 
																			END
	
				IF ( @Event = 'LoadOverrideReason' AND @EventType = 'OverrideType' )
				BEGIN
					SELECT @DataSetName AS DatasetName, OverrideID, OverrideNotes 
					FROM s01tblOverrideNotes 
					WHERE OverrideID = @OverrideID
				END
				IF ( @Event = 'LoadOverrideReason' AND @EventType = 'ExceptionType')
				BEGIN
					SELECT @DataSetName AS DatasetName, ExcID, ExcNotes 
					FROM s01qryExceptionNotesChoice 
					WHERE ExcID = @ExceptionID
				END

				EXECUTE  usp705WebConsoleAppCabEventLogs @Event = @Event
						, @udtSystemData = @udtSystemData
						, @udtEventData = @udtEventData

			COMMIT TRANSACTION
		END TRY
		BEGIN CATCH
			IF (XACT_STATE()) <> 0
				ROLLBACK TRANSACTION;
				SET @Msg = 'Error in Loading in Override Reasons';
			EXECUTE s01uspLogError;
		END CATCH;
	SET NOCOUNT OFF
GO
/****** Object:  StoredProcedure [dbo].[usp711WebConsoleLogUserRecentVisits]    Script Date: 11/21/2024 2:16:28 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE Procedure [dbo].[usp711WebConsoleLogUserRecentVisits]
@UserID int,
@ModuleName varchar(50)
as 
begin
Declare @Module_URL varchar(50)
    if exists (select Module_Name from tblModules where module_Name=@ModuleName)
	begin
	   set @Module_URL=(select Link from tblModules where module_Name=@ModuleName)
	   delete from tblusersrecenthistory where userid=@UserID and ModuleName=@ModuleName;
	   insert into tblusersrecenthistory values(@UserID,@ModuleName,@Module_URL,GETUTCDATE())
	end
	else
	begin
	   select 'Failed to add'
	end
end
GO
/****** Object:  StoredProcedure [dbo].[usp712WebConsoleAddPatient]    Script Date: 11/21/2024 2:16:28 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE Procedure [dbo].[usp712WebConsoleAddPatient]
	@Event VARCHAR(255)
	, @udtSystemData udtSPParam READONLY
	, @udtEventData udtSPParam READONLY
AS
	SET NOCOUNT ON;
	DECLARE @LastName VARCHAR(50), @FirstName VARCHAR(50), @MiddleName VARCHAR(50), @PatientID VARCHAR(20)
	DECLARE @AccountNo VARCHAR(50), @AdmitDate VARCHAR(50), @DischargeDate VARCHAR(50), @ProcedureDate VARCHAR(50), @CurrentUTCTime DATETIME
	DECLARE @ProcedureStartTime VARCHAR(50), @ProcedureEndTime VARCHAR(50), @ProcedureID INT , @PhysicianName VARCHAR(255), @DOB VARCHAR(50)
	DECLARE @EnteredUserID INT, @UserID INT, @EntryTypeID INT, @MAVisitID BIGINT, @MAPatientID BIGINT, @SessionID BIGINT, @PhysicianID VARCHAR(15)
	DECLARE @MAPhysicianID INT,  @MAScheduleID INT, @AppointmentID VARCHAR(20), @Msg VARCHAR(200), @DeviceID INT
	DECLARE @ClientTimeZone VARCHAR(500), @UTCStandard VARCHAR(100), @SecondDiff INT, @DataSetName VARCHAR(50)
	
	--Edited by subramanya to handle type mismatch exception
	SET XACT_ABORT ON;
	BEGIN TRY
	BEGIN TRANSACTION;

	SELECT @DeviceID = Value FROM @udtSystemData WHERE Property = 'DeviceID'
	SELECT @EntryTypeID = Value FROM @udtSystemData WHERE Property = 'EntryTypeID'
	SELECT @SessionID = Value FROM @udtEventData WHERE Property = 'SessionID'
	SELECT @UserID = Value FROM @udtEventData WHERE Property = 'UserID'
	SELECT @LastName = Value FROM @udtEventData WHERE Property = 'LastName'
	SELECT @MiddleName = Value FROM @udtEventData WHERE Property = 'MiddleName'
	SELECT @FirstName = Value FROM @udtEventData WHERE Property = 'FirstName'
	SELECT @PatientID = Value FROM @udtEventData WHERE Property = 'PatientID'
	SELECT @DOB = Value FROM @udtEventData WHERE Property = 'DOB' 
	SELECT @AccountNo = Value FROM @udtEventData WHERE Property = 'AccountNo'
	SELECT @AppointmentID = Value FROM @udtEventData WHERE Property = 'AppointmentID'
	SELECT @AdmitDate = Value FROM @udtEventData WHERE Property = 'AdmitDate'
	SELECT @DischargeDate = Value FROM @udtEventData WHERE Property = 'DischargeDate'
	SELECT @ProcedureDate = Value FROM @udtEventData WHERE Property = 'ProcedureDate'
	SELECT @ProcedureStartTime = Value FROM @udtEventData WHERE Property = 'ProcedureStartTime'
	SELECT @ProcedureEndTime = Value FROM @udtEventData WHERE Property = 'ProcedureEndTime'
	SELECT @ProcedureID = Value FROM @udtEventData WHERE Property = 'ProcedureID'
	SELECT @PhysicianID = Value FROM @udtEventData WHERE Property = 'PhysicianID'
	SELECT @DataSetName = Value FROM @udtEventData WHERE Property = 'EventOutPut'

	SET @DOB = FORMAT( CAST( @DOB AS DATETIME ), 'yyyy\-MM\-dd hh\:mm\:ss' )
	SET @AdmitDate = FORMAT( CAST( @AdmitDate AS DATETIME ), 'yyyy\-MM\-dd hh\:mm\:ss' )
	SET @DischargeDate = FORMAT( CAST( @DischargeDate AS DATETIME ), 'yyyy\-MM\-dd hh\:mm\:ss' )
	SET @ProcedureDate = FORMAT( CAST( @ProcedureDate AS DATETIME ), 'yyyy\-MM\-dd hh\:mm\:ss' )
	SET @ProcedureStartTime = FORMAT( CAST( @ProcedureStartTime AS DATETIME ), 'yyyy\-MM\-dd hh\:mm\:ss' )
	SET @ProcedureEndTime = FORMAT( CAST( @ProcedureEndTime AS DATETIME ), 'yyyy\-MM\-dd hh\:mm\:ss' )

	IF @ProcedureID IS NULL
		SELECT @ProcedureID = ProcedureID FROM s01tblProcedures WHERE ProcedureDesc = 'Unknown'
		SELECT @PhysicianName = [Physician Name], @MAPhysicianID = MAPhysicianID FROM s01qryPhysicians WHERE PhysicianID = @PhysicianID 
	
	SELECT @ClientTimeZone = [dbo].[udf700WebConsoleSUPFetchSettings] ( @DeviceId,'CLIENT_TIMEZONE' )
	SELECT @UTCStandard = UTCStandard FROM tblStandardTimeZone WHERE StandardTimeZone = @ClientTimeZone
	SELECT @SecondDiff =  REPLACE(@UTCStandard, 'UTC', '')
	--IF (SELECT [Setting Value] FROM s01tblSupplySettings WHERE [Setting Name] = 'ENABLE_UTC_TIMEZONE') = 'TRUE'
	--BEGIN
	--	SELECT @CurrentUTCTime = GETUTCDATE()
	--END
	--ELSE
	--BEGIN
	--	SELECT @CurrentUTCTime = GETDATE()
	--END
	SELECT @CurrentUTCTime = GETDATE()

		SELECT @MAPatientID = MAPatientID 
		FROM s01tblPatients 
		WHERE PatientID = @PatientID

		IF (@MAPatientID IS NULL)
		BEGIN

  			INSERT INTO s01tblPatients( PatientID, [Account Number], [First Name], [Middle Name], [Last Name], DOB, EntryTypeID, [Entered By]
						, [Entered Date], LastActivityDate )
			VALUES( @PatientID, @AccountNo, @FirstName, @MiddleName, @LastName, @DOB, @EntryTypeID, @UserID 
						, @CurrentUTCTime, @CurrentUTCTime )
            select  'Patinet Added Successfully' as Msg
			SET @MAPatientID = SCOPE_IDENTITY()
		END

		ELSE
		BEGIN
			UPDATE s01tblPatients 
			SET DOB = @DOB, [First Name] = @FirstName, [Middle Name] = @MiddleName
					, [Last Name] = @LastName, LastActivityDate = @CurrentUTCTime 
			WHERE @MAPatientID = MAPatientID

			SELECT @MAVisitID = MAVisitID 
			FROM s01tblPatientVisits 
			WHERE MAPatientID = @MAPatientID
			--AND [AltAcctNo] = @AccountNo
		END

		IF EXISTS ( SELECT  MAVisitID FROM s01tblPatientVisits WHERE MAPatientID = @MAPatientID )
		BEGIN
			PRINT 'VIST UPDATE'
			UPDATE s01tblPatientVisits 
			SET MAPatientID = @MAPatientID, AdmitDate = @AdmitDate, AltAcctNo = @AccountNo
					, DischargeDate = @DischargeDate, LastActivityDate = @CurrentUTCTime, UpdatedBy = @UserID				
			WHERE @MAVisitID = MAVisitID
		END
		ELSE

		BEGIN
			PRINT 'VIST INSERT'
			INSERT INTO s01tblPatientVisits( MAPatientID, AltAcctNo, UpdatedBy, EntryTypeID, AdmitDate, DischargeDate, LastActivityDate )
  			VALUES( @MAPatientID, @AccountNo, @UserID, @EntryTypeID, @AdmitDate, @DischargeDate, @CurrentUTCTime )
			SET @MAVisitID = SCOPE_IDENTITY()
		END

		SELECT  @MAScheduleID = MAScheduleID
		FROM s01tblPatientSchedules 
		WHERE MAVisitID = @MAVisitID
			AND AppointmentID = @AppointmentID
			
		IF EXISTS ( SELECT MAScheduleID FROM s01tblPatientSchedules WHERE MAVisitID = @MAVisitID AND AppointmentID = @AppointmentID )
		BEGIN
			PRINT 'Schedule UPDATE'
			UPDATE s01tblPatientSchedules 
			SET MAPatientID = @MAPatientID, AppointmentID = @AppointmentID, MAPhysicianID = @MAPhysicianID
							, MAVisitID = @MAVisitID, ProcedureID = @ProcedureID, ProcedureDate = @ProcedureDate, ProcedureStartTime = @ProcedureStartTime
							, ProcedureEndTime = @ProcedureEndTime, LastActivityDate = @CurrentUTCTime				
			WHERE @MAScheduleID = @MAScheduleID 
				AND AppointmentID = @AppointmentID
		END


		ELSE IF NOT EXISTS ( SELECT MAScheduleID FROM s01tblPatientSchedules WHERE MAVisitID = @MAVisitID AND AppointmentID = @AppointmentID )
		BEGIN
			PRINT 'Schedule INSERT'		
			INSERT INTO s01tblPatientSchedules ( MAPatientID, AppointmentID, MAPhysicianID, EntryTypeID, [Entered By], [Entered Date]
							, MAVisitID, ProcedureID, ProcedureDate, ProcedureStartTime, ProcedureEndTime, LastActivityDate )
			VALUES ( @MAPatientID, @AppointmentID, @MAPhysicianID, @EntryTypeID,  @UserID, @CurrentUTCTime, @MAVisitID, @ProcedureID
							, @ProcedureDate, @ProcedureStartTime, @ProcedureEndTime, @CurrentUTCTime )
			SET @MAScheduleID = SCOPE_IDENTITY()		
		END

		COMMIT TRANSACTION;
		SET @Msg = 'Successfully Added the Patinet'

		EXECUTE  usp705WebConsoleAppCabEventLogs @Event = @Event
						, @udtSystemData = @udtSystemData
						, @udtEventData = @udtEventData

		SELECT @DataSetName AS DataSetName, 'CSN: ' + ISNULL(SPD.VisitNumber,'N/A') AS CSN, PD.[Patient Name] AS PatientName
			, 'MRN: ' + ISNULL(PD.PatientID,'N/A') AS MRN, PD.[Physician Name] AS PhysicianName
			, CASE 
					WHEN DATEADD( SECOND, @SecondDiff, PD.ProcedureDate ) IS NULL 
						THEN PD.LastActivityDate 
					ELSE DATEADD( SECOND, @SecondDiff, PD.LastActivityDate ) 
			  END AS ProcedureDate
		    , 'CASEID: ' + ISNULL(PD.AppointmentID,'N/A') AS CASEID,PD.MAPatientID, PD.MAScheduleID, PD.MAVisitID, SPD.ProcedureCode 
		FROM s01qryPatientDisplayADT PD
							LEFT OUTER JOIN qry701WebConsoleSUPPatientDisplayADT SPD ON SPD.MaScheduleID = PD.MaScheduleID
		WHERE PD.MAScheduleID  = @MAScheduleID

	END TRY
	BEGIN CATCH
		IF (XACT_STATE()) <> 0
			ROLLBACK TRANSACTION;
			SET @Msg = 'Unable to Add Patient Data'
		EXECUTE s01uspLogError;
	END CATCH;
	SET NOCOUNT OFF
GO
/****** Object:  StoredProcedure [dbo].[usp712WebConsoleCheckUserModuleAccess]    Script Date: 11/21/2024 2:16:28 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE Procedure [dbo].[usp712WebConsoleCheckUserModuleAccess]
@UserGroupID int,
@ModuleName varchar(50)
as
begin
Declare @ModuleID int;
set @ModuleID= (select Module_ID from tblModules where Replace( Module_Name,' ','')=@ModuleName)
       if(@UserGroupID = 0)
	   begin 
	        if exists (select * from tblModules where Replace( Module_Name,' ','') =@ModuleName and ISENABLED =1)
			begin
			select 'true' as result;
			end
	   end
      else if exists (select * from tblUserRBAC where UserGroupID =@UserGroupID and ModuleID =@ModuleID and status=1 )
	   begin
	        select 'true' as result
	   end
	   else
	   begin
	        select 'false' as result
	   end
end
GO
/****** Object:  StoredProcedure [dbo].[usp714WebConsoleGetPatientUsage ]    Script Date: 11/21/2024 2:16:28 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE Procedure [dbo].[usp714WebConsoleGetPatientUsage ]
@MAScheduleID nvarchar(50)
as
begin
					--SELECT qry704.[Description], qry704.CatNo,CONVERT(varchar, CONVERT(datetime,qry704.DateUsed,120),22)as DateUsed, qry704.[REAL], qry704.Qty, qry704.PatientName, qry704.ItemUsageID, qry704.UsageTypeID,Ext.Capitated
					--	, RFID, ProductType,SerialNo, LotNo--, EpicStatus
					--FROM qry704WebConsoleSUPItemUsageList qry704
					--left JOIN tblItemUsageExt Ext ON qry704.ItemUsageID=Ext.ItemUsageID
					--WHERE MAScheduleID = @MAScheduleID
					--ORDER BY DateUsed DESC

					--updated by viresh -- 11/11/2024
					
					--SELECT qry704.[Description], qry704.CatNo,CONVERT(varchar, CONVERT(datetime,qry704.DateUsed,120),22)as DateUsed, qry704.[REAL], qry704.Qty, qry704.PatientName, qry704.ItemUsageID, qry704.UsageTypeID,Ext.Capitated
					--	, qry704.RFID, ProductType,SerialNo, LotNo,I.ItemStatusID
					--FROM qry704WebConsoleSUPItemUsageList qry704
					--left JOIN tblItemUsageExt Ext ON qry704.ItemUsageID=Ext.ItemUsageID
					--INNER JOIN qryItemUsage I ON qry704.ItemUsageID = I.ItemUsageID
					--WHERE qry704.MAScheduleID = @MAScheduleID --AND I.ItemStatusID = 4
					--ORDER BY DateUsed DESC
					SELECT * 
					FROM qryHUBFetchUsageItemList
					WHERE MAScheduleID = @MAScheduleID --AND I.ItemStatusID = 4
					ORDER BY DateUsed DESC
end
GO
/****** Object:  StoredProcedure [dbo].[usp717WebConsoleGetUserGroups]    Script Date: 11/21/2024 2:16:28 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE Procedure [dbo].[usp717WebConsoleGetUserGroups]
@GroupID int
AS
BEGIN
	if(@GroupID =1)
	begin
	     select groupid GROUP_ID,[User Group] USER_GROUP from s01tblUserGroup where GroupID not in (0);
	end	
	else if(@GroupID=10)
	begin
	     select groupid Group_ID,[User Group] User_Group from s01tblUserGroup where GroupID in (9,10)
	end
	else
	begin
	     select groupid GROUP_ID,[User Group] USER_GROUP from s01tblUserGroup where GroupID not in (0);
	end
END

GO
/****** Object:  StoredProcedure [dbo].[usp719WebConsoleGetUsers]    Script Date: 11/21/2024 2:16:28 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE Procedure [dbo].[usp719WebConsoleGetUsers]
@UserGroupID int
AS
BEGIN
if(@UserGroupID =10)
begin
    select UserID, CONCAT(t.[First Name],' ',t.[Last Name]) as Name,
    t.Username, 
    t.[Job Title] as Designation,
    t.[Employee ID],
    t.Department,
    t.[Entered Date] DateAdded,
    t.Domain,
    t.UserStatusID as Status,g.[User Group] as UserGroup
    from s01tblUsers as t
    inner join s01tblUserGroup as g on t.GroupID=g.GroupID
    where  t.GroupID not in (9,10)
    order by CONCAT(t.[First Name],' ',t.[Last Name])
end
else if(@UserGroupID =1)
   begin
    select UserID, CONCAT(t.[First Name],' ',t.[Last Name]) as Name,
    t.Username, 
    t.[Job Title] as Designation,
    t.[Employee ID],
    t.Department,
    t.[Entered Date] DateAdded,
    t.Domain,
    t.UserStatusID as Status,g.[User Group] as UserGroup
    from s01tblUsers as t
    inner join s01tblUserGroup as g on t.GroupID=g.GroupID
    where  t.GroupID not in (0)
    order by CONCAT(t.[First Name],' ',t.[Last Name])
end  
else
   begin
    select UserID, CONCAT(t.[First Name],' ',t.[Last Name]) as Name,
    t.Username, 
    t.[Job Title] as Designation,
    t.[Employee ID],
    t.Department,
    t.[Entered Date] DateAdded,
    t.Domain,
    t.UserStatusID as Status,g.[User Group] as UserGroup
    from s01tblUsers as t
    inner join s01tblUserGroup as g on t.GroupID=g.GroupID
    
    order by CONCAT(t.[First Name],' ',t.[Last Name])
end  
END
GO
/****** Object:  StoredProcedure [dbo].[usp720WebConsoleUpdateUserGroupAccess]    Script Date: 11/21/2024 2:16:28 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE Procedure [dbo].[usp720WebConsoleUpdateUserGroupAccess]
@UserGroupID int,	 
@ModuleNames dbo.udt706WebConsoleModules  READONLY,
@UnCheckedModuleNames dbo.udt707WebConsoleUnCheckedModules READONLY
AS
BEGIN

		SET NOCOUNT ON;

		--Module Enable disable 
		DROP TABLE IF EXISTS #tblModulesf

		CREATE TABLE #tblModules
		(
			Id INT IDENTITY(1, 1) PRIMARY KEY NOT NULL, 
			ModuleName VARCHAR(50)
			
		)
		INSERT INTO #tblModules (ModuleName)
		select [ModuleName] from @ModuleNames

		DROP TABLE IF EXISTS #tblUnCheckedModules

		CREATE TABLE #tblUnCheckedModules
		(
			Id INT IDENTITY(1, 1) PRIMARY KEY NOT NULL, 
			ModuleName VARCHAR(50)
			
		)

		INSERT INTO #tblUnCheckedModules (ModuleName)
		select [UnCheckedModuleNames] from @UnCheckedModuleNames

		DECLARE @Counter INT , @MaxId INT, @ModuleName NVARCHAR(100),@ModuleID int;
		DECLARE @Counter1 INT , @MaxId1 INT, @ModuleName1 NVARCHAR(100),@ModuleID1 int;
	

		SELECT @Counter = min(Id) , @MaxId = max(Id)
		FROM #tblModules;

		SELECT @Counter1 = min(Id) , @MaxId1 = max(Id)
		FROM #tblUnCheckedModules;



		
		--update tblUserRBAC set Status=0 where UserGroupID=@UserGroupID and Moduleid !=0;
			--Inactive 
		WHILE(@Counter1 IS NOT NULL AND @Counter1 <= @MaxId1)
		BEGIN
			SELECT @ModuleName1 = ModuleName
			
			FROM #tblUnCheckedModules WHERE Id = @Counter1
			Declare @Mid1 int;
			set @Mid1 =(select Module_ID from tblModules where MODULE_NAME=@ModuleName1)
			--PRINT CONVERT(VARCHAR,@Counter) + '. Cluster name is ' + @ClusterName
			if Exists (select top 1 * from tblUserRBAC where ModuleID=@Mid1 and UserGroupID =@UserGroupID)
			begin
				update tblUserRBAC set status= 0 where ModuleID=@Mid1 and UserGroupID =@UserGroupID;
			
			end
			else 
			begin
				insert into tblUserRBAC (UserGroupID,ModuleID,Status)
			        values (@UserGroupID,@Mid1,0)
			end
			
			 --Print @ClusterID
			SET @Counter1 = @Counter1 + 1
		END	
		--Active 
		WHILE(@Counter IS NOT NULL AND @Counter <= @MaxId)
		BEGIN
			SELECT @ModuleName = ModuleName
			
			FROM #tblModules WHERE Id = @Counter
		Declare  @Mid int;
			set @Mid =(select Module_ID from tblModules where MODULE_NAME=@ModuleName)
			--PRINT CONVERT(VARCHAR,@Counter) + '. Cluster name is ' + @ClusterName
			if Exists (select top 1 * from tblUserRBAC where ModuleID=@Mid and UserGroupID =@UserGroupID)
			begin
				update tblUserRBAC set status= 1 where ModuleID=@Mid and UserGroupID =@UserGroupID;
				Declare @ParentModuleID int;
				set @ParentModuleID= (select PARENT_MODULE_ID from tblModules where MODULE_ID=@Mid)
			
			if(@ParentModuleID >0)
			begin
				update tblUserRBAC set Status=1 where ModuleID=@ParentModuleID and UserGroupID=@UserGroupID;
			end
			end
			else 
			begin
				insert into tblUserRBAC (UserGroupID,ModuleID,Status)
			        values (@UserGroupID,@Mid,1)
			end
			
			 --Print @ClusterID
			SET @Counter = @Counter + 1
		END	
		
	
		
    
	
	
END
GO
/****** Object:  StoredProcedure [dbo].[usp721WebConsoleManageUsers]    Script Date: 11/21/2024 2:16:28 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE Procedure [dbo].[usp721WebConsoleManageUsers]
@Event varchar(50)
,@EventData udtSPParam readonly
as
Declare @EmployeeID varchar(100),@Username varchar(100),@FirstName varchar(100),@MiddleName varchar(100),@LastName varchar(100),@Department varchar(100),@JobTitle varchar(100),@Suffix varchar(100),
@UserGroupID varchar(100),@RFID varchar(100),@PIN varchar(100),@Title varchar(100),@Company varchar(100),
@Office varchar(100),@OfficeStreetAddress varchar(500),@OfficeCity varchar(100),@OfficeState varchar(100),
@OfficeZip varchar(100),@OfficeCountry varchar(100),@HomeStreetAddress varchar(100),@HomeCity varchar(100),
@HomeState varchar(100),@HomeZip varchar(100),@HomeCountry varchar(100),@HomePhoneNumber varchar(100),
@OfficePhoneNumber varchar(100),@MobilePhoneNumber varchar(100),@FaxNumber varchar(100),@PagerNumber varchar(100),@Email varchar(100),
@Password varchar(100),@Domain varchar(100),@EnteredBy varchar(100),@ComputerName varchar(100),@UserStatus int,@UserID int,@Room varchar(100)

set @Domain =(select [Value] from @EventData where [Property] = 'Domain')
set @EnteredBy =(select [Value] from @EventData where [Property] = 'EnteredBy')
set @ComputerName =(select [Value] from @EventData where [Property] = 'ComputerName')
set @Password =(select [Value] from @EventData where [Property] = 'Password')
set @Email =(select [Value] from @EventData where [Property] = 'Email')
set @EmployeeID =(select [Value] from @EventData where [Property] = 'EmployeeID')
set @Username =(select [Value] from @EventData where [Property] = 'Username')
set @FirstName =(select [Value] from @EventData where [Property] = 'FirstName ')
set @MiddleName =(select [Value] from @EventData where [Property] = 'MiddleName')
set @LastName =(select [Value] from @EventData where [Property] = 'LastName')
set @Department =(select [Value] from @EventData where [Property] = 'Department')
set @JobTitle =(select [Value] from @EventData where [Property] = 'JobTitle')
set @Suffix =(select [Value] from @EventData where [Property] = 'Suffix')
set @UserGroupID =(select [Value] from @EventData where [Property] = 'UserGroupID')
set @RFID =(select [Value] from @EventData where [Property] = 'RFID')
set @PIN =(select [Value] from @EventData where [Property] = 'PIN')
set @Title =(select [Value] from @EventData where [Property] = 'Title')
set @Company =(select [Value] from @EventData where [Property] = 'Company')
set @Office =(select [Value] from @EventData where [Property] = 'Office')
set @OfficeStreetAddress =(select [Value] from @EventData where [Property] = 'OfficeStreetAddress')
set @OfficeCity =(select [Value] from @EventData where [Property] = 'OfficeCity')
set @OfficeState =(select [Value] from @EventData where [Property] = 'OfficeState')
set @OfficeZip =(select [Value] from @EventData where [Property] = 'OfficeZip')
set @OfficeCountry =(select [Value] from @EventData where [Property] = 'OfficeCountry')
set @HomeStreetAddress =(select [Value] from @EventData where [Property] = 'HomeStreetAddress')
set @HomeCity =(select [Value] from @EventData where [Property] = 'HomeCity')
set @HomeState =(select [Value] from @EventData where [Property] = 'HomeState')
set @HomeZip =(select [Value] from @EventData where [Property] = 'HomeZip')
set @HomeCountry =(select [Value] from @EventData where [Property] = 'HomeCountry')
set @HomePhoneNumber =(select [Value] from @EventData where [Property] = 'HomePhoneNumber')
set @OfficePhoneNumber =(select [Value] from @EventData where [Property] = 'OfficePhoneNumber')
set @MobilePhoneNumber =(select [Value] from @EventData where [Property] = 'MobilePhoneNumber')
set @FaxNumber =(select [Value] from @EventData where [Property] = 'FaxNumber')
set @PagerNumber =(select [Value] from @EventData where [Property] = 'PagerNumber')
set @UserStatus =(select ID from tblReportStatus where Status=(select [Value] from @EventData where [Property] = 'UserStatus'))
set @UserID = (select [Value] from @EventData where [Property] = 'UserID')
set @Room= (select [Value] from @EventData where [Property] = 'Room')

if @Event ='GetUserdetails' 
Begin
	exec [dbo].[usp737WebConsoleGetUserDetails] @UserID

end

if @Event ='UpdateUserdetails' 
Begin
	exec [usp738WebConsoleUpdateUserDetails]
		@Domain,
		@EnteredBy,
		@ComputerName,
		@Password,
		@Email,
		@UserID,
		@EmployeeID ,
		@Username ,
		@FirstName ,
		@MiddleName,
		@LastName,
		@Department,
		@JobTitle,
		@Suffix,
		@UserGroupID,
		@RFID,
		@PIN,
		@Title,
		@Company,
		@Office,
		@OfficeStreetAddress,
		@OfficeCity,
		@OfficeState,
		@OfficeZip,
		@OfficeCountry,
		@HomeStreetAddress,
		@HomeCity,
		@HomeState,
		@HomeZip,
		@HomeCountry,
		@HomePhoneNumber,
		@OfficePhoneNumber,
		@MobilePhoneNumber,
		@FaxNumber,
		@PagerNumber,
		@UserStatus,
		@Room
		
		
end

if @Event ='AddNewUser' 
Begin
	exec [usp739WebConsoleAddNewUser]
		@Domain,
		@EnteredBy,
		@ComputerName,
		@Password,
		@Email,
		@EmployeeID ,
		@Username ,
		@FirstName ,
		@MiddleName,
		@LastName,
		@Department,
		@JobTitle,
		@Suffix,
		@UserGroupID,
		@RFID,
		@PIN,
		@Title,
		@Company,
		@Office,
		@OfficeStreetAddress,
		@OfficeCity,
		@OfficeState,
		@OfficeZip,
		@OfficeCountry,
		@HomeStreetAddress,
		@HomeCity,
		@HomeState,
		@HomeZip,
		@HomeCountry,
		@HomePhoneNumber,
		@OfficePhoneNumber,
		@MobilePhoneNumber,
		@FaxNumber,
		@PagerNumber,
		@UserStatus,
		@Room
end
GO
/****** Object:  StoredProcedure [dbo].[usp722WebConsoleGetStatesList]    Script Date: 11/21/2024 2:16:28 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE Procedure [dbo].[usp722WebConsoleGetStatesList]
AS
BEGIN
	select STATE_ID StateID,STATE_NAME StateName from TBL_STATES;
END

/****** Object:  StoredProcedure [dbo].[uspGetUserGroups]    Script Date: 05/17/2021 7.59.10 PM ******/
SET ANSI_NULLS ON
GO
/****** Object:  StoredProcedure [dbo].[usp723WebConsoleGetCountryList]    Script Date: 11/21/2024 2:16:28 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE Procedure [dbo].[usp723WebConsoleGetCountryList]
AS
BEGIN
	select Country_ID CountryID,Country_NAME CountryName from TBL_COUNTRIES;
END
GO
/****** Object:  StoredProcedure [dbo].[usp728WebConsoleGetStatistics]    Script Date: 11/21/2024 2:16:28 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE Procedure [dbo].[usp728WebConsoleGetStatistics]
AS
BEGIN


Declare @Days int
select @Days=[Setting Value] from s01tblSettingsCommon
where description like '%Default lead time for Lead Expiration Alerts%'
and [Setting Name] = 'ExpLeadTime'

Declare @Style1 nvarchar(255)='linear-gradient(45deg, #2c6b6e, #f20febf7)|fa fa-calendar-week';
Declare @Style2 nvarchar(255)='linear-gradient(45deg, #0000ff5e, #1035ea)|fa fa-calendar-minus';
Declare @Style3 nvarchar(255)='linear-gradient(45deg, #353b35c4, #1aff91f5)|fa fa-calendar-plus';
Declare @Style4 nvarchar(255)='linear-gradient(45deg, black, #2dcf11f5)|fas fa-calendar-check';
Declare @Style5 nvarchar(255)='linear-gradient(45deg, #6e6363, #ffdb02)|fas fa-exclamation-triangle';
Declare @Style6 nvarchar(255)='linear-gradient(45deg, #b29da6, #e9192d)|fas fa-trash-restore-alt';
Declare @Style7 nvarchar(255)='linear-gradient(45deg, #ef0e08d9, #c4ef0d)|fa fa-clock';
Declare @Style8 nvarchar(255)='linear-gradient(45deg, #403940d9, #7bbbff)|fa fa-user-plus';



SELECT '# Items Removed Against Patient' As [Name] ,'usp730WebConsoleRemovedItemsReport' as SPName,'Items Removed Against Patient' as 'LinkToReport', @Style1 as Style
	  ,Count(*) AS [Count]  from s01qryitemhistory I 
	  
		where itemstatusid=2 and MApatientID is not null and
		cast(lastActivitydate as date)=cast (getdate() as date)

SELECT '# Items Removed Without Patient selection' As [Name] ,'usp731WebConsoleUnknownPatientsRemovedItemsReport' as SPName,'Items Removed Without Patient selection' as 'LinkToReport', @Style2 as Style
	  ,Count(*) AS [Count]  from s01qryitemhistory I 
		where itemstatusid=2 and MApatientID is null and
		cast(lastActivitydate as date)=cast (getdate() as date)


SELECT '# Items Return' As [Name] ,'usp732WebConsoleReturnedItemsReport' as SPName,'Items Return' as 'LinkToReport', @Style3 as Style
	  ,Count(*) AS [Count]  from s01qryitemhistory I 
		where itemeventid=3 and cast(lastActivitydate as date)=cast (getdate() as date)


SELECT  '# Items In Cabinet' AS [Name],'usp733WebConsoleItemsInCabinetReport' as SPName,'Items In Cabinet' as 'LinkToReport', @Style4 as Style
		,format(COunt(*),'N0') AS [Count]
		from s01tblItems where ItemStatusID =1 








SELECT '# Items Near to Expire' AS [Name],'usp734WebConsoleItemsNearExpirationReport' as SPName,'Items Near to Expire' as 'LinkToReport', @Style5 as Style
	,Count(*) AS [Count]  from s01tblItems I 
	  WHERE I.[Expired Date] <= Dateadd(d,@Days,GETDATE()) 
	  and I.[Expired Date] >=GETDATE()
	  and I.ItemStatusID =1 


	  
SELECT 'Wasted Items' AS [Name],'usp735WebConsoleWastedItemsReport' as SPName,'Wasted Items' as 'LinkToReport', @Style6 as Style
	  ,Count(*) AS [Count]  from s01tblItems I     
	  WHERE I.[ItemStatusID]=5



SELECT 'Expired Items' AS [Name],'usp736WebConsoleExpiredItemsReport' as SPName,'Expired Items' as 'LinkToReport', @Style7 as Style
	, format(COunt(*),'N0') AS [Count]  from s01tblItems I 
	  WHERE I.[Expired Date] <= GETDATE()
	  AND I.ItemStatusID <= 1 



SELECT '# Patients Added' AS [Name],'usp737WebConsoleNewPatientsReport' as SPName,'Patients Added' as 'LinkToReport', @Style8 as Style
		,COunt(*) AS [Count]
		from s01tblpatients where CAST ([Entered Date] AS DATE)>= CAST (Dateadd(d,-30,GETDATE()) AS DATE)


END
GO
/****** Object:  StoredProcedure [dbo].[usp729WebConsoleUpdateUserDetails]    Script Date: 11/21/2024 2:16:28 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE Procedure [dbo].[usp729WebConsoleUpdateUserDetails]
@UserID int,
@EmployeeID varchar(100),
@Username varchar(100),
@FirstName varchar(100),
@MiddleName varchar(100),
@LastName varchar(100),
@Department varchar(100),
@JobTitle varchar(100),
@Suffix varchar(100),
--@UserGroupID varchar(100),
@RFID varchar(100),
--@PIN varchar(100),
@Title varchar(100),
@Company varchar(100),
@Office varchar(100),
@OfficeStreetAddress varchar(500),
@OfficeCity varchar(100),
@OfficeState varchar(100),
@OfficeZip varchar(100),
@OfficeCountry varchar(100),
@HomeStreetAddress varchar(100),
@HomeCity varchar(100),
@HomeState varchar(100),
@HomeZip varchar(100),
@HomeCountry varchar(100),
@HomePhoneNumber varchar(100),
@OfficePhoneNumber varchar(100),
@MobilePhoneNumber varchar(100),
@FaxNumber varchar(100),
@PagerNumber varchar(100),
@Email varchar(100),
@SessionID varchar(100),
@ComputerName varchar(100)
AS
BEGIN
  --   Declare @OldPin varchar(50),@OldBadge varchar(50),@Remarks varchar(max)='Self update';
     
	 --set @OldBadge=(select RFID from tblUsers where UserID=@UserID)
	

 
    update s01tblusers set
    [First Name]=@FirstName,
    [Last Name]=@LastName,
    Username =@Username,
    [Job Title] = @JobTitle,
    Department =@Department,
    [Entered Date] =GETDATE(),
    Suffix = @Suffix,
    [Middle Name] = @MiddleName,
    --GroupID=@UserGroupID,
    RFID=@RFID,
    --PIN=@PIN,
    Title=@Title,
    Company=@Company,
    Office=@Office,
    [Office Street]=@OfficeStreetAddress,
    [Office City]=@OfficeCity,
    [Office State]=@OfficeState,
    [Office ZIP]=@OfficeZip,
    [Office Country]=@OfficeCountry,
    [Home Street]=@HomeStreetAddress,
    [Home City]=@HomeCity,
    [Home State]=@HomeState,
    [Home ZIP]=@HomeZip,
    [Home Country]=@HomeCountry,
    [Home Phone No]=@HomePhoneNumber,
    [Office Phone No]=@OfficePhoneNumber,
    [Mobile Phone No]=@MobilePhoneNumber,
    [Fax No]=@FaxNumber,
    [Pager No]=@PagerNumber,
    Email = @Email,
    [Employee ID] =@EmployeeID
    where 
    UserID = @UserID
	
	
	--if( @OldBadge!=@RFID)
	--begin
	        
	--	insert into s01tblUsersBadgeHistory (UserID,[Updated UserID],[First Name],[Last Name],[User Name],[EntryTypeID],[Previous Badge],[New Badge],
	--		[Override Notes],[SessionID],[UpdatedBy],[Entered Date],[Computer Name],[Domain])
	--	values(@UserID,@UserID,@FirstName,@LastName,@Username,5,@OldBadge,@RFID,'Self Update',@SessionID,@Username,GETUTCDATE(),@ComputerName,'iRISecureBloodWeb')
	--end
END
GO
/****** Object:  StoredProcedure [dbo].[usp737WebConsoleGetUserDetails]    Script Date: 11/21/2024 2:16:28 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

create PROCEDURE [dbo].[usp737WebConsoleGetUserDetails]
@UserID int
AS
BEGIN

	select UserID, CONCAT([First Name],' ',[Last Name]) as Name,
    isnull([Employee ID],'') [Employee ID],
    RFID,
    PIN,
    [First Name],
    [Middle Name],
    [Last Name],
    Username,
    [Job Title] as Designation,
    Department,
    [Entered Date] DateAdded,
	Suffix,
	Title,
	Company,
    GroupID as [User Group],
    UserStatusID as Status,
	Office,
	[Office Street] OfficeStreetAddress,
	[Office City] OfficeCity,
	[Office State] OfficeState,
	[Office ZIP] OfficeZip,
	[Office Country] OfficeCountry,
	[Home Street] HomeStreetAddress,
	[Home City] HomeCity,
	[Home State] HomeState,
	[Home ZIP] HomeZip,
	[Home Country] HomeCountry,
	[Home Phone No] HomePhoneNumber,
	[Office Phone No] OfficePhoneNumber,
	[Mobile Phone No] MobilePhoneNumber,
    [Fax No] FaxNumber,
	[Pager No] PagerNumber,
	Email,
	Domain,
	--Room,
	
	Password,
	Password ConfirmPassword,
	GroupID
	--case when StatusType = 'Not Active' then 'Inactive'
	--else StatusType end [UserStatus]
    from s01tblUsers
    where 
	UserID =@UserID
END
GO
/****** Object:  StoredProcedure [dbo].[usp738WebConsoleUpdateUserDetails]    Script Date: 11/21/2024 2:16:28 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

Create PROCEDURE [dbo].[usp738WebConsoleUpdateUserDetails]
@Domain varchar(100),
@EnteredBy varchar(100),
@ComputerName varchar(100),
@Password varchar(100),
@Email varchar(100),
@UserID int,
@EmployeeID varchar(100),
@Username varchar(100),
@FirstName varchar(100),
@MiddleName varchar(100),
@LastName varchar(100),
@Department varchar(100),
@JobTitle varchar(100),
@Suffix varchar(100),
@UserGroupID varchar(100),
@RFID varchar(100),
@PIN varchar(100),
@Title varchar(100),
@Company varchar(100),
@Office varchar(100),
@OfficeStreetAddress varchar(500),
@OfficeCity varchar(100),
@OfficeState varchar(100),
@OfficeZip varchar(100),
@OfficeCountry varchar(100),
@HomeStreetAddress varchar(100),
@HomeCity varchar(100),
@HomeState varchar(100),
@HomeZip varchar(100),
@HomeCountry varchar(100),
@HomePhoneNumber varchar(100),
@OfficePhoneNumber varchar(100),
@MobilePhoneNumber varchar(100),
@FaxNumber varchar(100),
@PagerNumber varchar(100),
@UserStatus int,
@Room varchar(100)
AS
BEGIN

    if(@PIN='Null')
	begin
	update s01tblUsers set
	[Employee ID] =@EmployeeID,
	[First Name]=@FirstName,
	[Last Name]=@LastName,
	Username =@Username,
	[Job Title] = @JobTitle,
	Department =@Department,
	[Entered Date] =GETDATE(),
	Suffix = @Suffix,
	[Middle Name] = @MiddleName,
	GroupID=@UserGroupID,
	RFID=@RFID,
	--PIN=@PIN,
	Title=@Title,
	Company=@Company,
	Office=@Office,
	[Office Street]=@OfficeStreetAddress,
	[Office City]=@OfficeCity,
	[Office State]=@OfficeState,
	[Office ZIP]=@OfficeZip,
	[Office Country]=@OfficeCountry,
	[Home Street]=@HomeStreetAddress,
	[Home City]=@HomeCity,
	[Home State]=@HomeState,
	[Home ZIP]=@HomeZip,
	[Home Country]=@HomeCountry,
	[Home Phone No]=@HomePhoneNumber,
	[Office Phone No]=@OfficePhoneNumber,
	[Mobile Phone No]=@MobilePhoneNumber,
	[Fax No]=@FaxNumber,
	[Pager No]=@PagerNumber,
	Domain = @Domain,
	[Entered By] = @EnteredBy,
	[Computer Name] = @ComputerName,
	UserStatusID = @UserStatus,
	Email = @Email
	--,Room=@Room
	where UserID =@UserID;
	end
	else
	begin
	update s01tblUsers set
	[Employee ID] =@EmployeeID,
	[First Name]=@FirstName,
	[Last Name]=@LastName,
	Username =@Username,
	[Job Title] = @JobTitle,
	Department =@Department,
	[Entered Date] =GETDATE(),
	Suffix = @Suffix,
	[Middle Name] = @MiddleName,
	GroupID=@UserGroupID,
	RFID=@RFID,
	PIN=@PIN,
	Title=@Title,
	Company=@Company,
	Office=@Office,
	[Office Street]=@OfficeStreetAddress,
	[Office City]=@OfficeCity,
	[Office State]=@OfficeState,
	[Office ZIP]=@OfficeZip,
	[Office Country]=@OfficeCountry,
	[Home Street]=@HomeStreetAddress,
	[Home City]=@HomeCity,
	[Home State]=@HomeState,
	[Home ZIP]=@HomeZip,
	[Home Country]=@HomeCountry,
	[Home Phone No]=@HomePhoneNumber,
	[Office Phone No]=@OfficePhoneNumber,
	[Mobile Phone No]=@MobilePhoneNumber,
	[Fax No]=@FaxNumber,
	[Pager No]=@PagerNumber,
	Domain = @Domain,
	[Entered By] = @EnteredBy,
	[Computer Name] = @ComputerName,
	UserStatusID = @UserStatus,
	Email = @Email
	--,Room=@Room
	where UserID =@UserID;
	end
END
GO
/****** Object:  StoredProcedure [dbo].[usp739WebConsoleAddNewUser]    Script Date: 11/21/2024 2:16:28 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE PROCEDURE [dbo].[usp739WebConsoleAddNewUser]
@Domain varchar(100),
@EnteredBy varchar(100),
@ComputerName varchar(100),
@Password varchar(100),
@Email varchar(100),
@EmployeeID varchar(100),
@Username varchar(100),
@FirstName varchar(100),
@MiddleName varchar(100),
@LastName varchar(100),
@Department varchar(100),
@JobTitle varchar(100),
@Suffix varchar(100),
@UserGroupID varchar(100),
@RFID varchar(100),
@PIN varchar(100),
@Title varchar(100),
@Company varchar(100),
@Office varchar(100),
@OfficeStreetAddress varchar(500),
@OfficeCity varchar(100),
@OfficeState varchar(100),
@OfficeZip varchar(100),
@OfficeCountry varchar(100),
@HomeStreetAddress varchar(100),
@HomeCity varchar(100),
@HomeState varchar(100),
@HomeZip varchar(100),
@HomeCountry varchar(100),
@HomePhoneNumber varchar(100),
@OfficePhoneNumber varchar(100),
@MobilePhoneNumber varchar(100),
@FaxNumber varchar(100),
@PagerNumber varchar(100),
@UserStatus int,
@Room varchar(100)
AS
BEGIN
 
	insert into s01tblUsers 
	(
		[Employee ID],[First Name],[Last Name],Username ,[Job Title],Department,[Entered Date],Suffix ,[Middle Name],GroupID,
		RFID,PIN,Title,Company,Office,[Office Street],[Office City],[Office State],[Office ZIP],[Office Country],[Home Street],[Home City],
		[Home State],[Home ZIP],[Home Country],[Home Phone No],[Office Phone No],[Mobile Phone No],[Fax No],[Pager No],Email,Password,Domain
		,[Entered By],[Computer Name],UserStatusID--,Room
		,rowguid
	)
	values
	(
		@EmployeeID,@FirstName,@LastName,@Username,@JobTitle,@Department,GETDATE(),@Suffix,@MiddleName,@UserGroupID,
		@RFID,@PIN,@Title,@Company,@Office,@OfficeStreetAddress,@OfficeCity,@OfficeState,@OfficeZip,@OfficeCountry,@HomeStreetAddress,@HomeCity,
		@HomeState,@HomeZip,@HomeCountry,@HomePhoneNumber,@OfficePhoneNumber,@MobilePhoneNumber,@FaxNumber,@PagerNumber,@Email,@Password,@Domain
		,@EnteredBy,@ComputerName,@UserStatus--,@Room
		,newid()
	)
	select SCOPE_IDENTITY ()
END
GO
/****** Object:  StoredProcedure [dbo].[usp742WebConsoleProcessProcessRFIDItems]    Script Date: 11/21/2024 2:16:28 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE PROCEDURE [dbo].[usp742WebConsoleProcessProcessRFIDItems]
	@Event VARCHAR(200)
	, @udtSystemData udtSPParam READONLY
	, @udtEventData udtSPParam READONLY
	--, @udtTransactedItems udtTransactedItems READONLY
	--, @udtTagsData udtTags READONLY
AS
	SET NOCOUNT ON;

	DECLARE @UserID INT, @EntryTypeID INT, @SessionID BIGINT, @DeviceID INT, @BillingStatusID INT, @OverrideID INT, @OverrideNotesID INT, @OverrideNotes VARCHAR(255)
	DECLARE @ItemEventID INT, @UsageTypeID INT, @ItemStatusID INT, @MAPatientID VARCHAR(50), @MAScheduleID INT, @MAVisitID INT, @AlertMessage VARCHAR(512)
	DECLARE @CurrentUtcTime DATETIME, @OverrideDescription VARCHAR(50), @OverrideDescNotes VARCHAR(50), @UageSummarySettings VARCHAR(250), @AlertID INT, @DBID INT
	DECLARE @ClientTimeZone VARCHAR(500), @UTCStandard VARCHAR(100), @SecondDiff INT, @ShowUnAssociateItems VARCHAR(50), @PhysicianID VARCHAR(50)
	DECLARE @MAPhysicianID INT, @EnableEpicInterface VARCHAR(50), @SpuriousLogAlertEnabled BIT, @EXPDATE_CHECK VARCHAR(10), @CLOSEEXP_CHECK VARCHAR(10)
	DECLARE @MAX_RMV_PAT_ITEMS INT, @MAX_RMV_PAT_ITEMS_AlertEnabled BIT, @AntenaStatus VARCHAR(500), @AntennaFailure_AlertEnabled INT
	DECLARE @RFID VARCHAR(50),@ItemID INT, @CompartmentID INT,@Domain VARCHAR(100),@ItemUsageID INT
	DECLARE @udt701WebConsoleTransactedItemDetails udt701WebConsoleTransactedItemDetails 

		--SET XACT_ABORT ON;
		BEGIN TRY
		--BEGIN TRANSACTION

				
			
		SELECT @DeviceID = Value FROM @udtSystemData WHERE Property = 'DeviceID'
		
		SELECT @EntryTypeID = Value FROM @udtSystemData WHERE Property = 'EntryTypeID'
		
		SELECT @SessionID = Value FROM @udtEventData WHERE Property = 'SessionID'
		
		SELECT @UserID = Value FROM @udtEventData WHERE Property = 'UserID'
		SELECT @MAPatientID = Value FROM @udtEventData WHERE Property = 'MAPatientID'
		SELECT @OverrideDescription = Value FROM @udtEventData WHERE Property = 'OverrideDesc'
		SELECT @Domain = Value FROM @udtEventData WHERE Property = 'Domain'

		
		--SELECT @OverrideDescNotes = Value FROM @udtEventData WHERE Property = 'EventInput2'
		SELECT @RFID = Value FROM @udtEventData WHERE Property = 'RFID'
		SELECT @ItemID =(select ItemID from s01tblItems where RFID=@RFID)
		set @CompartmentID = (select CompartmentID from s01tblItems where ItemID=@ItemID)
			
		SELECT @UsageTypeID = UsageTypeID 
		FROM s01tblUsageType 
		WHERE UsageType = 'RF ITEM'

		--SET @EnableEpicInterface =  [dbo].[udf700WebConsoleSUPFetchSettings] (@DeviceId,'ENABLE_EPIC_INTERFACE') 
		
		SELECT @OverrideID = OverrideID 
		FROM s01tblOverride 
		WHERE OverrideDesc = CASE 
								WHEN @OverrideDescription = 'Waste Item' THEN 'Waste'   
								WHEN @OverrideDescription = 'Override' THEN 'Manual Override'
								WHEN @MAPatientID IS NOT NULL THEN 'Default'
								WHEN @Event = 'InitialScan' THEN 'Initial Scan'
								WHEN @Event = 'KeyAccessScan' THEN 'Key Access Scan'
								WHEN @OverrideDescription IN ('Take','Waste','Return') THEN 'Default' 
								ELSE @OverrideDescription 
							 END		
	
		SELECT @UageSummarySettings = [dbo].[udf700WebConsoleSUPFetchSettings] ( @DeviceId,'SHOW_USAGE_SUMMARY' ) 

		SELECT @ClientTimeZone = [dbo].[udf700WebConsoleSUPFetchSettings] ( @DeviceId,'CLIENT_TIMEZONE' )
		
		SELECT @UTCStandard = UTCStandard 
		FROM MA_SUP_TJUHOR_IRISUPPLYDB..tblStandardTimeZone 
		WHERE StandardTimeZone = @ClientTimeZone

		

		SELECT @SecondDiff =  REPLACE(@UTCStandard, 'UTC', '')
		 
		IF (SELECT [Setting Value] FROM s01tblSettingsCommon WHERE [Setting Name] = 'ENABLE_UTC_TIMEZONE') = 'TRUE'
		BEGIN
			SELECT @CurrentUTCTime = GETUTCDATE()
		END
		ELSE
		BEGIN
			SELECT @CurrentUTCTime = GETDATE()
		END
						
		--IF ( SELECT COUNT(*) FROM @udtTransactedItems WHERE TagType = 'FoundTag' ) > 0
		--BEGIN
			
		if (@Event='ReturnItem')
		begin
	IF EXISTS(Select TOP 1 * from s01tblitems where  RFID=@RFID and ItemStatusID=1 ) 
            BEGIN

            Select 'Error' as datasetname,'Item already stocked in cabinet.'  as msg
            END
			else if not exists( Select TOP 1 * from s01tblitems where  RFID=@RFID)
			begin
			Select 'Error' as datasetname,'RFID not associated to any item.'  as msg
			end
			else
			begin
			SELECT @ItemStatusID = ItemStatusID 
			FROM s01tblItemStatus 
			WHERE [Item Status] = 'In Cabinet'

			SELECT @ItemEventID = CASE 
									  WHEN @Event = 'ReturnItem' 
										THEN 3
                                      ELSE 1
								  END

			--DELETE FROM s01tblItemUsage where UsageItemID=@ItemID; --UPDATED BY VIRESH ON 11/11/2024
			--WHERE UsageTypeID = 1 AND UsageItemID IN ( SELECT I.ItemID 
			--											FROM s01tblItems I INNER JOIN @udt701WebConsoleTransactedItemDetails TI ON TI.RFID = I.RFID 
			--											WHERE TagType = 'FoundTag' )
			DELETE FROM s01tblItemUsage
			WHERE UsageItemID = @ItemID
			AND LastActivityDate = (
				SELECT MAX(LastActivityDate)
				FROM s01tblItemUsage
				WHERE UsageItemID = @ItemID
				); -- Deleting Records from s01tblItemUsage, becouse we are returning Items from HUB ,
				    --means not actully returning , only removing patient records from ItemUsage.
					--not Updating ItemUsage Record, instead deleting old usage and Inserting new.

			SELECT @ItemStatusID = ItemStatusID 
			FROM s01tblItemStatus 
			WHERE [Item Status] = 'Removed'
			
			UPDATE s01tblItems 
			SET ItemStatusID = @ItemStatusID,  [AddUserID] = @UserID -- changed from ItemStatusID = @ItemStatusID to ItemStatusID = 2 by viresh on 11/11/2024
				, [Date Added] = @CurrentUtcTime, EntryTypeID = @EntryTypeID, SupplierID = 0, LastActivityDate = @CurrentUtcTime
				, MAPatientID = NULL, MAScheduleID = NULL, MAPhysicianID = NULL 
				, Qty = 1 
			FROM s01tblItems I where RFID=@RFID;
			--	INNER JOIN @udt701WebConsoleTransactedItemDetails TI ON TI.RFID = I.RFID
			--WHERE TagType = 'FoundTag'

			--UPDATED BY VIRESH ON 11/11/2024 
			--BEGIN
			INSERT INTO s01tblItemUsage (UsedBy,DateUsed,Qty,UnitPrice,BillingStatusID,OverrideID,OverrideNotes,UpdatedBy,LastActivityDate,UsageTypeID,UsageItemID,SessionID,UCDM,EntryTypeID,rowguid)
			SELECT @UserID,@CurrentUtcTime,ISNULL(I.Qty,1),I.[Act Price],@BillingStatusID,@OverrideID, @OverrideNotes,@UserID,@CurrentUtcTime,@UsageTypeID,I.ItemID,@SessionID,I.ICDM,@EntryTypeID,NEWID()
			FROM s01qryItems I where RFID=@RFID;

			--END
			SELECT TOP 1 @ItemUsageID = ItemUsageID
			FROM s01tblItemUsage
			ORDER BY LastActivityDate DESC;
			--Performing Return and Remove Work folow , when item is removed from a HUB Application 
			--Updated By viresh on 11/18/2024
			INSERT INTO s01tblItemHistory ( ItemUsageID,ItemStatusID, UsageTypeID, UsageItemID, MAPatientID, MAScheduleID, MAVisitID, BillingStatusID, Qty, UnitPrice --UOMCode
							,  UpdatedBy,UsedBy, UCDM, LastActivityDate, EntryTypeID, OverrideID, OverrideNotes, SessionID, ItemEventID )
			SELECT @ItemUsageID,1, @UsageTypeID, I.ItemID, I.MAPatientID INT, I.MAScheduleID, I.MAVisitID, @BillingStatusID, ISNULL(I.Qty,1), I.[Act Price]-- TI.UOMCode
							, @UserID,@UserID, I.ICDM, @CurrentUtcTime, @EntryTypeID, @OverrideID, @OverrideNotes, @SessionID, @ItemEventID 
			FROM s01qryItems I where RFID=@RFID;

			INSERT INTO s01tblItemHistory ( ItemUsageID,ItemStatusID, UsageTypeID, UsageItemID, MAPatientID, MAScheduleID, MAVisitID, BillingStatusID, Qty, UnitPrice --UOMCode
							,  UpdatedBy,UsedBy, UCDM, LastActivityDate, EntryTypeID, OverrideID, OverrideNotes, SessionID, ItemEventID )
			SELECT @ItemUsageID,2, @UsageTypeID, I.ItemID, I.MAPatientID INT, I.MAScheduleID, I.MAVisitID, @BillingStatusID, ISNULL(I.Qty,1), I.[Act Price]-- TI.UOMCode
							, @UserID,@UserID, I.ICDM, @CurrentUtcTime, @EntryTypeID, @OverrideID, @OverrideNotes, @SessionID, @ItemEventID 
			FROM s01qryItems I where RFID=@RFID;
			--INNER JOIN @udt701WebConsoleTransactedItemDetails TI ON TI.RFID = I.RFID AND TagType = 'FoundTag'

			INSERT INTO s01tblitemhistoryextn (ItemHistoryID, LocationID)
			SELECT ItemHistoryID, @CompartmentID 
			--FROM @udt701WebConsoleTransactedItemDetails T
			from s01tblItemHistory H  --H.UsageItemID = T.ItemID 
			where H.LastActivityDate = @CurrentUtcTime
			AND H.UsageTypeID=1
			--INSERT INTO s01tblitemhistoryextn (ItemHistoryID, LocationID)
			--SELECT ItemHistoryID, T.LocationID 
			--FROM @udt701WebConsoleTransactedItemDetails T
			--	INNER JOIN [IrisDB].s01tblItemHistory H ON H.UsageItemID = T.ItemID 
			--	AND H.LastActivityDate = @CurrentUtcTime 
			--	AND H.UsageTypeID=1
			--	AND T.TagType = 'FoundTag'
				--EXECUTE  usp812WebConsoleLoadUsageList @Event = @Event
				--	, @udtSystemData = @udtSystemData
				--	, @udtEventData = @udtEventData
				select [Serial No],[Lot No],P.[Cat No],p.[Description 1],I.RFID from s01tblItems I
				inner join s01tblProducts P on P.ProductID=I.ProductID
				where I.RFID=@RFID
			
			--IF(@EnableEpicInterface = 'TRUE') added by viresh on 11/11/2024
			IF (1=1) --setting value
			BEGIN
				IF ( @RFID IS NOT NULL AND @RFID <> '' ) 
				BEGIN
					--Epic Transaction
					EXECUTE  uspSWCSendUsageToEpic @Event = @Event
							, @udtSystemData = @udtSystemData
							, @udtEventData = @udtEventData
							, @ItemEventID = @ItemEventID
							, @ItemStatusID = @ItemStatusID
							, @UsageTypeID = @UsageTypeID
							, @ItemID = @ItemID
				END				
			END
		end
		end
		else
		begin
			
		SELECT @MAVisitID = Value FROM @udtEventData WHERE Property = 'MAVisitID' 
		SELECT @MAScheduleID = Value FROM @udtEventData WHERE Property = 'MAScheduleID' 
		SELECT @PhysicianID = PhysicianID FROM s01qryPatientDisplayADT WHERE MAPatientID = @MAPatientID 
		SELECT @MAPhysicianID = MAPhysicianID FROM s01tblPhysicians WHERE PhysicianID = @PhysicianID

			SELECT @ItemStatusID = ItemStatusID 
			FROM s01tblItemStatus 
			WHERE [Item Status] = CASE 
									WHEN @OverrideDescription IN ( 'Waste Item','Waste') THEN 'Waste' 
									WHEN @OverrideDescription = 'Transfer Item' THEN 'Transfered'
									WHEN @OverrideDescription = 'Swap Item' THEN 'Swap'
									WHEN @OverrideDescription = 'Render To External Hospital' THEN 'Render to External Hospital'   
									WHEN @OverrideDescription = 'Remove Item' THEN 'Used'
									ELSE 'Removed' 
									END

			SELECT @ItemEventID = ItemEventID 
			FROM s01tblItemEvent 
			WHERE EventDescription = CASE 
											WHEN @OverrideDescription IN ('Waste Item','Waste') THEN 'Item has been wasted' 
											WHEN @OverrideDescription = 'Transfer Item' THEN 'Item has been transfered'
											ELSE 'Item taken out of the cabinet' 
										END
			
			--IF EXISTS(Select TOP 1 * from tblitems where  RFID=@RFID and ItemStatusID=2 )  viresh commented these code
			IF EXISTS(Select TOP 1 * from s01tblitems where  RFID=@RFID) 
            BEGIN
				EXECUTE usp823WebConsoleProcessMaxRmvPatItems @Event = @Event
						, @udtSystemData = @udtSystemData
						, @udtEventData = @udtEventData
						--, @udt701WebConsoleTransactedItemDetails = @udt701WebConsoleTransactedItemDetails
						, @ItemStatusID = @ItemStatusID
						, @ItemEventID = @ItemEventID
						, @CurrentUtcTime = @CurrentUtcTime
						, @UsageTypeID = @UsageTypeID
					
							EXECUTE  usp812WebConsoleLoadUsageList @Event = @Event
							, @udtSystemData = @udtSystemData
							, @udtEventData = @udtEventData
            END
			else if not exists( Select TOP 1 * from s01tblitems where  RFID=@RFID)
			begin
			Select 'Error' as datasetname,'RFID not associated to any item.'  as msg
			end
			else 
            


            begin


				EXECUTE usp823WebConsoleProcessMaxRmvPatItems @Event = @Event
						, @udtSystemData = @udtSystemData
						, @udtEventData = @udtEventData
						--, @udt701WebConsoleTransactedItemDetails = @udt701WebConsoleTransactedItemDetails
						, @ItemStatusID = @ItemStatusID
						, @ItemEventID = @ItemEventID
						, @CurrentUtcTime = @CurrentUtcTime
						, @UsageTypeID = @UsageTypeID
					
							EXECUTE  usp812WebConsoleLoadUsageList @Event = @Event
							, @udtSystemData = @udtSystemData
							, @udtEventData = @udtEventData
			end
			
			
			end
	END TRY
		BEGIN CATCH
			--IF (XACT_STATE()) <> 0
				--ROLLBACK TRANSACTION;
			EXECUTE s01uspLogError;
		END CATCH;
	SET NOCOUNT OFF




GO
/****** Object:  StoredProcedure [dbo].[usp750WebConsoleGetUserFavorites]    Script Date: 11/21/2024 2:16:28 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE Procedure [dbo].[usp750WebConsoleGetUserFavorites]
@UserID int
as
begin
     select Bookmark as BookMark, BookmarkURL as Link from tbluserbookmarks where UserID=@UserID;
end
GO
/****** Object:  StoredProcedure [dbo].[usp751WebConsoleGetSessionTimeOut]    Script Date: 11/21/2024 2:16:28 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE Procedure [dbo].[usp751WebConsoleGetSessionTimeOut]
@UserGroupId int 
as
begin
    if(@UserGroupId in (0,1))
	
	select [Setting Value] from s01tblSettingsCommon where [Setting Name]='TIME_OUT_ADM';

	else 
	select [Setting Value] from s01tblSettingsCommon where [Setting Name]='TIME_OUT';
end
GO
/****** Object:  StoredProcedure [dbo].[usp752WebConsoleUpdateFavorites]    Script Date: 11/21/2024 2:16:28 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE Procedure [dbo].[usp752WebConsoleUpdateFavorites] 
@UserID int,
@Bookmark varchar(50),
@Status varchar(50)
as 
begin
declare @BookmarkLink varchar(50);
   if exists (select Module_Name from tblModules where Module_name=@Bookmark)
   begin
   set @BookmarkLink=(select Link from tblmodules where Module_name=@Bookmark)
       if exists(select Bookmark from tbluserbookmarks where UserID=@UserID and Bookmark=@Bookmark)
	   begin
	         if(@Status='1')
			 begin
				 delete from tbluserbookmarks where UserID=@UserID and Bookmark=@Bookmark;
				 select 'Bookmark removed'
			 end
			 else
			 select'Failed to add bookmark'
	   end
	   else
	   begin
			if(@Status='0')
			 begin
				 insert into tbluserbookmarks values(@UserID,@Bookmark,@BookmarkLink,GETUTCDATE());
				 select'Bookmark added'
			 end
			 else 
			 begin
			     select 'Failed to remove bookmark'
			 end
	   end
   end
   else
      select'Failed to add bookmark'
end
GO
/****** Object:  StoredProcedure [dbo].[usp753WebConsoleGetUnpairedDevices]    Script Date: 11/21/2024 2:16:28 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

create procedure [dbo].[usp753WebConsoleGetUnpairedDevices]
as
begin
select count(*) from tblDeviceRegistration where Status='Inactive' or status is null;
end
GO
/****** Object:  StoredProcedure [dbo].[usp754WebConsoleGetmenuitems]    Script Date: 11/21/2024 2:16:28 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [dbo].[usp754WebConsoleGetmenuitems]
@UserGroupID int,
@UserID int
AS
BEGIN

IF (@UserGroupID <> 0) 
Begin
	select MODULE_ID,MODULE_NAME,DESCRIPTION,LINK,PARENT_MODULE_ID,MODULE_ICON
	,CASE WHEN bm.Bookmark IS NULL or bm.bookmark='' THEN 0 ELSE 1 END AS bookmarkstatus
	from 
	tblModules tMod
	left join tblUserRBAC u on tmod.MODULE_ID = u.ModuleID
	left join tbluserbookmarks bm on tMod.MODULE_NAME = bm.Bookmark and bm.UserID=@UserID
	where IsENABLED=1 and u.UserGroupID = @UserGroupID and u.Status = 1
	order by PARENT_MODULE_ID, ORDER_ID
	
END
Else
Begin
	select MODULE_ID,MODULE_NAME,DESCRIPTION,LINK,PARENT_MODULE_ID,MODULE_ICON
	,CASE WHEN bm.Bookmark IS NULL or bm.bookmark='' THEN 0 ELSE 1 END AS bookmarkstatus
	from 
	tblModules tMod
	left join tbluserbookmarks bm on tMod.MODULE_NAME = bm.Bookmark and bm.UserID=@UserID
	where IsENABLED=1
	order by PARENT_MODULE_ID, ORDER_ID
end
END
GO
/****** Object:  StoredProcedure [dbo].[usp755WebConsoleGetUserRecentVisitedHistory]    Script Date: 11/21/2024 2:16:28 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE procedure [dbo].[usp755WebConsoleGetUserRecentVisitedHistory]
@UserID int
as
begin
     select distinct top(3) ModuleName,ModuleLink,LastActivityDate 
	 from tblusersrecenthistory
	 where UserID=@UserID
	 Group by ModuleName,ModuleLink,LastActivityDate
	  order by LastActivityDate desc    
end
GO
/****** Object:  StoredProcedure [dbo].[usp800WebConsoleUserLogin]    Script Date: 11/21/2024 2:16:28 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [dbo].[usp800WebConsoleUserLogin]
    @Event VARCHAR(255)
    , @udtSystemData AS udtSPParam READONLY
    , @udtEventData AS udtSPParam READONLY

AS 
    --SET NOCOUNT ON;
        DECLARE @User VARCHAR(50), @Pwd VARCHAR(50), @UserID INT, @UserName VARCHAR(100), @SessionID BIGINT, @SPName VARCHAR(100), @UserStatus INT, @Msg VARCHAR(255)
        DECLARE @EventType VARCHAR(50), @IsCardAssociated BIT, @EntryTypeID INT, @DeviceID INT, @ComputerName VARCHAR(100), @ClusterName VARCHAR(100), @DataSetName VARCHAR(50)
        DECLARE @PIN VARCHAR(50), @RFID VARCHAR(100), @Password VARCHAR(500), @ClusterID INT, @Domain VARCHAR(50), @GroupID INT
        DECLARE @CurrentUtcTime DATETIME, @DataSetName1 VARCHAR(50), @Golivedate DATETIME, @CheckCompartmentID INT

        --SET XACT_ABORT ON;
        BEGIN --TRY
        --BEGIN TRANSACTION

        SELECT @DeviceID = Value FROM @udtSystemData WHERE Property = 'DeviceID'
        SELECT @EntryTypeID = Value FROM @udtSystemData WHERE Property = 'EntryTypeID'
        SELECT @ComputerName = Value FROM @udtSystemData WHERE Property = 'ComputerName'
        SELECT @Domain = Value FROM @udtSystemData WHERE Property = 'Domain'
        SELECT @EventType = Value FROM @udtEventData WHERE Property = 'EventType'
        --SELECT @DataSetName = Value FROM @udtEventData WHERE Property = 'EventOutPut'
        --SELECT @DataSetName1 = Value FROM @udtEventData WHERE Property = 'EventOutPut1'
        SELECT @User = Value FROM @udtEventData WHERE Property = 'User'
        SELECT @Pwd = Value FROM @udtEventData WHERE Property = 'Pwd'

        --IF ( SELECT COUNT(*) FROM dbo.s01qrycabinets WHERE ClusterID = @DeviceID GROUP BY ClusterID ) = 1
        --BEGIN
        --  SELECT @CheckCompartmentID = CompartmentID 
        --  FROM dbo.s01qrycabinets 
        --  WHERE ClusterID = @DeviceID AND CompartmentID IS NULL OR CompartmentID = ''
        --END

        IF (SELECT [Setting Value] FROM MA_SUP_TJUHOR_IRISUPPLYDB..tblSupplySettings WHERE [Setting Name] = 'ENABLE_UTC_TIMEZONE') = 'TRUE'
        BEGIN
            SELECT @CurrentUTCTime = GETUTCDATE()
            SELECT @SessionID = REPLACE( REPLACE( REPLACE( REPLACE( 
                                CONVERT( VARCHAR(30), @CurrentUTCTime, 121 ), ':', '' ), '-', '' ), ' ', '' ) , '.', '' )
        END
        ELSE
        BEGIN
            SELECT @CurrentUTCTime = GETDATE()
            SELECT @SessionID = REPLACE( REPLACE( REPLACE( REPLACE( 
                                CONVERT( VARCHAR(30), @CurrentUTCTime, 121 ), ':', '' ), '-', '' ), ' ', '' ) , '.', '' )
        END
        
        --SELECT @Golivedate = MA_SUP_TJUHOR_IRISUPPLYDB..[udfSUPFetchSettings] ( @DeviceId,'GoLiveDate' ) -- VIRESH

        --IF( @EventType = 'PIN' AND @Pwd IS NOT NULL AND ISNULL( @DeviceID, 0 ) <> 0 )
        --BEGIN
        --  --Checking for Authorization
        --  IF EXISTS (SELECT 1 FROM s01qrySUPUsersValidate WHERE PIN = @Pwd AND ClusterID = @DeviceID)
        --  BEGIN
        --      SELECT TOP 1 @DataSetName AS DataSetName, UserID, CONCAT([First Name], ' ', [Last Name]) AS [User Name], [User Group], 1 AS CabinetAccess, @SessionID AS SessionID 
        --      FROM s01qrySUPUsersValidate 
        --      WHERE PIN = @Pwd 
        --          AND ClusterID = @DeviceID

        --      --EXECUTE uspUserAppAccessRBAC @Event = @Event 
        --      --      , @udtSystemData = @udtSystemData
        --      --      , @udtEventData = @udtEventData

        --      --EXECUTE  uspLoadExpireItems @Event = @Event
        --      --  , @udtSystemData = @udtSystemData
        --      --  , @udtEventData = @udtEventData
        --  END
        --  --SR added condition for Console application having single comp id = Null (Login for Authenticated user)
        --  ELSE IF EXISTS (SELECT 1 FROM dbo.s01tblUsers WHERE PIN = @Pwd AND UserStatusID = 1 AND @CheckCompartmentID IS NULL )
        --  BEGIN
        --       SELECT TOP 1 @DataSetName AS DataSetName, UserID, CONCAT([First Name], ' ', [Last Name]) AS [User Name], G.[User Group], 1 AS CabinetAccess, @SessionID AS SessionID 
        --       FROM dbo.s01tblUsers U 
        --          LEFT OUTER JOIN dbo.s01tblUserGroup G ON U.GroupID = G.GroupID
        --       WHERE PIN = @Pwd AND UserStatusID = 1 

        --      EXECUTE uspUserAppAccessRBAC @Event = @Event 
        --              , @udtSystemData = @udtSystemData
        --              , @udtEventData = @udtEventData

        --      EXECUTE  uspLoadExpireItems @Event = @Event
        --          , @udtSystemData = @udtSystemData
        --          , @udtEventData = @udtEventData
        --  END
        --  ELSE
        --      SELECT 'Alert' AS DatasetName, 'Invalid PIN' AS MSG

        --END

        IF( @EventType = 'HID' AND @Pwd IS NOT NULL --AND ISNULL( @DeviceID, 0 ) <> 0 
		)
        BEGIN
            IF EXISTS (SELECT 1 FROM MA_SUP_TJUHOR_IRISUPPLYDB..qrySUPUsersValidate WHERE RFID = @Pwd AND ClusterID = @DeviceID)
            BEGIN
                SELECT TOP 1  UserID, CONCAT([First Name], ' ', [Last Name]) AS [Name],Username,[Employee ID],Department, GroupID as UserGroupID, 1 AS Status,
                @SessionID AS SessionID 
                FROM qry700WebConsoleSUPUsersValidate 
                WHERE RFID = @Pwd 
                   -- AND ClusterID = @DeviceID

                --EXECUTE uspUserAppAccessRBAC @Event = @Event 
                --  , @udtSystemData = @udtSystemData
                --  , @udtEventData = @udtEventData

                --EXECUTE  uspLoadExpireItems @Event = @Event
                --  , @udtSystemData = @udtSystemData
                --  , @udtEventData = @udtEventData
                  
            END
            --SR added condition for Console application having single comp id = Null (Login for Authenticated user)
            ELSE IF EXISTS (SELECT 1 FROM s01tblUsers WHERE RFID = @Pwd AND UserStatusID = 1 AND @CheckCompartmentID IS NULL )
            BEGIN
                 SELECT TOP 1 @DataSetName AS DataSetName, UserID, CONCAT([First Name], ' ', [Last Name]) AS [Name],Username,[Employee ID],Department, U.[GroupID] as UserGroupID, 1 AS Status,
                @SessionID AS SessionID
                 FROM s01tblUsers U 
                    LEFT OUTER JOIN s01tblUserGroup G ON U.GroupID = G.GroupID
                 WHERE RFID = @Pwd AND UserStatusID = 1 

                --EXECUTE uspUserAppAccessRBAC @Event = @Event 
                --        , @udtSystemData = @udtSystemData
                --        , @udtEventData = @udtEventData

                --EXECUTE  uspLoadExpireItems @Event = @Event
                --    , @udtSystemData = @udtSystemData
                --    , @udtEventData = @udtEventData
            END
            ELSE IF EXISTS ( SELECT 1 FROM s01qryUsers U INNER JOIN s01tblCompartmentAccess C ON U.UserID = C.UserID  
                        WHERE RFID =  @Pwd AND ClusterID = @DeviceID AND UserStatusID = 0 AND [Entered By] = 'Self Registered' AND getdate() < @Golivedate)

                SELECT  'AlertForGoLive' AS DatasetName, 'Login is not allowed till Go Live Date for self registered users' AS MSG
                    
            ELSE
                SELECT 'Alert' AS DatasetName, 'Invalid Badge' AS MSG
        END

        IF( @EventType = 'User' AND @User IS NOT NULL AND @Pwd IS NOT NULL --AND ISNULL( @DeviceID, 0 ) <> 0 
		)
        BEGIN
            SELECT TOP 1  UserID, CONCAT([First Name], ' ', [Last Name]) AS [Name],Username,[Employee ID],Department, GroupID as UserGroupID, 1 AS Status
                ,@SessionID AS SessionID
				--,[2FactorAuthStatus]
            FROM qry700WebConsoleSUPUsersValidate 
            WHERE [UserName] = @User 
                AND Password = @Pwd 
               -- AND ClusterID = @DeviceID

            --EXECUTE uspUserAppAccessRBAC @Event = @Event 
            --      , @udtSystemData = @udtSystemData
            --      , @udtEventData = @udtEventData

            --EXECUTE  uspLoadExpireItems @Event = @Event
            --      , @udtSystemData = @udtSystemData
            --      , @udtEventData = @udtEventData
        END
    
        IF ( ISNULL( @DeviceID, 0 ) = 0 )
        BEGIN
            IF ( @EventType = 'PIN' AND @Pwd IS NOT NULL )
            BEGIN
                SELECT @UserID = UserID, @PIN = PIN, @UserName = [User Name], @RFID = PIN 
                FROM s01qryUsers 
                WHERE PIN = @Pwd 
                    AND UserStatusID = 1 
                    AND GroupID IN ( 0, 1 )

                IF EXISTS ( SELECT 1 FROM s01qryUsers WHERE PIN = @Pwd AND UserStatusID = 1 AND GroupID IN ( 0, 1 ) )
                BEGIN
                        
                    --EXECUTE  uspAppCabEventLogs @Event = @Event
                    --              , @udtSystemData = @udtSystemData
                    --              , @udtEventData = @udtEventData
                    --              , @Logs =  'UtilityApp - Using PIN - Login Successfull'

                    SELECT TOP 1 @DataSetName AS DataSetName, UserID, CONCAT([First Name], ' ', [Last Name]) AS [User Name], [User Group], 1 AS CabinetAccess, @SessionID AS SessionID 
                    FROM s01qryUsers 
                    WHERE PIN = @Pwd 
                        AND UserStatusID = 1 
                        AND GroupID IN ( 0, 1 ) 
                END
                --ELSE IF NOT EXISTS ( SELECT 1 FROM dbo.s01qryUsers WHERE PIN = @Pwd AND UserStatusID = 1 AND GroupID IN ( 0, 1 ) )
                --BEGIN

                --  --EXECUTE  uspAppCabEventLogs @Event = @Event
                --  --              , @udtSystemData = @udtSystemData
                --  --              , @udtEventData = @udtEventData
                --  --              , @Logs =  'UtilityApp - Using PIN - Invalid PIN'

                --END
            END
        END 

        IF ( @EventType = 'User' AND  @User IS NOT NULL AND @Pwd IS NOT NULL AND ISNULL( @DeviceID, 0 ) <> 0)
        BEGIN
            SET @UserID = NULL

            SELECT @UserID = UserID, @PIN = PIN, @RFID = RFID , @UserName = [User Name], @Password = Password--, @ClusterID = [ClusterID] 
            FROM qry700WebConsoleSUPUsersValidate  
            WHERE [UserName] = @User 
                AND Password = @Pwd 
                --AND ClusterID = @DeviceID

            SELECT  @ClusterName = [Cluster Name] 
            FROM  s01qryCabinets 
            WHERE ClusterID = @DeviceID

            --IF EXISTS ( SELECT 1 FROM s01qrySUPUsersValidate WHERE [User Name] = @UserName AND Password = @Pwd ) 
          --  BEGIN

          --      EXECUTE  uspAppCabEventLogs @Event = @Event
          --                      , @udtSystemData = @udtSystemData
          --                      , @udtEventData = @udtEventData
          --                      , @Logs =  'Using Name - Login Successfull'
								  --, @UserSessionID =  @SessionID

          --  END 

            IF ( (@UserName IS NULL OR @UserName = '') AND ( @Password IS NULL OR @PassWord = '') AND ISNULL( @DeviceID, 0 ) <> 0 )
            BEGIN

                EXECUTE  usp705WebConsoleAppCabEventLogs @Event = @Event
                            , @udtSystemData = @udtSystemData
                            , @udtEventData = @udtEventData
                            , @Logs =  'Using Name - Invalid User/Password'

            END
                    
        END

        IF ( @EventType = 'HID' AND ISNULL( @DeviceID, 0 ) <> 0 )
        BEGIN
            SELECT @UserID = UserID, @RFID = RFID , @UserName = [User Name], @Password = Password--, @ClusterID = [ClusterID] 
            FROM qry700WebConsoleSUPUsersValidate  
            WHERE RFID = @Pwd 
               -- AND ClusterID = @DeviceID

            SELECT  @ClusterName = [Cluster Name] 
            FROM  s01qryCabinets 
            WHERE ClusterID = @DeviceID

            IF EXISTS ( SELECT 1 FROM qry700WebConsoleSUPUsersValidate WHERE RFID = @Pwd )
            BEGIN

                EXECUTE  usp704WebConsoleProcessUserLogs @Event = @Event
                                    , @udtSystemData = @udtSystemData
                                    , @udtEventData = @udtEventData
                                    , @UserSessionID =  @SessionID

        --        EXECUTE  uspAppCabEventLogs @Event = @Event
        --                    , @udtSystemData = @udtSystemData
        --                    , @udtEventData = @udtEventData
        --                    , @Logs =  'Using HID - Login Successfull'
							 --, @UserSessionID =  @SessionID

            END 

            IF NOT EXISTS ( SELECT 1 FROM s01qry700WebConsoleSUPUsersValidate WHERE RFID = @Pwd )
            BEGIN

                EXECUTE  usp705WebConsoleAppCabEventLogs @Event = @Event
                        , @udtSystemData = @udtSystemData
                        , @udtEventData = @udtEventData
                        , @Logs =  'Using HID - Invalid HID Card'

            END                 
        END

		 IF( @EventType = 'AD' AND @User IS NOT NULL )
        BEGIN
            SELECT TOP 1  UserID, CONCAT([First Name], ' ', [Last Name]) AS [Name],Username,[Employee ID],Department, GroupID as UserGroupID, 1 AS Status,--2FactorAuthStatus,
                @SessionID AS SessionID
           FROM qry700WebConsoleSUPUsersValidate         
            WHERE [UserName] = @User 
                --AND Password = @Pwd 
               -- AND ClusterID = @DeviceID

            --EXECUTE uspUserAppAccessRBAC @Event = @Event 
            --      , @udtSystemData = @udtSystemData
            --      , @udtEventData = @udtEventData

            --EXECUTE  uspLoadExpireItems @Event = @Event
            --      , @udtSystemData = @udtSystemData
            --      , @udtEventData = @udtEventData
        END

        --COMMIT TRANSACTION;

        END--TRY
        --BEGIN CATCH
        --    --IF (XACT_STATE()) <> 0
        --        --ROLLBACK TRANSACTION;
        --        SET @Msg = 'ERROR VALIDATING LOGIN';
        --    --EXECUTE uspLogError;
        --END CATCH;
    --SET NOCOUNT OFF
GO
/****** Object:  StoredProcedure [dbo].[usp801WebConsoleUserLogout]    Script Date: 11/21/2024 2:16:28 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE Procedure [dbo].[usp801WebConsoleUserLogout]
	@Event VARCHAR(255)
	, @udtSystemData udtSPParam READONLY
	, @udtEventData udtSPParam READONLY
AS
	SET NOCOUNT ON;
		DECLARE @UserID INT,@LoginSessionID BIGINT, @SessionID BIGINT
		DECLARE @Msg VARCHAR(500)
		DECLARE @EventType VARCHAR(50), @DeviceID INT
		DECLARE @CurrentUtcTime DATETIME,  @Message VARCHAR(50)
		--Edted by subramanya to handle type mismatch exception
		--SET XACT_ABORT ON;
		BEGIN TRY
		--BEGIN TRANSACTION
		SELECT @DeviceID = Value FROM @udtSystemData WHERE Property = 'DeviceID'
		SELECT @UserID = Value FROM @udtEventData WHERE Property = 'UserID'
		SELECT @SessionID = Value FROM @udtEventData WHERE Property = 'SessionID'
		SELECT @EventType = Value FROM @udtEventData WHERE Property = 'EventType'
		SELECT @Message = VALUE FROM @udtEventData WHERE Property = 'Message'
		IF (SELECT [Setting Value] FROM s01tblSettingsCommon WHERE [Setting Name] = 'ENABLE_UTC_TIMEZONE') = 'TRUE'
		BEGIN
			SELECT @CurrentUTCTime = GETUTCDATE()
		END
		ELSE
		BEGIN
			SELECT @CurrentUTCTime = GETDATE()
		END
				IF @EventType = 'SessionTimedout'
				BEGIN	 
					EXECUTE  usp704WebConsoleProcessUserLogs @Event = @Event
						, @udtSystemData = @udtSystemData
						, @udtEventData = @udtEventData
						, @UserSessionID =  @SessionID
					EXECUTE  usp705WebConsoleAppCabEventLogs @Event = @Event
						, @udtSystemData = @udtSystemData
						, @udtEventData = @udtEventData
						   , @Logs =  'Logout Successfull'
						, @UserSessionID =  @SessionID
				END
				IF @EventType = 'UserLogOut'
				BEGIN	
					EXECUTE  usp704WebConsoleProcessUserLogs @Event = @Event
						, @udtSystemData = @udtSystemData
						, @udtEventData = @udtEventData
						, @UserSessionID =  @SessionID
					--EXECUTE  usp705WebConsoleAppCabEventLogs @Event = @Event
					--	, @udtSystemData = @udtSystemData
					--	, @udtEventData = @udtEventData
					--	   , @Logs =  'Logout Successfull'
					--	, @UserSessionID =  @SessionID
				END
				IF @EventType = 'ExitAutoLogin'
				BEGIN	
					EXECUTE  usp704WebConsoleProcessUserLogs @Event = @Event
						, @udtSystemData = @udtSystemData
						, @udtEventData = @udtEventData
						, @UserSessionID =  @SessionID
					EXECUTE  usp705WebConsoleAppCabEventLogs @Event = @Event
						, @udtSystemData = @udtSystemData
						, @udtEventData = @udtEventData
						   , @Logs =  'Logout Successfull'
						, @UserSessionID =  @SessionID
				END
				
		END TRY
		BEGIN CATCH
			--IF (XACT_STATE()) <> 0
				--ROLLBACK TRANSACTION;
				SET @Msg = 'Error in Logout Application';
			EXECUTE s01uspLogError;
		END CATCH;
	SET NOCOUNT OFF
GO
/****** Object:  StoredProcedure [dbo].[usp802WebConsoleLoginValidation]    Script Date: 11/21/2024 2:16:28 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE Procedure [dbo].[usp802WebConsoleLoginValidation]
@UserName varchar(100),
@Password varchar(100)
AS
BEGIN

Declare @LatestPassword varchar(100),@UserPassword varchar(100),@Department nvarchar(50)

If  exists(select top 1 * from s01tblUsers where Username=@UserName and Password=@Password and UserStatusID=1)
Begin
     select 'Login Success'
	 --set @Department=(select Department from tblUsers where Username=@UserName);
	 --select RoomNumber  from tbllocationroom where Department=@Department;
	
end
else
      select 'Invalid login'
		--Select 'Error' as datasetname,'Invalid Username' as msg;
END
GO
/****** Object:  StoredProcedure [dbo].[usp810WebConsoleLoadPatientList]    Script Date: 11/21/2024 2:16:28 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE Procedure [dbo].[usp810WebConsoleLoadPatientList]
	@Event VARCHAR(255)
	, @udtSystemData udtSPParam READONLY
	, @udtEventData udtSPParam READONLY
AS
	SET NOCOUNT ON;
		DECLARE @SettingValue VARCHAR(512), @StrSQL VARCHAR(MAX), @Msg VARCHAR(200), @PatSysType VARCHAR (20), @DeviceID INT, @OrderBY VARCHAR(MAX)
		--SET XACT_ABORT ON;
		BEGIN TRY
		--BEGIN TRANSACTION
		SELECT @DeviceID = Value FROM @udtSystemData WHERE Property = 'DeviceID'
		SET @SettingValue=  [dbo].[udf700WebConsoleSUPFetchSettings] (@DeviceId,'HUB_FILTER_PAT_DEFAULT') 
		SET @PatSysType=  [dbo].[udf700WebConsoleSUPFetchSettings] (@DeviceId,'PAT_SYS_TYPE') 
		SET @OrderBY = ' ORDER BY PatientName ASC, ProcedureDate DESC, CSN ASC'
		--IF @Event <> 'LoadLastPatientList'
		--	EXECUTE  uspLoadSettingsList @Event = 'FetchAllSettings'
		--			, @udtSystemData = @udtSystemData
		--			, @udtEventData = @udtEventData
		SET @SettingValue = REPLACE( @SettingValue, 'Filter List|', '' )
		SET @StrSQL=   'DECLARE @ClientTimeZone VARCHAR(500), @UTCStandard VARCHAR(100), @SecondDiff INT
						SELECT @ClientTimeZone = [Setting Value] 
						FROM s01tblSettingsCommon 
						WHERE [Setting Name] = ''CLIENT_TIMEZONE'' 
						SELECT @UTCStandard = UTCStandard 
						FROM tblStandardTimeZone 
						WHERE StandardTimeZone = @ClientTimeZone
						SELECT @SecondDiff =  REPLACE(@UTCStandard, ''UTC'', '''')
						SELECT ''DefaultPatientList'' AS DataSetName, ''CSN: '' + LTRIM(RTRIM(ISNULL(SPD.VisitNumber,''N/A''))) AS CSN, LTRIM(RTRIM(PD.[Patient Name])) AS PatientName, 
									''MRN: '' + LTRIM(RTRIM(ISNULL(PD.PatientID,''N/A''))) AS MRN, LTRIM(RTRIM(PD.[Physician Name])) AS PhysicianName 
									, CASE WHEN DATEADD( SECOND, @SecondDiff, ProcedureDate ) IS NULL 
												THEN DATEADD( SECOND, @SecondDiff, LTRIM(RTRIM(PD.LastActivityDate))) 
											ELSE DATEADD( SECOND, @SecondDiff, LTRIM(RTRIM(ProcedureDate )))
										END AS ProcedureDate
									, ''CASEID: '' + LTRIM(RTRIM(ISNULL(PD.AppointmentID,''N/A''))) AS CASEID, PD.MAPatientID, PD.MAScheduleID, PD.MAVisitID,PD.ProcedureDesc--, LTRIM(RTRIM(SPD.ProcedureCode))
						FROM s01qryPatientDisplay'+@PatSysType+' PD
							LEFT OUTER JOIN qry701WebConsoleSUPPatientDisplayADT SPD ON SPD.MaScheduleID = PD.MaScheduleID 
							'
		SET @StrSQL = @StrSQL + ' ' + @SettingValue + @OrderBY
		PRINT( @StrSQL )
		EXECUTE( @StrSQL )
		--EXECUTE  usp705WebConsoleAppCabEventLogs @Event = @Event
		--				, @udtSystemData = @udtSystemData
		--				, @udtEventData = @udtEventData
		--COMMIT TRANSACTION		
		END TRY
		BEGIN CATCH
		--IF XACT_STATE() <> 0
			--ROLLBACK TRANSACTION
			SET @Msg = 'ERROR in Loading Patient';
		EXECUTE s01uspLogError
	END CATCH
	SET NOCOUNT OFF
GO
/****** Object:  StoredProcedure [dbo].[usp812WebConsoleLoadUsageList]    Script Date: 11/21/2024 2:16:28 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE Procedure [dbo].[usp812WebConsoleLoadUsageList]
	@Event VARCHAR(255)
	, @udtSystemData udtSPParam READONLY
	, @udtEventData udtSPParam READONLY
AS
	SET NOCOUNT ON;
	DECLARE @Msg VARCHAR(200), @DataSetName VARCHAR(50),  @SessionID VARCHAR(50), @PatientName VARCHAR(167)
	DECLARE @MAPatientID BIGINT, @SecondDiff INT, @ClientTimeZone VARCHAR(500), @UTCStandard VARCHAR(100), @ScanType VARCHAR(25)
	DECLARE  @MAScheduleID INT, @MAVisitID INT, @DeviceId INT 	
	SELECT @DeviceID = Value FROM @udtSystemData WHERE Property = 'DeviceID'
	SELECT @MAPatientID = Value FROM @udtEventData WHERE Property = 'MAPatientID'
	SELECT @SessionID = Value FROM @udtEventData WHERE Property = 'SessionID'
	--SELECT @DataSetName = Value FROM @udtEventData WHERE Property = 'EventOutPut'
	SELECT @MAVisitID = Value FROM @udtEventData WHERE Property = 'MAVisitID' 
	SELECT @MAScheduleID = Value FROM @udtEventData WHERE Property = 'MAScheduleID' 
	IF @Event IN ( 'StockNewItem', 'RemoveItem', 'ReturnItem', 'WasteItem', 'TransferItem', 'SwapItem', 'OverrideItem', 'RenderToExternalHospital')
		SET @ScanType = 'CabinetScan'
	ELSE
		SELECT @ScanType = Value FROM @udtEventData WHERE Property = 'EventInPut'
	SELECT @PatientName = [Patient Name] 
	FROM s01qryPatients 
	WHERE MAPatientID = @MAPatientID
	SELECT @ClientTimeZone = [dbo].[udf700WebConsoleSUPFetchSettings] ( @DeviceId,'CLIENT_TIMEZONE' )
	SELECT @UTCStandard = UTCStandard 
	FROM tblStandardTimeZone 
	WHERE StandardTimeZone = @ClientTimeZone
	SELECT @SecondDiff =  REPLACE(@UTCStandard, 'UTC', '')
	-- patient removal usage
	IF ( 
			@MAPatientID IS NOT NULL 
			AND @MAScheduleID IS NOT NULL 
		)
	BEGIN			
					SELECT top 1 [Description], CatNo,CONVERT(varchar, CONVERT(datetime,DateUsed,120),22)as DateUsed, [REAL], Qty, PatientName, ItemUsageID, UsageTypeID
						, RFID, ProductType,SerialNo, LotNo--, EpicStatus
					FROM qry704WebConsoleSUPItemUsageList
					WHERE MAScheduleID = @MAScheduleID
					and usagetypeid=1
					ORDER BY DateUsed DESC
	END
	--Non patient removal usage
	IF ( @MAPatientID IS NULL ) 
	BEGIN
			SELECT top 1 [Description], CatNo, DateUsed, [REAL], Qty, PatientName, ItemUsageID
			, UsageTypeID, RFID, ProductType, SerialNo, LotNo--, EpicStatus
			FROM qry704WebConsoleSUPItemUsageList
			WHERE SessionID = @SessionID
			AND MAPatientID is null
			and usagetypeid=1
			ORDER BY  DateUsed DESC
	END
		--EXECUTE  usp705WebConsoleAppCabEventLogs @Event = 'LoadUsageList'
		--		, @udtSystemData = @udtSystemData
		--		, @udtEventData = @udtEventData
	SET NOCOUNT OFF
GO
/****** Object:  StoredProcedure [dbo].[usp821WebConsoleExecuteAppEvent]    Script Date: 11/21/2024 2:16:28 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE Procedure [dbo].[usp821WebConsoleExecuteAppEvent]
	@Event VARCHAR(255),
	@udtSystemData udtSPParam READONLY,
	@udtEventData udtSPParam READONLY
	--@udt702WebConsoleTagsData udt702WebConsoleTags READONLY,
	--@udt703WebConsoleItemsData udt703WebConsoleItemsData READONLY,
	--@udt704WebConsoleScanData udt704WebConsoleScanData READONLY
AS


	SET NOCOUNT ON
	DECLARE @SPName VARCHAR(100), @AppID INT, @DeviceID INT, @Msg VARCHAR(200)
	SET XACT_ABORT ON;
	BEGIN TRY
	BEGIN TRANSACTION;
	SELECT @AppID = Value FROM @udtSystemData WHERE Property = 'EntryTypeID'
	SELECT @SPName = SPName
	FROM tblAppEvents
	WHERE [Event] = @Event AND AppID = @AppID
	IF NOT @SPName IS NULL
	BEGIN

		    IF @Event IN ( 'StockNewItem','ReturnItem','RemoveItem',
							'WasteItem','TransferItem')
				--EXEC @SPName @Event, @udtSystemData, @udtEventData ,  @udt702WebConsoleTagsData
				EXEC @SPName @Event, @udtSystemData, @udtEventData
			--ELSE IF @Event IN ('ReconcileInventoryList')
			--	EXEC @SPName @Event, @udtSystemData, @udtEventData ,  @udt703WebConsoleItemsData
			--ELSE IF @Event IN ('ScanStockInventory')
			--	EXEC @SPName @Event, @udtSystemData, @udtEventData ,  @udt704WebConsoleScanData
			ELSE
				EXEC usp825WebConsoleScanBarcodeInventory @Event, @udtSystemData, @udtEventData 
				--EXEC @SPName @Event, @udtSystemData, @udtEventData 
	END
	ELSE
		SELECT 'No SP Found' AS ErrorDataSet
	COMMIT TRANSACTION;
		END TRY
		BEGIN CATCH
			IF (XACT_STATE()) <> 0
				ROLLBACK TRANSACTION;
				SET @Msg = 'ERROR in tblAppEvents for calling SP';
			EXECUTE s01uspLogError;
		END CATCH;
	SET NOCOUNT OFF
GO
/****** Object:  StoredProcedure [dbo].[usp822WebConsoleProcessItems]    Script Date: 11/21/2024 2:16:28 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE Procedure [dbo].[usp822WebConsoleProcessItems]
	@Event VARCHAR(50)
	, @udtSystemData udtSPParam READONLY
	, @udtEventData udtSPParam READONLY
	--, @udt702WebConsoleTagsData udt702WebConsoleTags READONLY
AS

	SET NOCOUNT ON;
	DECLARE @udt700WebConsoleTransactedItems udt700WebConsoleTransactedItems, @ScannedLocation INT, @TransactionTime DATETIME, @ItemStatusID INT, @SacnType VARCHAR(20)
	BEGIN TRY	
	
	IF @Event IN ( 'StockNewItem', 'RemoveItem', 'ReturnItem', 'WasteItem'
					, 'TransferItem', 'SwapItem', 'OverrideItem', 'InitialScan', 'KeyAccessScan', 'RenderToExternalHospital' )
	BEGIN			
		--IF (@SacnType <> 'OutsideScan')
		--BEGIN
		
			EXECUTE usp742WebConsoleProcessProcessRFIDItems @Event = @Event
				, @udtSystemData = @udtSystemData
				, @udtEventData = @udtEventData
				--, @udt700WebConsoleTransactedItems = @udt700WebConsoleTransactedItems
				--, @udt702WebConsoleTagsData = @udt702WebConsoleTagsData
		--END 
	END
	END TRY
	BEGIN CATCH			
		EXECUTE s01uspLogError;
	END CATCH;
	SET NOCOUNT OFF
GO
/****** Object:  StoredProcedure [dbo].[usp823WebConsoleProcessMaxRmvPatItems]    Script Date: 11/21/2024 2:16:28 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE Procedure [dbo].[usp823WebConsoleProcessMaxRmvPatItems]
	@Event VARCHAR(200)
	, @udtSystemData udtSPParam READONLY
	, @udtEventData udtSPParam READONLY
--	, @udt701WebConsoleTransactedItemDetails udt701WebConsoleTransactedItemDetails READONLY
	, @ItemStatusID INT
	, @ItemEventID INT
	, @CurrentUtcTime DateTime
	, @UsageTypeID INT
AS
	SET NOCOUNT ON;
	DECLARE @UserID INT, @EntryTypeID INT, @SessionID BIGINT, @DeviceID INT
	DECLARE @ComputerName VARCHAR(50), @Domain VARCHAR(50), @UOMCode VARCHAR(50), @BillingStatusID INT, @ItemUsageID INT, @Msg VARCHAR(500), @MAX_RMV_PAT_ITEMS VARCHAR(10)
	DECLARE @MAPatientID VARCHAR(50), @MAScheduleID INT, @MAVisitID INT, @MAPhysicianID INT, @DBID INT, @AlertID INT, @AlertMessage VARCHAR(512), @AlertEnabled BIT
	DECLARE @RFID VARCHAR(50)
	DECLARE @ItemID INT
	DECLARE @CompartmentID INT
	DECLARE @OverrideNotes VARCHAR(50)
	DECLARE @OverrideID INT
	SET XACT_ABORT ON;
	BEGIN TRY
	BEGIN TRANSACTION	
	
	SELECT @DeviceID = Value FROM @udtSystemData WHERE Property = 'DeviceID'
	SELECT @EntryTypeID = Value FROM @udtSystemData WHERE Property = 'EntryTypeID'
	SELECT @Domain = Value FROM @udtSystemData WHERE Property = 'Domain'
	SELECT @ComputerName = Value FROM @udtSystemData WHERE Property = 'ComputerName'
	SELECT @SessionID = Value FROM @udtEventData WHERE Property = 'SessionID'
	SELECT @UserID = Value FROM @udtEventData WHERE Property = 'UserID'
	SELECT @RFID = Value FROM @udtEventData WHERE Property = 'RFID'
	SELECT @MAPatientID = Value FROM @udtEventData WHERE Property = 'MAPatientID'
	SELECT @MAVisitID = Value FROM @udtEventData WHERE Property = 'MAVisitID'
	SELECT @MAScheduleID = Value FROM @udtEventData WHERE Property = 'MAScheduleID'
	set @ItemID = (select itemid from s01tblItems where RFID=@RFID)
	set @CompartmentID = (select CompartmentID from s01tblItems where ItemID=@ItemID)
	set @MAPhysicianID= (select MAPhysicianID from s01tblPatientSchedules where MAScheduleID=@MAScheduleID)
	SELECT @OverrideNotes = Value FROM @udtEventData WHERE Property = 'OverrideNotes'
	if (@MAPatientID is null)
	begin
		set @OverrideID=7;
	end

	UPDATE dbo.s01tblItems 
	SET ItemStatusID = @ItemStatusID, RMUserID = @UserID, [Date Removed] = @CurrentUtcTime--, [Use Cycle] = TI.[UseCycle]
		, EntryTypeID = @EntryTypeID, MAScheduleID = @MAScheduleID, MAPatientID = @MAPatientID, LastActivityDate = @CurrentUtcTime
		, MAPhysicianID = @MAPhysicianID 
	FROM dbo.s01tblItems I where  I.RFID=@RFID;

	--DELETE FROM dbo.s01tblItemUsage where UsageItemID=@ItemID; --UPDATED BY VIRESH ON 12/11/2024 to remove dublicate from usage list after scanning from HUB , if its already remove from Cabinet

	DELETE FROM s01tblItemUsage
		   WHERE UsageItemID = @ItemID
		   AND LastActivityDate = (
				SELECT MAX(LastActivityDate)
				FROM s01tblItemUsage
				WHERE UsageItemID = @ItemID
				);--UPDATED BY VIRESH ON 12/11/2024 to remove dublicate from usage list after scanning from HUB 
				--, if its already remove from Cabinet

	INSERT INTO dbo.s01tblItemUsage ( MAPatientID, MAScheduleID, MAVisitID, UsedBy, DateUsed, Qty, UnitPrice, UOMCode, BillingStatusID, OverrideID, OverrideNotes, UpdatedBy
						, LastActivityDate , UsageTypeID, UsageItemID, SessionID, UCDM, EntryTypeID )
	SELECT @MAPatientID, @MAScheduleID, @MAVisitID, @UserID, @CurrentUtcTime, 1, I.[Act Price], null, @BillingStatusID, 0,@OverrideNotes , @UserID, @CurrentUtcTime
						, @UsageTypeID, I.ItemID, @SessionID, I.ICDM, @EntryTypeID 
	FROM dbo.s01tblItems I where  I.RFID=@RFID; 
	
	INSERT INTO dbo.s01tblItemHistory( ItemStatusID, ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAScheduleID, MAVisitID, BillingStatusID, UsedBy, Qty
						, UnitPrice, UOMCode, UpdatedBy, UCDM, LastActivityDate, EntryTypeID, DateUsed, OverrideID, OverrideNotes, SessionID, ItemEventID )
	SELECT @ItemStatusID, ItemUsageID, @UsageTypeID, U.UsageItemID, MAPatientID, MAScheduleID, MAVisitID, BillingStatusID, @UserID, U.Qty, U.UnitPrice
						, null, @UserID, U.UCDM, @CurrentUtcTime, @EntryTypeID, U.DateUsed, 0, U.OverrideNotes, @SessionID, @ItemEventID  
	FROM dbo.s01tblItemUsage U 
		--INNER JOIN @udt701WebConsoleTransactedItemDetails TI ON TI.ItemID = U.UsageItemID
	WHERE SessionID = @SessionID  AND U.LastActivityDate = @CurrentUtcTime --AND TagType = 'LostTag'
	and U.UsageItemID=@ItemID;

	INSERT INTO dbo.s01tblitemhistoryextn (ItemHistoryID, LocationID)
	SELECT ItemHistoryID, @CompartmentID 
	--FROM @udt701WebConsoleTransactedItemDetails T
		from dbo.s01tblItemHistory H  --H.UsageItemID = T.ItemID 
		where H.LastActivityDate = @CurrentUtcTime
		AND H.UsageTypeID=1

	--viresh added on 11/11/2024 
	--IF(@EnableEpicInterface = 'TRUE') added by viresh on 11/11/2024
	IF (1=1) --setting value
	BEGIN
		IF ( @RFID IS NOT NULL AND @RFID <> '' ) 
		BEGIN
			--Epic Transaction
			EXECUTE  uspSWCSendUsageToEpic @Event = @Event
					, @udtSystemData = @udtSystemData
					, @udtEventData = @udtEventData
					, @ItemEventID = @ItemEventID
					, @ItemStatusID = @ItemStatusID
					, @UsageTypeID = @UsageTypeID
					, @ItemID = @ItemID
		END					
	END
COMMIT TRANSACTION;
END TRY
		BEGIN CATCH
			IF (XACT_STATE()) <> 0
				ROLLBACK TRANSACTION;
				SET @Msg = 'ERROR in Item processing during cabinet scan';
			EXECUTE s01uspLogError;
		END CATCH;
	SET NOCOUNT OFF
GO
/****** Object:  StoredProcedure [dbo].[usp824WebConsoleProcessRFIDItems]    Script Date: 11/21/2024 2:16:28 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE Procedure [dbo].[usp824WebConsoleProcessRFIDItems]
	@Event VARCHAR(200)
	, @udtSystemData udtSPParam READONLY
	, @udtEventData udtSPParam READONLY
	--, @udt700WebConsoleTransactedItems udt700WebConsoleTransactedItems READONLY
	--, @udt702WebConsoleTagsData udt702WebConsoleTags READONLY
AS
	SET NOCOUNT ON;
	DECLARE @UserID INT, @EntryTypeID INT, @SessionID BIGINT, @DeviceID INT, @BillingStatusID INT, @OverrideID INT, @OverrideNotesID INT, @OverrideNotes VARCHAR(255)
	DECLARE @ItemEventID INT, @UsageTypeID INT, @ItemStatusID INT,@ItemStatus INT, @MAPatientID VARCHAR(50), @MAScheduleID INT, @MAVisitID INT, @AlertMessage VARCHAR(512)
	DECLARE @CurrentUtcTime DATETIME, @OverrideDescription VARCHAR(50), @OverrideDescNotes VARCHAR(50), @UageSummarySettings VARCHAR(250), @AlertID INT, @DBID INT
	DECLARE @ClientTimeZone VARCHAR(500), @UTCStandard VARCHAR(100), @SecondDiff INT, @ShowUnAssociateItems VARCHAR(50), @PhysicianID VARCHAR(50)
	DECLARE @MAPhysicianID INT, @EnableEpicInterface VARCHAR(50), @SpuriousLogAlertEnabled BIT, @EXPDATE_CHECK VARCHAR(10), @CLOSEEXP_CHECK VARCHAR(10)
	DECLARE @MAX_RMV_PAT_ITEMS INT, @MAX_RMV_PAT_ITEMS_AlertEnabled BIT, @AntenaStatus VARCHAR(500), @AntennaFailure_AlertEnabled INT
	DECLARE @RFID VARCHAR(50),@ItemID INT, @CompartmentID INT
	DECLARE @udt701WebConsoleTransactedItemDetails udt701WebConsoleTransactedItemDetails 
		--SET XACT_ABORT ON;
		BEGIN TRY
		--BEGIN TRANSACTION
		SELECT @DeviceID = Value FROM @udtSystemData WHERE Property = 'DeviceID'
		SELECT @EntryTypeID = Value FROM @udtSystemData WHERE Property = 'EntryTypeID'
		SELECT @SessionID = Value FROM @udtEventData WHERE Property = 'SessionID'
		SELECT @UserID = Value FROM @udtEventData WHERE Property = 'UserID'
		SELECT @MAPatientID = Value FROM @udtEventData WHERE Property = 'MAPatientID'
		SELECT @OverrideDescription = Value FROM @udtEventData WHERE Property = 'OverrideDesc'
		--SELECT @OverrideDescNotes = Value FROM @udtEventData WHERE Property = 'EventInput2'
		SELECT @RFID = Value FROM @udtEventData WHERE Property = 'RFID'
			SELECT @ItemStatus = Value FROM @udtEventData WHERE Property = 'ItemStatus' 
		SELECT @ItemID =(select ItemID from s01tblItems where RFID=@RFID)
		set @CompartmentID = (select CompartmentID from s01tblItems where ItemID=@ItemID)
		SELECT @MAX_RMV_PAT_ITEMS = [dbo].[udf700WebConsoleSUPFetchSettings] ( @MAX_RMV_PAT_ITEMS,'MAX_RMV_PAT_ITEMS' )
		SELECT @UsageTypeID = UsageTypeID 
		FROM dbo.s01tblUsageType 
		WHERE UsageType = 'RF ITEM'
		--SET @EnableEpicInterface =  [dbo].[udf700WebConsoleSUPFetchSettings] (@DeviceId,'ENABLE_EPIC_INTERFACE') 
		SELECT @OverrideID = OverrideID 
		FROM dbo.s01tblOverride 
		WHERE OverrideDesc = CASE 
								WHEN @OverrideDescription = 'Waste Item' THEN 'Waste'   
								WHEN @OverrideDescription = 'Override' THEN 'Manual Override'
								WHEN @MAPatientID IS NOT NULL THEN 'Default'
								WHEN @Event = 'InitialScan' THEN 'Initial Scan'
								WHEN @Event = 'KeyAccessScan' THEN 'Key Access Scan'
								WHEN @OverrideDescription IN ('Take','Waste','Return') THEN 'Default' 
								ELSE @OverrideDescription 
							 END
		SELECT @UageSummarySettings = [dbo].[udf700WebConsoleSUPFetchSettings] ( @DeviceId,'SHOW_USAGE_SUMMARY' ) 
		SELECT @ClientTimeZone = [dbo].[udf700WebConsoleSUPFetchSettings] ( @DeviceId,'CLIENT_TIMEZONE' )
		SELECT @UTCStandard = UTCStandard 
		FROM s01tblStandardTimeZone 
		WHERE StandardTimeZone = @ClientTimeZone
		SELECT @SecondDiff =  REPLACE(@UTCStandard, 'UTC', '')
		IF (SELECT [Setting Value] FROM dbo.s01tblSettingsCommon WHERE [Setting Name] = 'ENABLE_UTC_TIMEZONE') = 'TRUE'
		BEGIN
			SELECT @CurrentUTCTime = GETUTCDATE()
		END
		ELSE
		BEGIN
			SELECT @CurrentUTCTime = GETDATE()
		END
		--IF ( SELECT COUNT(*) FROM @udt700WebConsoleTransactedItems WHERE TagType = 'FoundTag' ) > 0
		--BEGIN
		if (@Event='ReturnItem')
		begin
	IF EXISTS(Select TOP 1 * from s01tblitems where  RFID=@RFID and ItemStatusID=1 ) 
            BEGIN
            Select 'Error' as datasetname,'Item already stocked in cabinet.'  as msg
            END
			else if not exists( Select TOP 1 * from s01tblitems where  RFID=@RFID)
			begin
			Select 'Error' as datasetname,'RFID not associated to any item.'  as msg
			end
			else
			begin
			SELECT @ItemStatusID = ItemStatusID 
			FROM dbo.s01tblItemStatus 
			WHERE [Item Status] = 'In Cabinet'
			SELECT @ItemEventID = CASE 
									  WHEN @Event = 'ReturnItem' 
										THEN 3
                                      ELSE 1
								  END
			DELETE FROM dbo.s01tblItemUsage where UsageItemID=@ItemID;
			--WHERE UsageTypeID = 1 AND UsageItemID IN ( SELECT I.ItemID 
			--											FROM dbo.s01tblItems I INNER JOIN @udt701WebConsoleTransactedItemDetails TI ON TI.RFID = I.RFID 
			--											WHERE TagType = 'FoundTag' )
			UPDATE dbo.s01tblItems 
			SET ItemStatusID = @ItemStatusID,  [AddUserID] = @UserID
				, [Date Added] = @CurrentUtcTime, EntryTypeID = @EntryTypeID, SupplierID = 0, LastActivityDate = @CurrentUtcTime
				, MAPatientID = NULL, MAScheduleID = NULL, MAPhysicianID = NULL 
				, Qty = 1 
			FROM dbo.s01tblItems I where RFID=@RFID;
			--	INNER JOIN @udt701WebConsoleTransactedItemDetails TI ON TI.RFID = I.RFID
			--WHERE TagType = 'FoundTag'
			INSERT INTO dbo.s01tblItemHistory ( ItemStatusID, UsageTypeID, UsageItemID, MAPatientID, MAScheduleID, MAVisitID, BillingStatusID, Qty, UnitPrice --UOMCode
							,  UpdatedBy, UCDM, LastActivityDate, EntryTypeID, OverrideID, OverrideNotes, SessionID, ItemEventID )
			SELECT @ItemStatusID, @UsageTypeID, I.ItemID, I.MAPatientID INT, I.MAScheduleID, I.MAVisitID, @BillingStatusID, ISNULL(I.Qty,1), I.[Act Price]-- TI.UOMCode
							, @UserID, I.ICDM, @CurrentUtcTime, @EntryTypeID, @OverrideID, @OverrideNotes, @SessionID, @ItemEventID 
			FROM dbo.s01qryItems I where RFID=@RFID;
			--INNER JOIN @udt701WebConsoleTransactedItemDetails TI ON TI.RFID = I.RFID AND TagType = 'FoundTag'
			INSERT INTO dbo.s01tblitemhistoryextn (ItemHistoryID, LocationID)
	SELECT ItemHistoryID, @CompartmentID 
	--FROM @udt701WebConsoleTransactedItemDetails T
		from dbo.s01tblItemHistory H  --H.UsageItemID = T.ItemID 
		where H.LastActivityDate = @CurrentUtcTime
		AND H.UsageTypeID=1
				select [Serial No],[Lot No],P.[Cat No],p.[Description 1],I.RFID from s01tblItems I
				inner join s01tblProducts P on P.ProductID=I.ProductID
				where I.RFID=@RFID
		end
		end
		else
		begin
		SELECT @MAVisitID = Value FROM @udtEventData WHERE Property = 'MAVisitID' 
		SELECT @MAScheduleID = Value FROM @udtEventData WHERE Property = 'MAScheduleID' 
		SELECT @PhysicianID = PhysicianID FROM dbo.s01qryPatientDisplayADT WHERE MAPatientID = @MAPatientID 
		SELECT @MAPhysicianID = MAPhysicianID FROM dbo.s01tblPhysicians WHERE PhysicianID = @PhysicianID
			SELECT @ItemStatusID = ItemStatusID 
			FROM dbo.s01tblItemStatus 
			WHERE [Item Status] = CASE 
									WHEN @OverrideDescription IN ( 'Waste Item','Waste') THEN 'Waste' 
									WHEN @OverrideDescription = 'Transfer Item' THEN 'Transfered'
									WHEN @OverrideDescription = 'Swap Item' THEN 'Swap'
									WHEN @OverrideDescription = 'Render To External Hospital' THEN 'Render to External Hospital'   
									ELSE 'Removed' 
									END
			SELECT @ItemEventID = ItemEventID 
			FROM dbo.s01tblItemEvent 
			WHERE EventDescription = CASE 
											WHEN @OverrideDescription IN ('Waste Item','Waste') THEN 'Item has been wasted' 
											WHEN @OverrideDescription = 'Transfer Item' THEN 'Item has been transfered'
											ELSE 'Item taken out of the cabinet' 
										END
			IF EXISTS(Select TOP 1 * from s01tblitems where  RFID=@RFID and ItemStatusID=2 ) 
            BEGIN
            Select 'Error' as datasetname,'Item already removed against patient.'  as msg
            END
			else if not exists( Select TOP 1 * from s01tblitems where  RFID=@RFID)
			begin
			Select 'Error' as datasetname,'RFID not associated to any item.'  as msg
			end
			else 
            begin
				EXECUTE usp823WebConsoleProcessMaxRmvPatItems @Event = @Event
						, @udtSystemData = @udtSystemData
						, @udtEventData = @udtEventData
						--, @udt701WebConsoleTransactedItemDetails = @udt701WebConsoleTransactedItemDetails
						, @ItemStatusID = @ItemStatus
						, @ItemEventID = @ItemEventID
						, @CurrentUtcTime = @CurrentUtcTime
						, @UsageTypeID = @UsageTypeID
							EXECUTE  usp812WebConsoleLoadUsageList @Event = @Event
							, @udtSystemData = @udtSystemData
							, @udtEventData = @udtEventData
			end
			end
	END TRY
		BEGIN CATCH
			EXECUTE s01uspLogError;
		END CATCH;
	SET NOCOUNT OFF
GO
/****** Object:  StoredProcedure [dbo].[usp825WebConsoleScanBarcodeInventory]    Script Date: 11/21/2024 2:16:28 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE Procedure [dbo].[usp825WebConsoleScanBarcodeInventory]
	@Event VARCHAR(255)
	, @udtSystemData udtSPParam READONLY
	, @udtEventData udtSPParam READONLY
AS
	SET NOCOUNT ON;
		DECLARE @DeviceID INT, @EntryTypeID INT, @Msg VARCHAR(200), @DataSetName VARCHAR(50), @SessionID varchar(50), @UserID INT, @ItemUsageID INT, @FDACatNo VARCHAR(50)
		DECLARE @CurrentPatientID INT, @CurrentUtcTime DATETIME, @ScannedBarcodeValue VARCHAR(100), @EventType VARCHAR(50), @CatNo VARCHAR(50), @ProductID INT, @LocationID INT, @LocationTypeID INT, @BarcodeSettings VARCHAR(10)
		DECLARE @UpdateQty VARCHAR(10), @UOMCode VARCHAR(10), @Qty INT,@inputQty INT, @ItemInventoryID INT, @EventInput3 VARCHAR(50), @EventInPut4 VARCHAR(50),  @OverrideID INT, @ItemEventID INT, @InventoryID INT
		DECLARE @ReasonWindowSetting VARCHAR(10), @OverrideNotes VARCHAR(50), @PatientName VARCHAR(200), @MAPatientID INT, @MAScheduleID INT, @MAVisitID INT, @ItemHistoryID INT, @UsageItemID INT, @IsFDA VARCHAR(50), @IsUsageExists VARCHAR(50)
		
		--Edted by subramanya to handle type mismatch exception
		--SET XACT_ABORT ON;
		BEGIN TRY
		--BEGIN TRANSACTION

		SELECT @DeviceID = Value FROM @udtSystemData WHERE Property = 'DeviceID'
		SELECT @EntryTypeID = Value FROM @udtSystemData WHERE Property = 'EntryTypeID'
		SELECT @UserID = Value FROM @udtEventData WHERE Property = 'UserID'
		SELECT @MAPatientID = Value FROM @udtEventData WHERE Property = 'MAPatientID'
		SELECT @ScannedBarcodeValue = Value FROM @udtEventData WHERE Property = 'EventInPut1'
		SELECT @UpdateQty = Value FROM @udtEventData WHERE Property = 'EventInPut2'
		SELECT @EventInPut3 = Value FROM @udtEventData WHERE Property = 'EventInPut3'
		SELECT @EventInPut4 = Value FROM @udtEventData WHERE Property = 'EventInPut4'
		SELECT @EventType = Value FROM @udtEventData WHERE Property = 'EventType'
		SELECT @SessionID = Value FROM @udtEventData WHERE Property = 'SessionID'
		SELECT @DataSetName = Value FROM @udtEventData WHERE Property = 'EventOutPut'
		SELECT @InventoryID = Value FROM @udtEventData WHERE Property = 'ItemInventoryID'
		
		SELECT @MAVisitID = Value FROM @udtEventData WHERE Property = 'MAVisitID' 
		SELECT @MAScheduleID = Value FROM @udtEventData WHERE Property = 'MAScheduleID' 
		SELECT @FDACatNo = Value FROM @udtEventData WHERE Property = 'CatNo' 
		SELECT @IsFDA = Value FROM @udtEventData WHERE Property = 'IsFDA' 
		SELECT @inputQty = Value FROM @udtEventData WHERE Property = 'Qty' 
		--select @IsFDA;
	

		SELECT @LocationTypeID = [dbo].[udf700WebConsoleSUPFetchSettings] ( @DeviceId, 'BarcodeInventory_LocationType' )

		SELECT @LocationID = [dbo].[udf700WebConsoleSUPFetchSettings] ( @DeviceId, 'BarcodeInventory_Location' )

		
		
		IF (SELECT [Setting Value] FROM s01tblSettingsCommon WHERE [Setting Name] = 'ENABLE_UTC_TIMEZONE') = 'TRUE'
		BEGIN
			SELECT @CurrentUTCTime = GETUTCDATE()
		END
		ELSE
		BEGIN
			SELECT @CurrentUTCTime = GETDATE()
		END
		

		SELECT @CatNo = [Cat No]
		FROM s01tblBarcodeRuleProduct 
		WHERE [Barcode No] = @ScannedBarcodeValue
			AND @ScannedBarcodeValue IS NOT NULL
			AND @ScannedBarcodeValue <> ''

		SELECT @ProductID = ProductID, @UOMCode = UOMCode 
		FROM s01tblProducts 
		WHERE [Cat No] = @CatNo
		
		--SR added > FDA/Master
		if (@IsFDA='true')
		begin
		
		--IF NOT EXISTS ( SELECT 1 FROM s01tblProducts 	
		--				WHERE REPLACE(REPLACE([Cat No],'-',''),'.','') = REPLACE(REPLACE(@CatNo,'-',''),'.','')
		--					OR @CatNo IS NULL OR @CatNo = ''
		--		      )
		--BEGIN
			
			EXECUTE usp826WebConsoleSaveBarcodeMasterFDAProducts 
					  @Event = @Event
					, @udtSystemData = @udtSystemData
					, @udtEventData = @udtEventData
			
			SELECT @CatNo = [Cat No]
			FROM s01tblBarcodeRuleProduct 
			WHERE [Barcode No] = @ScannedBarcodeValue

			SELECT @ProductID = ProductID, @UOMCode = UOMCode 
			FROM s01tblProducts 
			WHERE [Cat No] = @CatNo

			IF @ProductID IS NULL
			BEGIN
				SELECT @FDACatNo = [Cat No]
				FROM s01tblBarcodeRuleProduct 
				WHERE [Barcode No] = @ScannedBarcodeValue

				SELECT @ProductID = ProductID, @UOMCode = UOMCode 
				FROM s01tblProducts 
				WHERE [Cat No] = @FDACatNo
			END

			IF @ProductID IS NOT NULL
			BEGIN
				INSERT INTO s01tblItemInventory(ProductID,Qty,UpdatedBy,LastActivityDate,LocationID,LocationTypeID)
				VALUES (@ProductId, @inputQty, @UserID, @CurrentUTCTime, @LocationID, @LocationTypeID )
			
			IF 	@FDACatNo IS NOT NULL AND @FDACatNo <> ''
				SET @CatNo = @FDACatNo
			END
			
		--END
		end
		IF @ProductID IS NULL
		BEGIN
			SELECT 'Fail' AS DataSetName, 'Product is not registered' AS MSG
			RETURN
		END
		
		SELECT @PatientName = [Patient Name] 
		FROM s01qryPatients 
		WHERE MAPatientID = @MAPatientID

		SELECT @UsageItemID = ItemInventoryID 
		FROM s01tblItemInventory 
		WHERE ProductID = @ProductID 
			AND LocationID = @LocationID 
			AND LocationTypeID = @LocationTypeID

		SELECT @CurrentPatientID = MAPatientID 
		FROM s01tblItemUsage 
		WHERE MAPatientID = @MAPatientID 
			AND UsageItemID = @UsageItemID 
			AND UsageTypeID = 2
			
		SELECT @BarcodeSettings = [dbo].[udf700WebConsoleSUPFetchSettings] ( @DeviceId,'ENABLE_ITEMIZE_BARCODE' )
		
		SELECT @Qty = Qty, @ItemInventoryID = ItemInventoryID 
		FROM s01tblItemInventory 
		WHERE ProductID = @ProductID 
			AND LocationID = @LocationID 
			AND LocationTypeID = @LocationTypeID

		SELECT @OverrideID = OverrideID 
		FROM s01tblOverride 
		WHERE OverrideDesc = CASE 
								WHEN @EventInput3 = 'Waste Item' 
									THEN 'Waste'
							    WHEN @EventInput3 = 'Override' 
									THEN 'Manual Override'
								WHEN @MAPatientID IS NOT NULL AND @EventInput3 <> 'Waste' 
									THEN 'Default'
								ELSE @EventInput3 
							 END
		
		SELECT @ReasonWindowSetting = [dbo].[udf700WebConsoleSUPFetchSettings] ( @DeviceId,'ENABLE_OVERRIDEHANDLER' )

		IF ( @EventInput4 IS NULL )
		BEGIN
			SELECT @OverrideNotes = 'DEFAULT'
		END
		ELSE IF ( @EventInput4 IS NOT NULL )
		BEGIN
			SELECT @OverrideNotes = OverrideNotes 
			FROM s01tblOverrideNotes 
			WHERE OverrideNotes = @EventInput4 
				AND OverrideID = @OverrideID
		END

		SELECT @ItemEventID = ItemEventID 
		FROM s01tblItemEvent 
		WHERE EventDescription = CASE 
									 WHEN @EventInput3 = 'Waste Item' 
										THEN 'Barcode Item Wasted'
								     WHEN @EventInput3 = 'Transfer Item' 
										THEN 'Barcode Item Transferred'
									 WHEN @EventInput3 IN ('Return Item','Remove Item', 'Swap Item', 'Override', 'Waste Item') 
										THEN 'Item Used'
									 WHEN @EventType = 'StockBarcodeInventory' 
										THEN 'Stock quantity for barcode item has been updated'
									 WHEN @EventType = 'ReconcileBarcodeInventory' 
										THEN 'Quantity for barcode item has been updated'
									 WHEN @EventType = 'DeleteBarcodeInventory' 
										THEN 'Barcode Item Deleted'
									 ELSE 'Item returned to inventory' 
								 END


				--IF ( @EventType = 'FetchBarcodeInventory' )
				--BEGIN
				--	IF EXISTS ( SELECT 1 FROM s01tblItemInventory WHERE ProductID = @ProductID AND LocationID = @LocationID AND LocationTypeID = @LocationTypeID )
				--	BEGIN
				--		SELECT @DataSetName AS DataSetName, Product, Qty, UOMCode, Location, LocationID, ProductID, ItemInventoryID 
				--		FROM s01qryStockInventory
				--		WHERE REPLACE( REPLACE( REPLACE( [Cat No], '-', ''), '.', ''), ' ', '' )= @CatNo
				--			AND LocationID = @LocationID 
				--			AND LocationTypeID = @LocationTypeID

				--		--EXECUTE  usp705WebConsoleAppCabEventLogs @Event = @Event
				--		--, @udtSystemData = @udtSystemData
				--		--, @udtEventData = @udtEventData

				--	END
				--	ELSE
				--	BEGIN
				--		SELECT @DataSetName AS DataSetName, Product, 1 AS Qty, UOMCode, Location, LocationID, ProductID, ItemInventoryID 
				--		FROM s01qryStockInventory
				--		WHERE REPLACE( REPLACE( REPLACE( [Cat No], '-', ''), '.', ''), ' ', '' )= @CatNo

				--		--EXECUTE  usp705WebConsoleAppCabEventLogs @Event = @Event
				--		--, @udtSystemData = @udtSystemData
				--		--, @udtEventData = @udtEventData

				--	END
				--END

				--IF ( @EventType = 'DeleteBarcodeInventory' )
				--BEGIN				
				--	IF EXISTS ( SELECT 1 
				--				FROM s01tblItemInventory 
				--				WHERE ProductID = @ProductID 
				--					AND LocationID = @LocationID 
				--					AND LocationTypeID = @LocationTypeID )
				--	BEGIN
				--		SELECT @Qty = Qty, @ItemInventoryID = ItemInventoryID 
				--		FROM s01tblItemInventory 
				--		WHERE ProductID = @ProductID 
				--			AND LocationID = @LocationID 
				--			AND LocationTypeID = @LocationTypeID
							
				--		DELETE s01tblItemInventory 
				--		WHERE ProductID = @ProductID 
				--			AND ProductID = @ProductID 
				--			AND LocationTypeID = @LocationTypeID

				--		INSERT INTO tblUpdateBarcodeItemInventoryLog( ItemInventoryID, QtyInHand, QtyLastUpdated, QtyUsed 
				--									, QtyReturned, QtyDeleted, QtyStocked, QtyWasted, CutOffTime, UpdatedQtyInHand )
				--		SELECT ItemInventoryID, Qty - @Qty, QtyLastUpdated, 0, @Qty, 0, 0, 0, @CurrentUTCTime, Qty 
				--		FROM s01tblItemInventory
				--		WHERE ItemInventoryID = @ItemInventoryID

				--		INSERT INTO s01tblItemHistory ( UsageTypeID, UsageItemID, ItemEventID, Qty, UOMCode, UpdatedBy, LastActivityDate, EntryTypeID, SessionID )
				--		VALUES ( 2, @ItemInventoryID, @ItemEventID, @Qty, @UOMCode, @UserID, @CurrentUtcTime, @EntryTypeID, @SessionID )

				--		SELECT @ItemHistoryID = SCOPE_IDENTITY()
				--		INSERT INTO s01tblItemHistoryExtn (ItemHistoryID, LocationID ) 
				--		SELECT ItemHistoryID, @LocationID 
				--		FROM s01tblItemHistory 
				--		WHERE ItemHistoryID = @ItemHistoryID

				--		INSERT INTO tblInventoryLog ( ItemHistoryID, InvntoryID, ProcessStatus, ClusterID, LocationID, UpdatedBy, EntryTypeID, EnteredDate )
				--		SELECT ItemHistoryID, UsageItemID, 'P', @DeviceID, @LocationID, @UserID, @EntryTypeID, @CurrentUTCTime 
				--		FROM s01tblItemHistory 
				--		WHERE ItemHistoryID = @ItemHistoryID	

				--		--EXECUTE  usp705WebConsoleAppCabEventLogs @Event = @Event
				--		--, @udtSystemData = @udtSystemData
				--		--, @udtEventData = @udtEventData
										
				--	END

				--	SELECT @DataSetName AS DataSetName, Product, Qty, UOMCode, Location, LocationID, ProductID, ItemInventoryID 
				--	FROM s01qryStockInventory
				--	WHERE LocationID = @LocationID 
				--		AND LocationTypeID = @LocationTypeID
				--END
					
				IF ( @EventType = 'RemoveBarcodeInventory' )
				BEGIN	
					SELECT @DataSetName AS DataSetName, ItemInventoryID, [Ref Price], UOMCode, [Cat No], [Description 1], Qty, ProdUnitID, PCDM, LocationID ,[Barcode No]
					FROM s01qry703WebConsoleProductCatalog 
					WHERE [Cat No] = @CatNo 
						AND LocationTypeID = @LocationTypeID 
						AND LocationID = @LocationID
						
					IF ( @BarcodeSettings = 'TRUE')
					BEGIN
				--REPLACE( REPLACE( REPLACE ([Cat No], '-', ''), '.', ''), ' ', '' )
						IF ( SELECT 1 FROM s01qry703WebConsoleProductCatalog WHERE [Cat No] = @CatNo And LocationTypeID = @LocationTypeID And LocationID = @LocationID ) = 1 
						BEGIN
					
							INSERT INTO s01tblItemUsage ( MAPatientID, MAVisitID, MAScheduleID, UsedBy, DateUsed, Qty, UnitPrice, UOMCode, BillingStatusID, OverrideID, OverrideNotes
													, UpdatedBy, LastActivityDate, UsageTypeID, UsageItemID, SessionID, EntryTypeID )
							SELECT @MAPatientID, @MAVisitID, @MAScheduleID, @UserID, @CurrentUtcTime, @inputQty, [Ref Price], UOMCode, 1, @OverrideID, @OverrideNotes, @UserID
													, @CurrentUTCTime, 2, @ItemInventoryID, @SessionID, @EntryTypeID 
							FROM s01tblProducts 
							WHERE ProductID = @ProductID

							SELECT @ItemUsageID = SCOPE_IDENTITY()
							
							SELECT @UsageItemID = UsageItemID FROM s01tblItemUsage 
							WHERE ItemUsageID = @ItemUsageID

							UPDATE s01tblItemInventory 
							SET Qty = Qty - @inputQty, UpdatedBy = @UserID, QtyLastUpdated = @CurrentUTCTime 
							WHERE ItemInventoryID = @UsageItemID

							INSERT INTO s01tblUpdateBarcodeItemInventoryLog( ItemInventoryID, QtyInHand, QtyLastUpdated, QtyUsed 
													, QtyReturned, QtyDeleted, QtyStocked, QtyWasted, CutOffTime, UpdatedQtyInHand )
							SELECT ItemInventoryID, Qty + @inputQty, QtyLastUpdated, @inputQty, 0, 0, 0, 0, @CurrentUTCTime, Qty 
							FROM s01tblItemInventory
							WHERE ItemInventoryID = @UsageItemID

							INSERT INTO s01tblItemHistory ( ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAVisitID, MAScheduleID, UsedBy, DateUsed, UnitPrice, UOMCode
												, OverrideID, OverrideNotes, SessionID, UCDM, BillingStatusID, ItemStatusID, UpdatedBy, LastActivityDate, ItemEventID, Qty, EntryTypeID ) 
							SELECT ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAVisitID, MAScheduleID, UsedBy, DateUsed, UnitPrice, UOMCode, @OverrideID, @OverrideNotes, @SessionID 
												, UCDM, BillingStatusID, NULL, @UserID, @CurrentUTCTime, @ItemEventID, @inputQty, @EntryTypeID 
							FROM s01tblItemUsage 
							WHERE ItemUsageID = @ItemUsageID

							SELECT @ItemHistoryID = SCOPE_IDENTITY()
							INSERT INTO s01tblItemHistoryExtn (ItemHistoryID, LocationID ) 
							SELECT ItemHistoryID, @LocationID
							FROM s01tblItemHistory 
							WHERE ItemHistoryID = @ItemHistoryID

							INSERT INTO MA_SUP_TJUHOR_IRISUPPLYDB..tblInventoryLog ( ItemHistoryID, InvntoryID, ProcessStatus, ClusterID, LocationID, UpdatedBy, EntryTypeID, EnteredDate )
							SELECT ItemHistoryID, UsageItemID, 'P', @DeviceID, @LocationID, @UserID, @EntryTypeID, @CurrentUTCTime 
							FROM s01tblItemHistory 
							WHERE ItemHistoryID = @ItemHistoryID	
						END
					END
					IF ( @BarcodeSettings = 'FALSE')
					BEGIN
						IF ( @CurrentPatientID = @MAPatientID ) AND ( @MAPatientID IS NOT NULL )
						BEGIN
						
							IF EXISTS ( SELECT 1 
										FROM s01tblItemInventory I 
											INNER JOIN s01tblProducts P ON I.ProductID = P.ProductID 
											INNER JOIN s01tblItemUsage U ON I.ItemInventoryID = U.UsageItemID
										WHERE I.ProductID = @ProductID 
											AND I.LocationID = @LocationID 
											AND U.SessionID = @SessionID 
											AND U.MAPatientID = @MAPatientID)
							BEGIN
							
								SELECT @QTY = U.Qty+@inputQty, @ItemInventoryID = I.ItemInventoryID  
								FROM s01tblItemUsage U 
									INNER JOIN s01tblItemInventory I ON I.ItemInventoryID = U.UsageItemID 
								WHERE U.SessionID = @SessionID 
									AND I.ProductID = @ProductID 
									AND MAPatientID = @MAPatientID
								
								UPDATE s01tblItemUsage 
								SET Qty = @QTY, UpdatedBy = @UserID, LastActivityDate = @CurrentUTCTime, OverrideID = @OverrideID, OverrideNotes = @OverrideNotes
								WHERE UsageItemID = @ItemInventoryID 
									AND SessionID = @SessionID	
									AND MAPatientID = @MAPatientID 
									AND UsageTypeID = 2
							
								UPDATE s01tblItemInventory 
								SET Qty = Qty - @inputQty, UpdatedBy = @UserID, QtyLastUpdated = @CurrentUTCTime 
								WHERE ItemInventoryID = @ItemInventoryID 

								INSERT INTO s01tblUpdateBarcodeItemInventoryLog( ItemInventoryID, QtyInHand, QtyLastUpdated, QtyUsed 
														, QtyReturned, QtyDeleted, QtyStocked, QtyWasted, CutOffTime, UpdatedQtyInHand )
								SELECT ItemInventoryID, Qty + @inputQty, QtyLastUpdated, @inputQty, 0, 0, 0, 0, @CurrentUTCTime, Qty 
								FROM s01tblItemInventory
								WHERE ItemInventoryID = @ItemInventoryID

								SELECT @ItemUsageID = ItemUsageID 
								FROM s01tblItemUsage 
								WHERE UsageItemID = @ItemInventoryID 
									AND SessionID = @SessionID

								INSERT INTO s01tblItemHistory ( ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAVisitID, MAScheduleID, UsedBy, DateUsed, UnitPrice, UOMCode
													, OverrideID, OverrideNotes, SessionID, UCDM, BillingStatusID, ItemStatusID, UpdatedBy, LastActivityDate, ItemEventID, Qty, EntryTypeID ) 
								SELECT ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAVisitID, MAScheduleID, UsedBy, DateUsed, UnitPrice, UOMCode, @OverrideID, @OverrideNotes, @SessionID 
													, UCDM, BillingStatusID, NULL, @UserID, @CurrentUTCTime, @ItemEventID, @inputQty, @EntryTypeID 
								FROM s01tblItemUsage 
								WHERE ItemUsageID = @ItemUsageID
					
								SELECT @ItemHistoryID = SCOPE_IDENTITY()
								INSERT INTO s01tblItemHistoryExtn (ItemHistoryID, LocationID ) 
								SELECT ItemHistoryID, @LocationID 
								FROM s01tblItemHistory 
								WHERE ItemHistoryID = @ItemHistoryID

								INSERT INTO MA_SUP_TJUHOR_IRISUPPLYDB..tblInventoryLog ( ItemHistoryID, InvntoryID, ProcessStatus, ClusterID, LocationID, UpdatedBy, EntryTypeID, EnteredDate )
								SELECT ItemHistoryID, UsageItemID, 'P', @DeviceID, @LocationID, @UserID, @EntryTypeID, @CurrentUTCTime 
								FROM s01tblItemHistory 
								WHERE ItemHistoryID = @ItemHistoryID	
							END
							IF NOT EXISTS ( SELECT 1 
											FROM s01tblItemInventory I 
												INNER JOIN s01tblProducts P ON I.ProductID = P.ProductID 
												INNER JOIN s01tblItemUsage U ON I.ItemInventoryID = U.UsageItemID
										    WHERE I.ProductID = @ProductID 
												AND I.LocationID = @LocationID 
												AND U.SessionID = @SessionID 
												AND U.MAPatientid = @MAPatientID ) 				
							BEGIN

								IF  EXISTS ( 
												 SELECT 1 
												 FROM s01tblItemInventory I 
													INNER JOIN s01tblProducts P ON I.ProductID = P.ProductID 
													INNER JOIN s01tblItemUsage U ON I.ItemInventoryID = U.UsageItemID
												 WHERE I.ProductID = @ProductID 
													AND I.LocationID = @LocationID 
													AND U.MAPatientID = @MAPatientID
													AND U.MAScheduleID = @MAScheduleID
											 )
								BEGIN

									SELECT @QTY = U.Qty+@inputQty, @ItemInventoryID = I.ItemInventoryID  
									FROM s01tblItemUsage U 
										INNER JOIN s01tblItemInventory I ON I.ItemInventoryID = U.UsageItemID 
									WHERE I.ProductID = @ProductID 
										AND MAPatientID = @MAPatientID
										AND U.MAScheduleID = @MAScheduleID

									UPDATE s01tblItemUsage 
									SET Qty = @Qty, UpdatedBy = @UserID, LastActivityDate = @CurrentUTCTime, OverrideID = @OverrideID, OverrideNotes = @OverrideNotes
									WHERE UsageItemID = @ItemInventoryID 
										AND MAPatientID = @MAPatientID 
										AND UsageTypeID = 2

											

								--SELECT @ItemUsageID = SCOPE_IDENTITY()
								SELECT @ItemUsageID = ItemUsageID
								FROM s01tblItemUsage 
								WHERE  UsageItemID = @ItemInventoryID 
										AND MAPatientID = @MAPatientID 
										AND UsageTypeID = 2
								SELECT @UsageItemID = @ItemInventoryID
								--FROM s01tblItemUsage 
								--WHERE ItemUsageID = @ItemUsageID

								UPDATE s01tblItemInventory 
								SET Qty = Qty - @inputQty, UpdatedBy = @UserID, QtyLastUpdated = @CurrentUTCTime 
								WHERE ItemInventoryID = @UsageItemID 
								
								INSERT INTO s01tblUpdateBarcodeItemInventoryLog( ItemInventoryID, QtyInHand, QtyLastUpdated, QtyUsed 
														, QtyReturned, QtyDeleted, QtyStocked, QtyWasted, CutOffTime, UpdatedQtyInHand )
								SELECT ItemInventoryID, Qty + @inputQty, QtyLastUpdated, @inputQty, 0, 0, 0, 0, @CurrentUTCTime, Qty 
								FROM s01tblItemInventory
								WHERE ItemInventoryID = @UsageItemID

								INSERT INTO s01tblItemHistory ( ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAVisitID, MAScheduleID, UsedBy, DateUsed, UnitPrice, UOMCode
													, OverrideID, OverrideNotes, SessionID, UCDM, BillingStatusID, ItemStatusID, UpdatedBy, LastActivityDate, ItemEventID, Qty, EntryTypeID ) 
								SELECT ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAVisitID, MAScheduleID, UsedBy, DateUsed, UnitPrice, UOMCode, @OverrideID, @OverrideNotes, @SessionID 
													, UCDM, BillingStatusID, NULL, @UserID, @CurrentUTCTime, @ItemEventID, @inputQty, @EntryTypeID 
								FROM s01tblItemUsage 
								WHERE ItemUsageID = @ItemUsageID

								SELECT @ItemHistoryID = SCOPE_IDENTITY()
								INSERT INTO s01tblItemHistoryExtn (ItemHistoryID, LocationID ) 
								SELECT ItemHistoryID, @LocationID 
								FROM s01tblItemHistory 
								WHERE ItemHistoryID = @ItemHistoryID

								INSERT INTO MA_SUP_TJUHOR_IRISUPPLYDB..tblInventoryLog ( ItemHistoryID, InvntoryID, ProcessStatus, ClusterID, LocationID, UpdatedBy, EntryTypeID, EnteredDate )
								SELECT ItemHistoryID, UsageItemID, 'P', @DeviceID, @LocationID, @UserID, @EntryTypeID, @CurrentUTCTime 
								FROM s01tblItemHistory 
								WHERE ItemHistoryID = @ItemHistoryID

								END
								ELSE
								BEGIN
									INSERT INTO s01tblItemUsage ( MAPatientID, MAVisitID, MAScheduleID, UsedBy, DateUsed, Qty, UnitPrice, UOMCode, BillingStatusID, OverrideID, OverrideNotes
															, UpdatedBy, LastActivityDate, UsageTypeID, UsageItemID, SessionID, EntryTypeID )
									SELECT @MAPatientID, @MAVisitID, @MAScheduleID, @UserID, @CurrentUtcTime, @inputQty, [Ref Price], UOMCode, 1, @OverrideID, @OverrideNotes, @UserID
															, @CurrentUTCTime, 2, @ItemInventoryID, @SessionID, @EntryTypeID 
									FROM s01tblProducts 
									WHERE ProductID = @ProductID

								

								SELECT @ItemUsageID = SCOPE_IDENTITY()
								SELECT @UsageItemID = UsageItemID 
								FROM s01tblItemUsage 
								WHERE ItemUsageID = @ItemUsageID

								UPDATE s01tblItemInventory 
								SET Qty = Qty - @inputQty, UpdatedBy = @UserID, QtyLastUpdated = @CurrentUTCTime 
								WHERE ItemInventoryID = @UsageItemID 
								
								INSERT INTO s01tblUpdateBarcodeItemInventoryLog( ItemInventoryID, QtyInHand, QtyLastUpdated, QtyUsed 
														, QtyReturned, QtyDeleted, QtyStocked, QtyWasted, CutOffTime, UpdatedQtyInHand )
								SELECT ItemInventoryID, Qty + @inputQty, QtyLastUpdated, @inputQty, 0, 0, 0, 0, @CurrentUTCTime, Qty 
								FROM s01tblItemInventory
								WHERE ItemInventoryID = @UsageItemID

								INSERT INTO s01tblItemHistory ( ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAVisitID, MAScheduleID, UsedBy, DateUsed, UnitPrice, UOMCode
													, OverrideID, OverrideNotes, SessionID, UCDM, BillingStatusID, ItemStatusID, UpdatedBy, LastActivityDate, ItemEventID, Qty, EntryTypeID ) 
								SELECT ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAVisitID, MAScheduleID, UsedBy, DateUsed, UnitPrice, UOMCode, @OverrideID, @OverrideNotes, @SessionID 
													, UCDM, BillingStatusID, NULL, @UserID, @CurrentUTCTime, @ItemEventID, @inputQty, @EntryTypeID 
								FROM s01tblItemUsage 
								WHERE ItemUsageID = @ItemUsageID

								SELECT @ItemHistoryID = SCOPE_IDENTITY()
								INSERT INTO s01tblItemHistoryExtn (ItemHistoryID, LocationID ) 
								SELECT ItemHistoryID, @LocationID 
								FROM s01tblItemHistory 
								WHERE ItemHistoryID = @ItemHistoryID

								INSERT INTO MA_SUP_TJUHOR_IRISUPPLYDB..tblInventoryLog ( ItemHistoryID, InvntoryID, ProcessStatus, ClusterID, LocationID, UpdatedBy, EntryTypeID, EnteredDate )
								SELECT ItemHistoryID, UsageItemID, 'P', @DeviceID, @LocationID, @UserID, @EntryTypeID, @CurrentUTCTime 
								FROM s01tblItemHistory 
								WHERE ItemHistoryID = @ItemHistoryID
								END
							END
						END
						IF ( @CurrentPatientID IS NULL AND @MAPatientID IS NOT NULL )
						BEGIN
							--IF EXISTS ( SELECT 1 
							--			FROM s01tblItemInventory I 
							--				INNER JOIN s01tblProducts P ON I.ProductID = P.ProductID 
							--				INNER JOIN s01tblItemUsage U ON I.ItemInventoryID = U.UsageItemID
							--			WHERE I.ProductID = @ProductID 
							--				AND I.LocationID = @LocationID 
							--				AND U.SessionID = @SessionID 
							--				AND U.MAPatientid = @MAPatientID )
							--BEGIN
							--	SELECT @QTY = U.Qty+1, @ItemInventoryID = I.ItemInventoryID  
							--	FROM s01tblItemUsage U 
							--		INNER JOIN s01tblItemInventory I ON I.ItemInventoryID = U.UsageItemID 
							--	WHERE U.SessionID = @SessionID 
							--		AND I.ProductID = @ProductID

							--	UPDATE s01tblItemUsage 
							--	SET Qty = @Qty, UpdatedBy = @UserID, LastActivityDate = @CurrentUTCTime, OverrideID = @OverrideID, OverrideNotes = @OverrideNotes
							--	WHERE UsageItemID = @ItemInventoryID 
							--		AND SessionID = @SessionID	
							
							--	UPDATE s01tblItemInventory 
							--	SET Qty = Qty - 1, UpdatedBy = @UserID, QtyLastUpdated = @CurrentUTCTime 
							--	WHERE ItemInventoryID = @ItemInventoryID 

							--	INSERT INTO tblUpdateBarcodeItemInventoryLog( ItemInventoryID, QtyInHand, QtyLastUpdated, QtyUsed 
							--							, QtyReturned, QtyDeleted, QtyStocked, QtyWasted, CutOffTime, UpdatedQtyInHand )
							--	SELECT ItemInventoryID, Qty + 1, QtyLastUpdated, 1, 0, 0, 0, 0, @CurrentUTCTime, Qty 
							--	FROM s01tblItemInventory
							--	WHERE ItemInventoryID = @ItemInventoryID

							--	SELECT @ItemUsageID = ItemUsageID 
							--	FROM s01tblItemUsage 
							--	WHERE UsageItemID = @ItemInventoryID 
							--		AND SessionID = @SessionID

							--	INSERT INTO s01tblItemHistory ( ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAVisitID, MAScheduleID, UsedBy, DateUsed, UnitPrice, UOMCode
							--						, OverrideID, OverrideNotes, SessionID, UCDM, BillingStatusID, ItemStatusID, UpdatedBy, LastActivityDate, ItemEventID, Qty, EntryTypeID ) 
							--	SELECT ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAVisitID, MAScheduleID, UsedBy, DateUsed, UnitPrice, UOMCode, @OverrideID, @OverrideNotes, @SessionID 
							--						, UCDM, BillingStatusID, NULL, @UserID, @CurrentUTCTime, @ItemEventID, 1, @EntryTypeID 
							--	FROM s01tblItemUsage 
							--	WHERE ItemUsageID = @ItemUsageID
					
							--	SELECT @ItemHistoryID = SCOPE_IDENTITY()
							--	INSERT INTO s01tblItemHistoryExtn (ItemHistoryID, LocationID ) 
							--	SELECT ItemHistoryID, @LocationID 
							--	FROM s01tblItemHistory 
							--	WHERE ItemHistoryID = @ItemHistoryID

							--	INSERT INTO tblInventoryLog ( ItemHistoryID, InvntoryID, ProcessStatus, ClusterID, LocationID, UpdatedBy, EntryTypeID, EnteredDate )
							--	SELECT ItemHistoryID, UsageItemID, 'P', @DeviceID, @LocationID, @UserID, @EntryTypeID, @CurrentUTCTime 
							--	FROM s01tblItemHistory 
							--	WHERE ItemHistoryID = @ItemHistoryID	
							--END
							--IF NOT EXISTS ( SELECT 1 
							--				FROM s01tblItemInventory I 
							--					INNER JOIN s01tblProducts P ON I.ProductID = P.ProductID 
							--					INNER JOIN s01tblItemUsage U ON I.ItemInventoryID = U.UsageItemID
							--			    WHERE I.ProductID = @ProductID 
							--					AND I.LocationID = @LocationID 
							--					AND U.SessionID = @SessionID 
							--					AND U.MAPatientid = @MAPatientID ) 				
							--BEGIN
								INSERT INTO s01tblItemUsage ( MAPatientID, MAVisitID, MAScheduleID, UsedBy, DateUsed, Qty, UnitPrice, UOMCode, BillingStatusID, OverrideID, OverrideNotes
														, UpdatedBy, LastActivityDate, UsageTypeID, UsageItemID, SessionID, EntryTypeID )
								SELECT @MAPatientID, @MAVisitID, @MAScheduleID, @UserID, @CurrentUtcTime, @inputQty, [Ref Price], UOMCode, 1, @OverrideID, @OverrideNotes, @UserID
														, @CurrentUTCTime, 2, @ItemInventoryID, @SessionID, @EntryTypeID 
								FROM s01tblProducts 
								WHERE ProductID = @ProductID

								SELECT @ItemUsageID = SCOPE_IDENTITY()
								SELECT @UsageItemID = UsageItemID 
								FROM s01tblItemUsage 
								WHERE ItemUsageID = @ItemUsageID
							
								UPDATE s01tblItemInventory 
								SET Qty = Qty - @inputQty, UpdatedBy = @UserID, QtyLastUpdated = @CurrentUTCTime 
								WHERE ItemInventoryID = @UsageItemID 

								INSERT INTO s01tblUpdateBarcodeItemInventoryLog( ItemInventoryID, QtyInHand, QtyLastUpdated, QtyUsed 
														, QtyReturned, QtyDeleted, QtyStocked, QtyWasted, CutOffTime, UpdatedQtyInHand )
								SELECT ItemInventoryID, Qty + @inputQty, QtyLastUpdated, @inputQty, 0, 0, 0, 0, @CurrentUTCTime, Qty 
								FROM s01tblItemInventory
								WHERE ItemInventoryID = @UsageItemID

								INSERT INTO s01tblItemHistory ( ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAVisitID, MAScheduleID, UsedBy, DateUsed, UnitPrice, UOMCode
													, OverrideID, OverrideNotes, SessionID, UCDM, BillingStatusID, ItemStatusID, UpdatedBy, LastActivityDate, ItemEventID, Qty, EntryTypeID ) 
								SELECT ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAVisitID, MAScheduleID, UsedBy, DateUsed, UnitPrice, UOMCode, @OverrideID, @OverrideNotes, @SessionID 
													, UCDM, BillingStatusID, NULL, @UserID, @CurrentUTCTime, @ItemEventID, @inputQty, @EntryTypeID 
								FROM s01tblItemUsage 
								WHERE ItemUsageID = @ItemUsageID

								SELECT @ItemHistoryID = SCOPE_IDENTITY()
								INSERT INTO s01tblItemHistoryExtn (ItemHistoryID, LocationID ) 
								SELECT ItemHistoryID, @LocationID 
								FROM s01tblItemHistory 
								WHERE ItemHistoryID = @ItemHistoryID

								INSERT INTO MA_SUP_TJUHOR_IRISUPPLYDB..tblInventoryLog ( ItemHistoryID, InvntoryID, ProcessStatus, ClusterID, LocationID, UpdatedBy, EntryTypeID, EnteredDate )
								SELECT ItemHistoryID, UsageItemID, 'P', @DeviceID, @LocationID, @UserID, @EntryTypeID, @CurrentUTCTime 
								FROM s01tblItemHistory 
								WHERE ItemHistoryID = @ItemHistoryID	
							--END
						END

						IF ( @MAPatientID IS NULL )
						BEGIN
							PRINT 'FALSE'
							IF EXISTS ( SELECT 1 
										FROM s01tblItemInventory I 
											INNER JOIN s01tblProducts P ON I.ProductID = P.ProductID 
											INNER JOIN s01tblItemUsage U ON I.ItemInventoryID = U.UsageItemID
										WHERE I.ProductID = @ProductID 
											AND I.LocationID = @LocationID 
											AND U.SessionID = @SessionID 
											AND MAPatientID IS NULL )
							BEGIN
								SELECT @QTY = U.Qty+1, @ItemInventoryID = I.ItemInventoryID  
								FROM s01tblItemUsage U 
									INNER JOIN s01tblItemInventory I ON I.ItemInventoryID = U.UsageItemID 
								WHERE U.SessionID = @SessionID 
									AND I.ProductID = @ProductID

								UPDATE s01tblItemUsage 
								SET Qty = @Qty, UpdatedBy = @UserID, LastActivityDate = @CurrentUTCTime, OverrideID = @OverrideID, OverrideNotes = @OverrideNotes
								WHERE UsageItemID = @ItemInventoryID AND SessionID = @SessionID	AND MAPatientID IS NULL 
							
								UPDATE s01tblItemInventory 
								SET Qty = Qty - @inputQty, UpdatedBy = @UserID, QtyLastUpdated = @CurrentUTCTime 
								WHERE ItemInventoryID = @ItemInventoryID 

								INSERT INTO s01tblUpdateBarcodeItemInventoryLog( ItemInventoryID, QtyInHand, QtyLastUpdated, QtyUsed 
														, QtyReturned, QtyDeleted, QtyStocked, QtyWasted, CutOffTime, UpdatedQtyInHand )
								SELECT ItemInventoryID, Qty + @inputQty, QtyLastUpdated, @inputQty, 0, 0, 0, 0, @CurrentUTCTime, Qty 
								FROM s01tblItemInventory
								WHERE ItemInventoryID = @ItemInventoryID

								SELECT @ItemUsageID = ItemUsageID 
								FROM s01tblItemUsage 
								WHERE UsageItemID = @ItemInventoryID AND SessionID = @SessionID

								INSERT INTO s01tblItemHistory ( ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAVisitID, MAScheduleID, UsedBy, DateUsed, UnitPrice, UOMCode
													, OverrideID, OverrideNotes, SessionID, UCDM, BillingStatusID, ItemStatusID, UpdatedBy, LastActivityDate, ItemEventID, Qty, EntryTypeID ) 
								SELECT ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAVisitID, MAScheduleID, UsedBy, DateUsed, UnitPrice, UOMCode, @OverrideID, @OverrideNotes, @SessionID 
													, UCDM, BillingStatusID, NULL, @UserID, @CurrentUTCTime, @ItemEventID, @inputQty, @EntryTypeID 
								FROM s01tblItemUsage 
								WHERE ItemUsageID = @ItemUsageID
					
								SELECT @ItemHistoryID = SCOPE_IDENTITY()
								INSERT INTO s01tblItemHistoryExtn (ItemHistoryID, LocationID ) 
								SELECT ItemHistoryID, @LocationID 
								FROM s01tblItemHistory 
								WHERE ItemHistoryID = @ItemHistoryID

								INSERT INTO MA_SUP_TJUHOR_IRISUPPLYDB..tblInventoryLog ( ItemHistoryID, InvntoryID, ProcessStatus, ClusterID, LocationID, UpdatedBy, EntryTypeID, EnteredDate )
								SELECT ItemHistoryID, UsageItemID, 'P', @DeviceID, @LocationID, @UserID, @EntryTypeID, @CurrentUTCTime 
								FROM s01tblItemHistory 
								WHERE ItemHistoryID = @ItemHistoryID	
							END
							IF NOT EXISTS ( SELECT 1 
											FROM s01tblItemInventory I 
												INNER JOIN s01tblProducts P ON I.ProductID = P.ProductID 
												INNER JOIN s01tblItemUsage U ON I.ItemInventoryID = U.UsageItemID
											WHERE I.ProductID = @ProductID 
												AND I.LocationID = @LocationID 
												AND U.SessionID = @SessionID 
												AND MAPatientID IS NULL ) 				
							BEGIN
								INSERT INTO s01tblItemUsage ( MAPatientID, MAVisitID, MAScheduleID, UsedBy, DateUsed, Qty, UnitPrice, UOMCode, BillingStatusID, OverrideID, OverrideNotes
														, UpdatedBy, LastActivityDate, UsageTypeID, UsageItemID, SessionID, EntryTypeID )
								SELECT @MAPatientID, @MAVisitID, @MAScheduleID, @UserID, @CurrentUtcTime, 1, [Ref Price], UOMCode, 1, @OverrideID, @OverrideNotes, @UserID
														, @CurrentUTCTime, 2, @ItemInventoryID, @SessionID, @EntryTypeID 
								FROM s01tblProducts 
								WHERE ProductID = @ProductID

								SELECT @ItemUsageID = SCOPE_IDENTITY()

								SELECT @UsageItemID = UsageItemID 
								FROM s01tblItemUsage 
								WHERE ItemUsageID = @ItemUsageID

								UPDATE s01tblItemInventory 
								SET Qty = Qty - 1, UpdatedBy = @UserID, QtyLastUpdated = @CurrentUTCTime 
								WHERE ItemInventoryID = @UsageItemID 

								INSERT INTO s01tblUpdateBarcodeItemInventoryLog( ItemInventoryID, QtyInHand, QtyLastUpdated, QtyUsed 
														, QtyReturned, QtyDeleted, QtyStocked, QtyWasted, CutOffTime, UpdatedQtyInHand )
								SELECT ItemInventoryID, Qty + 1, QtyLastUpdated, 1, 0, 0, 0, 0, @CurrentUTCTime, Qty 
								FROM s01tblItemInventory
								WHERE ItemInventoryID = @UsageItemID

								INSERT INTO s01tblItemHistory ( ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAVisitID, MAScheduleID, UsedBy, DateUsed, UnitPrice, UOMCode
													, OverrideID, OverrideNotes, SessionID, UCDM, BillingStatusID, ItemStatusID, UpdatedBy, LastActivityDate, ItemEventID, Qty, EntryTypeID ) 
								SELECT ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAVisitID, MAScheduleID, UsedBy, DateUsed, UnitPrice, UOMCode, @OverrideID, @OverrideNotes, @SessionID 
													, UCDM, BillingStatusID, NULL, @UserID, @CurrentUTCTime, @ItemEventID, 1, @EntryTypeID 
								FROM s01tblItemUsage 
								WHERE ItemUsageID = @ItemUsageID

								SELECT @ItemHistoryID = SCOPE_IDENTITY()
								INSERT INTO s01tblItemHistoryExtn (ItemHistoryID, LocationID ) 
								SELECT ItemHistoryID, @LocationID 
								FROM s01tblItemHistory 
								WHERE ItemHistoryID = @ItemHistoryID

								INSERT INTO MA_SUP_TJUHOR_IRISUPPLYDB..tblInventoryLog ( ItemHistoryID, InvntoryID, ProcessStatus, ClusterID, LocationID, UpdatedBy, EntryTypeID, EnteredDate )
								SELECT ItemHistoryID, UsageItemID, 'P', @DeviceID, @LocationID, @UserID, @EntryTypeID, @CurrentUTCTime 
								FROM s01tblItemHistory 
								WHERE ItemHistoryID = @ItemHistoryID	
							END
						END
					END

					--EXECUTE  usp705WebConsoleAppCabEventLogs @Event = @Event
					--	, @udtSystemData = @udtSystemData
					--	, @udtEventData = @udtEventData
				END

				IF ( @EventType = 'ReturnBarcodeInventory' )
				BEGIN

					--PRINT 'Barcode Return'

					if exists (select top 1 * from s01tblitemusage where usageitemid=@UsageItemID)
					begin
						set @IsUsageExists='Available';
					end
	
					IF @IsUsageExists IS NULL
					BEGIN
						SELECT 'Fail' AS DataSetName, 'Item already stocked in cabinet.' AS MSG
					RETURN
					END

					EXECUTE  usp827WebConsoleReturnBarcodeItems @Event = @Event
							, @udtSystemData = @udtSystemData
							, @udtEventData = @udtEventData
							, @ProductID = @ProductID
							, @ItemEventID = @ItemEventID
							, @OverrideID = @OverrideID
		
				END
				

			--COMMIT TRANSACTION	
	END TRY
	BEGIN CATCH
		--IF XACT_STATE() <> 0
			--ROLLBACK TRANSACTION
			SET @Msg = 'Error in updating Barcode Inventory';
		EXECUTE uspLogError
	END CATCH
	SET NOCOUNT OFF
GO
/****** Object:  StoredProcedure [dbo].[usp826WebConsoleSaveBarcodeMasterFDAProducts]    Script Date: 11/21/2024 2:16:28 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


 

/************************************************************************************************************************
Name				: usp826WebConsoleSaveBarcodeMasterFDAProducts
Version				: ********
Purpose				: To save the product details from MASTER/FDA to Localhost for Barcode clusters
Author				: Subramanya Rao
Date Created 		: 24th march 2021
Application Affected: IriSupply 6.0
---------------------------------------------------------------------------------------------------------------------------
Release Notes		:

 

----------------------------------------------------------------------------------------------------------------------------
-- SP CALL
declare @p2 dbo.udtSPParam
insert into @p2 values(N'EntryTypeID',N'1')
insert into @p2 values(N'DeviceID',N'3')
insert into @p2 values(N'ComputerName',N'MAILPTP52')
insert into @p2 values(N'Domain',N'maspects.com')

 

declare @p3 dbo.udtSPParam
insert into @p3 values(N'UserID',N'2')
insert into @p3 values(N'SessionID',N'20210325120110970')
insert into @p3 values(N'EventType',N'RemoveBarcodeInventory')
insert into @p3 values(N'EventInPut1',N'bbaasss240ENSP35015UX') -- Scanned Full barcode
insert into @p3 values(N'EventInput3',N'Remove Item')
insert into @p3 values(N'EventInput4',N'TTT')

 

--If DataSetName = Fail and MSG is	Product is not registered then send below 4 parameters
--insert into @p3 values(N'CatNo',N'sscbazabcabababxxabc')
--insert into @p3 values(N'Description',N'test')
--insert into @p3 values(N'Manufacturer',N'test')
--insert into @p3 values(N'BrandName',N'test')

 

insert into @p3 values(N'EventOutPut',N'FetchBarcodeInventoryList')

 

exec usp821WebConsoleExecuteAppEvent @Event='UpdateBarcodeInventory',@udtSystemData=@p2,@udtEventData=@p3

 

 

--------------------------------------------------------------------------------------------------------*/
CREATE Procedure [dbo].[usp826WebConsoleSaveBarcodeMasterFDAProducts]
	@Event VARCHAR(255)
	, @udtSystemData udtSPParam READONLY
	, @udtEventData udtSPParam READONLY
AS

 

SET NOCOUNT ON;

 

DECLARE @Msg VARCHAR(200), @ProcessedBarcondeNo VARCHAR(250),  @FullBarcodeNo VARCHAR(250), @ProcessedCatNo VARCHAR (500), @SessionID BIGINT, @ItemStatusID INT, @ItemEventID INT
DECLARE @CatNo VARCHAR (200), @ProductId INT, @ItemID INT, @RemovedUserName VARCHAR(100), @RFID VARCHAR (500), @UserID INT, @CurrentUtcTime DATETIME, @ItemUsageID INT, @ItemHistoryID INT
DECLARE @MAPatientID VARCHAR(50), @MAScheduleID INT, @MAVisitID INT, @OverrideId INT, @OverrideNotes VARCHAR(255), @PhysicianID VARCHAR(50), @BillingStatusID INT, @EntryTypeID INT
		, @InsertFDAProduct INT, @InsertBracodeFDA INT,  @Description VARCHAR(50), @Manufacturer VARCHAR(50), @BrandName VARCHAR(50), @MastProductId INT
		, @ScannedBarcode VARCHAR(70), @MCatNo VARCHAR(50)

 

SET XACT_ABORT ON;
BEGIN TRY
BEGIN TRANSACTION

 

	--SELECT @ProcessedBarcodeNo =Value FROM  @udtEventData WHERE Property = 'ProcessedBarcodeNo'
	--SELECT @FullBarcodeNo =Value FROM  @udtEventData WHERE Property = 'FullBarcodeNo'
	--SELECT @ProcessedCatNo =Value FROM  @udtEventData WHERE Property = 'ProcessedCatNo'

	SELECT @ScannedBarcode =Value FROM  @udtEventData WHERE Property = 'EventInPut1'

 

	--SELECT @CatNo = REPLACE( REPLACE( REPLACE ([Cat No], '-', ''), '.', ''), ' ', '' ) 
	--FROM [$(IrisDB)].dbo.tblBarcodeRuleProduct 
	--WHERE [Barcode No] = @ScannedBarcode

 

	SELECT @CatNo =Value FROM  @udtEventData WHERE Property = 'CatNo'
	SELECT @Description =  Value FROM  @udtEventData WHERE Property = 'Description'	
	SELECT @Manufacturer =  Value FROM  @udtEventData WHERE Property = 'Manufacturer'	
	SELECT @BrandName =  Value FROM  @udtEventData WHERE Property = 'BrandName'

	--IF @CatNo IS NULL 
	--BEGIN
	--	IF EXISTS ( SELECT 1 FROM [iRISProductMaster].dbo.tblBarcodeRuleProductMaster WHERE [Barcode No] = @ScannedBarcode)
	--	BEGIN

 

	--			SELECT @MCatNo = [Cat No]
	--			FROM [iRISProductMaster].dbo.tblBarcodeRuleProductMaster
	--			WHERE [Barcode No] = @ScannedBarcode

 

	--			INSERT INTO [IrisDB].dbo.tblBarcodeRuleProduct ([Barcode No], [Cat No])
	--			SELECT [Barcode No], [Cat No]
	--			FROM [iRISProductMaster].dbo.tblBarcodeRuleProductMaster
	--			WHERE [Barcode No] = @ScannedBarcode

 

	--			PRINT 'Inserted into [IrisDB].dbo.tblBarcodeRuleProduct from MASTER'

 

	--	END

 

	--	IF EXISTS ( SELECT 1 FROM [iRISProductMaster].dbo.tblProductsMaster WHERE replace(replace([Cat No],'-',''),'.','') = replace(replace(@MCatNo,'-',''),'.',''))
	--	BEGIN

 

	--			INSERT INTO [IrisDB].dbo.tblProducts 
	--						( [Cat No], NDC, [Brand Name], [Other Name], [Description 1], [Description 2], [Description 3], Size, Material, Manufacturer, [Ref Price], Picture, QtyPerPkg
	--						, RFID, [SKU No],[ID 1], [ID 2], [ID 3], [ID 4], [ID 5], [Entered By], [Computer Name], Domain, [Entered Date], ReorderTypeID, [Notify Interval]
	--						, [Notify Time], [Notify Sent], ProductTypeID, PCDM, MMID, ProductCategoryID, ProductSubCategoryID, ProductGroupID, EntryTypeID
	--						, [Min Stock], [Max Stock])

 

	--			SELECT        [Cat No], NDC, [Brand Name], [Other Name], [Description 1], [Description 2], [Description 3], Size, Material, Manufacturer, [Ref Price], Picture, QtyPerPkg
	--						, RFID, [SKU No],[ID 1], [ID 2], [ID 3], [ID 4], [ID 5], [Entered By], [Computer Name], Domain, [Entered Date], ReorderTypeID, [Notify Interval]
	--						, [Notify Time], [Notify Sent], ProductTypeID, PCDM, MMID, ProductCategoryID, ProductSubCategoryID, ProductGroupID, EntryTypeID
	--						, [Min Stock], [Max Stock]
	--			FROM [iRISProductMaster].dbo.tblProductsMaster
	--			WHERE [Cat No] = @MCatNo

 

	--			PRINT 'Inserted into [IrisDB].dbo.tblProducts from MASTER'

 

	--	END
	--END

 

	IF ( @CatNo IS NOT NULL)
	BEGIN
		 --   INSERT INTO [iRISProductMaster].dbo.tblProductsMaster  
			--				( ProductID, [Cat No], [Brand Name], Manufacturer, [Description 1], IsFromFDA ) 
		 --   SELECT MAX(ProductID)+1, @CatNo, @BrandName, @Manufacturer, @Description, 1
		 --   FROM [iRISProductMaster].dbo.tblProductsMaster

 

			--SELECT @MastProductId = ProductId 
			--FROM [iRISProductMaster].dbo.s01tblProductsMaster 
			--WHERE replace(replace([Cat No],'-',''),'.','') = @CatNo


 

			INSERT INTO s01tblProducts
						(  [Cat No], [Brand Name], Manufacturer, [Description 1] ) 
			SELECT  @CatNo, @BrandName, @Manufacturer, @Description

 

			
		--	INSERT INTO [iRISProductMaster].dbo.tblProductsMaster  
		--					( [Cat No], NDC, [Brand Name], [Other Name], [Description 1], [Description 2], [Description 3], Size, Material, Manufacturer, [Ref Price], Picture, QtyPerPkg
		--					, RFID, [SKU No],[ID 1], [ID 2], [ID 3], [ID 4], [ID 5], [Entered By], [Computer Name], Domain, [Entered Date], ReorderTypeID, [Notify Interval]
		--					, [Notify Time], [Notify Sent], ProductTypeID, PCDM, MMID, ProductCategoryID, ProductSubCategoryID, ProductGroupID, EntryTypeID
		--					, [Min Stock], [Max Stock], IsFromFDA)
		--	SELECT          , 1 ) --last col 1 for FDA

 

		--	--Select missing
		--	INSERT INTO [$(IrisDB)].dbo.tblProducts 
		--					( [Cat No], NDC, [Brand Name], [Other Name], [Description 1], [Description 2], [Description 3], Size, Material, Manufacturer, [Ref Price], Picture, QtyPerPkg
		--					, RFID, [SKU No],[ID 1], [ID 2], [ID 3], [ID 4], [ID 5], [Entered By], [Computer Name], Domain, [Entered Date], ReorderTypeID, [Notify Interval]
		--					, [Notify Time], [Notify Sent], ProductTypeID, PCDM, MMID, ProductCategoryID, ProductSubCategoryID, ProductGroupID, EntryTypeID
		--					, [Min Stock], [Max Stock])

 

			PRINT 'Inserted into [IrisDB].dbo.tblProducts and MASTER from FDA'

 

	END

 

	IF (  @CatNo IS NOT NULL )
	BEGIN
		--INSERT INTO [iRISProductMaster].dbo.s01tblBarcodeRuleProductMaster 
		--	   (RuleID, [Barcode No], [Cat No], IsFromFDA, ProductID)
		--SELECT MAX(RuleID)+1, @ScannedBarcode, @CatNo, 1, @MastProductId
		--FROM [iRISProductMaster].dbo.s01tblBarcodeRuleProductMaster

 

		INSERT INTO s01tblBarcodeRuleProduct 
			   ([Barcode No], [Cat No])
		SELECT  @ScannedBarcode, @CatNo

 

		PRINT 'Inserted into [IrisDB].dbo.tblBarcodeRuleProduct and tblBarcodeRuleProductMaster from FDA'
	END

 

	
COMMIT TRANSACTION		
END TRY

 

BEGIN CATCH
	IF XACT_STATE() <> 0
		ROLLBACK TRANSACTION
		SET @Msg = 'Error in Searching Product details';
	EXECUTE s01uspLogError
END CATCH
SET NOCOUNT OFF

GO
/****** Object:  StoredProcedure [dbo].[usp827WebConsoleReturnBarcodeItems]    Script Date: 11/21/2024 2:16:28 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE Procedure [dbo].[usp827WebConsoleReturnBarcodeItems]
	@Event VARCHAR(255)
	, @udtSystemData udtSPParam READONLY
	, @udtEventData udtSPParam READONLY
	, @ProductID INT = ''
	, @ItemEventID INT = ''
	, @OverrideID INT = ''
AS
	SET NOCOUNT ON;
		
		SET XACT_ABORT ON;
		BEGIN TRY
		BEGIN TRANSACTION

		DECLARE   @DeviceID INT, @ItemInventoryID INT, @LocationID INT, @LocationTypeID INT, @UserID INT, @CurrentUtcTime DATETIME, @EntryTypeID INT
		DECLARE   @MAScheduleID INT,  @Qty INT,@inputQty INT, @UsageItemID INT, @MAVisitID INT,  @MAPatientID INT, @ItemUsageID INT,  @SessionID varchar(50),  @ItemHistoryID  INT
		

		SELECT @DeviceID = Value FROM @udtSystemData WHERE Property = 'DeviceID'
		SELECT @UserID = Value FROM @udtEventData WHERE Property = 'UserID'
		SELECT @SessionID = Value FROM @udtEventData WHERE Property = 'SessionID'
		SELECT @EntryTypeID = Value FROM @udtSystemData WHERE Property = 'EntryTypeID'

		SELECT @LocationTypeID = [dbo].[udf700WebConsoleSUPFetchSettings] ( @DeviceId, 'BarcodeInventory_LocationType' )
		SELECT @LocationID = [dbo].[udf700WebConsoleSUPFetchSettings] ( @DeviceId, 'BarcodeInventory_Location' )

		SELECT @MAVisitID = Value FROM @udtEventData WHERE Property = 'MAVisitID' 
		SELECT @MAScheduleID = Value FROM @udtEventData WHERE Property = 'MAScheduleID' 
		SELECT @MAPatientID = Value FROM @udtEventData WHERE Property = 'MAPatientID'
		SELECT @inputQty = Value FROM @udtEventData WHERE Property = 'Qty'
	
		IF (SELECT [Setting Value] FROM s01tblSettingsCommon WHERE [Setting Name] = 'ENABLE_UTC_TIMEZONE') = 'TRUE'
		BEGIN
			SELECT @CurrentUTCTime = GETUTCDATE()
		END
		ELSE
		BEGIN
			SELECT @CurrentUTCTime = GETDATE()
		END


		SELECT  @ItemInventoryID = ItemInventoryID  
		FROM s01tblItemInventory 
		WHERE ProductID = @ProductID
			AND LocationID = @LocationID
			AND LocationTypeID = @LocationTypeID
		
		IF (@MAScheduleID IS NOT NULL)
		BEGIN
			
			SELECT TOP 1 @Qty = Qty, @UsageItemID = UsageItemID 
			FROM s01tblItemUsage 
			WHERE UsageItemId IN (
									SELECT ItemInventoryID 
									FROM s01tblItemInventory
									WHERE ItemInventoryID = @ItemInventoryID
									)
				AND MAScheduleID = @MAScheduleID
				AND MAVisitID = @MAVisitID
				AND MAPatientID = @MAPatientID
			ORDER BY LastActivityDate DESC

			SELECT @ItemUsageID = ItemUsageID 
			FROM s01tblItemUsage 
			WHERE UsageItemID = @ItemInventoryID 
				AND MAScheduleID = @MAScheduleID
				AND MAVisitID = @MAVisitID
				AND MAPatientID = @MAPatientID
		END

		ELSE 
		BEGIN
		
		if exists (select sessionid from s01tblItemUsage where SessionID = @SessionID and UsageItemId=@ItemInventoryID)
		begin
			SELECT @Qty = Qty, @UsageItemID = UsageItemID 
			FROM s01tblItemUsage 
			WHERE UsageItemId IN (
									SELECT ItemInventoryID 
									FROM s01tblItemInventory
									WHERE ItemInventoryID = @ItemInventoryID

									)
				AND SessionID = @SessionID
	
			SELECT @ItemUsageID = ItemUsageID 
			FROM s01tblItemUsage 
			WHERE UsageItemID = @ItemInventoryID 
				AND SessionID = @SessionID

				
		end
		else
		begin
			if(@MAScheduleID IS not NULL)
			begin
				SELECT top 1 @Qty = Qty, @UsageItemID = UsageItemID 
				FROM s01tblItemUsage 
				WHERE UsageItemId IN (
										SELECT ItemInventoryID 
										FROM s01tblItemInventory
										WHERE ItemInventoryID = @ItemInventoryID

										)
				and MAPatientID is not null
				order by DateUsed desc
					
	
				SELECT top 1 @ItemUsageID = ItemUsageID 
				FROM s01tblItemUsage 
				WHERE UsageItemID = @ItemInventoryID and MAPatientID is not null
				order by DateUsed desc
			end
			else
			begin
				SELECT top 1 @Qty = Qty, @UsageItemID = UsageItemID 
				FROM s01tblItemUsage 
				WHERE UsageItemId IN (
										SELECT ItemInventoryID 
										FROM s01tblItemInventory
										WHERE ItemInventoryID = @ItemInventoryID

										)
				and MAPatientID is null
				order by DateUsed desc
				if(@Qty is null)
				begin
						SELECT top 1 @Qty = Qty, @UsageItemID = UsageItemID 
						FROM s01tblItemUsage 
						WHERE UsageItemId IN (
											SELECT ItemInventoryID 
											FROM s01tblItemInventory
											WHERE ItemInventoryID = @ItemInventoryID

											)
						and MAPatientID is not null
						order by DateUsed desc
				end
				
				SELECT top 1 @ItemUsageID = ItemUsageID 
				FROM s01tblItemUsage 
				WHERE UsageItemID = @ItemInventoryID and MAPatientID is null
				order by DateUsed desc

				if(@ItemUsageID is null)
				begin
					SELECT top 1 @ItemUsageID = ItemUsageID 
				FROM s01tblItemUsage 
				WHERE UsageItemID = @ItemInventoryID and MAPatientID is not null
				order by DateUsed desc
				end
			end
				
		end
		END
								 
		IF ( @Qty = 1 )
		BEGIN
			
			INSERT INTO s01tblItemHistory ( ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAVisitID, MAScheduleID, BillingStatusID, UsedBy,  UnitPrice, UOMCode
									, OverrideID, OverrideNotes, DateUsed, SessionID, UpdatedBy, LastActivityDate, ItemEventID, Qty, EntryTypeID ) 
			SELECT ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAVisitID, MAScheduleID, BillingStatusID, UsedBy, UnitPrice, UOMCode, @OverrideID, OverrideNotes 
									, DateUsed, @SessionID , @UserID, @CurrentUTCTime, @ItemEventID, 1 , @EntryTypeID 
			FROM s01tblItemUsage 
			WHERE ItemUsageID = @ItemUsageID
					
			SELECT @ItemHistoryID = SCOPE_IDENTITY()
			INSERT INTO s01tblItemHistoryExtn (ItemHistoryID, LocationID ) 
			SELECT ItemHistoryID, @LocationID 
			FROM s01tblItemHistory 
			WHERE ItemHistoryID = @ItemHistoryID

			INSERT INTO MA_SUP_TJUHOR_IRISUPPLYDB..tblInventoryLog ( ItemHistoryID, InvntoryID, ProcessStatus, ClusterID, LocationID, UpdatedBy, EntryTypeID, EnteredDate )
			SELECT ItemHistoryID, UsageItemID, 'P', @DeviceID, @LocationID, @UserID, @EntryTypeID, @CurrentUTCTime 
			FROM s01tblItemHistory 
			WHERE ItemHistoryID = @ItemHistoryID	
					
			UPDATE s01tblItemInventory 
			SET Qty = Qty + 1, UpdatedBy = @UserID, QtyLastUpdated = @CurrentUTCTime 
			WHERE ItemInventoryID = @ItemInventoryID
				AND LocationID = @LocationID
				AND LocationTypeID = @LocationTypeID

			INSERT INTO s01tblUpdateBarcodeItemInventoryLog( ItemInventoryID, QtyInHand, QtyLastUpdated, QtyUsed 
									, QtyReturned, QtyDeleted, QtyStocked, QtyWasted, CutOffTime, UpdatedQtyInHand )
			SELECT ItemInventoryID, Qty - 1, QtyLastUpdated, 0, 1, 0, 0, 0, @CurrentUTCTime, Qty 
			FROM s01tblItemInventory
			WHERE ItemInventoryID = @ItemInventoryID
			
			DELETE FROM s01tblItemUsage 
			WHERE ItemUsageID = @ItemUsageID		

			SELECT  ItemInventoryID, [Ref Price], UOMCode, [Cat No], [Description 1], Qty, ProdUnitID, PCDM, LocationID ,[Barcode No]
					FROM qry703WebConsoleProductCatalog 
					WHERE ItemInventoryID = @ItemInventoryID
		
			--SELECT 'Success' DataSetName, 'Item Returned to the Inventory' AS MSG
		END

		ELSE
		BEGIN
		
			IF (@MAScheduleID IS NOT NULL)
			BEGIN
				UPDATE s01tblItemUsage 
				SET Qty = @Qty - @inputQty, UpdatedBy = @UserID, LastActivityDate = @CurrentUTCTime, OverrideID = @OverrideID
				WHERE ItemUsageID = @ItemUsageID
					AND MAScheduleID = @MAScheduleID
					AND MAVisitID = @MAVisitID
					AND MAPatientID = @MAPatientID
			END
			ELSE
			BEGIN
			
				UPDATE s01tblItemUsage 
				SET Qty = @Qty - @inputQty, UpdatedBy = @UserID, LastActivityDate = @CurrentUTCTime, OverrideID = @OverrideID
				WHERE ItemUsageID = @ItemUsageID
					--AND SessionID = @SessionID
			END
						
			UPDATE s01tblItemInventory 
			SET Qty = Qty + @inputQty, UpdatedBy = @UserID, QtyLastUpdated = @CurrentUTCTime 
			WHERE ItemInventoryID = @ItemInventoryID
				AND LocationID = @LocationID
				AND LocationTypeID = @LocationTypeID

			INSERT INTO s01tblUpdateBarcodeItemInventoryLog( ItemInventoryID, QtyInHand, QtyLastUpdated, QtyUsed 
									, QtyReturned, QtyDeleted, QtyStocked, QtyWasted, CutOffTime, UpdatedQtyInHand )
			SELECT ItemInventoryID, Qty - @inputQty, QtyLastUpdated, 0, @inputQty, 0, 0, 0, @CurrentUTCTime, Qty 
			FROM s01tblItemInventory
			WHERE ItemInventoryID = @ItemInventoryID
				
			INSERT INTO s01tblItemHistory ( ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAVisitID, MAScheduleID, BillingStatusID, UsedBy,  UnitPrice, UOMCode
										, OverrideID, OverrideNotes, DateUsed, SessionID, UpdatedBy, LastActivityDate, ItemEventID, Qty, EntryTypeID ) 
			SELECT ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAVisitID, MAScheduleID, BillingStatusID, UsedBy, UnitPrice, UOMCode, @OverrideID, OverrideNotes 
										, DateUsed, @SessionID , @UserID, @CurrentUTCTime, @ItemEventID, @inputQty, @EntryTypeID 
			FROM s01tblItemUsage 
			WHERE ItemUsageID = @ItemUsageID
		
			SELECT @ItemHistoryID = SCOPE_IDENTITY()
			INSERT INTO s01tblItemHistoryExtn (ItemHistoryID, LocationID ) 
			SELECT ItemHistoryID, @LocationID 
			FROM s01tblItemHistory 
			WHERE ItemHistoryID = @ItemHistoryID

			INSERT INTO MA_SUP_TJUHOR_IRISUPPLYDB..tblInventoryLog ( ItemHistoryID, InvntoryID, ProcessStatus, ClusterID, LocationID, UpdatedBy, EntryTypeID, EnteredDate )
			SELECT ItemHistoryID, UsageItemID, 'P', @DeviceID, @LocationID, @UserID, @EntryTypeID, @CurrentUTCTime 
			FROM s01tblItemHistory 
			WHERE ItemHistoryID = @ItemHistoryID	

			SELECT  ItemInventoryID, [Ref Price], UOMCode, [Cat No], [Description 1], Qty, ProdUnitID, PCDM, LocationID ,[Barcode No]
					FROM qry703WebConsoleProductCatalog 
					WHERE ItemInventoryID = @ItemInventoryID

		--	SELECT 'Success' DataSetName, 'Item Returned to the Inventory' AS MSG

			
		END					

	COMMIT TRANSACTION	
	END TRY
	BEGIN CATCH
		IF XACT_STATE() <> 0
			ROLLBACK TRANSACTION
		EXECUTE s01uspLogError
	END CATCH
	SET NOCOUNT OFF
GO
/****** Object:  StoredProcedure [dbo].[usp850WebConsoleGetUserReports]    Script Date: 11/21/2024 2:16:28 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE Procedure [dbo].[usp850WebConsoleGetUserReports]
@UserID int
as
begin 

Declare @UserGroupID int

set @UserGroupID = (select GroupID from s01tblUsers where UserID =@UserID)


IF (@UserGroupID <> 0) and (@UserGroupID <> 1) 
Begin
	select REPORT_ID as ReportID,REPORT_NAME as ReportName,DESCRIPTION,REPORT_PATH as ReportPath, isENABLED as Status 
	from s01tblReports r
	left join tblReportRBAC u on r.REPORT_ID=u.ReportID
	where u.UserID =@UserID
	order by REPORT_NAME
End
else
	exec [uspGetReports]
end
GO
/****** Object:  StoredProcedure [dbo].[usp851WebConsoleAddNewReport]    Script Date: 11/21/2024 2:16:28 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE Procedure [dbo].[usp851WebConsoleAddNewReport]
@ReportName varchar(100),
@Description varchar(100),
@ReportPath varchar(100),
@IsEnabled bit
as

insert into s01tblReports(REPORT_NAME,DESCRIPTION,REPORT_PATH,isENABLED,SUB_MODULE_ID)
select @ReportName,@Description,@ReportPath,@IsEnabled,69 
GO
/****** Object:  StoredProcedure [dbo].[usp852WebConsoleGetReportDetails]    Script Date: 11/21/2024 2:16:28 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE Procedure [dbo].[usp852WebConsoleGetReportDetails]
@ReportID int
as
begin 

select REPORT_ID as ReportID,REPORT_NAME as ReportName,DESCRIPTION,REPORT_PATH as ReportPath, isENABLED as Status 
from s01tblReports 
where REPORT_ID =@ReportID

end
GO
/****** Object:  StoredProcedure [dbo].[usp853WebConsoleProcessReport]    Script Date: 11/21/2024 2:16:28 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE Procedure [dbo].[usp853WebConsoleProcessReport]
@ReportID int,
@ReportName varchar(100),
@Description varchar(100),
@ReportPath varchar(150),
@IsEnabled bit
as

update s01tblReports set REPORT_NAME=@ReportName,DESCRIPTION=@Description,
REPORT_PATH=@ReportPath,isENABLED=@IsEnabled
where REPORT_ID=@ReportID
GO
/****** Object:  StoredProcedure [dbo].[usp854WebConsoleActivityLogReport]    Script Date: 11/21/2024 2:16:28 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE Procedure [dbo].[usp854WebConsoleActivityLogReport]

--@SessionID varchar(200)

as

Begin


    select 

         AppEventLogID,

          Sessionid,

           [User Name] as UserName,

           --[Log Date] as[Date],

           [Log Date] as [Date],

           [Log Date] as [DateTime],

           [Computer Name] as ComputerName,

           --Location as Department,
		   
		   --Room,

           --WorkflowInfo as Overridenotes,


           LogMsg as Message,


           [Cluster Name] as ClusterName,


           RFID

    from MA_SUP_TJUHOR_IRISUPPLYDB..tblAppEventLog WEL

    where 

        EventID  in(94,95,96,97,98,99,100,101)

        and WEL.[User Name] !='System, System'

        --and WEL.SessionID = @SessionID

    order by [Log Date]

End

GO
/****** Object:  StoredProcedure [dbo].[usp901WebConsoleGetDeviceDetails]    Script Date: 11/21/2024 2:16:28 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE Procedure [dbo].[usp901WebConsoleGetDeviceDetails]
@DeviceID int
as
begin
    select d.[Registration Date],d.[Registered By],d.[Entered Date],d.SerialNumber,D.DeviceName,
	d.McAddress,d.Status,l.Department,l.RoomNumber as Room,d.[Device IPAddress] as ipAddress
	from tblDeviceRegistration as d 
	left join s01tblLocationRoom as l on d.[Location ID]=l.RoomID
	where Id=@DeviceID;

	select Department as Departments from s01tblLocationRoom where RoomDesc <>'iRISupplyWebConsole'
end
GO
/****** Object:  StoredProcedure [dbo].[usp903WebConsoleUpdateDeviceStatus]    Script Date: 11/21/2024 2:16:28 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE Procedure [dbo].[usp903WebConsoleUpdateDeviceStatus]
@McAddress varchar(50),@Status varchar(50)
as
begin
    if exists (select * from tblDeviceRegistration where McAddress=@McAddress)
	begin
	    update tblDeviceRegistration set Status= @Status where McAddress=@McAddress
		select 'Updated Successfully'
	end
	else
	begin
	    select 'Failed to update'
	end
end

GO
/****** Object:  StoredProcedure [dbo].[usp904WebConsoleTimeOfImplantCheck]    Script Date: 11/21/2024 2:16:28 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE PROCEDURE [dbo].[usp904WebConsoleTimeOfImplantCheck]
@RFID VARCHAR(100)
AS
BEGIN
    DECLARE @ItemID INT

    IF EXISTS (SELECT * FROM s01tblitems WHERE RFID = @RFID)
    BEGIN
        SET @ItemID = (SELECT itemid FROM s01tblitems WHERE RFID = @RFID)

        SELECT I.[Expired Date] AS [Expired Date],
              I.[Entered Date] AS [Entered Date],
               D.[Description 1] AS Description,
			   I.ItemStatusID
        FROM s01tblitems AS I
        INNER JOIN s01tblproducts AS D ON I.itemid = D.ProductID
        WHERE I.RFID = @RFID 
    END
	
END
GO
/****** Object:  StoredProcedure [dbo].[usp905WebConsoleUpdateCapitateItem]    Script Date: 11/21/2024 2:16:28 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE PROCEDURE [dbo].[usp905WebConsoleUpdateCapitateItem]
@ItemUsageID INT,
@CapitateValue INT
AS
BEGIN
  IF EXISTS (SELECT * FROM s01tblItemUsageExt WHERE ItemUsageID = @ItemUsageID)
  BEGIN
    UPDATE s01tblItemUsageExt 
    SET Capitated = @CapitateValue 
    WHERE ItemUsageID = @ItemUsageID

    IF @CapitateValue = 0
      SELECT 'ITEM UNCAPITATED SUCCESSFULLY'
    ELSE
      SELECT 'ITEM CAPITATED SUCCESSFULLY'
  END
  ELSE 
  BEGIN 
    INSERT INTO s01tblItemUsageExt (ItemUsageID, ManualScan, Capitated, BillOnly, PostedToDoc, EnteredDate, LastActivityDate)
    VALUES (@ItemUsageID, 0, @CapitateValue, 0, 0, GETDATE(), GETDATE())
    
    SELECT 'ITEM CAPITATED SUCCESSFULLY'
  END
END




GO
/****** Object:  StoredProcedure [dbo].[uspGetReports]    Script Date: 11/21/2024 2:16:28 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
Create procedure [dbo].[uspGetReports]
as
begin
	select REPORT_ID as ReportID,REPORT_NAME as ReportName,DESCRIPTION,REPORT_PATH as ReportPath, isENABLED as Status 
	from s01tblReports 
	order by REPORT_NAME
end
GO
/****** Object:  StoredProcedure [dbo].[uspSWCSendUsageToEpic]    Script Date: 11/21/2024 2:16:28 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE PROCEDURE [dbo].[uspSWCSendUsageToEpic]

	  @Event VARCHAR(200)

	, @udtSystemData udtSPParam READONLY

	, @udtEventData udtSPParam READONLY

	, @ItemEventID INT

	, @ItemStatusID INT

	, @UsageTypeID INT

	, @ItemID INT

AS
 
SET NOCOUNT ON;
 
	DECLARE @UserID INT, @SessionID BIGINT, @Msg VARCHAR(500), @Transaction VARCHAR(10), @DeviceID INT

			, @ComputerName VARCHAR(50), @UsageItemID INT, @ItemUsageID INT
 
	SET XACT_ABORT ON;

		BEGIN TRY

		BEGIN TRANSACTION
 
		SELECT @SessionID = Value FROM @udtEventData WHERE Property = 'SessionID'

		SELECT @UserID = Value FROM @udtEventData WHERE Property = 'UserID'

		SELECT @DeviceID = Value FROM @udtSystemData WHERE Property = 'DeviceID'

		SELECT @ComputerName = Value FROM @udtSystemData WHERE Property = 'ComputerName'

		SELECT @ItemUsageID = ItemUsageID 

		from s01tblItemHistory where UsageItemID = @ItemID

 
	

				EXECUTE s01usp_LogItemTransaction

					 @ItemStatusID=@ItemStatusID

					 , @UsageTypeID=@UsageTypeID

					 , @UsageItemID=@ItemID

					 , @ItemEventID = @ItemEventID

					 , @ItemUsageID = @ItemUsageID

					 , @UserID = @UserID

					 , @ClusterID = @DeviceID

					 , @ComputerName = @ComputerName


	COMMIT TRANSACTION;

	END TRY

		BEGIN CATCH

			IF (XACT_STATE()) <> 0

				ROLLBACK TRANSACTION;

				SET @Msg = 'ERROR in Item sending usage to EPIC';

			EXECUTE s01uspLogError;

		END CATCH;

	SET NOCOUNT OFF

 
GO
USE [master]
GO
ALTER DATABASE [MA_SUP_TJUHOR_IRISHUBDB] SET  READ_WRITE 
GO
