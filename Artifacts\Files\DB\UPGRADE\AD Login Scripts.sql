/****** Object:  StoredProcedure [dbo].[usp708WebConsoleAddADUser]    Script Date: 12/19/2024 4:56:38 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE Procedure [dbo].[usp708WebConsoleAddADUser]
@username varchar(max),	
@rfid varchar(max),
@firstname varchar(max),
@lastname varchar(max),
@middlename varchar(max),
@designation varchar(max),
@email varchar(max),
@domain varchar(max),
@groupid int,
@computername varchar(max)
AS
BEGIN

IF EXISTS ( SELECT 1 FROM s01tblUsers WHERE  [First Name] = @firstname AND [Last Name] = @lastname AND [Username] = @username)
	begin
		Select 'Status' as status,'User updated' as msg
	end
	else
	begin
		insert into s01tblUsers (Username,[Entered Date],[First Name],[Middle Name],[Last Name],Email,[Computer Name],Domain,[Job Title],GroupID,UserStatusID,RFID)
		values(@username,CURRENT_TIMESTAMP,@firstname,@middlename,@lastname,@email,@computername,@Domain,@designation,3,1,@rfid);

		Select 'Status' as status,'User registered' as msg
	end

END
GO
ALTER PROCEDURE [dbo].[usp800WebConsoleUserLogin]
    @Event VARCHAR(255)
    , @udtSystemData AS udtSPParam READONLY
    , @udtEventData AS udtSPParam READONLY

AS 
    --SET NOCOUNT ON;
        DECLARE @User VARCHAR(50), @Pwd VARCHAR(50), @UserID INT, @UserName VARCHAR(100), @SessionID BIGINT, @SPName VARCHAR(100), @UserStatus INT, @Msg VARCHAR(255)
        DECLARE @EventType VARCHAR(50), @IsCardAssociated BIT, @EntryTypeID INT, @DeviceID INT, @ComputerName VARCHAR(100), @ClusterName VARCHAR(100), @DataSetName VARCHAR(50)
        DECLARE @PIN VARCHAR(50), @RFID VARCHAR(100), @Password VARCHAR(500), @ClusterID INT, @Domain VARCHAR(50), @GroupID INT
        DECLARE @CurrentUtcTime DATETIME, @DataSetName1 VARCHAR(50), @Golivedate DATETIME, @CheckCompartmentID INT

        --SET XACT_ABORT ON;
        BEGIN --TRY
        --BEGIN TRANSACTION

        SELECT @DeviceID = Value FROM @udtSystemData WHERE Property = 'DeviceID'
        SELECT @EntryTypeID = Value FROM @udtSystemData WHERE Property = 'EntryTypeID'
        SELECT @ComputerName = Value FROM @udtSystemData WHERE Property = 'ComputerName'
        SELECT @Domain = Value FROM @udtSystemData WHERE Property = 'Domain'
        SELECT @EventType = Value FROM @udtEventData WHERE Property = 'EventType'
        --SELECT @DataSetName = Value FROM @udtEventData WHERE Property = 'EventOutPut'
        --SELECT @DataSetName1 = Value FROM @udtEventData WHERE Property = 'EventOutPut1'
        SELECT @User = Value FROM @udtEventData WHERE Property = 'User'
        SELECT @Pwd = Value FROM @udtEventData WHERE Property = 'Pwd'

        --IF ( SELECT COUNT(*) FROM dbo.s01qrycabinets WHERE ClusterID = @DeviceID GROUP BY ClusterID ) = 1
        --BEGIN
        --  SELECT @CheckCompartmentID = CompartmentID 
        --  FROM dbo.s01qrycabinets 
        --  WHERE ClusterID = @DeviceID AND CompartmentID IS NULL OR CompartmentID = ''
        --END

        IF (SELECT [Setting Value] FROM MA_SUP_SITENAME_IRISUPPLYDB..tblSupplySettings WHERE [Setting Name] = 'ENABLE_UTC_TIMEZONE') = 'TRUE'
        BEGIN
            SELECT @CurrentUTCTime = GETUTCDATE()
            SELECT @SessionID = REPLACE( REPLACE( REPLACE( REPLACE( 
                                CONVERT( VARCHAR(30), @CurrentUTCTime, 121 ), ':', '' ), '-', '' ), ' ', '' ) , '.', '' )
        END
        ELSE
        BEGIN
            SELECT @CurrentUTCTime = GETDATE()
            SELECT @SessionID = REPLACE( REPLACE( REPLACE( REPLACE( 
                                CONVERT( VARCHAR(30), @CurrentUTCTime, 121 ), ':', '' ), '-', '' ), ' ', '' ) , '.', '' )
        END
        
        --SELECT @Golivedate = MA_SUP_SITENAME_IRISUPPLYDB..[udfSUPFetchSettings] ( @DeviceId,'GoLiveDate' ) -- VIRESH

        --IF( @EventType = 'PIN' AND @Pwd IS NOT NULL AND ISNULL( @DeviceID, 0 ) <> 0 )
        --BEGIN
        --  --Checking for Authorization
        --  IF EXISTS (SELECT 1 FROM s01qrySUPUsersValidate WHERE PIN = @Pwd AND ClusterID = @DeviceID)
        --  BEGIN
        --      SELECT TOP 1 @DataSetName AS DataSetName, UserID, CONCAT([First Name], ' ', [Last Name]) AS [User Name], [User Group], 1 AS CabinetAccess, @SessionID AS SessionID 
        --      FROM s01qrySUPUsersValidate 
        --      WHERE PIN = @Pwd 
        --          AND ClusterID = @DeviceID

        --      --EXECUTE uspUserAppAccessRBAC @Event = @Event 
        --      --      , @udtSystemData = @udtSystemData
        --      --      , @udtEventData = @udtEventData

        --      --EXECUTE  uspLoadExpireItems @Event = @Event
        --      --  , @udtSystemData = @udtSystemData
        --      --  , @udtEventData = @udtEventData
        --  END
        --  --SR added condition for Console application having single comp id = Null (Login for Authenticated user)
        --  ELSE IF EXISTS (SELECT 1 FROM dbo.s01tblUsers WHERE PIN = @Pwd AND UserStatusID = 1 AND @CheckCompartmentID IS NULL )
        --  BEGIN
        --       SELECT TOP 1 @DataSetName AS DataSetName, UserID, CONCAT([First Name], ' ', [Last Name]) AS [User Name], G.[User Group], 1 AS CabinetAccess, @SessionID AS SessionID 
        --       FROM dbo.s01tblUsers U 
        --          LEFT OUTER JOIN dbo.s01tblUserGroup G ON U.GroupID = G.GroupID
        --       WHERE PIN = @Pwd AND UserStatusID = 1 

        --      EXECUTE uspUserAppAccessRBAC @Event = @Event 
        --              , @udtSystemData = @udtSystemData
        --              , @udtEventData = @udtEventData

        --      EXECUTE  uspLoadExpireItems @Event = @Event
        --          , @udtSystemData = @udtSystemData
        --          , @udtEventData = @udtEventData
        --  END
        --  ELSE
        --      SELECT 'Alert' AS DatasetName, 'Invalid PIN' AS MSG

        --END

        IF( @EventType = 'HID' AND @Pwd IS NOT NULL --AND ISNULL( @DeviceID, 0 ) <> 0 
		)
        BEGIN
            IF EXISTS (SELECT 1 FROM MA_SUP_SITENAME_IRISUPPLYDB..qrySUPUsersValidate WHERE RFID = @Pwd AND ClusterID = @DeviceID)
            BEGIN
                SELECT TOP 1  UserID, CONCAT([First Name], ' ', [Last Name]) AS [Name],Username,[Employee ID],Department, GroupID as UserGroupID, 1 AS Status,
                @SessionID AS SessionID 
                FROM qry700WebConsoleSUPUsersValidate 
                WHERE RFID = @Pwd 
                   -- AND ClusterID = @DeviceID

                --EXECUTE uspUserAppAccessRBAC @Event = @Event 
                --  , @udtSystemData = @udtSystemData
                --  , @udtEventData = @udtEventData

                --EXECUTE  uspLoadExpireItems @Event = @Event
                --  , @udtSystemData = @udtSystemData
                --  , @udtEventData = @udtEventData
                  
            END
            --SR added condition for Console application having single comp id = Null (Login for Authenticated user)
            ELSE IF EXISTS (SELECT 1 FROM s01tblUsers WHERE RFID = @Pwd AND UserStatusID = 1 AND @CheckCompartmentID IS NULL )
            BEGIN
                 SELECT TOP 1 @DataSetName AS DataSetName, UserID, CONCAT([First Name], ' ', [Last Name]) AS [Name],Username,[Employee ID],Department, U.[GroupID] as UserGroupID, 1 AS Status,
                @SessionID AS SessionID
                 FROM s01tblUsers U 
                    LEFT OUTER JOIN s01tblUserGroup G ON U.GroupID = G.GroupID
                 WHERE RFID = @Pwd AND UserStatusID = 1 

                --EXECUTE uspUserAppAccessRBAC @Event = @Event 
                --        , @udtSystemData = @udtSystemData
                --        , @udtEventData = @udtEventData

                --EXECUTE  uspLoadExpireItems @Event = @Event
                --    , @udtSystemData = @udtSystemData
                --    , @udtEventData = @udtEventData
            END
            ELSE IF EXISTS ( SELECT 1 FROM s01qryUsers U INNER JOIN s01tblCompartmentAccess C ON U.UserID = C.UserID  
                        WHERE RFID =  @Pwd AND ClusterID = @DeviceID AND UserStatusID = 0 AND [Entered By] = 'Self Registered' AND getdate() < @Golivedate)

                SELECT  'AlertForGoLive' AS DatasetName, 'Login is not allowed till Go Live Date for self registered users' AS MSG
                    
            ELSE
                SELECT 'Alert' AS DatasetName, 'Invalid Badge' AS MSG
        END

        IF( @EventType = 'User' AND @User IS NOT NULL AND @Pwd IS NOT NULL --AND ISNULL( @DeviceID, 0 ) <> 0 
		)
        BEGIN
            SELECT TOP 1  UserID, CONCAT([First Name], ' ', [Last Name]) AS [Name],Username,[Employee ID],Department, GroupID as UserGroupID, 1 AS Status
                ,@SessionID AS SessionID
				--,[2FactorAuthStatus]
            FROM qry700WebConsoleSUPUsersValidate 
            WHERE [UserName] = @User 
                AND Password = @Pwd 
               -- AND ClusterID = @DeviceID

            --EXECUTE uspUserAppAccessRBAC @Event = @Event 
            --      , @udtSystemData = @udtSystemData
            --      , @udtEventData = @udtEventData

            --EXECUTE  uspLoadExpireItems @Event = @Event
            --      , @udtSystemData = @udtSystemData
            --      , @udtEventData = @udtEventData
        END
    
        IF ( ISNULL( @DeviceID, 0 ) = 0 )
        BEGIN
            IF ( @EventType = 'PIN' AND @Pwd IS NOT NULL )
            BEGIN
                SELECT @UserID = UserID, @PIN = PIN, @UserName = [User Name], @RFID = PIN 
                FROM s01qryUsers 
                WHERE PIN = @Pwd 
                    AND UserStatusID = 1 
                    AND GroupID IN ( 0, 1 )

                IF EXISTS ( SELECT 1 FROM s01qryUsers WHERE PIN = @Pwd AND UserStatusID = 1 AND GroupID IN ( 0, 1 ) )
                BEGIN
                        
                    --EXECUTE  uspAppCabEventLogs @Event = @Event
                    --              , @udtSystemData = @udtSystemData
                    --              , @udtEventData = @udtEventData
                    --              , @Logs =  'UtilityApp - Using PIN - Login Successfull'

                    SELECT TOP 1 @DataSetName AS DataSetName, UserID, CONCAT([First Name], ' ', [Last Name]) AS [User Name], [User Group], 1 AS CabinetAccess, @SessionID AS SessionID 
                    FROM s01qryUsers 
                    WHERE PIN = @Pwd 
                        AND UserStatusID = 1 
                        AND GroupID IN ( 0, 1 ) 
                END
                --ELSE IF NOT EXISTS ( SELECT 1 FROM dbo.s01qryUsers WHERE PIN = @Pwd AND UserStatusID = 1 AND GroupID IN ( 0, 1 ) )
                --BEGIN

                --  --EXECUTE  uspAppCabEventLogs @Event = @Event
                --  --              , @udtSystemData = @udtSystemData
                --  --              , @udtEventData = @udtEventData
                --  --              , @Logs =  'UtilityApp - Using PIN - Invalid PIN'

                --END
            END
        END 

        IF ( @EventType = 'User' AND  @User IS NOT NULL AND @Pwd IS NOT NULL AND ISNULL( @DeviceID, 0 ) <> 0)
        BEGIN
            SET @UserID = NULL

            SELECT @UserID = UserID, @PIN = PIN, @RFID = RFID , @UserName = [User Name], @Password = Password--, @ClusterID = [ClusterID] 
            FROM qry700WebConsoleSUPUsersValidate  
            WHERE [UserName] = @User 
                AND Password = @Pwd 
                --AND ClusterID = @DeviceID

            SELECT  @ClusterName = [Cluster Name] 
            FROM  s01qryCabinets 
            WHERE ClusterID = @DeviceID

            --IF EXISTS ( SELECT 1 FROM s01qrySUPUsersValidate WHERE [User Name] = @UserName AND Password = @Pwd ) 
          --  BEGIN

          --      EXECUTE  uspAppCabEventLogs @Event = @Event
          --                      , @udtSystemData = @udtSystemData
          --                      , @udtEventData = @udtEventData
          --                      , @Logs =  'Using Name - Login Successfull'
								  --, @UserSessionID =  @SessionID

          --  END 

            IF ( (@UserName IS NULL OR @UserName = '') AND ( @Password IS NULL OR @PassWord = '') AND ISNULL( @DeviceID, 0 ) <> 0 )
            BEGIN

                EXECUTE  usp705WebConsoleAppCabEventLogs @Event = @Event
                            , @udtSystemData = @udtSystemData
                            , @udtEventData = @udtEventData
                            , @Logs =  'Using Name - Invalid User/Password'

            END
                    
        END

        IF ( @EventType = 'HID' AND ISNULL( @DeviceID, 0 ) <> 0 )
        BEGIN
            SELECT @UserID = UserID, @RFID = RFID , @UserName = [User Name], @Password = Password--, @ClusterID = [ClusterID] 
            FROM qry700WebConsoleSUPUsersValidate  
            WHERE RFID = @Pwd 
               -- AND ClusterID = @DeviceID

            SELECT  @ClusterName = [Cluster Name] 
            FROM  s01qryCabinets 
            WHERE ClusterID = @DeviceID

            IF EXISTS ( SELECT 1 FROM qry700WebConsoleSUPUsersValidate WHERE RFID = @Pwd )
            BEGIN

                EXECUTE  usp704WebConsoleProcessUserLogs @Event = @Event
                                    , @udtSystemData = @udtSystemData
                                    , @udtEventData = @udtEventData
                                    , @UserSessionID =  @SessionID

        --        EXECUTE  uspAppCabEventLogs @Event = @Event
        --                    , @udtSystemData = @udtSystemData
        --                    , @udtEventData = @udtEventData
        --                    , @Logs =  'Using HID - Login Successfull'
							 --, @UserSessionID =  @SessionID

            END 

            IF NOT EXISTS ( SELECT 1 FROM qry700WebConsoleSUPUsersValidate WHERE RFID = @Pwd )
            BEGIN

                EXECUTE  usp705WebConsoleAppCabEventLogs @Event = @Event
                        , @udtSystemData = @udtSystemData
                        , @udtEventData = @udtEventData
                        , @Logs =  'Using HID - Invalid HID Card'

            END                 
        END

		 IF( @EventType = 'AD' AND @User IS NOT NULL )
        BEGIN
            SELECT TOP 1  UserID, CONCAT([First Name], ' ', [Last Name]) AS [Name],Username,[Employee ID],Department, GroupID as UserGroupID, 1 AS Status,--2FactorAuthStatus,
                @SessionID AS SessionID
           FROM s01tblUsers     
            WHERE [UserName] = @User 
                --AND Password = @Pwd 
               -- AND ClusterID = @DeviceID

            --EXECUTE uspUserAppAccessRBAC @Event = @Event 
            --      , @udtSystemData = @udtSystemData
            --      , @udtEventData = @udtEventData

            --EXECUTE  uspLoadExpireItems @Event = @Event
            --      , @udtSystemData = @udtSystemData
            --      , @udtEventData = @udtEventData
        END

        --COMMIT TRANSACTION;

        END--TRY
        --BEGIN CATCH
        --    --IF (XACT_STATE()) <> 0
        --        --ROLLBACK TRANSACTION;
        --        SET @Msg = 'ERROR VALIDATING LOGIN';
        --    --EXECUTE uspLogError;
        --END CATCH;
    --SET NOCOUNT OFF
GO