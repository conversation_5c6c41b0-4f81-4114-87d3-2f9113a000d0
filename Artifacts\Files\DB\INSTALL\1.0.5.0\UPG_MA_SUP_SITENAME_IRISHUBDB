ALTER Procedure [dbo].[usp821WebConsoleExecuteAppEvent]
	@Event VARCHAR(255),
	@udtSystemData udtSPParam READONLY,
	@udtEventData udtSPParam READONLY
AS


	SET NOCOUNT ON
	DECLARE @SPName VARCHAR(100), @AppID INT, @DeviceID INT, @Msg VARCHAR(200)
	SET XACT_ABORT ON;
	BEGIN TRY
	BEGIN TRANSACTION;
	SELECT @AppID = Value FROM @udtSystemData WHERE Property = 'EntryTypeID'
	SELECT @SPName = SPName
	FROM tblAppEvents
	WHERE [Event] = @Event AND AppID = @AppID
	IF NOT @SPName IS NULL
	BEGIN

		    IF @Event IN ( 'StockNewItem','ReturnItem','RemoveItem',
							'WasteItem','TransferItem','UpdateFDAItemDetails')
				EXEC @SPName @Event, @udtSystemData, @udtEventData
			ELSE IF @Event IN ( 'FetchBillOnlyItemDetails','UpdateOTUItemDetails','FetchOneTimeUseItemDetails','FetchItemDetailsByCatNo','CheckIteminMASystem','StoreFDADetailsinMASystem','ProcessFDABarcode')
				EXEC @SPName @Event, @udtSystemData, @udtEventData
			ELSE
				EXEC usp825WebConsoleScanBarcodeInventory @Event, @udtSystemData, @udtEventData 
	END
	ELSE
		SELECT 'No SP Found' AS ErrorDataSet
	COMMIT TRANSACTION;
		END TRY
		BEGIN CATCH
			IF (XACT_STATE()) <> 0
				ROLLBACK TRANSACTION;
				SET @Msg = 'ERROR in tblAppEvents for calling SP';
			EXECUTE s01uspLogError;
		END CATCH;
	SET NOCOUNT OFF
GO

/************************************************************************************************************************
Name				: [uspFetchBillOnlyItems]
Version				: 6.0.10.0
Purpose				: To fetch the product details through processed barcode
Author				: Subramanya Rao
Date Created 		: 6 Mar 2020
Application Affected: IriSupply 6.0
---------------------------------------------------------------------------------------------------------------------------
Release Notes		:
6.0.10.0 [Subramanya, 6 Apr 2021] - Bill only feature
----------------------------------------------------------------------------------------------------------------------------
--SP CALL
-- @Event='FetchTagData'
DECLARE @p2 dbo.udtSPParam
DECLARE @p3 dbo.udtSPParam
INSERT INTO @p2 VALUES(N'DeviceID',4)
INSERT INTO @p2 VALUES(N'EntryTypeID',1)
INSERT INTO @p2 values(N'ComputerName',N'TestMachine')

insert into @p3 values(N'ProcessedBarcodeNo',N'+H739PG3980BPS3X')
insert into @p3 values(N'ProcessedCatNo',N'PG3980BPS') -- 00026201100 Prod Master
insert into @p3 values(N'FullBarcodeNo',N'+H739PGeeeeeeeeeeeee3960BPS3V')

EXECUTE [uspFetchBillOnlyDetails] @Event='BillOnly',@udtSystemData=@p2, @udtEventData=@p3

--------------------------------------------------------------------------------------------------------*/
CREATE PROCEDURE [dbo].[uspCheckIteminMASystem]
	@Event VARCHAR(255)
	, @udtSystemData udtSPParam READONLY
	, @udtEventData udtSPParam READONLY
AS

SET NOCOUNT ON;

DECLARE @Msg VARCHAR(200), @ProcessedBarcodeNo VARCHAR(250), @BarcodeCatNo VARCHAR(250), @FullBarcodeNo VARCHAR(250), @ProcessedCatNo VARCHAR (500)
DECLARE
	
        @ScannedBarcode VARCHAR(500),
		@ScannedRFID VARCHAR(500),
		@CatNo VARCHAR(25),
		@Qty INT,
        @UserID INT = 0,
		@SessionID INT,
		@DeviceID INT,
		@EntryTypeID INT,
        @ComputerName VARCHAR(25), 
		@ProductUnitID INT,
		@SettingValue VARCHAR(150),
		@DataSetName VARCHAR(50),
		@BarCodeType VARCHAR(100),
        @CurrentUtcTime DATETIME;

SET XACT_ABORT ON;
BEGIN TRY
BEGIN TRANSACTION

	SELECT @ProcessedBarcodeNo =Value FROM  @udtEventData WHERE Property = 'ProcessedBarcodeNo'
	SELECT @FullBarcodeNo =Value FROM  @udtEventData WHERE Property = 'FullBarcodeNo'
	SELECT @ProcessedCatNo =Value FROM  @udtEventData WHERE Property = 'ProcessedCatNo'
	SELECT @DataSetName = Value FROM @udtEventData WHERE Property = 'EventOutPut'

	IF @BarcodeCatNo IS NULL
	BEGIN
		--SELECT @BarcodeCatNo = [Cat No] 
		--FROM s01tblBarcodeRuleProduct 
		--WHERE [Barcode No] = @ProcessedBarcodeNo AND @ProcessedBarcodeNo <> ''
		--OR replace(replace([Cat No],'-',''),'.','')= @ProcessedCatNo AND @ProcessedCatNo <> ''
		--OR [Barcode No] = @FullBarcodeNo AND @FullBarcodeNo <> ''

		IF (@FullBarcodeNo IS NOT NULL AND @FullBarcodeNo <> '')
		BEGIN
			SELECT @BarcodeCatNo = [Cat No]
			FROM s01tblBarcodeRuleProduct 
			WHERE [Barcode No] = @FullBarcodeNo
			OR [Barcode No] = @ProcessedBarcodeNo 
		END	
		ELSE
		BEGIN
			SET @BarcodeCatNo = NULL
		END
 
		IF (@BarcodeCatNo IS NULL)
		BEGIN
			IF( @BarCodeType = 'GS1-128'AND LEN(@ProcessedBarcodeNo)=12)
			BEGIN
				SELECT @BarcodeCatNo = [Cat No]
				FROM s01tblBarcodeRuleProduct 
				WHERE [Barcode No] = @ProcessedBarcodeNo
			END
		END
		IF (@BarcodeCatNo IS NULL)
		BEGIN
			IF((LEN(@ProcessedBarcodeNo)=16 OR LEN(@ProcessedBarcodeNo)=20) AND @BarCodeType = 'GS1-128')
			BEGIN
				SELECT @BarcodeCatNo = [Cat No]
				FROM s01tblBarcodeRuleProduct 
				WHERE [Barcode No] = @ProcessedBarcodeNo
			END
		END

		IF(@BarcodeCatNo IS NOT NULL)
		BEGIN
			SELECT @ProductUnitID = ISNULL(ProdUnitID,1)
			FROM s01tblProducts
			WHERE [Cat No] = @BarcodeCatNo

			IF(@ProductUnitID = 4)
			BEGIN
				SELECT 'Product Barcode Found' as MSG
				--SELECT @DataSetName AS DataSetName, [Cat No] AS CatNo,ProductID,'Product Barcode Found' as MSG 
				--FROM s01tblProducts
				--WHERE [Cat No] = @BarcodeCatNo
			END
			ELSE
			BEGIN
				SELECT 'This is RFID tracked item please scan RFID' as MSG
				--SELECT @DataSetName AS DataSetName, [Cat No],ProductID,'This is RFID tracked item please scan RFID' as MSG
				--FROM s01tblProducts
				--WHERE [Cat No] = @BarcodeCatNo
			END
		END
		ELSE
		BEGIN
			SELECT 'Product Not in system' as MSG
		END
		
		   
	END

		
COMMIT TRANSACTION		
END TRY

BEGIN CATCH
	IF XACT_STATE() <> 0
		ROLLBACK TRANSACTION
		SET @Msg = 'Error in Searching Product details';
	EXECUTE s01uspLogError
END CATCH
SET NOCOUNT OFF
GO

CREATE PROCEDURE [dbo].[uspProcessFDABarcodeItems]
    @Event VARCHAR(255),
    @udtSystemData udtSPParam READONLY,
    @udtEventData udtSPParam READONLY
AS
BEGIN
    SET NOCOUNT ON;
    SET XACT_ABORT ON;

    DECLARE @Msg VARCHAR(200), 
            @ProcessedBarcodeNo VARCHAR(250),  
            @FullBarcodeNo VARCHAR(250), 
            @ProcessedCatNo VARCHAR(500), 
            @SessionID BIGINT, 
            @ItemStatusID INT, 
            @ItemEventID INT, 
            @Acknowledge VARCHAR(50),
            @CatNo VARCHAR(200), 
            @ProductId INT, 
            @ItemID INT, 
            @RFID VARCHAR(50), 
            @UserID INT, 
            @CurrentUtcTime DATETIME, 
            @ItemUsageID INT, 
            @ItemHistoryID INT,
            @MAPatientID VARCHAR(50), 
            @MAScheduleID INT, 
            @MAVisitID INT, 
            @OverrideId INT, 
            @OverrideNotes VARCHAR(255), 
            @PhysicianID VARCHAR(50), 
            @BillingStatusID INT, 
            @EntryTypeID INT,
            @SerialNo VARCHAR(50), 
            @MAPhysicianID INT, 
            @ComputerName VARCHAR(50), 
            @LotNo VARCHAR(50), 
            @ExpDate VARCHAR(50), 
            @Description VARCHAR(50), 
            @Manufacturer VARCHAR(50), 
            @BrandName VARCHAR(50),
            @BOID INT, 
            @DeviceID INT,  
            @LocationID INT, 
            @LocationTypeID INT, 
            @LocationID1 INT,  
            @LocationTypeID1 INT,
			@IsImplant VARCHAR(50),
			@IsLotNoReq VARCHAR(50),
			@IsSerialNoReq VARCHAR(50),
			@IsDiscontinue VARCHAR(50),
			@IsDiscontinueDate VARCHAR(50),
			@IsExpirationDateReq VARCHAR(50),
			@ModelNumber VARCHAR(200),
			@CompartmentID INT,
			@Qty INT,
			@ItemInventoryID INT,
			@UsageItemID INT,
			@isFDA VARCHAR(50);

    BEGIN TRY
        BEGIN TRANSACTION;

        SELECT @ProcessedBarcodeNo = Value FROM @udtEventData WHERE Property = 'ProcessedBarcodeNo';
        SELECT @FullBarcodeNo = Value FROM @udtEventData WHERE Property = 'FullBarcodeNo';
        SELECT @ProcessedCatNo = Value FROM @udtEventData WHERE Property = 'ProcessedCatNo';
        SELECT @UserID = Value FROM @udtEventData WHERE Property = 'UserID';
        SELECT @DeviceID = Value FROM @udtSystemData WHERE Property = 'DeviceID';
        SELECT @ComputerName = Value FROM @udtSystemData WHERE Property = 'ComputerName';
        SELECT @MAPatientID = Value FROM @udtEventData WHERE Property = 'MAPatientID';
        SELECT @MAVisitID = Value FROM @udtEventData WHERE Property = 'MAVisitID'; 
        SELECT @MAScheduleID = Value FROM @udtEventData WHERE Property = 'MAScheduleID'; 
        SELECT @ExpDate = Value FROM @udtEventData WHERE Property = 'ExpiryDate';
        SELECT @LotNo = Value FROM @udtEventData WHERE Property = 'LotNumber';
        SELECT @SerialNo = Value FROM @udtEventData WHERE Property = 'SerialNumber';
        SELECT @CatNo = Value FROM @udtEventData WHERE Property = 'CatalogNumber';    
        SELECT @Description = Value FROM @udtEventData WHERE Property = 'Description';    
        SELECT @Manufacturer = Value FROM @udtEventData WHERE Property = 'Manufacturer';    
        SELECT @BrandName = Value FROM @udtEventData WHERE Property = 'BrandName';
        SELECT @IsImplant = Value FROM @udtEventData WHERE Property = 'IsImplant';
		SELECT @IsLotNoReq = Value FROM @udtEventData WHERE Property = 'IsLotNoReq';
		SELECT @IsSerialNoReq = Value FROM @udtEventData WHERE Property = 'IsSerialNoReq';
		SELECT @IsDiscontinue = Value FROM @udtEventData WHERE Property = 'IsDiscontinue';
		SELECT @IsDiscontinueDate = Value FROM @udtEventData WHERE Property = 'IsDiscontinueDate';
		SELECT @IsExpirationDateReq = Value FROM @udtEventData WHERE Property = 'IsExpirationDateReq';
		SELECT @ModelNumber = Value FROM @udtEventData WHERE Property = 'ModelNumber';
		SELECT @isFDA = Value FROM @udtEventData WHERE Property = 'isFDA';
		SELECT @MAPatientID = Value FROM @udtEventData WHERE Property = 'MAPatientID'
		SELECT @MAVisitID = Value FROM @udtEventData WHERE Property = 'MAVisitID'
		SELECT @MAScheduleID = Value FROM @udtEventData WHERE Property = 'MAScheduleID'
		SELECT @Qty = Value FROM @udtEventData WHERE Property = 'Qty'
		set @ItemID = (select itemid from s01tblItems where RFID=@RFID)
		set @CompartmentID = (select CompartmentID from s01tblItems where ItemID=@ItemID)
		set @MAPhysicianID= (select MAPhysicianID from s01tblPatientSchedules where MAScheduleID=@MAScheduleID)
		SELECT @OverrideNotes = Value FROM @udtEventData WHERE Property = 'OverrideNotes'

        SELECT @PhysicianID = PhysicianID FROM s01qryPatientDisplayADT WHERE MAPatientID = @MAPatientID;
        SELECT @MAPhysicianID = MAPhysicianID FROM s01tblPhysicians WHERE PhysicianID = @PhysicianID;
        SELECT @SessionID = Value FROM @udtEventData WHERE Property = 'SessionID';
        SELECT @EntryTypeID = Value FROM @udtSystemData WHERE Property = 'EntryTypeID';
        SELECT @PhysicianID = PhysicianID FROM s01qryPatientDisplayADT WHERE MAPatientID = @MAPatientID;
        SELECT @BillingStatusID = BillingStatusID FROM s01tblbillingstatus WHERE BillingStatus = 'Pending Bill Approval';
        SELECT @OverrideId = OverrideID, @OverrideNotes = OverrideDesc FROM s01tblOverride WHERE OverrideDesc = 'One Time Use';

        IF (SELECT [Setting Value] FROM s01tblSettingsCommon WHERE [Setting Name] = 'ENABLE_UTC_TIMEZONE') = 'TRUE'
        BEGIN
            SELECT @CurrentUTCTime = GETUTCDATE();
        END
        ELSE
        BEGIN
            SELECT @CurrentUTCTime = GETDATE();
        END

        SELECT @ItemStatusID = ItemStatusID FROM s01tblItemStatus WHERE [Item Status] = 'Used';
        SELECT @ItemEventID = ItemEventID FROM s01tblItemEvent WHERE EventDescription = 'Item used';
        SELECT @LocationTypeID = [Setting Value] FROM s01tblSettingscommon WHERE [Setting Name] = 'BarcodeInventory_LocationType' --AND ClusterID = @DeviceID;
        SELECT @LocationID = [Setting Value] FROM s01tblSettingscommon WHERE [Setting Name] = 'BarcodeInventory_Location' --AND ClusterID = @DeviceID;

        SELECT @ProductId = ProductID FROM s01tblProducts WHERE  [Cat No] = @CatNo;

		IF(1=1)
		BEGIN
			IF(@IsImplant = 'Y' AND (@SerialNo IS NOT NULL OR @LotNo IS NOT NULL OR @ExpDate IS NOT NULL))
			BEGIN
				INSERT INTO s01tblItems (
					ProductID, [Serial No], RFID, [Lot No], [Expired Date], [ItemStatusID], [Use Cycle], [AddUserID], [Date Added],
					[RmUserID], [Date Removed], [MAScheduleID], [MAPatientID], [MAPhysicianID], [Computer Name], [LastActivityDate], [Qty], [EntryTypeID]
				)
				SELECT @ProductId, @SerialNo, 'MA' + REPLACE(CAST(RAND() AS VARCHAR(50)), '.', '') + REPLACE(CONVERT(VARCHAR(10), @CurrentUtcTime, 120), '-', ''), @LotNo, @ExpDate, 4, 1, @UserID, @CurrentUtcTime, @UserID, @CurrentUtcTime,
					   @MAScheduleID, @MAPatientID, @MAPhysicianID, @ComputerName, @CurrentUtcTime, 1, @EntryTypeID;

				SELECT @ItemID = SCOPE_IDENTITY();

				INSERT INTO s01tblItemUsage (
					MAPatientID, MAScheduleID, MAVisitID, UsedBy, DateUsed, Qty, UnitPrice, BillingStatusID, OverrideID, OverrideNotes, UpdatedBy,
					LastActivityDate, UsageTypeID, UsageItemID, SessionID, UCDM, EntryTypeID
				)
				SELECT @MAPatientID, @MAScheduleID, @MAVisitID, @UserID, @CurrentUtcTime, 1, I.[Act Price], @BillingStatusID, @OverrideID, @OverrideNotes, @UserID, @CurrentUtcTime,
					   1, I.ItemID, @SessionID, I.ICDM, @EntryTypeID
				FROM s01tblItems I WHERE ItemID = @ItemID;

				SELECT @ItemUsageID = SCOPE_IDENTITY();

				INSERT INTO s01tblItemHistory(
					ItemStatusID, ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAScheduleID, MAVisitID, BillingStatusID, UsedBy, Qty,
					UnitPrice, UpdatedBy, UCDM, LastActivityDate, EntryTypeID, DateUsed, OverrideID, OverrideNotes, SessionID, ItemEventID
				)
				SELECT 1, @ItemUsageID, 1, U.UsageItemID, MAPatientID, MAScheduleID, MAVisitID, BillingStatusID, @UserID, U.Qty, U.UnitPrice,
					   @UserID, U.UCDM, @CurrentUtcTime, @EntryTypeID, U.DateUsed, 4, U.OverrideNotes, @SessionID, @ItemEventID  
				FROM s01tblItemUsage U WHERE SessionID = @SessionID AND U.LastActivityDate = @CurrentUtcTime;

				SELECT @ItemHistoryID = SCOPE_IDENTITY();

				INSERT INTO s01tblitemhistoryextn (ItemHistoryID, LocationID)
				SELECT @ItemHistoryID, @LocationID;

				INSERT INTO s01tblItemHistory(
					ItemStatusID, ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAScheduleID, MAVisitID, BillingStatusID, UsedBy, Qty,
					UnitPrice, UpdatedBy, UCDM, LastActivityDate, EntryTypeID, DateUsed, OverrideID, OverrideNotes, SessionID, ItemEventID
				)
				SELECT 1, @ItemUsageID, 1, U.UsageItemID, MAPatientID, MAScheduleID, MAVisitID, BillingStatusID, @UserID, U.Qty, U.UnitPrice,
					   @UserID, U.UCDM, @CurrentUtcTime, @EntryTypeID, U.DateUsed, 7, U.OverrideNotes, @SessionID, @ItemEventID  
				FROM s01tblItemUsage U WHERE SessionID = @SessionID AND U.LastActivityDate = @CurrentUtcTime;

				INSERT INTO s01tblitemhistoryextn (ItemHistoryID, LocationID)
				SELECT @ItemHistoryID, @LocationID;


				EXEC uspProcessTissueVerification @ItemUsageID = @ItemUsageID
				--EXECUTE  uspSWCSendUsageToEpic @Event = @Event
				--	, @udtSystemData = @udtSystemData
				--	, @udtEventData = @udtEventData
				--	, @ItemEventID = 13 -- Credit , we can use 3 also here
				--	, @ItemStatusID = @ItemStatusID
				--	, @UsageTypeID = 1
				--	, @ItemID = @ItemID


				SELECT 'SUCCESS' as DataSetName, 'Item has been removed' AS MSG;           
			END
			--ELSE IF(@IsImplant = 'N' AND (@IsLotNoReq = 'TRUE' OR @IsSerialNoReq = 'TRUE' OR @IsExpirationDateReq = 'TRUE'))
			ELSE IF(@IsImplant = 'N' AND (@SerialNo IS NOT NULL OR @LotNo IS NOT NULL OR @ExpDate IS NOT NULL))
			BEGIN
			   INSERT INTO s01tblItems (
						ProductID, [Serial No], RFID, [Lot No], [Expired Date], [ItemStatusID], [Use Cycle], [AddUserID], [Date Added],
						[RmUserID], [Date Removed], [MAScheduleID], [MAPatientID], [MAPhysicianID], [Computer Name], [LastActivityDate], [Qty], [EntryTypeID]
					)
					SELECT @ProductId, @SerialNo, 'MA' + REPLACE(CAST(RAND() AS VARCHAR(50)), '.', '') + REPLACE(CONVERT(VARCHAR(10), @CurrentUtcTime, 120), '-', ''), @LotNo, @ExpDate, 4, 1, @UserID, @CurrentUtcTime, @UserID, @CurrentUtcTime,
						   @MAScheduleID, @MAPatientID, @MAPhysicianID, @ComputerName, @CurrentUtcTime, 1, @EntryTypeID;

					SELECT @ItemID = SCOPE_IDENTITY();

					INSERT INTO s01tblItemUsage (
						MAPatientID, MAScheduleID, MAVisitID, UsedBy, DateUsed, Qty, UnitPrice, BillingStatusID, OverrideID, OverrideNotes, UpdatedBy,
						LastActivityDate, UsageTypeID, UsageItemID, SessionID, UCDM, EntryTypeID
					)
					SELECT @MAPatientID, @MAScheduleID, @MAVisitID, @UserID, @CurrentUtcTime, 1, I.[Act Price], @BillingStatusID, @OverrideID, @OverrideNotes, @UserID, @CurrentUtcTime,
						   1, I.ItemID, @SessionID, I.ICDM, @EntryTypeID
					FROM s01tblItems I WHERE ItemID = @ItemID;

					SELECT @ItemUsageID = SCOPE_IDENTITY();

					INSERT INTO s01tblItemHistory(
						ItemStatusID, ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAScheduleID, MAVisitID, BillingStatusID, UsedBy, Qty,
						UnitPrice, UpdatedBy, UCDM, LastActivityDate, EntryTypeID, DateUsed, OverrideID, OverrideNotes, SessionID, ItemEventID
					)
					SELECT 1, @ItemUsageID, 1, U.UsageItemID, MAPatientID, MAScheduleID, MAVisitID, BillingStatusID, @UserID, U.Qty, U.UnitPrice,
						   @UserID, U.UCDM, @CurrentUtcTime, @EntryTypeID, U.DateUsed, 4, U.OverrideNotes, @SessionID, @ItemEventID  
					FROM s01tblItemUsage U WHERE SessionID = @SessionID AND U.LastActivityDate = @CurrentUtcTime;

					SELECT @ItemHistoryID = SCOPE_IDENTITY();

					INSERT INTO s01tblitemhistoryextn (ItemHistoryID, LocationID)
					SELECT @ItemHistoryID, @LocationID;

					INSERT INTO s01tblItemHistory(
					ItemStatusID, ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAScheduleID, MAVisitID, BillingStatusID, UsedBy, Qty,
					UnitPrice, UpdatedBy, UCDM, LastActivityDate, EntryTypeID, DateUsed, OverrideID, OverrideNotes, SessionID, ItemEventID
					)
					SELECT @ItemStatusID, @ItemUsageID, 1, U.UsageItemID, MAPatientID, MAScheduleID, MAVisitID, BillingStatusID, @UserID, U.Qty, U.UnitPrice,
						   @UserID, U.UCDM, @CurrentUtcTime, @EntryTypeID, U.DateUsed, 7, U.OverrideNotes, @SessionID, @ItemEventID  
					FROM s01tblItemUsage U WHERE SessionID = @SessionID AND U.LastActivityDate = @CurrentUtcTime;

					INSERT INTO s01tblitemhistoryextn (ItemHistoryID, LocationID)
					SELECT @ItemHistoryID, @LocationID;


					--EXECUTE  uspSWCSendUsageToEpic @Event = @Event
					--, @udtSystemData = @udtSystemData
					--, @udtEventData = @udtEventData
					--, @ItemEventID = 13 -- Credit , we can use 3 also here
					--, @ItemStatusID = @ItemStatusID
					--, @UsageTypeID = 1
					--, @ItemID = @ItemID

					SELECT 'SUCCESS' as DataSetName, 'Item has been removed' AS MSG;
			END
			ELSE IF(@IsImplant = 'N' AND (@SerialNo IS NULL AND @LotNo IS NULL AND @ExpDate IS NULL)) 
			BEGIN
				INSERT INTO s01tblItemInventory(ProductID,Qty,UpdatedBy,LastActivityDate,LocationID,LocationTypeID)
				VALUES (@ProductId, @Qty, @UserID, @CurrentUTCTime, @LocationID, @LocationTypeID )

				SELECT @ItemInventoryID = SCOPE_IDENTITY()

				INSERT INTO s01tblItemUsage ( MAPatientID, MAVisitID, MAScheduleID, UsedBy, DateUsed, Qty, UnitPrice, UOMCode, BillingStatusID, OverrideID, OverrideNotes
															, UpdatedBy, LastActivityDate, UsageTypeID, UsageItemID, SessionID, EntryTypeID )
									SELECT @MAPatientID, @MAVisitID, @MAScheduleID, @UserID, @CurrentUtcTime, 1, [Ref Price], UOMCode, 1, @OverrideID, @OverrideNotes, @UserID
															, @CurrentUTCTime, 2, @ItemInventoryID, @SessionID, @EntryTypeID 
									FROM s01tblProducts 
									WHERE ProductID = @ProductID

									SELECT @ItemUsageID = SCOPE_IDENTITY()
									SELECT @UsageItemID = UsageItemID 
									FROM s01tblItemUsage 
									WHERE ItemUsageID = @ItemUsageID
							
									--UPDATE s01tblItemInventory 
									--SET Qty = Qty - 1, UpdatedBy = @UserID, QtyLastUpdated = @CurrentUTCTime 
									--WHERE ItemInventoryID = @UsageItemID 

									INSERT INTO s01tblUpdateBarcodeItemInventoryLog( ItemInventoryID, QtyInHand, QtyLastUpdated, QtyUsed 
															, QtyReturned, QtyDeleted, QtyStocked, QtyWasted, CutOffTime, UpdatedQtyInHand )
									SELECT ItemInventoryID, Qty + 1, QtyLastUpdated, 1, 0, 0, 0, 0, @CurrentUTCTime, Qty 
									FROM s01tblItemInventory
									WHERE ItemInventoryID = @UsageItemID

									INSERT INTO s01tblItemHistory ( ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAVisitID, MAScheduleID, UsedBy, DateUsed, UnitPrice, UOMCode
														, OverrideID, OverrideNotes, SessionID, UCDM, BillingStatusID, ItemStatusID, UpdatedBy, LastActivityDate, ItemEventID, Qty, EntryTypeID ) 
									SELECT ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAVisitID, MAScheduleID, UsedBy, DateUsed, UnitPrice, UOMCode, @OverrideID, @OverrideNotes, @SessionID 
														, UCDM, BillingStatusID, NULL, @UserID, @CurrentUTCTime, @ItemEventID, 1, @EntryTypeID 
									FROM s01tblItemUsage 
									WHERE ItemUsageID = @ItemUsageID

									SELECT @ItemHistoryID = SCOPE_IDENTITY()
									INSERT INTO s01tblItemHistoryExtn (ItemHistoryID, LocationID ) 
									SELECT ItemHistoryID, @LocationID 
									FROM s01tblItemHistory 
									WHERE ItemHistoryID = @ItemHistoryID

									INSERT INTO tblInventoryLog ( ItemHistoryID, InvntoryID, ProcessStatus, ClusterID, LocationID, UpdatedBy, EntryTypeID, EnteredDate )
									SELECT ItemHistoryID, UsageItemID, 'P', @DeviceID, @LocationID, @UserID, @EntryTypeID, @CurrentUTCTime 
									FROM s01tblItemHistory 
									WHERE ItemHistoryID = @ItemHistoryID	
			END
		END
		

    COMMIT TRANSACTION;
    END TRY

    BEGIN CATCH
	IF XACT_STATE() <> 0
		SET @Msg = 'Error in Searching Product details';
	EXECUTE s01uspLogError
END CATCH
SET NOCOUNT OFF
END;
GO

CREATE PROCEDURE [dbo].[uspProcessTissueVerification]
    @ItemUsageID varchar(25)
    
AS
    SET NOCOUNT ON;
    DECLARE @lUpdateCnt BIGINT=0
    DECLARE @sIsImplant varchar(10)='N'
	DECLARE @UsageItemID VARCHAR(200)
	DECLARE @UsageTypeID VARCHAR(50)
    SELECT @UsageItemID = UsageItemID, @UsageTypeID = UsageTypeID, @sIsImplant=ISNULL(RFID,'N')
    from s01qryItemUsage
    WHERE ItemUsageID = @ItemUsageID
   
    --SET @TransactionType = ISNULL(UPPER(@TransactionType), 'SCAN')    
    IF @UsageTypeID=1 AND @sIsImplant='Y'
    BEGIN
        IF EXISTS(SELECT * FROM s01tblItemUsageExt WHERE ItemUsageID = @ItemUsageID)
        BEGIN
            UPDATE s01tblItemUsageExt
            SET PckIntegrityVerified = 1, IsImplanted = 1, ImplantedDate = GETDATE()
            WHERE ItemUsageID = @ItemUsageID        
            SELECT @lUpdateCnt = @@ROWCOUNT    
        END
        ELSE
        BEGIN
            INSERT INTO s01tblItemUsageExt(ItemUsageID, PckIntegrityVerified, IsImplanted, ImplantedDate)
            VALUES(@ItemUsageID, 1, 1, GETDATE())
            SELECT @lUpdateCnt = @@ROWCOUNT
        END
    END
    Select @lUpdateCnt
GO

--select *
--from [tblProductsExtn]
--select *
--from tblProducts
--where [Cat No] = 'BL-1800-10'

--select *
--from tblBarcodeRuleProduct
--where [Barcode No] like '%010088985860844217291117212415105-3117%'

--select *
--from tblItems
--where ProductID = 9869

--select *
--from tblBarcodeRuleProduct
--where [Barcode No] = '00889858608442'



CREATE PROCEDURE [dbo].[uspSaveFDADetailsinMASystem]
	@Event VARCHAR(255),
	@udtSystemData udtSPParam READONLY,
	@udtEventData udtSPParam READONLY
    --@ScannedBarcode VARCHAR(100),
    --@ProductBarcode varchar(100),
    --@CatNo varchar(100),
    --@Description varchar(100),
    --@Manufacturer varchar(100),
    --@ClusterID varchar(25),
    --@UserID varchar(25),
    --@ComputerName varchar(100),
    --@Domain varchar(100)
    --@IsImplant varchar(10)='N',
    --@ReqSerialNo varchar(10)='N',
    --@ReqLotNo varchar(10)='N',
    --@ProcessedFDAData varchar(2000)=''
AS
    SET NOCOUNT ON;
    DECLARE @CatNoExists varchar(100)
    --DECLARE @ProductID BIGINT
	DECLARE @Msg VARCHAR(200), 
            @ProcessedBarcodeNo VARCHAR(250),  
            @FullBarcodeNo VARCHAR(250), 
            @ProcessedCatNo VARCHAR(500), 
            @SessionID BIGINT, 
            @ItemStatusID INT, 
            @ItemEventID INT, 
            @Acknowledge VARCHAR(50),
            @CatNo VARCHAR(200), 
            @ProductId INT, 
            @ItemID INT, 
            @RFID VARCHAR(50), 
            @UserID INT, 
            @CurrentUtcTime DATETIME, 
            @ItemUsageID INT, 
            @ItemHistoryID INT,
            @MAPatientID VARCHAR(50), 
            @MAScheduleID INT, 
            @MAVisitID INT, 
            @OverrideId INT, 
            @OverrideNotes VARCHAR(255), 
            @PhysicianID VARCHAR(50), 
            @BillingStatusID INT, 
            @EntryTypeID INT,
            @SerialNo VARCHAR(50), 
            @MAPhysicianID INT, 
            @ComputerName VARCHAR(100), 
            @LotNo VARCHAR(50), 
            @ExpDate VARCHAR(50), 
            @Description VARCHAR(50), 
            @Manufacturer VARCHAR(50), 
            @BrandName VARCHAR(50),
            @BOID INT, 
            @DeviceID INT,  
            @LocationID INT, 
            @LocationTypeID INT, 
            @LocationID1 INT,  
            @LocationTypeID1 INT,
			@IsImplant VARCHAR(50),
			@IsLotNoReq VARCHAR(50),
			@IsSerialNoReq VARCHAR(50),
			@IsDiscontinue VARCHAR(50),
			@IsDiscontinueDate VARCHAR(50),
			@IsExpirationDateReq VARCHAR(50),
			@ModelNumber VARCHAR(200),
			@CompartmentID INT,
			@Qty INT,
			@Domain varchar(100),
			@ItemInventoryID INT,
			@UsageItemID INT,
			@CurrentDate datetime,
			@isFDA VARCHAR(50);
		SELECT @ProcessedBarcodeNo = Value FROM @udtEventData WHERE Property = 'ProcessedBarcodeNo';
        SELECT @FullBarcodeNo = Value FROM @udtEventData WHERE Property = 'FullBarcodeNo';
        SELECT @ProcessedCatNo = Value FROM @udtEventData WHERE Property = 'ProcessedCatNo';
        SELECT @UserID = Value FROM @udtEventData WHERE Property = 'UserID';
        SELECT @DeviceID = Value FROM @udtSystemData WHERE Property = 'DeviceID';
        SELECT @ComputerName = Value FROM @udtSystemData WHERE Property = 'ComputerName';
        SELECT @MAPatientID = Value FROM @udtEventData WHERE Property = 'MAPatientID';
        SELECT @MAVisitID = Value FROM @udtEventData WHERE Property = 'MAVisitID'; 
        SELECT @MAScheduleID = Value FROM @udtEventData WHERE Property = 'MAScheduleID'; 
        SELECT @ExpDate = Value FROM @udtEventData WHERE Property = 'ExpiryDate';
        SELECT @LotNo = Value FROM @udtEventData WHERE Property = 'LotNumber';
        SELECT @SerialNo = Value FROM @udtEventData WHERE Property = 'SerialNumber';
        SELECT @CatNo = Value FROM @udtEventData WHERE Property = 'CatalogNumber';    
        SELECT @Description = Value FROM @udtEventData WHERE Property = 'Description';    
        SELECT @Manufacturer = Value FROM @udtEventData WHERE Property = 'Manufacturer';    
        SELECT @BrandName = Value FROM @udtEventData WHERE Property = 'BrandName';
        SELECT @IsImplant = Value FROM @udtEventData WHERE Property = 'IsImplant';
		SELECT @IsLotNoReq = Value FROM @udtEventData WHERE Property = 'IsLotNoReq';
		SELECT @IsSerialNoReq = Value FROM @udtEventData WHERE Property = 'IsSerialNoReq';
		SELECT @IsDiscontinue = Value FROM @udtEventData WHERE Property = 'IsDiscontinue';
		SELECT @IsDiscontinueDate = Value FROM @udtEventData WHERE Property = 'IsDiscontinueDate';
		SELECT @IsExpirationDateReq = Value FROM @udtEventData WHERE Property = 'IsExpirationDateReq';
		SELECT @ModelNumber = Value FROM @udtEventData WHERE Property = 'ModelNumber';
		SELECT @isFDA = Value FROM @udtEventData WHERE Property = 'isFDA';
		SELECT @MAPatientID = Value FROM @udtEventData WHERE Property = 'MAPatientID'
		SELECT @MAVisitID = Value FROM @udtEventData WHERE Property = 'MAVisitID'
		SELECT @MAScheduleID = Value FROM @udtEventData WHERE Property = 'MAScheduleID'
		SELECT @Qty = Value FROM @udtEventData WHERE Property = 'Qty'
		set @ItemID = (select itemid from s01tblItems where RFID=@RFID)
		set @CompartmentID = (select CompartmentID from s01tblItems where ItemID=@ItemID)
		set @MAPhysicianID= (select MAPhysicianID from s01tblPatientSchedules where MAScheduleID=@MAScheduleID)
		SELECT @OverrideNotes = Value FROM @udtEventData WHERE Property = 'OverrideNotes'
		SET @CurrentDate = GETDATE();

        SELECT @PhysicianID = PhysicianID FROM s01qryPatientDisplayADT WHERE MAPatientID = @MAPatientID;
        SELECT @MAPhysicianID = MAPhysicianID FROM s01tblPhysicians WHERE PhysicianID = @PhysicianID;
        SELECT @SessionID = Value FROM @udtEventData WHERE Property = 'SessionID';
        SELECT @EntryTypeID = Value FROM @udtSystemData WHERE Property = 'EntryTypeID';
        SELECT @PhysicianID = PhysicianID FROM s01qryPatientDisplayADT WHERE MAPatientID = @MAPatientID;
        SELECT @BillingStatusID = BillingStatusID FROM s01tblbillingstatus WHERE BillingStatus = 'Pending Bill Approval';
        SELECT @OverrideId = OverrideID, @OverrideNotes = OverrideDesc FROM s01tblOverride WHERE OverrideDesc = 'One Time Use';
   
    IF (@CatNo is not null)
    BEGIN
        SELECT @CatNoExists = [Cat No]
        FROM s01tblProducts
        WHERE [Cat No] = @CatNo OR [Cat No] = @ProcessedCatNo
        SELECT @CatNoExists = [Cat No]
        FROM s01tblBarcodeRuleProduct
        WHERE [Barcode No] = @ProcessedBarcodeNo OR [Barcode No] = @FullBarcodeNo
        IF @CatNoExists is null
        BEGIN
            BEGIN
                INSERT INTO [dbo].[s01tblProducts]
                   ([Cat No]
                   ,[Description 1]
                   ,[Manufacturer]
                   ,[SKU No]
                   ,[Entered By]
                   ,[Computer Name]
                   ,[Domain]
                   ,[Entered Date]
                   ,[ProductTypeID]
                   ,[EntryTypeID]
                   ,[ProdUnitID]
                   ,[RFID]
                   ,[ID 3]
                   ,[ID 4]
				   ,[Brand Name])
                VALUES
                   (@CatNo
                   ,@Description
                   ,@Manufacturer
                   ,@ProcessedBarcodeNo
                   ,@UserID
                   ,@ComputerName
                   ,@Domain
                   ,GETDATE()
                   ,1
                   ,1
                   ,4
                   ,@IsImplant
                   ,@IsSerialNoReq
                   ,@IsLotNoReq
				   ,@BrandName)            
                SET @ProductID = SCOPE_IDENTITY()
            END
            BEGIN
                INSERT INTO .[dbo].[s01tblBarcodeRuleProduct]
                   ([Barcode No]
                   ,[Cat No])
                VALUES
                   (@ProcessedBarcodeNo
                   ,@CatNo)
            END
			BEGIN
                INSERT INTO .[dbo].[s01tblProductsExtn]
                   (ProductID
                   ,ModelNumber
				   ,isSerialNoReq
				   ,isLotNoReq
				   ,isImplant
				   ,[ExpirationDate]
				   ,isExpirationDateReq
				   ,isDiscontiued
				   ,isDiscontiueDate
				   ,LastActivityDate
				   ,CreatedDate)
                VALUES
                   (@ProductID
                   ,@ModelNumber
				   ,@IsSerialNoReq
				   ,@IsLotNoReq
				   ,@IsImplant
				   ,@ExpDate
				   ,@IsExpirationDateReq
				   ,@IsDiscontinue
				   ,@IsDiscontinueDate
				   ,@CurrentDate
				   ,@CurrentDate)
            END
			--BEGIN
			--	IF NOT EXISTS (SELECT 1 FROM s01tblItems WHERE ProductID = @ProductID AND (@IsImplant = 'Y' OR @IsImplant IS NULL))
			--	BEGIN
			--		INSERT INTO s01tblItems (
			--		ProductID, [Serial No], RFID, [Lot No], [Expired Date], [ItemStatusID], [Use Cycle], [AddUserID], [Date Added],
			--		[RmUserID], [Date Removed], [MAScheduleID], [MAPatientID], [MAPhysicianID], [Computer Name], [LastActivityDate], [Qty], [EntryTypeID]
			--		)
			--		SELECT @ProductId, @SerialNo, 'MA' + REPLACE(CAST(RAND() AS VARCHAR(50)), '.', '') + REPLACE(CONVERT(VARCHAR(10), @CurrentUtcTime, 120), '-', ''), @LotNo, @ExpDate, 4, 1, @UserID, @CurrentDate, @UserID, @CurrentDate,
			--		@MAScheduleID, @MAPatientID, @MAPhysicianID, @ComputerName, @CurrentUtcTime, 1, @EntryTypeID;
			--	END
			--	ELSE
			--	BEGIN
			--		INSERT INTO s01tblItemInventory(ProductID,Qty,UpdatedBy,LastActivityDate)
			--		VALUES (@ProductId, @Qty, @UserID, @CurrentDate)
			--	END
			--END
        END
    END
    SELECT P.ProductID, P.[Cat No], P.[Description 1], P.Manufacturer, P.[Brand Name],PE.isImplant,PE.isLotNoReq,PE.isSerialNoReq,PE.ModelNumber,PE.[ExpirationDate]
	,NULL AS [Serial No],NULL AS [Lot No]
	FROM s01tblProducts P LEFT OUTER JOIN
	s01tblProductsExtn PE ON P.ProductID = PE.ProductID
	WHERE P.ProductID = @ProductID
	--SELECT P.ProductID, P.[Cat No], P.[Description 1], P.Manufacturer, P.[Brand Name],PE.isImplant,PE.isLotNoReq,PE.isSerialNoReq,PE.ModelNumber,PE.[ExpirationDate]
	--,I.[Serial No],I.[Lot No]
	--FROM s01tblProducts P LEFT OUTER JOIN
	--s01tblProductsExtn PE ON P.ProductID = PE.ProductID LEFT OUTER JOIN
	--s01tblItems I ON P.ProductID = I.ProductID
	--WHERE P.ProductID = @ProductID
GO

