"use strict";
(function(root, factory) {
  if(typeof exports === 'object') {
    module.exports = factory({{{cjsDependencies}}});
  }
  else if(typeof define === 'function' && define.amd) {
    define({{#if amdModuleId}}'{{amdModuleId}}', {{/if}}[{{{amdDependencies}}}], factory);
  }
  else {
    root.{{globalAlias}} = factory({{{globalDependencies}}});
  }


}(this, function({{dependencies}}) {

{{{code}}}

return {{objectToExport}};
}));
