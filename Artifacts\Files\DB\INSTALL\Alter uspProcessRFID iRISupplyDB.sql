/****************************************************************************************************
Name				: uspProcessRFIDItems
Version				: *******
Purpose				: To Process the Items through cabinet scan with passing different Event
Author				: Rashmita
Date Created 		: 29th November 2018
Application Affected: IriSupply
-----------------------------------------------------------------------------------------------------
Release Notes		: 
******* - [Rashmita- 6th Dec 2018] Updated Script as the Process workflow SP Merged to One SP for different Event.
******* - [Ra<PERSON>mita- 6th Dec 2018] Added the Fixed for the Bug # 4768 & 4630.
******** - [<PERSON><PERSON><PERSON>a- 11th Dec 2018] Updated Script to support 'Swap' workflow.
******** - [Rashmita- 11th Dec 2018] Updated Script to support 'Override' workflow.
******** - [Rashmita- 24th Dec 2018] Updated Script to log expired Items.
******** - [Rashmita- 24th Jan 2018] Added logging the Expired in cabinet Items
******** - [Rashmita- 3rd May 2019] Added Emergency Override workflow condition.
******** - [Rashmita- 5th May 2019] Bug#16260 - Updated the condition to update OverrideNotes as 'Default' when selecting patient.
******** - [Rashmita- 20th June 2019] Added the script to send teh dataset for usage summary screen.
******** - [Rashmita- 7th Aug 2019] Added the script for Intila and Key Access scan
******** - [Rashmita- 20th Auf 2019] Added the Unassociated Item Workflow.
******** - [Subramanya- 20th Aug 2020] Updated script for ALERT Enhancement and written code  as per code indentation
******** - [Subramanya- 16th Oct 2020] Handled unassociated Items based on setting value
******* -  [Subramanya- 4th Feb 2021] Deleted temparory table (@udtTransactedItemDetails) for transacted items and created user defined tables for
                                      sending transacted items as parameter to other SPs
******** - [Subramanya- 4th Feb 2021] Implemented EPIC
*****************************************************************************************************/
CREATE PROCEDURE [dbo].[uspProcessRFIDItems]
	@Event VARCHAR(200)
	, @udtSystemData udtSPParam READONLY
	, @udtEventData udtSPParam READONLY
	, @udtTransactedItems udtTransactedItems READONLY
	, @udtTagsData udtTags READONLY
AS
	SET NOCOUNT ON;

	DECLARE @UserID INT, @EntryTypeID INT, @SessionID BIGINT, @DeviceID INT, @BillingStatusID INT, @OverrideID INT, @OverrideNotesID INT, @OverrideNotes VARCHAR(255)
	DECLARE @ItemEventID INT, @UsageTypeID INT, @ItemStatusID INT, @MAPatientID VARCHAR(50), @MAScheduleID INT, @MAVisitID INT, @AlertMessage VARCHAR(512)
	DECLARE @CurrentUtcTime DATETIME, @Description VARCHAR(50), @OverrideDescNotes VARCHAR(50), @UageSummarySettings VARCHAR(250), @AlertID INT, @DBID INT
	DECLARE @ClientTimeZone VARCHAR(500), @UTCStandard VARCHAR(100), @SecondDiff INT, @ShowUnAssociateItems VARCHAR(50), @PhysicianID VARCHAR(50)
	DECLARE @MAPhysicianID INT, @EnableEpicInterface VARCHAR(50), @SpuriousLogAlertEnabled BIT, @EXPDATE_CHECK VARCHAR(10), @CLOSEEXP_CHECK VARCHAR(10)
	DECLARE @MAX_RMV_PAT_ITEMS INT, @MAX_RMV_PAT_ITEMS_AlertEnabled BIT, @AntenaStatus VARCHAR(500), @AntennaFailure_AlertEnabled INT,@FetchRFID VARCHAR(50),@isScannedInHUB VARCHAR(10)
	DECLARE @OldcompartmentID INT, @OldMAScheduleID INT, @ItemID INT

	DECLARE @udtTransactedItemDetails udtTransactedItemDetails 

	  --SET XACT_ABORT ON;
		BEGIN TRY
	 --BEGIN TRANSACTION

		SELECT @DeviceID = Value FROM @udtSystemData WHERE Property = 'DeviceID'
		SELECT @EntryTypeID = Value FROM @udtSystemData WHERE Property = 'EntryTypeID'
		SELECT @SessionID = Value FROM @udtEventData WHERE Property = 'SessionID'
		SELECT @UserID = Value FROM @udtEventData WHERE Property = 'UserID'
		SELECT @MAPatientID = Value FROM @udtEventData WHERE Property = 'MAPatientID'
		SELECT @Description = Value FROM @udtEventData WHERE Property = 'EventInput1'
		SELECT @OverrideDescNotes = Value FROM @udtEventData WHERE Property = 'EventInput2'

		SELECT @MAVisitID = Value FROM @udtEventData WHERE Property = 'MAVisitID' 
		SELECT @MAScheduleID = Value FROM @udtEventData WHERE Property = 'MAScheduleID' 
		SELECT @AntenaStatus = Value FROM @udtEventData WHERE Property = 'AntennaStatus' 

		SELECT @PhysicianID = PhysicianID FROM s01qryPatientDisplayADT WHERE MAPatientID = @MAPatientID 
		SELECT @MAPhysicianID = MAPhysicianID FROM s01tblPhysicians WHERE PhysicianID = @PhysicianID

		SELECT @ShowUnAssociateItems = [dbo].[udfSUPFetchSettings] ( @DeviceId,'SHOW_UNASSOCIATEDITEM' )

		SELECT @DBID = [dbo].[udfSUPFetchSettings] ( @DeviceId,'DBID' ) 

		SELECT @MAX_RMV_PAT_ITEMS = [dbo].[udfSUPFetchSettings] ( @MAX_RMV_PAT_ITEMS,'MAX_RMV_PAT_ITEMS' ) 

		SELECT @MAX_RMV_PAT_ITEMS_AlertEnabled = AlertEnabled 
		FROM tblAlerts 
		WHERE AlertDesc = 'Maximum Item removal for patient workflow'

		SELECT @AntennaFailure_AlertEnabled = AlertEnabled 
		FROM tblAlerts 
		WHERE AlertDesc = 'Antenna Failure'

		SELECT @UsageTypeID = UsageTypeID 
		FROM s01tblUsageType 
		WHERE UsageType = 'RF ITEM'

		--11/10/2024  viresh add below scrips to validate item is scanned in HUB or Not
		SELECT @FetchRFID =  RFID FROM @udtTransactedItems
		SELECT @isScannedInHUB = CASE WHEN ItemStatusID = 4 THEN 'TRUE' ELSE 'FALSE' END
		FROM s01tblItems
		WHERE RFID = @FetchRFID;
		--end

		SET @EnableEpicInterface =  [dbo].[udfSUPFetchSettings] (@DeviceId,'ENABLE_EPIC_INTERFACE') 

		SELECT @OverrideID = OverrideID 
		FROM s01tblOverride 
		WHERE OverrideDesc = CASE 
								WHEN @Description = 'Waste Item' THEN 'Waste'   
								WHEN @Description = 'Override' THEN 'Manual Override'
								WHEN @MAPatientID IS NOT NULL THEN 'Default'
								WHEN @Event = 'InitialScan' THEN 'Initial Scan'
								WHEN @Event = 'KeyAccessScan' THEN 'Key Access Scan'
								WHEN @Description IN ( 'Swap Item','SwapItem') THEN 'Swap Item'
								WHEN @Description IN ('Take','Waste','Return') THEN 'Default' 
								WHEN @Event= 'EmergencyOverride' THEN 'Emergency Override'
								ELSE @Description 
							 END

		IF ( @OverrideDescNotes IS NULL )
		BEGIN
			SELECT @OverrideNotes = NULL -- SR --'DEFAULT'
		END

		ELSE IF ( @OverrideDescNotes IS NOT NULL )
			SELECT @OverrideNotes = OverrideNotes 
			FROM s01tblOverrideNotes 
			WHERE OverrideNotes = @OverrideDescNotes AND OverrideID = @OverrideID

		IF ( @OverrideDescNotes  = 'Emergency Override')
		BEGIN
			SELECT @OverrideID = OverrideID, @OverrideNotes = 'Emergency Override' 
			FROM s01tblOverride 
			WHERE OverrideDesc = @OverrideDescNotes
		END
		
		IF ( @Event IN ( 'InitialScan' , 'KeyAccessScan' ) ) 
		BEGIN
			SELECT @OverrideNotes = OverrideDesc 
			FROM s01tblOverride 
			WHERE OverrideDesc = @Event
		END

		IF(@UserID IS NULL)
		BEGIN
           SET @UserID=(Select userID 
		   from s01tblUsers
		   where [Last Name] like '%System%')
		END
		
		SET @EXPDATE_CHECK =  [dbo].[udfSUPFetchSettings] (@DeviceId,'EXPDATE_CHECK') 
		SET @CLOSEEXP_CHECK = [dbo].[udfSUPFetchSettings] (@DeviceId,'CLOSEEXP_CHECK') 

		SELECT @AlertID = AlertID, @AlertMessage = AlertMessage
		FROM tblAlerts 
		WHERE AlertDesc = 'Expired Item found in cabinet'

		IF (@ShowUnAssociateItems = 'FALSE')
		BEGIN
			INSERT INTO @udtTransactedItemDetails(ItemID, RFID, LocationID, ProductID, UseCycle, Qty, UOMCode, TagType, MA_ScheduleID )
			SELECT TI.ItemID, TI.RFID, TI.LocationID, I.ProductID
				   , CASE  
						WHEN TagType = 'LostTag' THEN ISNULL( I.[Use Cycle], 0 ) + 1
						ELSE I.[Use Cycle] 
					END, I.Qty, P.UOMCode, TI.TagType, @MAScheduleID
			FROM @udtTransactedItems TI 
				LEFT OUTER JOIN s01tblItems I ON I.RFID = TI.RFID 
				LEFT OUTER JOIN s01tblProducts P ON I.ProductID = P.ProductID
			WHERE I.ProductID <> 0 

			SELECT 'TagDetails' AS DataSetName,* 
			FROM @udtTransactedItemDetails TI
				INNER JOIN s01qryItems I ON I.RFID = TI.RFID

		END
		ELSE
		BEGIN
			INSERT INTO @udtTransactedItemDetails(ItemID, RFID, LocationID, ProductID, UseCycle, Qty, UOMCode, TagType, MA_ScheduleID  )
			SELECT TI.ItemID, TI.RFID, TI.LocationID, I.ProductID
				   , CASE  
						WHEN TagType = 'LostTag' THEN ISNULL( I.[Use Cycle], 0 ) + 1
						ELSE I.[Use Cycle] 
					END, I.Qty, P.UOMCode, TI.TagType, @MAScheduleID 
			FROM @udtTransactedItems TI 
				LEFT OUTER JOIN s01tblItems I ON I.RFID = TI.RFID 
				LEFT OUTER JOIN s01tblProducts P ON I.ProductID = P.ProductID
            
			SELECT 'TagDetails' AS DataSetName,* 
			FROM @udtTransactedItemDetails TI
				LEFT OUTER JOIN s01qryItems I ON I.RFID = TI.RFID

			IF ( SELECT COUNT(*) FROM @udtTransactedItems WHERE TagType = 'NewTag' ) > 0 
			BEGIN

				EXECUTE uspProcessUnassociatedItems @Event = @Event
						, @udtSystemData = @udtSystemData 
						, @udtEventData = @udtEventData		
						, @udtTransactedItemDetails = @udtTransactedItemDetails

			END

		END

		SELECT @SpuriousLogAlertEnabled = AlertEnabled 
		FROM tblAlerts 
		WHERE AlertDesc = 'Items appearing from other areas'

		IF (@SpuriousLogAlertEnabled = 1)
		BEGIN
			EXECUTE uspLogSpuriousTags  @Event = @Event
					, @udtSystemData = @udtSystemData 
					, @udtEventData = @udtEventData		
					, @udtTransactedItemDetails = @udtTransactedItemDetails
		END

		SELECT @UageSummarySettings = [dbo].[udfSUPFetchSettings] ( @DeviceId,'SHOW_USAGE_SUMMARY' ) 

		SELECT @ClientTimeZone = [dbo].[udfSUPFetchSettings] ( @DeviceId,'CLIENT_TIMEZONE' )

		SELECT @UTCStandard = UTCStandard 
		FROM tblStandardTimeZone 
		WHERE StandardTimeZone = @ClientTimeZone

		SELECT @SecondDiff =  REPLACE(@UTCStandard, 'UTC', '')

		IF (SELECT [Setting Value] FROM dbo.tblSupplySettings WHERE [Setting Name] = 'ENABLE_UTC_TIMEZONE') = 'TRUE'
		BEGIN
			SELECT @CurrentUTCTime = GETUTCDATE()
		END
		ELSE
		BEGIN
			SELECT @CurrentUTCTime = GETDATE()
		END
								
		IF ( SELECT COUNT(*) FROM @udtTransactedItems WHERE TagType = 'FoundTag' ) > 0
		BEGIN
			SELECT @ItemStatusID = ItemStatusID 
			FROM s01tblItemStatus 
			WHERE [Item Status] = 'In Cabinet'

			SELECT @ItemEventID = CASE 
									  WHEN @Event = 'StockNewItem' 
										THEN 1
                                      ELSE 3
								  END

			UPDATE s01tblItems 
			SET ItemStatusID = @ItemStatusID, [CompartmentID] = TI.LocationID, [Use Cycle] = TI.[UseCycle], [AddUserID] = @UserID
				, [Date Added] = @CurrentUtcTime, EntryTypeID = @EntryTypeID, SupplierID = 0, LastActivityDate = @CurrentUtcTime
				, MAPatientID = NULL, MAScheduleID = NULL, MAPhysicianID = NULL 
				, Qty = 1 
			FROM s01tblItems I 
				INNER JOIN @udtTransactedItemDetails TI ON TI.RFID = I.RFID
			WHERE TagType = 'FoundTag'

         IF(@Event ='StockNewItem' )
			BEGIN
				INSERT INTO s01tblItemHistory ( ItemStatusID, UsageTypeID, UsageItemID, MAPatientID, MAScheduleID, MAVisitID, BillingStatusID, Qty, UnitPrice, UOMCode
							,  UpdatedBy, UCDM, LastActivityDate, EntryTypeID, OverrideID, OverrideNotes, SessionID, ItemEventID )
				SELECT @ItemStatusID, @UsageTypeID, I.ItemID, I.MAPatientID INT, I.MAScheduleID, I.MAVisitID, @BillingStatusID, I.Qty, I.[Act Price], TI.UOMCode
							, @UserID, I.ICDM, @CurrentUtcTime, @EntryTypeID, @OverrideID, @OverrideNotes, @SessionID, @ItemEventID 
				FROM    s01qryItems I INNER JOIN @udtTransactedItemDetails TI ON TI.RFID = I.RFID AND TagType = 'FoundTag'
			END
		 ELSE
			BEGIN
				INSERT INTO s01tblItemHistory ( ItemStatusID, UsageTypeID, UsageItemID, MAPatientID, MAScheduleID, MAVisitID, BillingStatusID, Qty, UnitPrice, UOMCode
							,  UpdatedBy, UCDM, LastActivityDate, EntryTypeID, OverrideID, OverrideNotes, SessionID, ItemEventID ,ItemUsageID,UsedBy,DateUsed)
				SELECT @ItemStatusID, @UsageTypeID, TI.ItemID, IU.MAPatientID INT, IU.MAScheduleID, IU.MAVisitID, IU.BillingStatusID, ISNULL(IU.Qty,1), IU.UnitPrice, TI.UOMCode
							, @UserID, IU.UCDM, @CurrentUtcTime, @EntryTypeID, @OverrideID, @OverrideNotes, @SessionID, @ItemEventID,IU.ItemUsageID,IU.UsedBY,IU.Dateused
				FROM s01tblItemUsage IU INNER JOIN @udtTransactedItemDetails TI ON TI.ItemID = IU.UsageItemID AND IU.UsageTypeID=1 AND TagType = 'FoundTag'
			END

			INSERT INTO s01tblitemhistoryextn (ItemHistoryID, LocationID)
			SELECT ItemHistoryID, T.LocationID 
			FROM @udtTransactedItemDetails T
				INNER JOIN s01tblItemHistory H ON H.UsageItemID = T.ItemID 
				AND H.LastActivityDate = @CurrentUtcTime 
				AND H.UsageTypeID=1
				AND T.TagType = 'FoundTag'

			
			DELETE FROM s01tblItemUsage
			WHERE UsageTypeID = 1 AND UsageItemID IN ( SELECT I.ItemID 
														FROM s01tblItems I INNER JOIN @udtTransactedItemDetails TI ON TI.RFID = I.RFID 
														WHERE TagType = 'FoundTag' )

			IF @EXPDATE_CHECK = 'TRUE'
			BEGIN
				INSERT INTO tblAlertLog( AlertLogDBID, AlertID, ItemID, MAPatientID, MAScheduleID, MAVisitID, UseCycle, ExceptionNotesID, LocationID, UserID, EntryTypeID, SessionID, AlertLogDesc, AlertLogDate )
				SELECT @DBID, @AlertID, I.ItemID, @MAPatientID, @MAScheduleID, @MAVisitID, I.[Use Cycle], @OverrideNotesID, I.CompartmentID, @UserID, @EntryTypeID, @SessionID, @AlertMessage, CONVERT( VARCHAR, DATEADD( SECOND, @SecondDiff, @CurrentUtcTime ), 121)
				FROM @udtTransactedItemDetails F 
					INNER JOIN s01tblItems I ON I.RFID = F.RFID 
				WHERE I.[Expired Date] <= @CurrentUtcTime 
				AND F.TagType = 'FoundTag'	
				AND I.ItemID NOT IN ( SELECT ItemID 
										FROM tblAlertLog 
										WHERE AlertID = 1  
											AND SESSIONID = @SessionID )									
			END 

			EXECUTE  uspLoadUsageList @Event = @Event
					, @udtSystemData = @udtSystemData
					, @udtEventData = @udtEventData
						
		END

		IF ( SELECT COUNT(*) FROM @udtTransactedItems WHERE TagType = 'LostTag' ) > 0
		BEGIN
			SELECT @ItemStatusID = ItemStatusID 
			FROM s01tblItemStatus 
			WHERE [Item Status] = CASE 
									WHEN @Description IN ( 'Waste Item','Waste') THEN 'Waste' 
									WHEN @Description = 'Transfer Item' THEN 'Transfered'
									WHEN @Description IN ( 'Swap Item','SwapItem') THEN 'Swap'
									WHEN @Description = 'Render To External Hospital' THEN 'Render to External Hospital'   
									ELSE 'Removed' 
									END

			SELECT @ItemEventID = ItemEventID 
			FROM s01tblItemEvent 
			WHERE EventDescription = CASE 
											WHEN @Description IN ('Waste Item','Waste') THEN 'Item has been wasted' 
											WHEN @Description = 'Transfer Item' THEN 'Item has been transfered'
											ELSE 'Item taken out of the cabinet' 
										END

			SELECT @BillingStatusID = BillingStatusID 
			FROM s01tblbillingstatus 
			WHERE BillingStatus= 'Pending Bill Approval'

			IF ( ( SELECT COUNT(*) FROM @udtTransactedItems WHERE TagType = 'LostTag' ) > @MAX_RMV_PAT_ITEMS 
						AND @MAX_RMV_PAT_ITEMS_AlertEnabled = 1 AND @MAPatientID IS NOT NULL)
			BEGIN

				EXECUTE uspProcessMaxRmvPatItems @Event = @Event
						, @udtSystemData = @udtSystemData
						, @udtEventData = @udtEventData
						, @udtTransactedItemDetails = @udtTransactedItemDetails
						, @ItemStatusID = @ItemStatusID
						, @ItemEventID = @ItemEventID
						, @CurrentUtcTime = @CurrentUtcTime
						, @UsageTypeID = @UsageTypeID
			END
			ELSE
			BEGIN

				UPDATE s01tblItems 
				SET ItemStatusID = @ItemStatusID, RMUserID = @UserID, [Date Removed] = @CurrentUtcTime, [Use Cycle] = TI.[UseCycle]
					, EntryTypeID = @EntryTypeID, MAScheduleID = @MAScheduleID, MAPatientID = @MAPatientID, LastActivityDate = @CurrentUtcTime
					, MAPhysicianID = @MAPhysicianID 
				FROM s01tblItems I 
					INNER JOIN @udtTransactedItemDetails TI ON TI.RFID = I.RFID WHERE TagType = 'LostTag' 

				DELETE FROM  s01tblItemUsage
				WHERE UsageTypeID = 1 AND UsageItemID IN ( SELECT I.ItemID 
															FROM  s01tblItems I INNER JOIN @udtTransactedItemDetails TI ON TI.RFID = I.RFID 
															WHERE TagType = 'LostTag' )

				INSERT INTO s01tblItemUsage ( MAPatientID, MAScheduleID, MAVisitID, UsedBy, DateUsed, Qty, UnitPrice, UOMCode, BillingStatusID, OverrideID, OverrideNotes, UpdatedBy
									, LastActivityDate , UsageTypeID, UsageItemID, SessionID, UCDM, EntryTypeID )
				SELECT @MAPatientID, @MAScheduleID, @MAVisitID, @UserID, @CurrentUtcTime, TI.Qty, I.[Act Price], TI.UOMCode, @BillingStatusID, @OverrideID, @OverrideNotes, @UserID, @CurrentUtcTime
									, @UsageTypeID, I.ItemID, @SessionID, I.ICDM, @EntryTypeID 
				FROM s01tblItems I 
					INNER JOIN @udtTransactedItemDetails TI ON TI.RFID = I.RFID AND TagType = 'LostTag' 	
				

				INSERT INTO s01tblItemHistory( ItemStatusID, ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAScheduleID, MAVisitID, BillingStatusID, UsedBy, Qty
									, UnitPrice, UOMCode, UpdatedBy, UCDM, LastActivityDate, EntryTypeID, DateUsed, OverrideID, OverrideNotes, SessionID, ItemEventID )
				SELECT @ItemStatusID, ItemUsageID, @UsageTypeID, U.UsageItemID, MAPatientID, MAScheduleID, MAVisitID, BillingStatusID, @UserID, U.Qty, U.UnitPrice
									, TI.UOMCode, @UserID, U.UCDM, @CurrentUtcTime, @EntryTypeID, U.DateUsed, U.OverrideID, U.OverrideNotes, @SessionID, @ItemEventID  
				FROM s01tblItemUsage U 
					INNER JOIN @udtTransactedItemDetails TI ON TI.ItemID = U.UsageItemID
				WHERE SessionID = @SessionID AND TagType = 'LostTag' AND U.LastActivityDate = @CurrentUtcTime

				INSERT INTO s01tblitemhistoryextn (ItemHistoryID, LocationID)
				SELECT ItemHistoryID, T.LocationID 
				FROM @udtTransactedItemDetails T
					INNER JOIN s01tblItemHistory H ON  H.UsageItemID = T.ItemID 
					AND H.LastActivityDate = @CurrentUtcTime
					AND H.UsageTypeID=1
					AND T.TagType = 'LostTag' 
			END

			IF @EXPDATE_CHECK = 'TRUE'
			BEGIN
				INSERT INTO tblAlertLog( AlertLogDBID, AlertID, ItemID, MAPatientID, MAScheduleID, MAVisitID, UseCycle, ExceptionNotesID, LocationID, UserID, EntryTypeID, SessionID, AlertLogDesc, AlertLogDate )
				SELECT @DBID, @AlertID, I.ItemID, @MAPatientID, @MAScheduleID, @MAVisitID, I.[Use Cycle], @OverrideNotesID, I.CompartmentID, @UserID, @EntryTypeID, @SessionID, @AlertMessage, CONVERT( VARCHAR, DATEADD( SECOND, @SecondDiff, @CurrentUtcTime ), 121)
				FROM @udtTransactedItemDetails F INNER JOIN s01tblItems I ON I.RFID = F.RFID 
				WHERE I.[Expired Date] <= @CurrentUtcTime 
					AND F.TagType = 'LostTag'
					AND I.ItemID NOT IN ( SELECT ItemID 
											FROM tblAlertLog 
											WHERE AlertID = 1  
											AND SESSIONID = @SessionID )
																		
			END

			EXECUTE  uspLoadUsageList @Event = @Event
					, @udtSystemData = @udtSystemData
					, @udtEventData = @udtEventData

		END

	EXECUTE uspProcessUnassociatedItems @Event = 'InsertUnassociatedItems'
				, @udtSystemData = @udtSystemData 
				, @udtEventData = @udtEventData		
				, @udtTransactedItemDetails = @udtTransactedItemDetails
				, @udtTransactedItems = @udtTransactedItems
	
	IF ( @UageSummarySettings = 'TRUE' )
	BEGIN
		SELECT 'DataUsageSummary' AS DataSetName, [Cat No] AS [Catalog Number], COALESCE( I.[Serial No], I.[Lot No]) AS ModelNo, I.[Description 1] AS Description
												, COALESCE( I.[Added By], I.[Removed By]) AS UpdatedUser, I.ItemStatusID 
		FROM @udtTransactedItemDetails TI 
			INNER JOIN s01qryItems I ON I.RFID = TI.RFID AND TI.TagType = 'LostTag'

		SELECT 'AddedItemSummary' AS DataSetName, [Cat No] AS [Catalog Number], COALESCE( I.[Serial No], I.[Lot No]) AS ModelNo, I.[Description 1] AS Description
												, COALESCE( I.[Added By], I.[Removed By]) AS UpdatedUser, I.ItemStatusID 
		FROM @udtTransactedItemDetails TI 
			INNER JOIN s01qryItems I ON I.RFID = TI.RFID AND TI.TagType = 'FoundTag'
	END
	
	--COMMIT TRANSACTION;

	EXECUTE [uspCabinetItemCounts] @Event = 'OnloadItemCount'
				, @udtSystemData = @udtSystemData
				, @udtEventData = @udtEventData

	IF ( @Event = 'InitialScan' AND @EXPDATE_CHECK = 'TRUE')
	BEGIN
		EXECUTE uspLoadExpireItems @Event = @Event
			, @udtSystemData = @udtSystemData
			, @udtEventData = @udtEventData
	END

	IF ( @Event <> 'InitialScan' AND @EXPDATE_CHECK = 'TRUE')
	BEGIN
		EXECUTE uspTransactedExpireItems @Event = @Event
			, @udtSystemData = @udtSystemData 
			, @udtEventData = @udtEventData		
			, @udtTransactedItems = @udtTransactedItems
	END
	
	IF (@CLOSEEXP_CHECK = 'TRUE')
	BEGIN
		EXECUTE uspLogSimilarTypeExpireItem @Event = 'LoadSimilarExpireItem' 
			, @udtSystemData = @udtSystemData 
			, @udtEventData = @udtEventData
			, @udtTransactedItems = @udtTransactedItems
	END

	IF (@EnableEpicInterface = 'TRUE' AND @isScannedInHUB = 'TRUE') --11/10/2024 viresh added this condition to validate before sending to epic item scanned in HUB or Not
	BEGIN
		EXECUTE  uspSendUsageToEpic @Event = @Event
					, @udtSystemData = @udtSystemData
					, @udtEventData = @udtEventData
					, @udtTransactedItemDetails = @udtTransactedItemDetails
					, @ItemEventID = @ItemEventID
					, @CurrentUtcTime = @CurrentUtcTime
	END

	EXECUTE  uspAppCabScanLogs @Event = @Event
			, @udtSystemData = @udtSystemData
			, @udtEventData = @udtEventData
			, @udtTransactedItemDetails = @udtTransactedItemDetails
			, @udtTagsData = @udtTagsData

	EXECUTE  uspUpdateTransactionLogs @Event = @Event
			, @udtSystemData = @udtSystemData
			, @udtEventData = @udtEventData
			, @CurrentUtcTime = @CurrentUtcTime

	END TRY
		BEGIN CATCH
			IF (XACT_STATE()) <> 0
				--ROLLBACK TRANSACTION;
			EXECUTE uspLogError;
		END CATCH;
	SET NOCOUNT OFF
GO

