### html ###

<h4>CurvedLines: random stacked data</h4>
<div id="flotContainer" class="chart-style"></div>

<h4>CurvedLines: same data connected with curved lines</h4>
<div id="flotContainer2" class="chart-style"></div>

### css ###

.chart-style {
    width: 600px;
    height: 260px;
}

### javascript ###

//random data
var d1 = [];
for (var i = 0; i <= 10; i += 1) {
    d1.push([i, parseInt(Math.random() * 30)]);
}
var d2 = [];
for (var i = 0; i <= 10; i += 1) {
    d2.push([i, parseInt(Math.random() * 30)]);
}    
var d3 = [];
for (var i = 0; i <= 10; i += 1) {
    d3.push([i, parseInt(Math.random() * 30)]);
}

//flot options
var options = {
                 series: {
                     curvedLines: {
                              apply: true,
                              active: true,
                              monotonicFit: true
                     }
                 }
              };
//plotting
$.plot($("#flotContainer"), [
       {data: d1, lines: { show: true, fill: true }, stack: true },
       {data: d2, lines: { show: true, fill: true }, stack: true },
       {data: d3, lines: { show: true, fill: true }, stack: true }
], {});

$.plot($("#flotContainer2"), [
       {data: d1, lines: { show: true, fill: true }, stack: true },
       {data: d2, lines: { show: true, fill: true }, stack: true },
       {data: d3, lines: { show: true, fill: true }, stack: true }
    ], options);