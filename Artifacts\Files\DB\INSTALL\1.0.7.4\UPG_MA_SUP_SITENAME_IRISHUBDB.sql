/******************************************************************************
Name                       : qryProductCatalog
Version                    : 4.0.43
Purpose                    : 
Author                     : 
Date Created               : 
Tables Affected            : s01tblItemInventory,s01tblProducts
Application Affected       : 
-----------------------------------------------------------------------------------------------------
Release Notes         :
********************************************************************************/
ALTER VIEW [dbo].[qry703WebConsoleProductCatalog]
AS
SELECT 	LEFT(s01tblProducts.[Description 1], 45) + '||11' AS Field1, 'Cat No : ' + s01tblProducts.[Cat No] + '|||||0' AS Field3,
	'Qty : ' + CAST(s01tblItemInventory.Qty AS nvarchar) + '|||||0' AS Field4, s01tblProducts.Manufacturer + N'|||||0' AS Field5,
	s01tblItemInventory.UOMCode + '|||||0' AS Field6, s01tblProducts.[Cat No] AS FieldID1, s01tblItemInventory.ItemInventoryID, s01tblProducts.[Cat No],
	s01tblProducts.[Description 1], s01tblProducts.UOMCode, s01tblProducts.[Ref Price], s01tblProducts.ProdUnitID, s01tblItemInventory.Qty,
	s01tblProducts.PCDM,s01tblItemInventory.LocationID,s01tblItemInventory.LocationTypeID,BP.[Barcode No],s01tblProducts.Manufacturer,s01tblProducts.[Brand Name]
	,s01tblProducts.ProductID
FROM 	s01tblItemInventory INNER JOIN
     	s01tblProducts ON s01tblItemInventory.ProductID = s01tblProducts.ProductID
		inner join s01tblbarcoderuleproduct BP on s01tblProducts.[Cat No]=BP.[Cat No]
GO

ALTER Procedure [dbo].[usp714WebConsoleGetPatientUsage ]
@MAScheduleID nvarchar(50)
as
begin
					--SELECT qry704.[Description], qry704.CatNo,CONVERT(varchar, CONVERT(datetime,qry704.DateUsed,120),22)as DateUsed, qry704.[REAL], qry704.Qty, qry704.PatientName, qry704.ItemUsageID, qry704.UsageTypeID,Ext.Capitated
					--	, RFID, ProductType,SerialNo, LotNo--, EpicStatus
					--FROM qry704WebConsoleSUPItemUsageList qry704
					--left JOIN tblItemUsageExt Ext ON qry704.ItemUsageID=Ext.ItemUsageID
					--WHERE MAScheduleID = @MAScheduleID
					--ORDER BY DateUsed DESC

					--updated by viresh -- 11/11/2024
					
					--SELECT qry704.[Description], qry704.CatNo,CONVERT(varchar, CONVERT(datetime,qry704.DateUsed,120),22)as DateUsed, qry704.[REAL], qry704.Qty, qry704.PatientName, qry704.ItemUsageID, qry704.UsageTypeID,Ext.Capitated
					--	, qry704.RFID, ProductType,SerialNo, LotNo,I.ItemStatusID
					--FROM qry704WebConsoleSUPItemUsageList qry704
					--left JOIN tblItemUsageExt Ext ON qry704.ItemUsageID=Ext.ItemUsageID
					--INNER JOIN qryItemUsage I ON qry704.ItemUsageID = I.ItemUsageID
					--WHERE qry704.MAScheduleID = @MAScheduleID --AND I.ItemStatusID = 4
					--ORDER BY DateUsed DESC
					SELECT * 
					FROM qryHUBFetchUsageItemList
					WHERE MAScheduleID = @MAScheduleID --AND I.ItemStatusID = 4
					ORDER BY DateUsedReal DESC
					
end
GO

ALTER Procedure [dbo].[usp825WebConsoleScanBarcodeInventory]
	@Event VARCHAR(255)
	, @udtSystemData udtSPParam READONLY
	, @udtEventData udtSPParam READONLY
AS
	SET NOCOUNT ON;
		DECLARE @DeviceID INT, @EntryTypeID INT, @Msg VARCHAR(200), @DataSetName VARCHAR(50), @SessionID varchar(50), @UserID INT, @ItemUsageID INT, @FDACatNo VARCHAR(50)
		DECLARE @CurrentPatientID INT, @CurrentUtcTime DATETIME, @ScannedBarcodeValue VARCHAR(100), @EventType VARCHAR(50), @CatNo VARCHAR(50), @ProductID INT, @LocationID INT, @LocationTypeID INT, @BarcodeSettings VARCHAR(10)
		DECLARE @UpdateQty VARCHAR(10), @UOMCode VARCHAR(10), @Qty INT,@inputQty INT, @ItemInventoryID INT, @EventInput3 VARCHAR(50), @EventInPut4 VARCHAR(50),  @OverrideID INT, @ItemEventID INT, @InventoryID INT
		DECLARE @ReasonWindowSetting VARCHAR(10), @OverrideNotes VARCHAR(50), @PatientName VARCHAR(200), @MAPatientID INT, @MAScheduleID INT, @MAVisitID INT, @ItemHistoryID INT, @UsageItemID INT, @IsFDA VARCHAR(50), @IsUsageExists VARCHAR(50),@implantStatus VARCHAR(50),@isImplant VARCHAR(10)
		
		--Edted by subramanya to handle type mismatch exception
		--SET XACT_ABORT ON;
		BEGIN TRY
		--BEGIN TRANSACTION

		SELECT @DeviceID = Value FROM @udtSystemData WHERE Property = 'DeviceID'
		SELECT @EntryTypeID = Value FROM @udtSystemData WHERE Property = 'EntryTypeID'
		SELECT @UserID = Value FROM @udtEventData WHERE Property = 'UserID'
		SELECT @MAPatientID = Value FROM @udtEventData WHERE Property = 'MAPatientID'
		SELECT @ScannedBarcodeValue = Value FROM @udtEventData WHERE Property = 'EventInPut1'
		SELECT @UpdateQty = Value FROM @udtEventData WHERE Property = 'EventInPut2'
		SELECT @EventInPut3 = Value FROM @udtEventData WHERE Property = 'EventInPut3'
		SELECT @EventInPut4 = Value FROM @udtEventData WHERE Property = 'EventInPut4'
		SELECT @EventType = Value FROM @udtEventData WHERE Property = 'EventType'
		SELECT @SessionID = Value FROM @udtEventData WHERE Property = 'SessionID'
		SELECT @DataSetName = Value FROM @udtEventData WHERE Property = 'EventOutPut'
		SELECT @InventoryID = Value FROM @udtEventData WHERE Property = 'ItemInventoryID'
		
		SELECT @MAVisitID = Value FROM @udtEventData WHERE Property = 'MAVisitID' 
		SELECT @MAScheduleID = Value FROM @udtEventData WHERE Property = 'MAScheduleID' 
		SELECT @FDACatNo = Value FROM @udtEventData WHERE Property = 'CatNo' 
		SELECT @IsFDA = Value FROM @udtEventData WHERE Property = 'IsFDA' 
		SELECT @inputQty = Value FROM @udtEventData WHERE Property = 'Qty' 
		SELECT @implantStatus = Value FROM @udtEventData WHERE Property = 'ImplantStatus' 
		SELECT @CatNo = Value FROM @udtEventData WHERE Property = 'CatalogNo' 
		SELECT @ProductID = Value FROM @udtEventData WHERE Property = 'ProductID' 
		
		--select @IsFDA;
	

		SELECT @LocationTypeID = [dbo].[udf700WebConsoleSUPFetchSettings] ( @DeviceId, 'BarcodeInventory_LocationType' )

		SELECT @LocationID = [dbo].[udf700WebConsoleSUPFetchSettings] ( @DeviceId, 'BarcodeInventory_Location' )

		IF(@implantStatus IS NOT NULL)
		BEGIN
			IF(@implantStatus = 'Implant')
			BEGIN
				SET @isImplant = 'Y';
			END
			ELSE
			BEGIN
				SET @isImplant = 'N';
			END
		END
		
		IF (SELECT [Setting Value] FROM s01tblSettingsCommon WHERE [Setting Name] = 'ENABLE_UTC_TIMEZONE') = 'TRUE'
		BEGIN
			SELECT @CurrentUTCTime = GETUTCDATE()
		END
		ELSE
		BEGIN
			SELECT @CurrentUTCTime = GETDATE()
		END
		

		--IF @CatNo IS NULL
		--BEGIN
		--	SELECT @CatNo = [Cat No]
		--FROM s01tblBarcodeRuleProduct 
		--WHERE [Barcode No] = @ScannedBarcodeValue
		--	AND @ScannedBarcodeValue IS NOT NULL
		--	AND @ScannedBarcodeValue <> ''
		--END

		--SELECT @ProductID = ProductID, @UOMCode = UOMCode 
		--FROM s01tblProducts 
		--WHERE [Cat No] = @CatNo
		
		--SR added > FDA/Master
		--if (@IsFDA='true')
		--begin
		
		----IF NOT EXISTS ( SELECT 1 FROM s01tblProducts 	
		----				WHERE REPLACE(REPLACE([Cat No],'-',''),'.','') = REPLACE(REPLACE(@CatNo,'-',''),'.','')
		----					OR @CatNo IS NULL OR @CatNo = ''
		----		      )
		----BEGIN
			
		--	EXECUTE usp826WebConsoleSaveBarcodeMasterFDAProducts 
		--			  @Event = @Event
		--			, @udtSystemData = @udtSystemData
		--			, @udtEventData = @udtEventData
		--			, @ProductID = @ProductID -- viresh added on 12/02/2024
			
		--	SELECT @CatNo = [Cat No]
		--	FROM s01tblBarcodeRuleProduct 
		--	WHERE [Barcode No] = @ScannedBarcodeValue

		--	SELECT @ProductID = ProductID, @UOMCode = UOMCode 
		--	FROM s01tblProducts 
		--	WHERE [Cat No] = @CatNo

		--	IF @ProductID IS NULL
		--	BEGIN
		--		SELECT @FDACatNo = [Cat No]
		--		FROM s01tblBarcodeRuleProduct 
		--		WHERE [Barcode No] = @ScannedBarcodeValue

		--		SELECT @ProductID = ProductID, @UOMCode = UOMCode 
		--		FROM s01tblProducts 
		--		WHERE [Cat No] = @FDACatNo
		--	END

		--	IF @ProductID IS NOT NULL
		--	BEGIN
		--		INSERT INTO s01tblItemInventory(ProductID,Qty,UpdatedBy,LastActivityDate,LocationID,LocationTypeID)
		--		VALUES (@ProductId, @inputQty, @UserID, @CurrentUTCTime, @LocationID, @LocationTypeID )
			
		--	IF 	@FDACatNo IS NOT NULL AND @FDACatNo <> ''
		--		SET @CatNo = @FDACatNo
		--	END
			
		----END
		--end
		IF @ProductID IS NULL
		BEGIN
			SELECT 'Fail' AS DataSetName, 'Product is not registered' AS MSG
			RETURN
		END
		
		SELECT @PatientName = [Patient Name] 
		FROM s01qryPatients 
		WHERE MAPatientID = @MAPatientID

		SELECT @UsageItemID = ItemInventoryID 
		FROM s01tblItemInventory 
		WHERE ProductID = @ProductID 
			AND LocationID = @LocationID 
			AND LocationTypeID = @LocationTypeID

		SELECT @CurrentPatientID = MAPatientID 
		FROM s01tblItemUsage 
		WHERE MAPatientID = @MAPatientID 
			AND UsageItemID = @UsageItemID 
			AND UsageTypeID = 2
			
		SELECT @BarcodeSettings = [dbo].[udf700WebConsoleSUPFetchSettings] ( @DeviceId,'ENABLE_ITEMIZE_BARCODE' )
		
		SELECT @Qty = Qty, @ItemInventoryID = ItemInventoryID 
		FROM s01tblItemInventory 
		WHERE ProductID = @ProductID 
			AND LocationID = @LocationID 
			AND LocationTypeID = @LocationTypeID

		SELECT @OverrideID = OverrideID 
		FROM s01tblOverride 
		WHERE OverrideDesc = CASE 
								WHEN @EventInput3 = 'Waste Item' 
									THEN 'Waste'
							    WHEN @EventInput3 = 'Override' 
									THEN 'Manual Override'
								WHEN @MAPatientID IS NOT NULL AND @EventInput3 <> 'Waste' 
									THEN 'Default'
								ELSE @EventInput3 
							 END
		
		SELECT @ReasonWindowSetting = [dbo].[udf700WebConsoleSUPFetchSettings] ( @DeviceId,'ENABLE_OVERRIDEHANDLER' )

		IF ( @EventInput4 IS NULL )
		BEGIN
			SELECT @OverrideNotes = 'DEFAULT'
		END
		ELSE IF ( @EventInput4 IS NOT NULL )
		BEGIN
			SELECT @OverrideNotes = OverrideNotes 
			FROM s01tblOverrideNotes 
			WHERE OverrideNotes = @EventInput4 
				AND OverrideID = @OverrideID
		END

		SELECT @ItemEventID = ItemEventID 
		FROM s01tblItemEvent 
		WHERE EventDescription = CASE 
									 WHEN @EventInput3 = 'Waste Item' 
										THEN 'Barcode Item Wasted'
								     WHEN @EventInput3 = 'Transfer Item' 
										THEN 'Barcode Item Transferred'
									 WHEN @EventInput3 IN ('Return Item','Remove Item', 'Swap Item', 'Override', 'Waste Item') 
										THEN 'Item Used'
									 WHEN @EventType = 'StockBarcodeInventory' 
										THEN 'Stock quantity for barcode item has been updated'
									 WHEN @EventType = 'ReconcileBarcodeInventory' 
										THEN 'Quantity for barcode item has been updated'
									 WHEN @EventType = 'DeleteBarcodeInventory' 
										THEN 'Barcode Item Deleted'
									 ELSE 'Item returned to inventory' 
								 END
					
				IF ( @EventType = 'RemoveBarcodeInventory' )
				BEGIN	
					--SELECT @DataSetName AS DataSetName, ItemInventoryID, [Ref Price], UOMCode, [Cat No], [Description 1], Qty, ProdUnitID, PCDM, LocationID ,[Barcode No]
					--FROM qry703WebConsoleProductCatalog 
					--WHERE [Cat No] = @CatNo 
					--	AND LocationTypeID = @LocationTypeID 
					--	AND LocationID = @LocationID

					--UPDATE s01tblProducts
					--SET RFID = @isImplant
					--WHERE [Cat No] = @CatNo; --Updating implant status from Detail Screen

					DECLARE @Counter INT = 1;
					DECLARE @BarCodeQty INT = 1;
					WHILE @Counter <= @inputQty
					BEGIN
						IF NOT EXISTS ( SELECT 1 
									FROM s01tblItemInventory I 
										INNER JOIN s01tblProducts P ON I.ProductID = P.ProductID 
									WHERE I.ProductID = @ProductID --AND I.LocationID = @LocationID 
									)
						BEGIN
							PRINT 'InventoryNotExists'
							INSERT INTO s01tblIteminventory( Qty, ProductID, LocationTypeID, LocationID, EntUserID, LastActivityDate, [Entered Date] ) 
							VALUES ( 1, @ProductID, @LocationTypeID, @LocationID, @UserID, @CurrentUTCTime, @CurrentUTCTime )

							SELECT @ItemInventoryID = SCOPE_IDENTITY()
							--INSERT INTO tblUpdateBarcodeItemInventoryLog( ItemInventoryID, QtyInHand, QtyLastUpdated, QtyUsed 
							--						, QtyReturned, QtyDeleted, QtyStocked, QtyWasted, CutOffTime, UpdatedQtyInHand )
							--SELECT ItemInventoryID, Qty - 1, QtyLastUpdated, 0, 0, 0, 1, 0, @CurrentUTCTime, Qty 
							--FROM s01tblItemInventory
							--WHERE ItemInventoryID = @ItemInventoryID

							INSERT INTO s01tblItemUsage ( MAPatientID, MAVisitID, MAScheduleID, UsedBy, DateUsed, Qty, UnitPrice, UOMCode, BillingStatusID, OverrideID, OverrideNotes
												, UpdatedBy, LastActivityDate, UsageTypeID, UsageItemID, SessionID, EntryTypeID )
							SELECT @MAPatientID, @MAVisitID, @MAScheduleID, @UserID, @CurrentUtcTime, 1, [Ref Price], UOMCode, 1, @OverrideID, @OverrideNotes, @UserID
												, @CurrentUTCTime, 2, @ItemInventoryID, @SessionID, @EntryTypeID 
							FROM s01tblProducts 
							WHERE ProductID = @ProductID

							SELECT @ItemUsageID = SCOPE_IDENTITY()
							INSERT INTO s01tblItemHistory ( ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAVisitID, MAScheduleID, UsedBy, DateUsed, UnitPrice, UOMCode
												, OverrideID, OverrideNotes, SessionID, UCDM, BillingStatusID, ItemStatusID, UpdatedBy, LastActivityDate, ItemEventID, Qty, EntryTypeID ) 
							SELECT ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAVisitID, MAScheduleID, UsedBy, DateUsed, UnitPrice, UOMCode, OverrideID, @OverrideNotes, @SessionID 
												, UCDM, BillingStatusID, NULL, @UserID, @CurrentUTCTime, @ItemEventID, 1, @EntryTypeID 
							FROM s01tblItemUsage 
							WHERE ItemUsageID= @ItemUsageID

							SELECT @ItemHistoryID = SCOPE_IDENTITY()

							INSERT INTO s01tblItemHistoryExtn (ItemHistoryID, LocationID ) 
							SELECT ItemHistoryID, @LocationID 
							FROM s01tblItemHistory 
							WHERE ItemHistoryID = @ItemHistoryID

							INSERT INTO tblInventoryLog ( ItemHistoryID, InvntoryID, ProcessStatus, ClusterID, LocationID, UpdatedBy, EntryTypeID, EnteredDate )
							SELECT ItemHistoryID, UsageItemID, 'P', @DeviceID, @LocationID, @UserID, @EntryTypeID, @CurrentUTCTime FROM s01tblItemHistory WHERE ItemHistoryID = @ItemHistoryID	

						END
						ELSE IF ( @BarcodeSettings = 'TRUE')
						BEGIN
						--REPLACE( REPLACE( REPLACE ([Cat No], '-', ''), '.', ''), ' ', '' )
							IF ( SELECT 1 FROM qry703WebConsoleProductCatalog WHERE ProductID = @ProductID And LocationTypeID = @LocationTypeID And LocationID = @LocationID ) = 1 
							BEGIN
					
								INSERT INTO s01tblItemUsage ( MAPatientID, MAVisitID, MAScheduleID, UsedBy, DateUsed, Qty, UnitPrice, UOMCode, BillingStatusID, OverrideID, OverrideNotes
														, UpdatedBy, LastActivityDate, UsageTypeID, UsageItemID, SessionID, EntryTypeID )
								SELECT @MAPatientID, @MAVisitID, @MAScheduleID, @UserID, @CurrentUtcTime, @inputQty, [Ref Price], UOMCode, 1, @OverrideID, @OverrideNotes, @UserID
														, @CurrentUTCTime, 2, @ItemInventoryID, @SessionID, @EntryTypeID 
								FROM s01tblProducts 
								WHERE ProductID = @ProductID

								SELECT @ItemUsageID = SCOPE_IDENTITY()
							
								SELECT @UsageItemID = UsageItemID FROM s01tblItemUsage 
								WHERE ItemUsageID = @ItemUsageID

								UPDATE s01tblItemInventory 
								SET Qty = Qty - @inputQty, UpdatedBy = @UserID, QtyLastUpdated = @CurrentUTCTime 
								WHERE ItemInventoryID = @UsageItemID

								INSERT INTO s01tblUpdateBarcodeItemInventoryLog( ItemInventoryID, QtyInHand, QtyLastUpdated, QtyUsed 
														, QtyReturned, QtyDeleted, QtyStocked, QtyWasted, CutOffTime, UpdatedQtyInHand )
								SELECT ItemInventoryID, Qty + @inputQty, QtyLastUpdated, @inputQty, 0, 0, 0, 0, @CurrentUTCTime, Qty 
								FROM s01tblItemInventory
								WHERE ItemInventoryID = @UsageItemID

								INSERT INTO s01tblItemHistory ( ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAVisitID, MAScheduleID, UsedBy, DateUsed, UnitPrice, UOMCode
													, OverrideID, OverrideNotes, SessionID, UCDM, BillingStatusID, ItemStatusID, UpdatedBy, LastActivityDate, ItemEventID, Qty, EntryTypeID ) 
								SELECT ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAVisitID, MAScheduleID, UsedBy, DateUsed, UnitPrice, UOMCode, @OverrideID, @OverrideNotes, @SessionID 
													, UCDM, BillingStatusID, NULL, @UserID, @CurrentUTCTime, @ItemEventID, @inputQty, @EntryTypeID 
								FROM s01tblItemUsage 
								WHERE ItemUsageID = @ItemUsageID

								SELECT @ItemHistoryID = SCOPE_IDENTITY()
								INSERT INTO s01tblItemHistoryExtn (ItemHistoryID, LocationID ) 
								SELECT ItemHistoryID, @LocationID
								FROM s01tblItemHistory 
								WHERE ItemHistoryID = @ItemHistoryID

								INSERT INTO tblInventoryLog ( ItemHistoryID, InvntoryID, ProcessStatus, ClusterID, LocationID, UpdatedBy, EntryTypeID, EnteredDate )
								SELECT ItemHistoryID, UsageItemID, 'P', @DeviceID, @LocationID, @UserID, @EntryTypeID, @CurrentUTCTime 
								FROM s01tblItemHistory 
								WHERE ItemHistoryID = @ItemHistoryID	
							END
						END
						ELSE IF ( @BarcodeSettings = 'FALSE')
						BEGIN
							IF ( @CurrentPatientID = @MAPatientID ) AND ( @MAPatientID IS NOT NULL )
							BEGIN
						
								IF EXISTS ( SELECT 1 
											FROM s01tblItemInventory I 
												INNER JOIN s01tblProducts P ON I.ProductID = P.ProductID 
												INNER JOIN s01tblItemUsage U ON I.ItemInventoryID = U.UsageItemID
											WHERE I.ProductID = @ProductID 
												AND I.LocationID = @LocationID 
												AND U.SessionID = @SessionID 
												AND U.MAPatientID = @MAPatientID)
								BEGIN
							
									SELECT @QTY = U.Qty+@BarCodeQty, @ItemInventoryID = I.ItemInventoryID  
									FROM s01tblItemUsage U 
										INNER JOIN s01tblItemInventory I ON I.ItemInventoryID = U.UsageItemID 
									WHERE U.SessionID = @SessionID 
										AND I.ProductID = @ProductID 
										AND MAPatientID = @MAPatientID
								
									UPDATE s01tblItemUsage 
									SET Qty = @QTY, UpdatedBy = @UserID, LastActivityDate = @CurrentUTCTime, OverrideID = @OverrideID, OverrideNotes = @OverrideNotes
									WHERE UsageItemID = @ItemInventoryID 
										AND SessionID = @SessionID	
										AND MAPatientID = @MAPatientID 
										AND UsageTypeID = 2
							
									UPDATE s01tblItemInventory 
									SET Qty = Qty - @BarCodeQty, UpdatedBy = @UserID, QtyLastUpdated = @CurrentUTCTime 
									WHERE ItemInventoryID = @ItemInventoryID 

									INSERT INTO s01tblUpdateBarcodeItemInventoryLog( ItemInventoryID, QtyInHand, QtyLastUpdated, QtyUsed 
															, QtyReturned, QtyDeleted, QtyStocked, QtyWasted, CutOffTime, UpdatedQtyInHand )
									SELECT ItemInventoryID, Qty + @BarCodeQty, QtyLastUpdated, @BarCodeQty, 0, 0, 0, 0, @CurrentUTCTime, Qty 
									FROM s01tblItemInventory
									WHERE ItemInventoryID = @ItemInventoryID

									SELECT @ItemUsageID = ItemUsageID 
									FROM s01tblItemUsage 
									WHERE UsageItemID = @ItemInventoryID 
										AND SessionID = @SessionID

									INSERT INTO s01tblItemHistory ( ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAVisitID, MAScheduleID, UsedBy, DateUsed, UnitPrice, UOMCode
														, OverrideID, OverrideNotes, SessionID, UCDM, BillingStatusID, ItemStatusID, UpdatedBy, LastActivityDate, ItemEventID, Qty, EntryTypeID ) 
									SELECT ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAVisitID, MAScheduleID, UsedBy, DateUsed, UnitPrice, UOMCode, @OverrideID, @OverrideNotes, @SessionID 
														, UCDM, BillingStatusID, NULL, @UserID, @CurrentUTCTime, @ItemEventID, @BarCodeQty, @EntryTypeID 
									FROM s01tblItemUsage 
									WHERE ItemUsageID = @ItemUsageID
					
									SELECT @ItemHistoryID = SCOPE_IDENTITY()
									INSERT INTO s01tblItemHistoryExtn (ItemHistoryID, LocationID ) 
									SELECT ItemHistoryID, @LocationID 
									FROM s01tblItemHistory 
									WHERE ItemHistoryID = @ItemHistoryID

									INSERT INTO tblInventoryLog ( ItemHistoryID, InvntoryID, ProcessStatus, ClusterID, LocationID, UpdatedBy, EntryTypeID, EnteredDate )
									SELECT ItemHistoryID, UsageItemID, 'P', @DeviceID, @LocationID, @UserID, @EntryTypeID, @CurrentUTCTime 
									FROM s01tblItemHistory 
									WHERE ItemHistoryID = @ItemHistoryID	
								END
								IF NOT EXISTS ( SELECT 1 
												FROM s01tblItemInventory I 
													INNER JOIN s01tblProducts P ON I.ProductID = P.ProductID 
													INNER JOIN s01tblItemUsage U ON I.ItemInventoryID = U.UsageItemID
												WHERE I.ProductID = @ProductID 
													AND I.LocationID = @LocationID 
													AND U.SessionID = @SessionID 
													AND U.MAPatientid = @MAPatientID ) 				
								BEGIN

									IF  EXISTS ( 
													 SELECT 1 
													 FROM s01tblItemInventory I 
														INNER JOIN s01tblProducts P ON I.ProductID = P.ProductID 
														INNER JOIN s01tblItemUsage U ON I.ItemInventoryID = U.UsageItemID
													 WHERE I.ProductID = @ProductID 
														AND I.LocationID = @LocationID 
														AND U.MAPatientID = @MAPatientID
														AND U.MAScheduleID = @MAScheduleID
												 )
									BEGIN

										SELECT @QTY = U.Qty+@BarCodeQty, @ItemInventoryID = I.ItemInventoryID  
										FROM s01tblItemUsage U 
											INNER JOIN s01tblItemInventory I ON I.ItemInventoryID = U.UsageItemID 
										WHERE I.ProductID = @ProductID 
											AND MAPatientID = @MAPatientID
											AND U.MAScheduleID = @MAScheduleID

										UPDATE s01tblItemUsage 
										SET Qty = @Qty, UpdatedBy = @UserID, LastActivityDate = @CurrentUTCTime, OverrideID = @OverrideID, OverrideNotes = @OverrideNotes
										WHERE UsageItemID = @ItemInventoryID 
											AND MAPatientID = @MAPatientID 
											AND UsageTypeID = 2

											

									--SELECT @ItemUsageID = SCOPE_IDENTITY()
									SELECT @ItemUsageID = ItemUsageID
									FROM s01tblItemUsage 
									WHERE  UsageItemID = @ItemInventoryID 
											AND MAPatientID = @MAPatientID 
											AND UsageTypeID = 2
									SELECT @UsageItemID = @ItemInventoryID
									--FROM s01tblItemUsage 
									--WHERE ItemUsageID = @ItemUsageID

									UPDATE s01tblItemInventory 
									SET Qty = Qty - @BarCodeQty, UpdatedBy = @UserID, QtyLastUpdated = @CurrentUTCTime 
									WHERE ItemInventoryID = @UsageItemID 
								
									INSERT INTO s01tblUpdateBarcodeItemInventoryLog( ItemInventoryID, QtyInHand, QtyLastUpdated, QtyUsed 
															, QtyReturned, QtyDeleted, QtyStocked, QtyWasted, CutOffTime, UpdatedQtyInHand )
									SELECT ItemInventoryID, Qty + @BarCodeQty, QtyLastUpdated, @BarCodeQty, 0, 0, 0, 0, @CurrentUTCTime, Qty 
									FROM s01tblItemInventory
									WHERE ItemInventoryID = @UsageItemID

									INSERT INTO s01tblItemHistory ( ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAVisitID, MAScheduleID, UsedBy, DateUsed, UnitPrice, UOMCode
														, OverrideID, OverrideNotes, SessionID, UCDM, BillingStatusID, ItemStatusID, UpdatedBy, LastActivityDate, ItemEventID, Qty, EntryTypeID ) 
									SELECT ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAVisitID, MAScheduleID, UsedBy, DateUsed, UnitPrice, UOMCode, @OverrideID, @OverrideNotes, @SessionID 
														, UCDM, BillingStatusID, NULL, @UserID, @CurrentUTCTime, @ItemEventID, @BarCodeQty, @EntryTypeID 
									FROM s01tblItemUsage 
									WHERE ItemUsageID = @ItemUsageID

									SELECT @ItemHistoryID = SCOPE_IDENTITY()
									INSERT INTO s01tblItemHistoryExtn (ItemHistoryID, LocationID ) 
									SELECT ItemHistoryID, @LocationID 
									FROM s01tblItemHistory 
									WHERE ItemHistoryID = @ItemHistoryID

									INSERT INTO tblInventoryLog ( ItemHistoryID, InvntoryID, ProcessStatus, ClusterID, LocationID, UpdatedBy, EntryTypeID, EnteredDate )
									SELECT ItemHistoryID, UsageItemID, 'P', @DeviceID, @LocationID, @UserID, @EntryTypeID, @CurrentUTCTime 
									FROM s01tblItemHistory 
									WHERE ItemHistoryID = @ItemHistoryID

									END
									ELSE
									BEGIN
										INSERT INTO s01tblItemUsage ( MAPatientID, MAVisitID, MAScheduleID, UsedBy, DateUsed, Qty, UnitPrice, UOMCode, BillingStatusID, OverrideID, OverrideNotes
																, UpdatedBy, LastActivityDate, UsageTypeID, UsageItemID, SessionID, EntryTypeID )
										SELECT @MAPatientID, @MAVisitID, @MAScheduleID, @UserID, @CurrentUtcTime, @BarCodeQty, [Ref Price], UOMCode, 1, @OverrideID, @OverrideNotes, @UserID
																, @CurrentUTCTime, 2, @ItemInventoryID, @SessionID, @EntryTypeID 
										FROM s01tblProducts 
										WHERE ProductID = @ProductID

								

									SELECT @ItemUsageID = SCOPE_IDENTITY()
									SELECT @UsageItemID = UsageItemID 
									FROM s01tblItemUsage 
									WHERE ItemUsageID = @ItemUsageID

									UPDATE s01tblItemInventory 
									SET Qty = Qty - @BarCodeQty, UpdatedBy = @UserID, QtyLastUpdated = @CurrentUTCTime 
									WHERE ItemInventoryID = @UsageItemID 
								
									INSERT INTO s01tblUpdateBarcodeItemInventoryLog( ItemInventoryID, QtyInHand, QtyLastUpdated, QtyUsed 
															, QtyReturned, QtyDeleted, QtyStocked, QtyWasted, CutOffTime, UpdatedQtyInHand )
									SELECT ItemInventoryID, Qty + @BarCodeQty, QtyLastUpdated, @BarCodeQty, 0, 0, 0, 0, @CurrentUTCTime, Qty 
									FROM s01tblItemInventory
									WHERE ItemInventoryID = @UsageItemID

									INSERT INTO s01tblItemHistory ( ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAVisitID, MAScheduleID, UsedBy, DateUsed, UnitPrice, UOMCode
														, OverrideID, OverrideNotes, SessionID, UCDM, BillingStatusID, ItemStatusID, UpdatedBy, LastActivityDate, ItemEventID, Qty, EntryTypeID ) 
									SELECT ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAVisitID, MAScheduleID, UsedBy, DateUsed, UnitPrice, UOMCode, @OverrideID, @OverrideNotes, @SessionID 
														, UCDM, BillingStatusID, NULL, @UserID, @CurrentUTCTime, @ItemEventID, @BarCodeQty, @EntryTypeID 
									FROM s01tblItemUsage 
									WHERE ItemUsageID = @ItemUsageID

									SELECT @ItemHistoryID = SCOPE_IDENTITY()
									INSERT INTO s01tblItemHistoryExtn (ItemHistoryID, LocationID ) 
									SELECT ItemHistoryID, @LocationID 
									FROM s01tblItemHistory 
									WHERE ItemHistoryID = @ItemHistoryID

									INSERT INTO tblInventoryLog ( ItemHistoryID, InvntoryID, ProcessStatus, ClusterID, LocationID, UpdatedBy, EntryTypeID, EnteredDate )
									SELECT ItemHistoryID, UsageItemID, 'P', @DeviceID, @LocationID, @UserID, @EntryTypeID, @CurrentUTCTime 
									FROM s01tblItemHistory 
									WHERE ItemHistoryID = @ItemHistoryID
									END
								END
							END
							IF ( @CurrentPatientID IS NULL AND @MAPatientID IS NOT NULL )
							BEGIN

									INSERT INTO s01tblItemUsage ( MAPatientID, MAVisitID, MAScheduleID, UsedBy, DateUsed, Qty, UnitPrice, UOMCode, BillingStatusID, OverrideID, OverrideNotes
															, UpdatedBy, LastActivityDate, UsageTypeID, UsageItemID, SessionID, EntryTypeID )
									SELECT @MAPatientID, @MAVisitID, @MAScheduleID, @UserID, @CurrentUtcTime, @BarCodeQty, [Ref Price], UOMCode, 1, @OverrideID, @OverrideNotes, @UserID
															, @CurrentUTCTime, 2, @ItemInventoryID, @SessionID, @EntryTypeID 
									FROM s01tblProducts 
									WHERE ProductID = @ProductID

									SELECT @ItemUsageID = SCOPE_IDENTITY()
									SELECT @UsageItemID = UsageItemID 
									FROM s01tblItemUsage 
									WHERE ItemUsageID = @ItemUsageID
							
									UPDATE s01tblItemInventory 
									SET Qty = Qty - @BarCodeQty, UpdatedBy = @UserID, QtyLastUpdated = @CurrentUTCTime 
									WHERE ItemInventoryID = @UsageItemID 

									INSERT INTO s01tblUpdateBarcodeItemInventoryLog( ItemInventoryID, QtyInHand, QtyLastUpdated, QtyUsed 
															, QtyReturned, QtyDeleted, QtyStocked, QtyWasted, CutOffTime, UpdatedQtyInHand )
									SELECT ItemInventoryID, Qty + @BarCodeQty, QtyLastUpdated, @BarCodeQty, 0, 0, 0, 0, @CurrentUTCTime, Qty 
									FROM s01tblItemInventory
									WHERE ItemInventoryID = @UsageItemID

									INSERT INTO s01tblItemHistory ( ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAVisitID, MAScheduleID, UsedBy, DateUsed, UnitPrice, UOMCode
														, OverrideID, OverrideNotes, SessionID, UCDM, BillingStatusID, ItemStatusID, UpdatedBy, LastActivityDate, ItemEventID, Qty, EntryTypeID ) 
									SELECT ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAVisitID, MAScheduleID, UsedBy, DateUsed, UnitPrice, UOMCode, @OverrideID, @OverrideNotes, @SessionID 
														, UCDM, BillingStatusID, NULL, @UserID, @CurrentUTCTime, @ItemEventID, @BarCodeQty, @EntryTypeID 
									FROM s01tblItemUsage 
									WHERE ItemUsageID = @ItemUsageID

									SELECT @ItemHistoryID = SCOPE_IDENTITY()
									INSERT INTO s01tblItemHistoryExtn (ItemHistoryID, LocationID ) 
									SELECT ItemHistoryID, @LocationID 
									FROM s01tblItemHistory 
									WHERE ItemHistoryID = @ItemHistoryID

									INSERT INTO tblInventoryLog ( ItemHistoryID, InvntoryID, ProcessStatus, ClusterID, LocationID, UpdatedBy, EntryTypeID, EnteredDate )
									SELECT ItemHistoryID, UsageItemID, 'P', @DeviceID, @LocationID, @UserID, @EntryTypeID, @CurrentUTCTime 
									FROM s01tblItemHistory 
									WHERE ItemHistoryID = @ItemHistoryID	
								--END
							END

							IF ( @MAPatientID IS NULL )
							BEGIN
								PRINT 'FALSE'
								IF EXISTS ( SELECT 1 
											FROM s01tblItemInventory I 
												INNER JOIN s01tblProducts P ON I.ProductID = P.ProductID 
												INNER JOIN s01tblItemUsage U ON I.ItemInventoryID = U.UsageItemID
											WHERE I.ProductID = @ProductID 
												AND I.LocationID = @LocationID 
												AND U.SessionID = @SessionID 
												AND MAPatientID IS NULL )
								BEGIN
									SELECT @QTY = U.Qty+1, @ItemInventoryID = I.ItemInventoryID  
									FROM s01tblItemUsage U 
										INNER JOIN s01tblItemInventory I ON I.ItemInventoryID = U.UsageItemID 
									WHERE U.SessionID = @SessionID 
										AND I.ProductID = @ProductID

									UPDATE s01tblItemUsage 
									SET Qty = @Qty, UpdatedBy = @UserID, LastActivityDate = @CurrentUTCTime, OverrideID = @OverrideID, OverrideNotes = @OverrideNotes
									WHERE UsageItemID = @ItemInventoryID AND SessionID = @SessionID	AND MAPatientID IS NULL 
							
									UPDATE s01tblItemInventory 
									SET Qty = Qty - @BarCodeQty, UpdatedBy = @UserID, QtyLastUpdated = @CurrentUTCTime 
									WHERE ItemInventoryID = @ItemInventoryID 

									INSERT INTO s01tblUpdateBarcodeItemInventoryLog( ItemInventoryID, QtyInHand, QtyLastUpdated, QtyUsed 
															, QtyReturned, QtyDeleted, QtyStocked, QtyWasted, CutOffTime, UpdatedQtyInHand )
									SELECT ItemInventoryID, Qty + @BarCodeQty, QtyLastUpdated, @BarCodeQty, 0, 0, 0, 0, @CurrentUTCTime, Qty 
									FROM s01tblItemInventory
									WHERE ItemInventoryID = @ItemInventoryID

									SELECT @ItemUsageID = ItemUsageID 
									FROM s01tblItemUsage 
									WHERE UsageItemID = @ItemInventoryID AND SessionID = @SessionID

									INSERT INTO s01tblItemHistory ( ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAVisitID, MAScheduleID, UsedBy, DateUsed, UnitPrice, UOMCode
														, OverrideID, OverrideNotes, SessionID, UCDM, BillingStatusID, ItemStatusID, UpdatedBy, LastActivityDate, ItemEventID, Qty, EntryTypeID ) 
									SELECT ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAVisitID, MAScheduleID, UsedBy, DateUsed, UnitPrice, UOMCode, @OverrideID, @OverrideNotes, @SessionID 
														, UCDM, BillingStatusID, NULL, @UserID, @CurrentUTCTime, @ItemEventID, @BarCodeQty, @EntryTypeID 
									FROM s01tblItemUsage 
									WHERE ItemUsageID = @ItemUsageID
					
									SELECT @ItemHistoryID = SCOPE_IDENTITY()
									INSERT INTO s01tblItemHistoryExtn (ItemHistoryID, LocationID ) 
									SELECT ItemHistoryID, @LocationID 
									FROM s01tblItemHistory 
									WHERE ItemHistoryID = @ItemHistoryID

									INSERT INTO tblInventoryLog ( ItemHistoryID, InvntoryID, ProcessStatus, ClusterID, LocationID, UpdatedBy, EntryTypeID, EnteredDate )
									SELECT ItemHistoryID, UsageItemID, 'P', @DeviceID, @LocationID, @UserID, @EntryTypeID, @CurrentUTCTime 
									FROM s01tblItemHistory 
									WHERE ItemHistoryID = @ItemHistoryID	
								END
								IF NOT EXISTS ( SELECT 1 
												FROM s01tblItemInventory I 
													INNER JOIN s01tblProducts P ON I.ProductID = P.ProductID 
													INNER JOIN s01tblItemUsage U ON I.ItemInventoryID = U.UsageItemID
												WHERE I.ProductID = @ProductID 
													AND I.LocationID = @LocationID 
													AND U.SessionID = @SessionID 
													AND MAPatientID IS NULL ) 				
								BEGIN
									INSERT INTO s01tblItemUsage ( MAPatientID, MAVisitID, MAScheduleID, UsedBy, DateUsed, Qty, UnitPrice, UOMCode, BillingStatusID, OverrideID, OverrideNotes
															, UpdatedBy, LastActivityDate, UsageTypeID, UsageItemID, SessionID, EntryTypeID )
									SELECT @MAPatientID, @MAVisitID, @MAScheduleID, @UserID, @CurrentUtcTime, 1, [Ref Price], UOMCode, 1, @OverrideID, @OverrideNotes, @UserID
															, @CurrentUTCTime, 2, @ItemInventoryID, @SessionID, @EntryTypeID 
									FROM s01tblProducts 
									WHERE ProductID = @ProductID

									SELECT @ItemUsageID = SCOPE_IDENTITY()

									SELECT @UsageItemID = UsageItemID 
									FROM s01tblItemUsage 
									WHERE ItemUsageID = @ItemUsageID

									UPDATE s01tblItemInventory 
									SET Qty = Qty - 1, UpdatedBy = @UserID, QtyLastUpdated = @CurrentUTCTime 
									WHERE ItemInventoryID = @UsageItemID 

									INSERT INTO s01tblUpdateBarcodeItemInventoryLog( ItemInventoryID, QtyInHand, QtyLastUpdated, QtyUsed 
															, QtyReturned, QtyDeleted, QtyStocked, QtyWasted, CutOffTime, UpdatedQtyInHand )
									SELECT ItemInventoryID, Qty + 1, QtyLastUpdated, 1, 0, 0, 0, 0, @CurrentUTCTime, Qty 
									FROM s01tblItemInventory
									WHERE ItemInventoryID = @UsageItemID

									INSERT INTO s01tblItemHistory ( ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAVisitID, MAScheduleID, UsedBy, DateUsed, UnitPrice, UOMCode
														, OverrideID, OverrideNotes, SessionID, UCDM, BillingStatusID, ItemStatusID, UpdatedBy, LastActivityDate, ItemEventID, Qty, EntryTypeID ) 
									SELECT ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAVisitID, MAScheduleID, UsedBy, DateUsed, UnitPrice, UOMCode, @OverrideID, @OverrideNotes, @SessionID 
														, UCDM, BillingStatusID, NULL, @UserID, @CurrentUTCTime, @ItemEventID, 1, @EntryTypeID 
									FROM s01tblItemUsage 
									WHERE ItemUsageID = @ItemUsageID

									SELECT @ItemHistoryID = SCOPE_IDENTITY()
									INSERT INTO s01tblItemHistoryExtn (ItemHistoryID, LocationID ) 
									SELECT ItemHistoryID, @LocationID 
									FROM s01tblItemHistory 
									WHERE ItemHistoryID = @ItemHistoryID

									INSERT INTO tblInventoryLog ( ItemHistoryID, InvntoryID, ProcessStatus, ClusterID, LocationID, UpdatedBy, EntryTypeID, EnteredDate )
									SELECT ItemHistoryID, UsageItemID, 'P', @DeviceID, @LocationID, @UserID, @EntryTypeID, @CurrentUTCTime 
									FROM s01tblItemHistory 
									WHERE ItemHistoryID = @ItemHistoryID	
								END
							END
						END
						PRINT 'Executing task iteration ' + CAST(@Counter AS VARCHAR(10));

						-- Increment the counter
						SET @Counter = @Counter + 1;
					END
					SELECT @DataSetName AS DataSetName, ItemInventoryID, [Ref Price], UOMCode, [Cat No], [Description 1], Qty, ProdUnitID, PCDM, LocationID ,[Barcode No]
					FROM qry703WebConsoleProductCatalog 
					WHERE --[Cat No] = @CatNo 
					ProductID = @ProductID
						AND LocationTypeID = @LocationTypeID 
						AND LocationID = @LocationID
				END

				IF ( @EventType = 'ReturnBarcodeInventory' )
				BEGIN

					--PRINT 'Barcode Return'

					if exists (select top 1 * from s01tblitemusage where usageitemid=@UsageItemID)
					begin
						set @IsUsageExists='Available';
					end
	
					IF @IsUsageExists IS NULL
					BEGIN
						SELECT 'Fail' AS DataSetName, 'Item already stocked in cabinet.' AS MSG
					RETURN
					END

					EXECUTE  usp827WebConsoleReturnBarcodeItems @Event = @Event
							, @udtSystemData = @udtSystemData
							, @udtEventData = @udtEventData
							, @ProductID = @ProductID
							, @ItemEventID = @ItemEventID
							, @OverrideID = @OverrideID
		
				END
				

			--COMMIT TRANSACTION	
	END TRY
	BEGIN CATCH
		--IF XACT_STATE() <> 0
			--ROLLBACK TRANSACTION
			SET @Msg = 'Error in updating Barcode Inventory';
		EXECUTE s01uspLogError
	END CATCH
	SET NOCOUNT OFF
GO

ALTER Procedure [dbo].[uspCheckIteminMASystem]
	@Event VARCHAR(255)
	, @udtSystemData udtSPParam READONLY
	, @udtEventData udtSPParam READONLY
AS

SET NOCOUNT ON;

DECLARE @Msg VARCHAR(200), @ProcessedBarcodeNo VARCHAR(250), @BarcodeCatNo VARCHAR(250), @FullBarcodeNo VARCHAR(250), @ProcessedCatNo VARCHAR (500)
DECLARE
	
        @ScannedBarcode VARCHAR(500),
		@ScannedRFID VARCHAR(500),
		@CatNo VARCHAR(25),
		@Qty INT,
        @UserID INT = 0,
		@SessionID INT,
		@DeviceID INT,
		@EntryTypeID INT,
        @ComputerName VARCHAR(25), 
		@ProductUnitID INT,
		@SettingValue VARCHAR(150),
		@DataSetName VARCHAR(50),
		@BarCodeType VARCHAR(100),
		@SerialNumber VARCHAR(100),
		@LotNumber VARCHAR(100),
		@ExpirationDate DATETIME,
        @CurrentUtcTime DATETIME;

SET XACT_ABORT ON;
BEGIN TRY
BEGIN TRANSACTION

	SELECT @ProcessedBarcodeNo =Value FROM  @udtEventData WHERE Property = 'ProcessedBarcodeNo'
	SELECT @FullBarcodeNo =Value FROM  @udtEventData WHERE Property = 'FullBarcodeNo'
	SELECT @ProcessedCatNo =Value FROM  @udtEventData WHERE Property = 'ProcessedCatNo'
	SELECT @BarCodeType =Value FROM  @udtEventData WHERE Property = 'BarcodeType'
	SELECT @DataSetName = Value FROM @udtEventData WHERE Property = 'EventOutPut'
	SELECT @SerialNumber = Value FROM @udtEventData WHERE Property = 'SerialNumber'
	SELECT @LotNumber = Value FROM @udtEventData WHERE Property = 'LotNumber'
	SELECT @ExpirationDate = Value FROM @udtEventData WHERE Property = 'ExpirationDate'

	IF @BarcodeCatNo IS NULL
	BEGIN
		--SELECT @BarcodeCatNo = [Cat No] 
		--FROM s01tblBarcodeRuleProduct 
		--WHERE [Barcode No] = @ProcessedBarcodeNo AND @ProcessedBarcodeNo <> ''
		--OR replace(replace([Cat No],'-',''),'.','')= @ProcessedCatNo AND @ProcessedCatNo <> ''
		--OR [Barcode No] = @FullBarcodeNo AND @FullBarcodeNo <> ''

		IF (@FullBarcodeNo IS NOT NULL AND @FullBarcodeNo <> '')
		BEGIN
			SELECT @BarcodeCatNo = [Cat No]
			FROM s01tblBarcodeRuleProduct 
			WHERE [Barcode No] = @FullBarcodeNo
			OR [Barcode No] = @ProcessedBarcodeNo 
		END	
		ELSE
		BEGIN
			SET @BarcodeCatNo = NULL
		END
 
		IF (@BarcodeCatNo IS NULL)
		BEGIN
			IF( @BarCodeType = 'GS1-128'AND LEN(@ProcessedBarcodeNo)=12)
			BEGIN
				SELECT @BarcodeCatNo = [Cat No]
				FROM s01tblBarcodeRuleProduct 
				WHERE [Barcode No] = @ProcessedBarcodeNo
			END
		END
		IF (@BarcodeCatNo IS NULL)
		BEGIN
			IF((LEN(@ProcessedBarcodeNo)=16 OR LEN(@ProcessedBarcodeNo)=20) AND @BarCodeType = 'GS1-128')
			BEGIN
				SELECT @BarcodeCatNo = [Cat No]
				FROM s01tblBarcodeRuleProduct 
				WHERE [Barcode No] = @ProcessedBarcodeNo
			END
		END
		IF (@BarcodeCatNo IS NULL)
		BEGIN
			IF(1=1)
			BEGIN
				SELECT @BarcodeCatNo = [Cat No]
				FROM s01tblProducts 
				WHERE [SKU No] LIKE '%' + @ProcessedBarcodeNo +'%'
			END
		END

		IF(@BarcodeCatNo IS NOT NULL)
		BEGIN
			SELECT @ProductUnitID = ISNULL(ProdUnitID,1)
			FROM s01tblProducts
			WHERE [Cat No] = @BarcodeCatNo --OR [SKU No] LIKE '%' + @ProcessedBarcodeNo +'%'

			IF(@ProductUnitID = 4 OR @ProductUnitID = 1)
			BEGIN
						SELECT [Cat No] AS CatNo,P.ProductID,@SerialNumber AS SerialNumber,@LotNumber AS LotNumber,@ExpirationDate AS ExpirationDate
						,[Description 1] AS Description,Manufacturer,
						CASE 
							WHEN @ProcessedBarcodeNo IS NULL OR @ProcessedBarcodeNo = '' 
							THEN @FullBarcodeNo 
							ELSE @ProcessedBarcodeNo 
						END AS ProcessedBarcodeNo
						,ISNULL(RFID, 'N') AS isImplant
						,[Brand Name] AS BrandName
						,ProdUnitID
						,PX.isLotNoReq, PX.isSerialNoReq, PX.isExpirationDateReq
						,'Product Barcode Found' as MSG 
						FROM s01tblProducts P 					
						LEFT JOIN (SELECT DISTINCT PRODUCTID AS UNIQUEPRODID, * FROM s01tblProductsExtn) PX ON P.ProductID = PX.ProductID
						WHERE [Cat No] = @BarcodeCatNo
			END
			ELSE
			BEGIN
				SELECT 'This is RFID tracked item please scan RFID' as MSG
				--SELECT @DataSetName AS DataSetName, [Cat No],ProductID,'This is RFID tracked item please scan RFID' as MSG
				--FROM s01tblProducts
				--WHERE [Cat No] = @BarcodeCatNo
			END
		END
		ELSE
		BEGIN
			SELECT 'Product Not in system' as MSG
		END
		
		   
	END

		
COMMIT TRANSACTION		
END TRY

BEGIN CATCH
	IF XACT_STATE() <> 0
		ROLLBACK TRANSACTION
		SET @Msg = 'Error in Searching Product details';
	EXECUTE s01uspLogError
END CATCH
SET NOCOUNT OFF
GO

ALTER PROCEDURE [dbo].[uspFetchItemDetailsByCatNo]
    @Event VARCHAR(200),
    @udtSystemData udtSPParam READONLY,
    @udtEventData udtSPParam READONLY
AS
BEGIN
    SET NOCOUNT ON;
    DECLARE
        @ScannedBarcode VARCHAR(500),
		@ScannedRFID VARCHAR(500),
		@value VARCHAR(250),
		@EventOutPut VARCHAR(25),
		@Qty INT,
        @UserID INT = 0,
		@SessionID INT,
		@DeviceID INT,
		@EntryTypeID INT,
        @ComputerName VARCHAR(25), 
		@ProductUnitID INT,
		@SettingValue VARCHAR(150),
        @CurrentUtcTime DATETIME,
		@Trimedvalue VARCHAR(250),
		@RowCount INT
 
 
    BEGIN TRY
        SELECT @DeviceID = Value FROM @udtSystemData WHERE Property = 'DeviceID';
        SELECT @ComputerName = Value FROM @udtSystemData WHERE Property = 'ComputerName';
        SELECT @EntryTypeID = Value FROM @udtSystemData WHERE Property = 'EntryTypeID';
        SELECT @EventOutPut = Value FROM @udtEventData WHERE Property = 'EventOutPut';
		SELECT @value = Value FROM @udtEventData WHERE Property = 'CatalogNumber';
	     
		 SET @Trimedvalue = REPLACE(REPLACE(REPLACE(@value, '-', ''), '$', ''), '*', '');
		  IF(@value IS NOT NULL AND @value <> '')
           BEGIN
	            SELECT @RowCount = COUNT(*)
			    FROM s01qryProducts P LEFT OUTER JOIN s01tblBarcodeRuleProduct BP ON P.[Cat No] = BP.[Cat No]
			    LEFT OUTER JOIN s01tblItemInventory PC ON P.ProductID = PC.ProductID
			    WHERE (P.[Cat No] LIKE '%' + @value + '%' OR P.[Cat No] LIKE '%' + @Trimedvalue + '%')
				OR (P.[Description 1] LIKE '%' + @value + '%')
				OR (P.[Brand Name] LIKE '%' + @value + '%')
				OR (P.Manufacturer LIKE '%' + @value + '%')
				OR (P.MMID LIKE '%' + @value + '%');

					IF @RowCount > 0
					BEGIN
						SELECT 
							P.[Cat No] AS CatNo,
							P.ProductID,
							NULL AS Location, 
							PC.Qty, 
							P.Manufacturer AS Manufacturer,
							P.[Description 1] AS Description, 
							NULL AS BrandName, 
							P.[SKU No] AS BarCodeNo, 
							NULL AS ImplantStatus
						FROM 
							s01qryProducts P 
						LEFT OUTER JOIN 
							s01tblItemInventory PC ON P.ProductID = PC.ProductID
						WHERE  
							(P.[Cat No] LIKE '%' + @value + '%' OR P.[Cat No] LIKE '%' + @Trimedvalue + '%')
							OR (P.[Description 1] LIKE '%' + @value + '%')
							OR (P.[Brand Name] LIKE '%' + @value + '%')
							OR (P.Manufacturer LIKE '%' + @value + '%')
							OR (P.MMID LIKE '%' + @value + '%');
					END
					ELSE
					BEGIN
						SELECT 'Data not found' AS Message;
					END
		END
		ELSE
			BEGIN
						SELECT 'Data not found' AS Message;
					END
END TRY
 
BEGIN CATCH
        IF (XACT_STATE()) <> 0
 
        EXECUTE s01uspLogError;
END CATCH;
    SET NOCOUNT OFF;
END
GO

ALTER PROCEDURE [dbo].[uspProcessFDABarcodeItems]
    @Event VARCHAR(255),
    @udtSystemData udtSPParam READONLY,
    @udtEventData udtSPParam READONLY
AS
BEGIN
    SET NOCOUNT ON;
    SET XACT_ABORT ON;

    DECLARE @Msg VARCHAR(200), 
            @ProcessedBarcodeNo VARCHAR(250),  
            @FullBarcodeNo VARCHAR(250), 
            @ProcessedCatNo VARCHAR(500), 
            @SessionID BIGINT, 
            @ItemStatusID INT, 
            @ItemEventID INT, 
            @Acknowledge VARCHAR(50),
            @CatNo VARCHAR(200), 
            @ProductId INT, 
            @ItemID INT, 
            @RFID VARCHAR(50), 
            @UserID INT, 
            @CurrentUtcTime DATETIME, 
            @ItemUsageID INT, 
            @ItemHistoryID INT,
            @MAPatientID VARCHAR(50), 
            @MAScheduleID INT, 
            @MAVisitID INT, 
            @OverrideId INT, 
            @OverrideNotes VARCHAR(255), 
            @PhysicianID VARCHAR(50), 
            @BillingStatusID INT, 
            @EntryTypeID INT,
            @SerialNo VARCHAR(50), 
            @MAPhysicianID INT, 
            @ComputerName VARCHAR(50), 
            @LotNo VARCHAR(50), 
            @ExpDate VARCHAR(50), 
            @Description VARCHAR(50), 
            @Manufacturer VARCHAR(50), 
            @BrandName VARCHAR(50),
            @BOID INT, 
            @DeviceID INT,  
            @LocationID INT, 
            @LocationTypeID INT, 
            @LocationID1 INT,  
            @LocationTypeID1 INT,
			@IsImplant VARCHAR(50),
			@IsLotNoReq VARCHAR(50),
			@IsSerialNoReq VARCHAR(50),
			@IsDiscontinue VARCHAR(50),
			@IsDiscontinueDate VARCHAR(50),
			@IsExpirationDateReq VARCHAR(50),
			@ModelNumber VARCHAR(200),
			@CompartmentID INT,
			@Qty INT,
			@ItemInventoryID INT,
			@UsageItemID INT,
			@isFDA VARCHAR(50);

    BEGIN TRY
        BEGIN TRANSACTION;

        SELECT @ProcessedBarcodeNo = Value FROM @udtEventData WHERE Property = 'ProcessedBarcodeNo';
        SELECT @FullBarcodeNo = Value FROM @udtEventData WHERE Property = 'FullBarcodeNo';
        SELECT @ProcessedCatNo = Value FROM @udtEventData WHERE Property = 'ProcessedCatNo';
        SELECT @UserID = Value FROM @udtEventData WHERE Property = 'UserID';
        SELECT @DeviceID = Value FROM @udtSystemData WHERE Property = 'DeviceID';
        SELECT @ComputerName = Value FROM @udtSystemData WHERE Property = 'ComputerName';
        SELECT @MAPatientID = Value FROM @udtEventData WHERE Property = 'MAPatientID';
        SELECT @MAVisitID = Value FROM @udtEventData WHERE Property = 'MAVisitID'; 
        SELECT @MAScheduleID = Value FROM @udtEventData WHERE Property = 'MAScheduleID'; 
        SELECT @ExpDate = Value FROM @udtEventData WHERE Property = 'ExpiryDate';
        SELECT @LotNo = Value FROM @udtEventData WHERE Property = 'LotNumber';
        SELECT @SerialNo = Value FROM @udtEventData WHERE Property = 'SerialNumber';
        SELECT @CatNo = Value FROM @udtEventData WHERE Property = 'CatalogNumber';    
        SELECT @Description = Value FROM @udtEventData WHERE Property = 'Description';    
        SELECT @Manufacturer = Value FROM @udtEventData WHERE Property = 'Manufacturer';    
        SELECT @BrandName = Value FROM @udtEventData WHERE Property = 'BrandName';
        SELECT @IsImplant = Value FROM @udtEventData WHERE Property = 'IsImplant';
		SELECT @IsLotNoReq = Value FROM @udtEventData WHERE Property = 'IsLotNoReq';
		SELECT @IsSerialNoReq = Value FROM @udtEventData WHERE Property = 'IsSerialNoReq';
		SELECT @IsDiscontinue = Value FROM @udtEventData WHERE Property = 'IsDiscontinue';
		SELECT @IsDiscontinueDate = Value FROM @udtEventData WHERE Property = 'IsDiscontinueDate';
		SELECT @IsExpirationDateReq = Value FROM @udtEventData WHERE Property = 'IsExpirationDateReq';
		SELECT @ModelNumber = Value FROM @udtEventData WHERE Property = 'ModelNumber';
		SELECT @isFDA = Value FROM @udtEventData WHERE Property = 'isFDA';
		SELECT @MAPatientID = Value FROM @udtEventData WHERE Property = 'MAPatientID'
		SELECT @MAVisitID = Value FROM @udtEventData WHERE Property = 'MAVisitID'
		SELECT @MAScheduleID = Value FROM @udtEventData WHERE Property = 'MAScheduleID'
		SELECT @Qty = Value FROM @udtEventData WHERE Property = 'Qty'
		set @ItemID = (select itemid from s01tblItems where RFID=@RFID)
		set @CompartmentID = (select CompartmentID from s01tblItems where ItemID=@ItemID)
		set @MAPhysicianID= (select MAPhysicianID from s01tblPatientSchedules where MAScheduleID=@MAScheduleID)
		SELECT @OverrideNotes = Value FROM @udtEventData WHERE Property = 'OverrideNotes'

        SELECT @PhysicianID = PhysicianID FROM s01qryPatientDisplayADT WHERE MAPatientID = @MAPatientID;
        SELECT @MAPhysicianID = MAPhysicianID FROM s01tblPhysicians WHERE PhysicianID = @PhysicianID;
        SELECT @SessionID = Value FROM @udtEventData WHERE Property = 'SessionID';
        SELECT @EntryTypeID = Value FROM @udtSystemData WHERE Property = 'EntryTypeID';
        SELECT @PhysicianID = PhysicianID FROM s01qryPatientDisplayADT WHERE MAPatientID = @MAPatientID;
        SELECT @BillingStatusID = BillingStatusID FROM s01tblbillingstatus WHERE BillingStatus = 'Pending Bill Approval';
        SELECT @OverrideId = OverrideID, @OverrideNotes = OverrideDesc FROM s01tblOverride WHERE OverrideDesc = 'One Time Use';

        IF (SELECT [Setting Value] FROM s01tblSettingsCommon WHERE [Setting Name] = 'ENABLE_UTC_TIMEZONE') = 'TRUE'
        BEGIN
            SELECT @CurrentUTCTime = GETUTCDATE();
        END
        ELSE
        BEGIN
            SELECT @CurrentUTCTime = GETDATE();
        END

        SELECT @ItemStatusID = ItemStatusID FROM s01tblItemStatus WHERE [Item Status] = 'Used';
        SELECT @ItemEventID = ItemEventID FROM s01tblItemEvent WHERE EventDescription = 'Item used';
        SELECT @LocationTypeID = [Setting Value] FROM s01tblSettingscommon WHERE [Setting Name] = 'BarcodeInventory_LocationType' --AND ClusterID = @DeviceID;
        SELECT @LocationID = [Setting Value] FROM s01tblSettingscommon WHERE [Setting Name] = 'BarcodeInventory_Location' --AND ClusterID = @DeviceID;

        SELECT @ProductId = ProductID FROM s01tblProducts WHERE  [Cat No] = @CatNo;

		IF(1=1)
		BEGIN
			IF(@IsImplant = 'Y' AND (@SerialNo IS NOT NULL OR @LotNo IS NOT NULL OR @ExpDate IS NOT NULL))
			BEGIN
				INSERT INTO s01tblItems (
					ProductID, [Serial No], RFID, [Lot No], [Expired Date], [ItemStatusID], [Use Cycle], [AddUserID], [Date Added],
					[RmUserID], [Date Removed], [MAScheduleID], [MAPatientID], [MAPhysicianID], [Computer Name], [LastActivityDate], [Qty], [EntryTypeID]
				)
				SELECT @ProductId, @SerialNo, 'MA' + REPLACE(CAST(RAND() AS VARCHAR(50)), '.', '') + REPLACE(CONVERT(VARCHAR(10), @CurrentUtcTime, 120), '-', ''), @LotNo, @ExpDate, 4, 1, @UserID, @CurrentUtcTime, @UserID, @CurrentUtcTime,
					   @MAScheduleID, @MAPatientID, @MAPhysicianID, @ComputerName, @CurrentUtcTime, 1, @EntryTypeID;

				SELECT @ItemID = SCOPE_IDENTITY();

				INSERT INTO s01tblItemUsage (
					MAPatientID, MAScheduleID, MAVisitID, UsedBy, DateUsed, Qty, UnitPrice, BillingStatusID, OverrideID, OverrideNotes, UpdatedBy,
					LastActivityDate, UsageTypeID, UsageItemID, SessionID, UCDM, EntryTypeID
				)
				SELECT @MAPatientID, @MAScheduleID, @MAVisitID, @UserID, @CurrentUtcTime, 1, I.[Act Price], @BillingStatusID, @OverrideID, @OverrideNotes, @UserID, @CurrentUtcTime,
					   1, I.ItemID, @SessionID, I.ICDM, @EntryTypeID
				FROM s01tblItems I WHERE ItemID = @ItemID;

				SELECT @ItemUsageID = SCOPE_IDENTITY();

				INSERT INTO s01tblItemHistory(
					ItemStatusID, ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAScheduleID, MAVisitID, BillingStatusID, UsedBy, Qty,
					UnitPrice, UpdatedBy, UCDM, LastActivityDate, EntryTypeID, DateUsed, OverrideID, OverrideNotes, SessionID, ItemEventID
				)
				SELECT 1, @ItemUsageID, 1, U.UsageItemID, MAPatientID, MAScheduleID, MAVisitID, BillingStatusID, @UserID, U.Qty, U.UnitPrice,
					   @UserID, U.UCDM, @CurrentUtcTime, @EntryTypeID, U.DateUsed, 4, U.OverrideNotes, @SessionID, @ItemEventID  
				FROM s01tblItemUsage U WHERE SessionID = @SessionID AND U.LastActivityDate = @CurrentUtcTime;

				SELECT @ItemHistoryID = SCOPE_IDENTITY();

				INSERT INTO s01tblitemhistoryextn (ItemHistoryID, LocationID)
				SELECT @ItemHistoryID, @LocationID;

				INSERT INTO s01tblItemHistory(
					ItemStatusID, ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAScheduleID, MAVisitID, BillingStatusID, UsedBy, Qty,
					UnitPrice, UpdatedBy, UCDM, LastActivityDate, EntryTypeID, DateUsed, OverrideID, OverrideNotes, SessionID, ItemEventID
				)
				SELECT 1, @ItemUsageID, 1, U.UsageItemID, MAPatientID, MAScheduleID, MAVisitID, BillingStatusID, @UserID, U.Qty, U.UnitPrice,
					   @UserID, U.UCDM, @CurrentUtcTime, @EntryTypeID, U.DateUsed, 7, U.OverrideNotes, @SessionID, @ItemEventID  
				FROM s01tblItemUsage U WHERE SessionID = @SessionID AND U.LastActivityDate = @CurrentUtcTime;

				INSERT INTO s01tblitemhistoryextn (ItemHistoryID, LocationID)
				SELECT @ItemHistoryID, @LocationID;


				EXEC uspProcessTissueVerification @ItemUsageID = @ItemUsageID
				--EXECUTE  uspSWCSendUsageToEpic @Event = @Event
				--	, @udtSystemData = @udtSystemData
				--	, @udtEventData = @udtEventData
				--	, @ItemEventID = 13 -- Credit , we can use 3 also here
				--	, @ItemStatusID = @ItemStatusID
				--	, @UsageTypeID = 1
				--	, @ItemID = @ItemID


				SELECT 'SUCCESS' as DataSetName, 'Item has been removed' AS MSG;           
			END
			IF(@IsImplant = 'Y' AND (@SerialNo IS NULL AND @LotNo IS NULL AND @ExpDate IS NULL))
			BEGIN
				INSERT INTO s01tblItems (
					ProductID, [Serial No], RFID, [Lot No], [Expired Date], [ItemStatusID], [Use Cycle], [AddUserID], [Date Added],
					[RmUserID], [Date Removed], [MAScheduleID], [MAPatientID], [MAPhysicianID], [Computer Name], [LastActivityDate], [Qty], [EntryTypeID]
				)
				SELECT @ProductId, @SerialNo, 'MA' + REPLACE(CAST(RAND() AS VARCHAR(50)), '.', '') + REPLACE(CONVERT(VARCHAR(10), @CurrentUtcTime, 120), '-', ''), @LotNo, @ExpDate, 4, 1, @UserID, @CurrentUtcTime, @UserID, @CurrentUtcTime,
					   @MAScheduleID, @MAPatientID, @MAPhysicianID, @ComputerName, @CurrentUtcTime, 1, @EntryTypeID;

				SELECT @ItemID = SCOPE_IDENTITY();

				INSERT INTO s01tblItemUsage (
					MAPatientID, MAScheduleID, MAVisitID, UsedBy, DateUsed, Qty, UnitPrice, BillingStatusID, OverrideID, OverrideNotes, UpdatedBy,
					LastActivityDate, UsageTypeID, UsageItemID, SessionID, UCDM, EntryTypeID
				)
				SELECT @MAPatientID, @MAScheduleID, @MAVisitID, @UserID, @CurrentUtcTime, 1, I.[Act Price], @BillingStatusID, @OverrideID, @OverrideNotes, @UserID, @CurrentUtcTime,
					   1, I.ItemID, @SessionID, I.ICDM, @EntryTypeID
				FROM s01tblItems I WHERE ItemID = @ItemID;

				SELECT @ItemUsageID = SCOPE_IDENTITY();

				INSERT INTO s01tblItemHistory(
					ItemStatusID, ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAScheduleID, MAVisitID, BillingStatusID, UsedBy, Qty,
					UnitPrice, UpdatedBy, UCDM, LastActivityDate, EntryTypeID, DateUsed, OverrideID, OverrideNotes, SessionID, ItemEventID
				)
				SELECT 1, @ItemUsageID, 1, U.UsageItemID, MAPatientID, MAScheduleID, MAVisitID, BillingStatusID, @UserID, U.Qty, U.UnitPrice,
					   @UserID, U.UCDM, @CurrentUtcTime, @EntryTypeID, U.DateUsed, 4, U.OverrideNotes, @SessionID, @ItemEventID  
				FROM s01tblItemUsage U WHERE SessionID = @SessionID AND U.LastActivityDate = @CurrentUtcTime;

				SELECT @ItemHistoryID = SCOPE_IDENTITY();

				INSERT INTO s01tblitemhistoryextn (ItemHistoryID, LocationID)
				SELECT @ItemHistoryID, @LocationID;

				INSERT INTO s01tblItemHistory(
					ItemStatusID, ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAScheduleID, MAVisitID, BillingStatusID, UsedBy, Qty,
					UnitPrice, UpdatedBy, UCDM, LastActivityDate, EntryTypeID, DateUsed, OverrideID, OverrideNotes, SessionID, ItemEventID
				)
				SELECT 1, @ItemUsageID, 1, U.UsageItemID, MAPatientID, MAScheduleID, MAVisitID, BillingStatusID, @UserID, U.Qty, U.UnitPrice,
					   @UserID, U.UCDM, @CurrentUtcTime, @EntryTypeID, U.DateUsed, 7, U.OverrideNotes, @SessionID, @ItemEventID  
				FROM s01tblItemUsage U WHERE SessionID = @SessionID AND U.LastActivityDate = @CurrentUtcTime;

				INSERT INTO s01tblitemhistoryextn (ItemHistoryID, LocationID)
				SELECT @ItemHistoryID, @LocationID;


				EXEC uspProcessTissueVerification @ItemUsageID = @ItemUsageID
				--EXECUTE  uspSWCSendUsageToEpic @Event = @Event
				--	, @udtSystemData = @udtSystemData
				--	, @udtEventData = @udtEventData
				--	, @ItemEventID = 13 -- Credit , we can use 3 also here
				--	, @ItemStatusID = @ItemStatusID
				--	, @UsageTypeID = 1
				--	, @ItemID = @ItemID


				SELECT 'SUCCESS' as DataSetName, 'Item has been removed' AS MSG;           
			END
			--ELSE IF(@IsImplant = 'N' AND (@IsLotNoReq = 'TRUE' OR @IsSerialNoReq = 'TRUE' OR @IsExpirationDateReq = 'TRUE'))
			ELSE IF(@IsImplant = 'N' AND (@SerialNo IS NOT NULL OR @LotNo IS NOT NULL OR @ExpDate IS NOT NULL))
			BEGIN
			   INSERT INTO s01tblItems (
						ProductID, [Serial No], RFID, [Lot No], [Expired Date], [ItemStatusID], [Use Cycle], [AddUserID], [Date Added],
						[RmUserID], [Date Removed], [MAScheduleID], [MAPatientID], [MAPhysicianID], [Computer Name], [LastActivityDate], [Qty], [EntryTypeID]
					)
					SELECT @ProductId, @SerialNo, 'MA' + REPLACE(CAST(RAND() AS VARCHAR(50)), '.', '') + REPLACE(CONVERT(VARCHAR(10), @CurrentUtcTime, 120), '-', ''), @LotNo, @ExpDate, 4, 1, @UserID, @CurrentUtcTime, @UserID, @CurrentUtcTime,
						   @MAScheduleID, @MAPatientID, @MAPhysicianID, @ComputerName, @CurrentUtcTime, 1, @EntryTypeID;

					SELECT @ItemID = SCOPE_IDENTITY();

					INSERT INTO s01tblItemUsage (
						MAPatientID, MAScheduleID, MAVisitID, UsedBy, DateUsed, Qty, UnitPrice, BillingStatusID, OverrideID, OverrideNotes, UpdatedBy,
						LastActivityDate, UsageTypeID, UsageItemID, SessionID, UCDM, EntryTypeID
					)
					SELECT @MAPatientID, @MAScheduleID, @MAVisitID, @UserID, @CurrentUtcTime, 1, I.[Act Price], @BillingStatusID, @OverrideID, @OverrideNotes, @UserID, @CurrentUtcTime,
						   1, I.ItemID, @SessionID, I.ICDM, @EntryTypeID
					FROM s01tblItems I WHERE ItemID = @ItemID;

					SELECT @ItemUsageID = SCOPE_IDENTITY();

					INSERT INTO s01tblItemHistory(
						ItemStatusID, ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAScheduleID, MAVisitID, BillingStatusID, UsedBy, Qty,
						UnitPrice, UpdatedBy, UCDM, LastActivityDate, EntryTypeID, DateUsed, OverrideID, OverrideNotes, SessionID, ItemEventID
					)
					SELECT 1, @ItemUsageID, 1, U.UsageItemID, MAPatientID, MAScheduleID, MAVisitID, BillingStatusID, @UserID, U.Qty, U.UnitPrice,
						   @UserID, U.UCDM, @CurrentUtcTime, @EntryTypeID, U.DateUsed, 4, U.OverrideNotes, @SessionID, @ItemEventID  
					FROM s01tblItemUsage U WHERE SessionID = @SessionID AND U.LastActivityDate = @CurrentUtcTime;

					SELECT @ItemHistoryID = SCOPE_IDENTITY();

					INSERT INTO s01tblitemhistoryextn (ItemHistoryID, LocationID)
					SELECT @ItemHistoryID, @LocationID;

					INSERT INTO s01tblItemHistory(
					ItemStatusID, ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAScheduleID, MAVisitID, BillingStatusID, UsedBy, Qty,
					UnitPrice, UpdatedBy, UCDM, LastActivityDate, EntryTypeID, DateUsed, OverrideID, OverrideNotes, SessionID, ItemEventID
					)
					SELECT @ItemStatusID, @ItemUsageID, 1, U.UsageItemID, MAPatientID, MAScheduleID, MAVisitID, BillingStatusID, @UserID, U.Qty, U.UnitPrice,
						   @UserID, U.UCDM, @CurrentUtcTime, @EntryTypeID, U.DateUsed, 7, U.OverrideNotes, @SessionID, @ItemEventID  
					FROM s01tblItemUsage U WHERE SessionID = @SessionID AND U.LastActivityDate = @CurrentUtcTime;

					INSERT INTO s01tblitemhistoryextn (ItemHistoryID, LocationID)
					SELECT @ItemHistoryID, @LocationID;


					--EXECUTE  uspSWCSendUsageToEpic @Event = @Event
					--, @udtSystemData = @udtSystemData
					--, @udtEventData = @udtEventData
					--, @ItemEventID = 13 -- Credit , we can use 3 also here
					--, @ItemStatusID = @ItemStatusID
					--, @UsageTypeID = 1
					--, @ItemID = @ItemID

					SELECT 'SUCCESS' as DataSetName, 'Item has been removed' AS MSG;
			END
			ELSE IF(@IsImplant = 'N' AND (@SerialNo IS NULL AND @LotNo IS NULL AND @ExpDate IS NULL)) 
			BEGIN
				INSERT INTO s01tblItemInventory(ProductID,Qty,UpdatedBy,LastActivityDate,LocationID,LocationTypeID)
				VALUES (@ProductId, @Qty, @UserID, @CurrentUTCTime, @LocationID, @LocationTypeID )

				SELECT @ItemInventoryID = SCOPE_IDENTITY()

				INSERT INTO s01tblItemUsage ( MAPatientID, MAVisitID, MAScheduleID, UsedBy, DateUsed, Qty, UnitPrice, UOMCode, BillingStatusID, OverrideID, OverrideNotes
															, UpdatedBy, LastActivityDate, UsageTypeID, UsageItemID, SessionID, EntryTypeID )
									SELECT @MAPatientID, @MAVisitID, @MAScheduleID, @UserID, @CurrentUtcTime, 1, [Ref Price], UOMCode, 1, @OverrideID, @OverrideNotes, @UserID
															, @CurrentUTCTime, 2, @ItemInventoryID, @SessionID, @EntryTypeID 
									FROM s01tblProducts 
									WHERE ProductID = @ProductID

									SELECT @ItemUsageID = SCOPE_IDENTITY()
									SELECT @UsageItemID = UsageItemID 
									FROM s01tblItemUsage 
									WHERE ItemUsageID = @ItemUsageID
							
									--UPDATE s01tblItemInventory 
									--SET Qty = Qty - 1, UpdatedBy = @UserID, QtyLastUpdated = @CurrentUTCTime 
									--WHERE ItemInventoryID = @UsageItemID 

									INSERT INTO s01tblUpdateBarcodeItemInventoryLog( ItemInventoryID, QtyInHand, QtyLastUpdated, QtyUsed 
															, QtyReturned, QtyDeleted, QtyStocked, QtyWasted, CutOffTime, UpdatedQtyInHand )
									SELECT ItemInventoryID, Qty + 1, QtyLastUpdated, 1, 0, 0, 0, 0, @CurrentUTCTime, Qty 
									FROM s01tblItemInventory
									WHERE ItemInventoryID = @UsageItemID

									INSERT INTO s01tblItemHistory ( ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAVisitID, MAScheduleID, UsedBy, DateUsed, UnitPrice, UOMCode
														, OverrideID, OverrideNotes, SessionID, UCDM, BillingStatusID, ItemStatusID, UpdatedBy, LastActivityDate, ItemEventID, Qty, EntryTypeID ) 
									SELECT ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAVisitID, MAScheduleID, UsedBy, DateUsed, UnitPrice, UOMCode, @OverrideID, @OverrideNotes, @SessionID 
														, UCDM, BillingStatusID, NULL, @UserID, @CurrentUTCTime, @ItemEventID, 1, @EntryTypeID 
									FROM s01tblItemUsage 
									WHERE ItemUsageID = @ItemUsageID

									SELECT @ItemHistoryID = SCOPE_IDENTITY()
									INSERT INTO s01tblItemHistoryExtn (ItemHistoryID, LocationID ) 
									SELECT ItemHistoryID, @LocationID 
									FROM s01tblItemHistory 
									WHERE ItemHistoryID = @ItemHistoryID

									INSERT INTO tblInventoryLog ( ItemHistoryID, InvntoryID, ProcessStatus, ClusterID, LocationID, UpdatedBy, EntryTypeID, EnteredDate )
									SELECT ItemHistoryID, UsageItemID, 'P', @DeviceID, @LocationID, @UserID, @EntryTypeID, @CurrentUTCTime 
									FROM s01tblItemHistory 
									WHERE ItemHistoryID = @ItemHistoryID
						SELECT 'SUCCESS' as DataSetName, 'Item has been removed' AS MSG;
			END
		END
		

    COMMIT TRANSACTION;
    END TRY

    BEGIN CATCH
	IF XACT_STATE() <> 0
		SET @Msg = 'Error in Searching Product details';
	EXECUTE s01uspLogError
END CATCH
SET NOCOUNT OFF
END;
GO

--select *
--from [tblProductsExtn]
--select *
--from tblProducts
--where [Cat No] = 'BL-1800-10'

--select *
--from tblBarcodeRuleProduct
--where [Barcode No] like '%010088985860844217291117212415105-3117%'

--select *
--from tblItems
--where ProductID = 9869

--select *
--from tblBarcodeRuleProduct
--where [Barcode No] = '00889858608442'



ALTER PROCEDURE [dbo].[uspSaveFDADetailsinMASystem]
	@Event VARCHAR(255),
	@udtSystemData udtSPParam READONLY,
	@udtEventData udtSPParam READONLY
    --@ScannedBarcode VARCHAR(100),
    --@ProductBarcode varchar(100),
    --@CatNo varchar(100),
    --@Description varchar(100),
    --@Manufacturer varchar(100),
    --@ClusterID varchar(25),
    --@UserID varchar(25),
    --@ComputerName varchar(100),
    --@Domain varchar(100)
    --@IsImplant varchar(10)='N',
    --@ReqSerialNo varchar(10)='N',
    --@ReqLotNo varchar(10)='N',
    --@ProcessedFDAData varchar(2000)=''
AS
    SET NOCOUNT ON;
    DECLARE @CatNoExists varchar(100)
    --DECLARE @ProductID BIGINT
	DECLARE @Msg VARCHAR(200), 
            @ProcessedBarcodeNo VARCHAR(250),  
            @FullBarcodeNo VARCHAR(250), 
            @ProcessedCatNo VARCHAR(500), 
            @SessionID BIGINT, 
            @ItemStatusID INT, 
            @ItemEventID INT, 
            @Acknowledge VARCHAR(50),
            @CatNo VARCHAR(200), 
            @ProductId INT, 
            @ItemID INT, 
            @RFID VARCHAR(50), 
            @UserID INT, 
            @CurrentUtcTime DATETIME, 
            @ItemUsageID INT, 
            @ItemHistoryID INT,
            @MAPatientID VARCHAR(50), 
            @MAScheduleID INT, 
            @MAVisitID INT, 
            @OverrideId INT, 
            @OverrideNotes VARCHAR(255), 
            @PhysicianID VARCHAR(50), 
            @BillingStatusID INT, 
            @EntryTypeID INT,
            @SerialNo VARCHAR(50), 
            @MAPhysicianID INT, 
            @ComputerName VARCHAR(100), 
            @LotNo VARCHAR(50), 
            @ExpDate VARCHAR(50), 
            @Description VARCHAR(50), 
            @Manufacturer VARCHAR(50), 
            @BrandName VARCHAR(50),
            @BOID INT, 
            @DeviceID INT,  
            @LocationID INT, 
            @LocationTypeID INT, 
            @LocationID1 INT,  
            @LocationTypeID1 INT,
			@IsImplant VARCHAR(50),
			@IsLotNoReq VARCHAR(50),
			@IsSerialNoReq VARCHAR(50),
			@IsDiscontinue VARCHAR(50),
			@IsDiscontinueDate VARCHAR(50),
			@IsExpirationDateReq VARCHAR(50),
			@ModelNumber VARCHAR(200),
			@CompartmentID INT,
			@Qty INT,
			@Domain varchar(100),
			@ItemInventoryID INT,
			@UsageItemID INT,
			@CurrentDate datetime,
			@isFDA VARCHAR(50);
		SELECT @ProcessedBarcodeNo = Value FROM @udtEventData WHERE Property = 'ProcessedBarcodeNo';
        SELECT @FullBarcodeNo = Value FROM @udtEventData WHERE Property = 'FullBarcodeNo';
        SELECT @ProcessedCatNo = Value FROM @udtEventData WHERE Property = 'ProcessedCatNo';
        SELECT @UserID = Value FROM @udtEventData WHERE Property = 'UserID';
        SELECT @DeviceID = Value FROM @udtSystemData WHERE Property = 'DeviceID';
        SELECT @ComputerName = Value FROM @udtSystemData WHERE Property = 'ComputerName';
        SELECT @MAPatientID = Value FROM @udtEventData WHERE Property = 'MAPatientID';
        SELECT @MAVisitID = Value FROM @udtEventData WHERE Property = 'MAVisitID'; 
        SELECT @MAScheduleID = Value FROM @udtEventData WHERE Property = 'MAScheduleID'; 
        SELECT @ExpDate = Value FROM @udtEventData WHERE Property = 'ExpiryDate';
        SELECT @LotNo = Value FROM @udtEventData WHERE Property = 'LotNumber';
        SELECT @SerialNo = Value FROM @udtEventData WHERE Property = 'SerialNumber';
        SELECT @CatNo = Value FROM @udtEventData WHERE Property = 'CatalogNumber';    
        SELECT @Description = Value FROM @udtEventData WHERE Property = 'Description';    
        SELECT @Manufacturer = Value FROM @udtEventData WHERE Property = 'Manufacturer';    
        SELECT @BrandName = Value FROM @udtEventData WHERE Property = 'BrandName';
        SELECT @IsImplant = Value FROM @udtEventData WHERE Property = 'IsImplant';
		SELECT @IsLotNoReq = Value FROM @udtEventData WHERE Property = 'IsLotNoReq';
		SELECT @IsSerialNoReq = Value FROM @udtEventData WHERE Property = 'IsSerialNoReq';
		SELECT @IsDiscontinue = Value FROM @udtEventData WHERE Property = 'IsDiscontinue';
		SELECT @IsDiscontinueDate = Value FROM @udtEventData WHERE Property = 'IsDiscontinueDate';
		SELECT @IsExpirationDateReq = Value FROM @udtEventData WHERE Property = 'IsExpirationDateReq';
		SELECT @ModelNumber = Value FROM @udtEventData WHERE Property = 'ModelNumber';
		SELECT @isFDA = Value FROM @udtEventData WHERE Property = 'isFDA';
		SELECT @MAPatientID = Value FROM @udtEventData WHERE Property = 'MAPatientID'
		SELECT @MAVisitID = Value FROM @udtEventData WHERE Property = 'MAVisitID'
		SELECT @MAScheduleID = Value FROM @udtEventData WHERE Property = 'MAScheduleID'
		SELECT @Qty = Value FROM @udtEventData WHERE Property = 'Qty'
		set @ItemID = (select itemid from s01tblItems where RFID=@RFID)
		set @CompartmentID = (select CompartmentID from s01tblItems where ItemID=@ItemID)
		set @MAPhysicianID= (select MAPhysicianID from s01tblPatientSchedules where MAScheduleID=@MAScheduleID)
		SELECT @OverrideNotes = Value FROM @udtEventData WHERE Property = 'OverrideNotes'
		SET @CurrentDate = GETDATE();

        SELECT @PhysicianID = PhysicianID FROM s01qryPatientDisplayADT WHERE MAPatientID = @MAPatientID;
        SELECT @MAPhysicianID = MAPhysicianID FROM s01tblPhysicians WHERE PhysicianID = @PhysicianID;
        SELECT @SessionID = Value FROM @udtEventData WHERE Property = 'SessionID';
        SELECT @EntryTypeID = Value FROM @udtSystemData WHERE Property = 'EntryTypeID';
        SELECT @PhysicianID = PhysicianID FROM s01qryPatientDisplayADT WHERE MAPatientID = @MAPatientID;
        SELECT @BillingStatusID = BillingStatusID FROM s01tblbillingstatus WHERE BillingStatus = 'Pending Bill Approval';
        SELECT @OverrideId = OverrideID, @OverrideNotes = OverrideDesc FROM s01tblOverride WHERE OverrideDesc = 'One Time Use';
   
    IF (@CatNo is not null)
    BEGIN
        SELECT @CatNoExists = [Cat No]
        FROM s01tblProducts
        WHERE --[Cat No] = @CatNo OR 
		[Cat No] = @ProcessedCatNo
        SELECT @CatNoExists = [Cat No]
        FROM s01tblBarcodeRuleProduct
        WHERE [Barcode No] = @ProcessedBarcodeNo OR [Barcode No] = @FullBarcodeNo
        IF @CatNoExists is null
        BEGIN
            BEGIN
                INSERT INTO [dbo].[s01tblProducts]
                   ([Cat No]
                   ,[Description 1]
                   ,[Manufacturer]
                   ,[SKU No]
                   ,[Entered By]
                   ,[Computer Name]
                   ,[Domain]
                   ,[Entered Date]
                   ,[ProductTypeID]
                   ,[EntryTypeID]
                   ,[ProdUnitID]
                   ,[RFID]
                   ,[ID 3]
                   ,[ID 4]
				   ,[Brand Name])
                VALUES
                   (@CatNo
                   ,@Description
                   ,@Manufacturer
                   ,@ProcessedBarcodeNo
                   ,@UserID
                   ,@ComputerName
                   ,@Domain
                   ,GETDATE()
                   ,1
                   ,1
                   ,1
                   ,@IsImplant
                   ,@IsSerialNoReq
                   ,@IsLotNoReq
				   ,@BrandName)            
                SET @ProductID = SCOPE_IDENTITY()
            END
            BEGIN
                INSERT INTO .[dbo].[s01tblBarcodeRuleProduct]
                   ([Barcode No]
                   ,[Cat No])
                VALUES
                   (@ProcessedBarcodeNo
                   ,@CatNo)
            END
			BEGIN
                INSERT INTO .[dbo].[s01tblProductsExtn]
                   (ProductID
                   ,ModelNumber
				   ,isSerialNoReq
				   ,isLotNoReq
				   ,isImplant
				   ,[ExpirationDate]
				   ,isExpirationDateReq
				   ,isDiscontiued
				   ,isDiscontiueDate
				   ,LastActivityDate
				   ,CreatedDate)
                VALUES
                   (@ProductID
                   ,@ModelNumber
				   ,@IsSerialNoReq
				   ,@IsLotNoReq
				   ,@IsImplant
				   ,@ExpDate
				   ,@IsExpirationDateReq
				   ,@IsDiscontinue
				   ,@IsDiscontinueDate
				   ,@CurrentDate
				   ,@CurrentDate)
            END
			--BEGIN
			--	IF NOT EXISTS (SELECT 1 FROM s01tblItems WHERE ProductID = @ProductID AND (@IsImplant = 'Y' OR @IsImplant IS NULL))
			--	BEGIN
			--		INSERT INTO s01tblItems (
			--		ProductID, [Serial No], RFID, [Lot No], [Expired Date], [ItemStatusID], [Use Cycle], [AddUserID], [Date Added],
			--		[RmUserID], [Date Removed], [MAScheduleID], [MAPatientID], [MAPhysicianID], [Computer Name], [LastActivityDate], [Qty], [EntryTypeID]
			--		)
			--		SELECT @ProductId, @SerialNo, 'MA' + REPLACE(CAST(RAND() AS VARCHAR(50)), '.', '') + REPLACE(CONVERT(VARCHAR(10), @CurrentUtcTime, 120), '-', ''), @LotNo, @ExpDate, 4, 1, @UserID, @CurrentDate, @UserID, @CurrentDate,
			--		@MAScheduleID, @MAPatientID, @MAPhysicianID, @ComputerName, @CurrentUtcTime, 1, @EntryTypeID;
			--	END
			--	ELSE
			--	BEGIN
			--		INSERT INTO s01tblItemInventory(ProductID,Qty,UpdatedBy,LastActivityDate)
			--		VALUES (@ProductId, @Qty, @UserID, @CurrentDate)
			--	END
			--END
        END
		ELSE
		BEGIN
			SELECT @ProductId = ProductID
			FROM s01tblProducts
			WHERE [Cat No] = @CatNo
		END
    END
	ELSE
	BEGIN
		SELECT @ProductId = ProductID
		FROM s01tblProducts
		WHERE [Cat No] = @CatNo
	END
    SELECT P.ProductID, P.[Cat No], P.[Description 1], P.Manufacturer, P.[Brand Name],PE.isImplant,PE.isLotNoReq,PE.isSerialNoReq,PE.ModelNumber,PE.[ExpirationDate]
	,NULL AS [Serial No],NULL AS [Lot No]
	FROM s01tblProducts P LEFT OUTER JOIN
	s01tblProductsExtn PE ON P.ProductID = PE.ProductID
	WHERE P.ProductID = @ProductID
GO

CREATE PROCEDURE [dbo].[uspProcessBarcodeItems]
    @Event VARCHAR(255),
    @udtSystemData udtSPParam READONLY,
    @udtEventData udtSPParam READONLY
AS
BEGIN
    SET NOCOUNT ON;
    SET XACT_ABORT ON;

    DECLARE @Msg VARCHAR(200), 
            @ProcessedBarcodeNo VARCHAR(250),  
            @FullBarcodeNo VARCHAR(250), 
            @ProcessedCatNo VARCHAR(500), 
            @SessionID BIGINT, 
            @ItemStatusID INT, 
            @ItemEventID INT, 
            @Acknowledge VARCHAR(50),
            @CatNo VARCHAR(200), 
            @ProductId INT, 
            @ItemID INT, 
            @RFID VARCHAR(50), 
            @UserID INT, 
            @CurrentUtcTime DATETIME, 
            @ItemUsageID INT, 
            @ItemHistoryID INT,
            @MAPatientID VARCHAR(50), 
            @MAScheduleID INT, 
            @MAVisitID INT, 
            @OverrideId INT, 
            @OverrideNotes VARCHAR(255), 
            @PhysicianID VARCHAR(50), 
            @BillingStatusID INT, 
            @EntryTypeID INT,
            @SerialNo VARCHAR(50), 
            @MAPhysicianID INT, 
            @ComputerName VARCHAR(50), 
            @LotNo VARCHAR(50), 
            @ExpDate VARCHAR(50), 
            @Description VARCHAR(50), 
            @Manufacturer VARCHAR(50), 
            @BrandName VARCHAR(50),
            @BOID INT, 
            @DeviceID INT,  
            @LocationID INT, 
            @LocationTypeID INT, 
            @LocationID1 INT,  
            @LocationTypeID1 INT,
			@IsImplant VARCHAR(50),
			@IsLotNoReq VARCHAR(50),
			@IsSerialNoReq VARCHAR(50),
			@IsDiscontinue VARCHAR(50),
			@IsDiscontinueDate VARCHAR(50),
			@IsExpirationDateReq VARCHAR(50),
			@ModelNumber VARCHAR(200),
			@CompartmentID INT,
			@Qty INT,
			@ItemInventoryID INT,
			@UsageItemID INT,
			@isFDA VARCHAR(50);

    BEGIN TRY
        BEGIN TRANSACTION;

        SELECT @ProcessedBarcodeNo = Value FROM @udtEventData WHERE Property = 'ProcessedBarcodeNo';
        SELECT @FullBarcodeNo = Value FROM @udtEventData WHERE Property = 'FullBarcodeNo';
        SELECT @ProcessedCatNo = Value FROM @udtEventData WHERE Property = 'ProcessedCatNo';
        SELECT @UserID = Value FROM @udtEventData WHERE Property = 'UserID';
        SELECT @DeviceID = Value FROM @udtSystemData WHERE Property = 'DeviceID';
        SELECT @ComputerName = Value FROM @udtSystemData WHERE Property = 'ComputerName';
        SELECT @MAPatientID = Value FROM @udtEventData WHERE Property = 'MAPatientID';
        SELECT @MAVisitID = Value FROM @udtEventData WHERE Property = 'MAVisitID'; 
        SELECT @MAScheduleID = Value FROM @udtEventData WHERE Property = 'MAScheduleID'; 
        SELECT @ExpDate = Value FROM @udtEventData WHERE Property = 'ExpiryDate';
        SELECT @LotNo = Value FROM @udtEventData WHERE Property = 'LotNumber';
        SELECT @SerialNo = Value FROM @udtEventData WHERE Property = 'SerialNumber';
        SELECT @CatNo = Value FROM @udtEventData WHERE Property = 'CatalogNumber';    
        SELECT @Description = Value FROM @udtEventData WHERE Property = 'Description';    
        SELECT @Manufacturer = Value FROM @udtEventData WHERE Property = 'Manufacturer';    
        SELECT @BrandName = Value FROM @udtEventData WHERE Property = 'BrandName';
        SELECT @IsImplant = Value FROM @udtEventData WHERE Property = 'IsImplant';
		SELECT @IsLotNoReq = Value FROM @udtEventData WHERE Property = 'IsLotNoReq';
		SELECT @IsSerialNoReq = Value FROM @udtEventData WHERE Property = 'IsSerialNoReq';
		SELECT @IsDiscontinue = Value FROM @udtEventData WHERE Property = 'IsDiscontinue';
		SELECT @IsDiscontinueDate = Value FROM @udtEventData WHERE Property = 'IsDiscontinueDate';
		SELECT @IsExpirationDateReq = Value FROM @udtEventData WHERE Property = 'IsExpirationDateReq';
		SELECT @ModelNumber = Value FROM @udtEventData WHERE Property = 'ModelNumber';
		SELECT @isFDA = Value FROM @udtEventData WHERE Property = 'isFDA';
		SELECT @MAPatientID = Value FROM @udtEventData WHERE Property = 'MAPatientID'
		SELECT @MAVisitID = Value FROM @udtEventData WHERE Property = 'MAVisitID'
		SELECT @MAScheduleID = Value FROM @udtEventData WHERE Property = 'MAScheduleID'
		SELECT @Qty = Value FROM @udtEventData WHERE Property = 'Qty'
		set @ItemID = (select itemid from s01tblItems where RFID=@RFID)
		set @CompartmentID = (select CompartmentID from s01tblItems where ItemID=@ItemID)
		set @MAPhysicianID= (select MAPhysicianID from s01tblPatientSchedules where MAScheduleID=@MAScheduleID)
		SELECT @OverrideNotes = Value FROM @udtEventData WHERE Property = 'OverrideNotes'

        SELECT @PhysicianID = PhysicianID FROM s01qryPatientDisplayADT WHERE MAPatientID = @MAPatientID;
        SELECT @MAPhysicianID = MAPhysicianID FROM s01tblPhysicians WHERE PhysicianID = @PhysicianID;
        SELECT @SessionID = Value FROM @udtEventData WHERE Property = 'SessionID';
        SELECT @EntryTypeID = Value FROM @udtSystemData WHERE Property = 'EntryTypeID';
        SELECT @PhysicianID = PhysicianID FROM s01qryPatientDisplayADT WHERE MAPatientID = @MAPatientID;
        SELECT @BillingStatusID = BillingStatusID FROM s01tblbillingstatus WHERE BillingStatus = 'Pending Bill Approval';
        SELECT @OverrideId = OverrideID, @OverrideNotes = OverrideDesc FROM s01tblOverride WHERE OverrideDesc = 'One Time Use';

        IF (SELECT [Setting Value] FROM s01tblSettingsCommon WHERE [Setting Name] = 'ENABLE_UTC_TIMEZONE') = 'TRUE'
        BEGIN
            SELECT @CurrentUTCTime = GETUTCDATE();
        END
        ELSE
        BEGIN
            SELECT @CurrentUTCTime = GETDATE();
        END

        SELECT @ItemStatusID = ItemStatusID FROM s01tblItemStatus WHERE [Item Status] = 'Used';
        SELECT @ItemEventID = ItemEventID FROM s01tblItemEvent WHERE EventDescription = 'Item used';
        SELECT @LocationTypeID = [Setting Value] FROM s01tblSettingscommon WHERE [Setting Name] = 'BarcodeInventory_LocationType' --AND ClusterID = @DeviceID;
        SELECT @LocationID = [Setting Value] FROM s01tblSettingscommon WHERE [Setting Name] = 'BarcodeInventory_Location' --AND ClusterID = @DeviceID;

        SELECT @ProductId = ProductID FROM s01tblProducts WHERE  [Cat No] = @CatNo;

		IF(1=1)
		BEGIN
			--IF EXISTS( SELECT TOP 1 * FROM s01tblItems WHERE [Serial No] = @SerialNo)
			--BEGIN
			--	SELECT 'Serial Number Already Exist' AS MSG
			--END

			IF(@IsImplant = 'Y' AND (@SerialNo IS NOT NULL OR @LotNo IS NOT NULL OR @ExpDate IS NOT NULL))
			BEGIN
				INSERT INTO s01tblItems (
					ProductID, [Serial No], RFID, [Lot No], [Expired Date], [ItemStatusID], [Use Cycle], [AddUserID], [Date Added],
					[RmUserID], [Date Removed], [MAScheduleID], [MAPatientID], [MAPhysicianID], [Computer Name], [LastActivityDate], [Qty], [EntryTypeID]
				)
				SELECT @ProductId, @SerialNo, 'MA' + REPLACE(CAST(RAND() AS VARCHAR(50)), '.', '') + REPLACE(CONVERT(VARCHAR(10), @CurrentUtcTime, 120), '-', ''), @LotNo, @ExpDate, 4, 1, @UserID, @CurrentUtcTime, @UserID, @CurrentUtcTime,
					   @MAScheduleID, @MAPatientID, @MAPhysicianID, @ComputerName, @CurrentUtcTime, 1, @EntryTypeID;

				SELECT @ItemID = SCOPE_IDENTITY();

				INSERT INTO s01tblItemUsage (
					MAPatientID, MAScheduleID, MAVisitID, UsedBy, DateUsed, Qty, UnitPrice, BillingStatusID, OverrideID, OverrideNotes, UpdatedBy,
					LastActivityDate, UsageTypeID, UsageItemID, SessionID, UCDM, EntryTypeID
				)
				SELECT @MAPatientID, @MAScheduleID, @MAVisitID, @UserID, @CurrentUtcTime, 1, I.[Act Price], @BillingStatusID, @OverrideID, @OverrideNotes, @UserID, @CurrentUtcTime,
					   1, I.ItemID, @SessionID, I.ICDM, @EntryTypeID
				FROM s01tblItems I WHERE ItemID = @ItemID;

				SELECT @ItemUsageID = SCOPE_IDENTITY();

				INSERT INTO s01tblItemHistory(
					ItemStatusID, ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAScheduleID, MAVisitID, BillingStatusID, UsedBy, Qty,
					UnitPrice, UpdatedBy, UCDM, LastActivityDate, EntryTypeID, DateUsed, OverrideID, OverrideNotes, SessionID, ItemEventID
				)
				SELECT 1, @ItemUsageID, 1, U.UsageItemID, MAPatientID, MAScheduleID, MAVisitID, BillingStatusID, @UserID, U.Qty, U.UnitPrice,
					   @UserID, U.UCDM, @CurrentUtcTime, @EntryTypeID, U.DateUsed, 4, U.OverrideNotes, @SessionID, @ItemEventID  
				FROM s01tblItemUsage U WHERE SessionID = @SessionID AND U.LastActivityDate = @CurrentUtcTime;

				SELECT @ItemHistoryID = SCOPE_IDENTITY();

				INSERT INTO s01tblitemhistoryextn (ItemHistoryID, LocationID)
				SELECT @ItemHistoryID, @LocationID;

				INSERT INTO s01tblItemHistory(
					ItemStatusID, ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAScheduleID, MAVisitID, BillingStatusID, UsedBy, Qty,
					UnitPrice, UpdatedBy, UCDM, LastActivityDate, EntryTypeID, DateUsed, OverrideID, OverrideNotes, SessionID, ItemEventID
				)
				SELECT 1, @ItemUsageID, 1, U.UsageItemID, MAPatientID, MAScheduleID, MAVisitID, BillingStatusID, @UserID, U.Qty, U.UnitPrice,
					   @UserID, U.UCDM, @CurrentUtcTime, @EntryTypeID, U.DateUsed, 7, U.OverrideNotes, @SessionID, @ItemEventID  
				FROM s01tblItemUsage U WHERE SessionID = @SessionID AND U.LastActivityDate = @CurrentUtcTime;

				INSERT INTO s01tblitemhistoryextn (ItemHistoryID, LocationID)
				SELECT @ItemHistoryID, @LocationID;


				EXEC uspProcessTissueVerification @ItemUsageID = @ItemUsageID
				--EXECUTE  uspSWCSendUsageToEpic @Event = @Event
				--	, @udtSystemData = @udtSystemData
				--	, @udtEventData = @udtEventData
				--	, @ItemEventID = 13 -- Credit , we can use 3 also here
				--	, @ItemStatusID = @ItemStatusID
				--	, @UsageTypeID = 1
				--	, @ItemID = @ItemID


				SELECT 'SUCCESS' as DataSetName, 'Item has been removed' AS MSG;           
			END
			--ELSE IF(@IsImplant = 'N' AND (@IsLotNoReq = 'TRUE' OR @IsSerialNoReq = 'TRUE' OR @IsExpirationDateReq = 'TRUE'))
			ELSE IF(@IsImplant = 'N' AND (@SerialNo IS NOT NULL OR @LotNo IS NOT NULL OR @ExpDate IS NOT NULL))
			BEGIN
			   INSERT INTO s01tblItems (
						ProductID, [Serial No], RFID, [Lot No], [Expired Date], [ItemStatusID], [Use Cycle], [AddUserID], [Date Added],
						[RmUserID], [Date Removed], [MAScheduleID], [MAPatientID], [MAPhysicianID], [Computer Name], [LastActivityDate], [Qty], [EntryTypeID]
					)
					SELECT @ProductId, @SerialNo, 'MA' + REPLACE(CAST(RAND() AS VARCHAR(50)), '.', '') + REPLACE(CONVERT(VARCHAR(10), @CurrentUtcTime, 120), '-', ''), @LotNo, @ExpDate, 4, 1, @UserID, @CurrentUtcTime, @UserID, @CurrentUtcTime,
						   @MAScheduleID, @MAPatientID, @MAPhysicianID, @ComputerName, @CurrentUtcTime, 1, @EntryTypeID;

					SELECT @ItemID = SCOPE_IDENTITY();

					INSERT INTO s01tblItemUsage (
						MAPatientID, MAScheduleID, MAVisitID, UsedBy, DateUsed, Qty, UnitPrice, BillingStatusID, OverrideID, OverrideNotes, UpdatedBy,
						LastActivityDate, UsageTypeID, UsageItemID, SessionID, UCDM, EntryTypeID
					)
					SELECT @MAPatientID, @MAScheduleID, @MAVisitID, @UserID, @CurrentUtcTime, 1, I.[Act Price], @BillingStatusID, @OverrideID, @OverrideNotes, @UserID, @CurrentUtcTime,
						   1, I.ItemID, @SessionID, I.ICDM, @EntryTypeID
					FROM s01tblItems I WHERE ItemID = @ItemID;

					SELECT @ItemUsageID = SCOPE_IDENTITY();

					INSERT INTO s01tblItemHistory(
						ItemStatusID, ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAScheduleID, MAVisitID, BillingStatusID, UsedBy, Qty,
						UnitPrice, UpdatedBy, UCDM, LastActivityDate, EntryTypeID, DateUsed, OverrideID, OverrideNotes, SessionID, ItemEventID
					)
					SELECT 1, @ItemUsageID, 1, U.UsageItemID, MAPatientID, MAScheduleID, MAVisitID, BillingStatusID, @UserID, U.Qty, U.UnitPrice,
						   @UserID, U.UCDM, @CurrentUtcTime, @EntryTypeID, U.DateUsed, 4, U.OverrideNotes, @SessionID, @ItemEventID  
					FROM s01tblItemUsage U WHERE SessionID = @SessionID AND U.LastActivityDate = @CurrentUtcTime;

					SELECT @ItemHistoryID = SCOPE_IDENTITY();

					INSERT INTO s01tblitemhistoryextn (ItemHistoryID, LocationID)
					SELECT @ItemHistoryID, @LocationID;

					INSERT INTO s01tblItemHistory(
					ItemStatusID, ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAScheduleID, MAVisitID, BillingStatusID, UsedBy, Qty,
					UnitPrice, UpdatedBy, UCDM, LastActivityDate, EntryTypeID, DateUsed, OverrideID, OverrideNotes, SessionID, ItemEventID
					)
					SELECT @ItemStatusID, @ItemUsageID, 1, U.UsageItemID, MAPatientID, MAScheduleID, MAVisitID, BillingStatusID, @UserID, U.Qty, U.UnitPrice,
						   @UserID, U.UCDM, @CurrentUtcTime, @EntryTypeID, U.DateUsed, 7, U.OverrideNotes, @SessionID, @ItemEventID  
					FROM s01tblItemUsage U WHERE SessionID = @SessionID AND U.LastActivityDate = @CurrentUtcTime;

					INSERT INTO s01tblitemhistoryextn (ItemHistoryID, LocationID)
					SELECT @ItemHistoryID, @LocationID;


					--EXECUTE  uspSWCSendUsageToEpic @Event = @Event
					--, @udtSystemData = @udtSystemData
					--, @udtEventData = @udtEventData
					--, @ItemEventID = 13 -- Credit , we can use 3 also here
					--, @ItemStatusID = @ItemStatusID
					--, @UsageTypeID = 1
					--, @ItemID = @ItemID

					SELECT 'SUCCESS' as DataSetName, 'Item has been removed' AS MSG;
			END
			ELSE IF(@IsImplant = 'N' AND (@SerialNo IS NULL AND @LotNo IS NULL AND @ExpDate IS NULL)) 
			BEGIN
				INSERT INTO s01tblItemInventory(ProductID,Qty,UpdatedBy,LastActivityDate,LocationID,LocationTypeID)
				VALUES (@ProductId, @Qty, @UserID, @CurrentUTCTime, @LocationID, @LocationTypeID )

				SELECT @ItemInventoryID = SCOPE_IDENTITY()

				INSERT INTO s01tblItemUsage ( MAPatientID, MAVisitID, MAScheduleID, UsedBy, DateUsed, Qty, UnitPrice, UOMCode, BillingStatusID, OverrideID, OverrideNotes
															, UpdatedBy, LastActivityDate, UsageTypeID, UsageItemID, SessionID, EntryTypeID )
									SELECT @MAPatientID, @MAVisitID, @MAScheduleID, @UserID, @CurrentUtcTime, 1, [Ref Price], UOMCode, 1, @OverrideID, @OverrideNotes, @UserID
															, @CurrentUTCTime, 2, @ItemInventoryID, @SessionID, @EntryTypeID 
									FROM s01tblProducts 
									WHERE ProductID = @ProductID

									SELECT @ItemUsageID = SCOPE_IDENTITY()
									SELECT @UsageItemID = UsageItemID 
									FROM s01tblItemUsage 
									WHERE ItemUsageID = @ItemUsageID
							
									--UPDATE s01tblItemInventory 
									--SET Qty = Qty - 1, UpdatedBy = @UserID, QtyLastUpdated = @CurrentUTCTime 
									--WHERE ItemInventoryID = @UsageItemID 

									INSERT INTO s01tblUpdateBarcodeItemInventoryLog( ItemInventoryID, QtyInHand, QtyLastUpdated, QtyUsed 
															, QtyReturned, QtyDeleted, QtyStocked, QtyWasted, CutOffTime, UpdatedQtyInHand )
									SELECT ItemInventoryID, Qty + 1, QtyLastUpdated, 1, 0, 0, 0, 0, @CurrentUTCTime, Qty 
									FROM s01tblItemInventory
									WHERE ItemInventoryID = @UsageItemID

									INSERT INTO s01tblItemHistory ( ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAVisitID, MAScheduleID, UsedBy, DateUsed, UnitPrice, UOMCode
														, OverrideID, OverrideNotes, SessionID, UCDM, BillingStatusID, ItemStatusID, UpdatedBy, LastActivityDate, ItemEventID, Qty, EntryTypeID ) 
									SELECT ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAVisitID, MAScheduleID, UsedBy, DateUsed, UnitPrice, UOMCode, @OverrideID, @OverrideNotes, @SessionID 
														, UCDM, BillingStatusID, NULL, @UserID, @CurrentUTCTime, @ItemEventID, 1, @EntryTypeID 
									FROM s01tblItemUsage 
									WHERE ItemUsageID = @ItemUsageID

									SELECT @ItemHistoryID = SCOPE_IDENTITY()
									INSERT INTO s01tblItemHistoryExtn (ItemHistoryID, LocationID ) 
									SELECT ItemHistoryID, @LocationID 
									FROM s01tblItemHistory 
									WHERE ItemHistoryID = @ItemHistoryID

									INSERT INTO tblInventoryLog ( ItemHistoryID, InvntoryID, ProcessStatus, ClusterID, LocationID, UpdatedBy, EntryTypeID, EnteredDate )
									SELECT ItemHistoryID, UsageItemID, 'P', @DeviceID, @LocationID, @UserID, @EntryTypeID, @CurrentUTCTime 
									FROM s01tblItemHistory 
									WHERE ItemHistoryID = @ItemHistoryID	
			END
		END
		

    COMMIT TRANSACTION;
    END TRY

    BEGIN CATCH
	IF XACT_STATE() <> 0
		SET @Msg = 'Error in Searching Product details';
	EXECUTE s01uspLogError
END CATCH
SET NOCOUNT OFF
END;
GO

CREATE Procedure [dbo].[uspWebConsoleScanBarcodeInventory]
	@Event VARCHAR(255)
	, @udtSystemData udtSPParam READONLY
	, @udtEventData udtSPParam READONLY
	,@CatNoFromFDA VARCHAR(200)
AS
	SET NOCOUNT ON;
		DECLARE @DeviceID INT, @EntryTypeID INT, @Msg VARCHAR(200), @DataSetName VARCHAR(50), @SessionID varchar(50), @UserID INT, @ItemUsageID INT, @FDACatNo VARCHAR(50)
		DECLARE @CurrentPatientID INT, @CurrentUtcTime DATETIME, @ScannedBarcodeValue VARCHAR(100), @EventType VARCHAR(50), @CatNo VARCHAR(50), @ProductID INT, @LocationID INT, @LocationTypeID INT, @BarcodeSettings VARCHAR(10)
		DECLARE @UpdateQty VARCHAR(10), @UOMCode VARCHAR(10), @Qty INT,@inputQty INT, @ItemInventoryID INT, @EventInput3 VARCHAR(50), @EventInPut4 VARCHAR(50),  @OverrideID INT, @ItemEventID INT, @InventoryID INT
		DECLARE @ReasonWindowSetting VARCHAR(10), @OverrideNotes VARCHAR(50), @PatientName VARCHAR(200), @MAPatientID INT, @MAScheduleID INT, @MAVisitID INT, @ItemHistoryID INT, @UsageItemID INT, @IsFDA VARCHAR(50), @IsUsageExists VARCHAR(50),@implantStatus VARCHAR(50),@isImplant VARCHAR(10)
		
		--Edted by subramanya to handle type mismatch exception
		--SET XACT_ABORT ON;
		BEGIN TRY
		--BEGIN TRANSACTION

		SELECT @DeviceID = Value FROM @udtSystemData WHERE Property = 'DeviceID'
		SELECT @EntryTypeID = Value FROM @udtSystemData WHERE Property = 'EntryTypeID'
		SELECT @UserID = Value FROM @udtEventData WHERE Property = 'UserID'
		SELECT @MAPatientID = Value FROM @udtEventData WHERE Property = 'MAPatientID'
		SELECT @ScannedBarcodeValue = Value FROM @udtEventData WHERE Property = 'EventInPut1'
		SELECT @UpdateQty = Value FROM @udtEventData WHERE Property = 'EventInPut2'
		SELECT @EventInPut3 = Value FROM @udtEventData WHERE Property = 'EventInPut3'
		SELECT @EventInPut4 = Value FROM @udtEventData WHERE Property = 'EventInPut4'
		SELECT @EventType = Value FROM @udtEventData WHERE Property = 'EventType'
		SELECT @SessionID = Value FROM @udtEventData WHERE Property = 'SessionID'
		SELECT @DataSetName = Value FROM @udtEventData WHERE Property = 'EventOutPut'
		SELECT @InventoryID = Value FROM @udtEventData WHERE Property = 'ItemInventoryID'
		
		SELECT @MAVisitID = Value FROM @udtEventData WHERE Property = 'MAVisitID' 
		SELECT @MAScheduleID = Value FROM @udtEventData WHERE Property = 'MAScheduleID' 
		SELECT @FDACatNo = Value FROM @udtEventData WHERE Property = 'CatNo' 
		SELECT @IsFDA = Value FROM @udtEventData WHERE Property = 'IsFDA' 
		SELECT @inputQty = Value FROM @udtEventData WHERE Property = 'Qty' 
		SELECT @implantStatus = Value FROM @udtEventData WHERE Property = 'ImplantStatus' 
		--select @IsFDA;
	

		SELECT @LocationTypeID = [dbo].[udf700WebConsoleSUPFetchSettings] ( @DeviceId, 'BarcodeInventory_LocationType' )

		SELECT @LocationID = [dbo].[udf700WebConsoleSUPFetchSettings] ( @DeviceId, 'BarcodeInventory_Location' )

		IF(@implantStatus IS NOT NULL)
		BEGIN
			IF(@implantStatus = 'Implant')
			BEGIN
				SET @isImplant = 'Y';
			END
			ELSE
			BEGIN
				SET @isImplant = 'N';
			END
		END
		
		IF (SELECT [Setting Value] FROM s01tblSettingsCommon WHERE [Setting Name] = 'ENABLE_UTC_TIMEZONE') = 'TRUE'
		BEGIN
			SELECT @CurrentUTCTime = GETUTCDATE()
		END
		ELSE
		BEGIN
			SELECT @CurrentUTCTime = GETDATE()
		END
		

		SELECT @CatNo = [Cat No]
		FROM s01tblBarcodeRuleProduct 
		WHERE [Barcode No] = @ScannedBarcodeValue
			AND @ScannedBarcodeValue IS NOT NULL
			AND @ScannedBarcodeValue <> ''

		SELECT @ProductID = ProductID, @UOMCode = UOMCode 
		FROM s01tblProducts 
		WHERE [Cat No] = @CatNo OR [Cat No] = @CatNoFromFDA
		
		--SR added > FDA/Master
		if (@IsFDA='true')
		begin
		
		--IF NOT EXISTS ( SELECT 1 FROM s01tblProducts 	
		--				WHERE REPLACE(REPLACE([Cat No],'-',''),'.','') = REPLACE(REPLACE(@CatNo,'-',''),'.','')
		--					OR @CatNo IS NULL OR @CatNo = ''
		--		      )
		--BEGIN
			
			EXECUTE usp826WebConsoleSaveBarcodeMasterFDAProducts 
					  @Event = @Event
					, @udtSystemData = @udtSystemData
					, @udtEventData = @udtEventData
					, @ProductID = @ProductID -- viresh added on 12/02/2024
			
			SELECT @CatNo = [Cat No]
			FROM s01tblBarcodeRuleProduct 
			WHERE [Barcode No] = @ScannedBarcodeValue

			SELECT @ProductID = ProductID, @UOMCode = UOMCode 
			FROM s01tblProducts 
			WHERE [Cat No] = @CatNo

			IF @ProductID IS NULL
			BEGIN
				SELECT @FDACatNo = [Cat No]
				FROM s01tblBarcodeRuleProduct 
				WHERE [Barcode No] = @ScannedBarcodeValue

				SELECT @ProductID = ProductID, @UOMCode = UOMCode 
				FROM s01tblProducts 
				WHERE [Cat No] = @FDACatNo
			END

			IF @ProductID IS NOT NULL
			BEGIN
				INSERT INTO s01tblItemInventory(ProductID,Qty,UpdatedBy,LastActivityDate,LocationID,LocationTypeID)
				VALUES (@ProductId, @inputQty, @UserID, @CurrentUTCTime, @LocationID, @LocationTypeID )
			
			IF 	@FDACatNo IS NOT NULL AND @FDACatNo <> ''
				SET @CatNo = @FDACatNo
			END
			
		--END
		end
		IF @ProductID IS NULL
		BEGIN
			SELECT 'Fail' AS DataSetName, 'Product is not registered' AS MSG
			RETURN
		END
		
		SELECT @PatientName = [Patient Name] 
		FROM s01qryPatients 
		WHERE MAPatientID = @MAPatientID

		SELECT @UsageItemID = ItemInventoryID 
		FROM s01tblItemInventory 
		WHERE ProductID = @ProductID 
			AND LocationID = @LocationID 
			AND LocationTypeID = @LocationTypeID

		SELECT @CurrentPatientID = MAPatientID 
		FROM s01tblItemUsage 
		WHERE MAPatientID = @MAPatientID 
			AND UsageItemID = @UsageItemID 
			AND UsageTypeID = 2
			
		SELECT @BarcodeSettings = [dbo].[udf700WebConsoleSUPFetchSettings] ( @DeviceId,'ENABLE_ITEMIZE_BARCODE' )
		
		SELECT @Qty = Qty, @ItemInventoryID = ItemInventoryID 
		FROM s01tblItemInventory 
		WHERE ProductID = @ProductID 
			AND LocationID = @LocationID 
			AND LocationTypeID = @LocationTypeID

		SELECT @OverrideID = OverrideID 
		FROM s01tblOverride 
		WHERE OverrideDesc = CASE 
								WHEN @EventInput3 = 'Waste Item' 
									THEN 'Waste'
							    WHEN @EventInput3 = 'Override' 
									THEN 'Manual Override'
								WHEN @MAPatientID IS NOT NULL AND @EventInput3 <> 'Waste' 
									THEN 'Default'
								ELSE @EventInput3 
							 END
		
		SELECT @ReasonWindowSetting = [dbo].[udf700WebConsoleSUPFetchSettings] ( @DeviceId,'ENABLE_OVERRIDEHANDLER' )

		IF ( @EventInput4 IS NULL )
		BEGIN
			SELECT @OverrideNotes = 'DEFAULT'
		END
		ELSE IF ( @EventInput4 IS NOT NULL )
		BEGIN
			SELECT @OverrideNotes = OverrideNotes 
			FROM s01tblOverrideNotes 
			WHERE OverrideNotes = @EventInput4 
				AND OverrideID = @OverrideID
		END

		SELECT @ItemEventID = ItemEventID 
		FROM s01tblItemEvent 
		WHERE EventDescription = CASE 
									 WHEN @EventInput3 = 'Waste Item' 
										THEN 'Barcode Item Wasted'
								     WHEN @EventInput3 = 'Transfer Item' 
										THEN 'Barcode Item Transferred'
									 WHEN @EventInput3 IN ('Return Item','Remove Item', 'Swap Item', 'Override', 'Waste Item') 
										THEN 'Item Used'
									 WHEN @EventType = 'StockBarcodeInventory' 
										THEN 'Stock quantity for barcode item has been updated'
									 WHEN @EventType = 'ReconcileBarcodeInventory' 
										THEN 'Quantity for barcode item has been updated'
									 WHEN @EventType = 'DeleteBarcodeInventory' 
										THEN 'Barcode Item Deleted'
									 ELSE 'Item returned to inventory' 
								 END
					
				IF ( @EventType = 'RemoveBarcodeInventory' )
				BEGIN	
					--SELECT @DataSetName AS DataSetName, ItemInventoryID, [Ref Price], UOMCode, [Cat No], [Description 1], Qty, ProdUnitID, PCDM, LocationID ,[Barcode No]
					--FROM qry703WebConsoleProductCatalog 
					--WHERE [Cat No] = @CatNo 
					--	AND LocationTypeID = @LocationTypeID 
					--	AND LocationID = @LocationID

					--UPDATE s01tblProducts
					--SET RFID = @isImplant
					--WHERE [Cat No] = @CatNo; --Updating implant status from Detail Screen

					DECLARE @Counter INT = 1;
					DECLARE @BarCodeQty INT = 1;
					WHILE @Counter <= @inputQty
					BEGIN
						IF NOT EXISTS ( SELECT 1 
									FROM s01tblItemInventory I 
										INNER JOIN s01tblProducts P ON I.ProductID = P.ProductID 
									WHERE I.ProductID = @ProductID --AND I.LocationID = @LocationID 
									)
						BEGIN
							PRINT 'InventoryNotExists'
							INSERT INTO s01tblIteminventory( Qty, ProductID, LocationTypeID, LocationID, EntUserID, LastActivityDate, [Entered Date] ) 
							VALUES ( 1, @ProductID, @LocationTypeID, @LocationID, @UserID, @CurrentUTCTime, @CurrentUTCTime )

							--SELECT @ItemInventoryID = SCOPE_IDENTITY()
							--INSERT INTO tblUpdateBarcodeItemInventoryLog( ItemInventoryID, QtyInHand, QtyLastUpdated, QtyUsed 
							--						, QtyReturned, QtyDeleted, QtyStocked, QtyWasted, CutOffTime, UpdatedQtyInHand )
							--SELECT ItemInventoryID, Qty - 1, QtyLastUpdated, 0, 0, 0, 1, 0, @CurrentUTCTime, Qty 
							--FROM s01tblItemInventory
							--WHERE ItemInventoryID = @ItemInventoryID

							INSERT INTO s01tblItemUsage ( MAPatientID, MAVisitID, MAScheduleID, UsedBy, DateUsed, Qty, UnitPrice, UOMCode, BillingStatusID, OverrideID, OverrideNotes
												, UpdatedBy, LastActivityDate, UsageTypeID, UsageItemID, SessionID, EntryTypeID )
							SELECT @MAPatientID, @MAVisitID, @MAScheduleID, @UserID, @CurrentUtcTime, 1, [Ref Price], UOMCode, 1, @OverrideID, @OverrideNotes, @UserID
												, @CurrentUTCTime, 2, @ItemInventoryID, @SessionID, @EntryTypeID 
							FROM s01tblProducts 
							WHERE ProductID = @ProductID

							SELECT @ItemUsageID = SCOPE_IDENTITY()
							INSERT INTO s01tblItemHistory ( ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAVisitID, MAScheduleID, UsedBy, DateUsed, UnitPrice, UOMCode
												, OverrideID, OverrideNotes, SessionID, UCDM, BillingStatusID, ItemStatusID, UpdatedBy, LastActivityDate, ItemEventID, Qty, EntryTypeID ) 
							SELECT ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAVisitID, MAScheduleID, UsedBy, DateUsed, UnitPrice, UOMCode, OverrideID, @OverrideNotes, @SessionID 
												, UCDM, BillingStatusID, NULL, @UserID, @CurrentUTCTime, @ItemEventID, 1, @EntryTypeID 
							FROM s01tblItemUsage 
							WHERE ItemUsageID= @ItemUsageID

							SELECT @ItemHistoryID = SCOPE_IDENTITY()

							INSERT INTO s01tblItemHistoryExtn (ItemHistoryID, LocationID ) 
							SELECT ItemHistoryID, @LocationID 
							FROM s01tblItemHistory 
							WHERE ItemHistoryID = @ItemHistoryID

							INSERT INTO tblInventoryLog ( ItemHistoryID, InvntoryID, ProcessStatus, ClusterID, LocationID, UpdatedBy, EntryTypeID, EnteredDate )
							SELECT ItemHistoryID, UsageItemID, 'P', @DeviceID, @LocationID, @UserID, @EntryTypeID, @CurrentUTCTime FROM s01tblItemHistory WHERE ItemHistoryID = @ItemHistoryID	

						END
						ELSE IF ( @BarcodeSettings = 'TRUE')
						BEGIN
						--REPLACE( REPLACE( REPLACE ([Cat No], '-', ''), '.', ''), ' ', '' )
							IF ( SELECT 1 FROM qry703WebConsoleProductCatalog WHERE [Cat No] = @CatNo And LocationTypeID = @LocationTypeID And LocationID = @LocationID ) = 1 
							BEGIN
					
								INSERT INTO s01tblItemUsage ( MAPatientID, MAVisitID, MAScheduleID, UsedBy, DateUsed, Qty, UnitPrice, UOMCode, BillingStatusID, OverrideID, OverrideNotes
														, UpdatedBy, LastActivityDate, UsageTypeID, UsageItemID, SessionID, EntryTypeID )
								SELECT @MAPatientID, @MAVisitID, @MAScheduleID, @UserID, @CurrentUtcTime, @inputQty, [Ref Price], UOMCode, 1, @OverrideID, @OverrideNotes, @UserID
														, @CurrentUTCTime, 2, @ItemInventoryID, @SessionID, @EntryTypeID 
								FROM s01tblProducts 
								WHERE ProductID = @ProductID

								SELECT @ItemUsageID = SCOPE_IDENTITY()
							
								SELECT @UsageItemID = UsageItemID FROM s01tblItemUsage 
								WHERE ItemUsageID = @ItemUsageID

								UPDATE s01tblItemInventory 
								SET Qty = Qty - @inputQty, UpdatedBy = @UserID, QtyLastUpdated = @CurrentUTCTime 
								WHERE ItemInventoryID = @UsageItemID

								INSERT INTO s01tblUpdateBarcodeItemInventoryLog( ItemInventoryID, QtyInHand, QtyLastUpdated, QtyUsed 
														, QtyReturned, QtyDeleted, QtyStocked, QtyWasted, CutOffTime, UpdatedQtyInHand )
								SELECT ItemInventoryID, Qty + @inputQty, QtyLastUpdated, @inputQty, 0, 0, 0, 0, @CurrentUTCTime, Qty 
								FROM s01tblItemInventory
								WHERE ItemInventoryID = @UsageItemID

								INSERT INTO s01tblItemHistory ( ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAVisitID, MAScheduleID, UsedBy, DateUsed, UnitPrice, UOMCode
													, OverrideID, OverrideNotes, SessionID, UCDM, BillingStatusID, ItemStatusID, UpdatedBy, LastActivityDate, ItemEventID, Qty, EntryTypeID ) 
								SELECT ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAVisitID, MAScheduleID, UsedBy, DateUsed, UnitPrice, UOMCode, @OverrideID, @OverrideNotes, @SessionID 
													, UCDM, BillingStatusID, NULL, @UserID, @CurrentUTCTime, @ItemEventID, @inputQty, @EntryTypeID 
								FROM s01tblItemUsage 
								WHERE ItemUsageID = @ItemUsageID

								SELECT @ItemHistoryID = SCOPE_IDENTITY()
								INSERT INTO s01tblItemHistoryExtn (ItemHistoryID, LocationID ) 
								SELECT ItemHistoryID, @LocationID
								FROM s01tblItemHistory 
								WHERE ItemHistoryID = @ItemHistoryID

								INSERT INTO tblInventoryLog ( ItemHistoryID, InvntoryID, ProcessStatus, ClusterID, LocationID, UpdatedBy, EntryTypeID, EnteredDate )
								SELECT ItemHistoryID, UsageItemID, 'P', @DeviceID, @LocationID, @UserID, @EntryTypeID, @CurrentUTCTime 
								FROM s01tblItemHistory 
								WHERE ItemHistoryID = @ItemHistoryID	
							END
						END
						ELSE IF ( @BarcodeSettings = 'FALSE')
						BEGIN
							IF ( @CurrentPatientID = @MAPatientID ) AND ( @MAPatientID IS NOT NULL )
							BEGIN
						
								IF EXISTS ( SELECT 1 
											FROM s01tblItemInventory I 
												INNER JOIN s01tblProducts P ON I.ProductID = P.ProductID 
												INNER JOIN s01tblItemUsage U ON I.ItemInventoryID = U.UsageItemID
											WHERE I.ProductID = @ProductID 
												AND I.LocationID = @LocationID 
												AND U.SessionID = @SessionID 
												AND U.MAPatientID = @MAPatientID)
								BEGIN
							
									SELECT @QTY = U.Qty+@BarCodeQty, @ItemInventoryID = I.ItemInventoryID  
									FROM s01tblItemUsage U 
										INNER JOIN s01tblItemInventory I ON I.ItemInventoryID = U.UsageItemID 
									WHERE U.SessionID = @SessionID 
										AND I.ProductID = @ProductID 
										AND MAPatientID = @MAPatientID
								
									UPDATE s01tblItemUsage 
									SET Qty = @QTY, UpdatedBy = @UserID, LastActivityDate = @CurrentUTCTime, OverrideID = @OverrideID, OverrideNotes = @OverrideNotes
									WHERE UsageItemID = @ItemInventoryID 
										AND SessionID = @SessionID	
										AND MAPatientID = @MAPatientID 
										AND UsageTypeID = 2
							
									UPDATE s01tblItemInventory 
									SET Qty = Qty - @BarCodeQty, UpdatedBy = @UserID, QtyLastUpdated = @CurrentUTCTime 
									WHERE ItemInventoryID = @ItemInventoryID 

									INSERT INTO s01tblUpdateBarcodeItemInventoryLog( ItemInventoryID, QtyInHand, QtyLastUpdated, QtyUsed 
															, QtyReturned, QtyDeleted, QtyStocked, QtyWasted, CutOffTime, UpdatedQtyInHand )
									SELECT ItemInventoryID, Qty + @BarCodeQty, QtyLastUpdated, @BarCodeQty, 0, 0, 0, 0, @CurrentUTCTime, Qty 
									FROM s01tblItemInventory
									WHERE ItemInventoryID = @ItemInventoryID

									SELECT @ItemUsageID = ItemUsageID 
									FROM s01tblItemUsage 
									WHERE UsageItemID = @ItemInventoryID 
										AND SessionID = @SessionID

									INSERT INTO s01tblItemHistory ( ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAVisitID, MAScheduleID, UsedBy, DateUsed, UnitPrice, UOMCode
														, OverrideID, OverrideNotes, SessionID, UCDM, BillingStatusID, ItemStatusID, UpdatedBy, LastActivityDate, ItemEventID, Qty, EntryTypeID ) 
									SELECT ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAVisitID, MAScheduleID, UsedBy, DateUsed, UnitPrice, UOMCode, @OverrideID, @OverrideNotes, @SessionID 
														, UCDM, BillingStatusID, NULL, @UserID, @CurrentUTCTime, @ItemEventID, @BarCodeQty, @EntryTypeID 
									FROM s01tblItemUsage 
									WHERE ItemUsageID = @ItemUsageID
					
									SELECT @ItemHistoryID = SCOPE_IDENTITY()
									INSERT INTO s01tblItemHistoryExtn (ItemHistoryID, LocationID ) 
									SELECT ItemHistoryID, @LocationID 
									FROM s01tblItemHistory 
									WHERE ItemHistoryID = @ItemHistoryID

									INSERT INTO tblInventoryLog ( ItemHistoryID, InvntoryID, ProcessStatus, ClusterID, LocationID, UpdatedBy, EntryTypeID, EnteredDate )
									SELECT ItemHistoryID, UsageItemID, 'P', @DeviceID, @LocationID, @UserID, @EntryTypeID, @CurrentUTCTime 
									FROM s01tblItemHistory 
									WHERE ItemHistoryID = @ItemHistoryID	
								END
								IF NOT EXISTS ( SELECT 1 
												FROM s01tblItemInventory I 
													INNER JOIN s01tblProducts P ON I.ProductID = P.ProductID 
													INNER JOIN s01tblItemUsage U ON I.ItemInventoryID = U.UsageItemID
												WHERE I.ProductID = @ProductID 
													AND I.LocationID = @LocationID 
													AND U.SessionID = @SessionID 
													AND U.MAPatientid = @MAPatientID ) 				
								BEGIN

									IF  EXISTS ( 
													 SELECT 1 
													 FROM s01tblItemInventory I 
														INNER JOIN s01tblProducts P ON I.ProductID = P.ProductID 
														INNER JOIN s01tblItemUsage U ON I.ItemInventoryID = U.UsageItemID
													 WHERE I.ProductID = @ProductID 
														AND I.LocationID = @LocationID 
														AND U.MAPatientID = @MAPatientID
														AND U.MAScheduleID = @MAScheduleID
												 )
									BEGIN

										SELECT @QTY = U.Qty+@BarCodeQty, @ItemInventoryID = I.ItemInventoryID  
										FROM s01tblItemUsage U 
											INNER JOIN s01tblItemInventory I ON I.ItemInventoryID = U.UsageItemID 
										WHERE I.ProductID = @ProductID 
											AND MAPatientID = @MAPatientID
											AND U.MAScheduleID = @MAScheduleID

										UPDATE s01tblItemUsage 
										SET Qty = @Qty, UpdatedBy = @UserID, LastActivityDate = @CurrentUTCTime, OverrideID = @OverrideID, OverrideNotes = @OverrideNotes
										WHERE UsageItemID = @ItemInventoryID 
											AND MAPatientID = @MAPatientID 
											AND UsageTypeID = 2

											

									--SELECT @ItemUsageID = SCOPE_IDENTITY()
									SELECT @ItemUsageID = ItemUsageID
									FROM s01tblItemUsage 
									WHERE  UsageItemID = @ItemInventoryID 
											AND MAPatientID = @MAPatientID 
											AND UsageTypeID = 2
									SELECT @UsageItemID = @ItemInventoryID
									--FROM s01tblItemUsage 
									--WHERE ItemUsageID = @ItemUsageID

									UPDATE s01tblItemInventory 
									SET Qty = Qty - @BarCodeQty, UpdatedBy = @UserID, QtyLastUpdated = @CurrentUTCTime 
									WHERE ItemInventoryID = @UsageItemID 
								
									INSERT INTO s01tblUpdateBarcodeItemInventoryLog( ItemInventoryID, QtyInHand, QtyLastUpdated, QtyUsed 
															, QtyReturned, QtyDeleted, QtyStocked, QtyWasted, CutOffTime, UpdatedQtyInHand )
									SELECT ItemInventoryID, Qty + @BarCodeQty, QtyLastUpdated, @BarCodeQty, 0, 0, 0, 0, @CurrentUTCTime, Qty 
									FROM s01tblItemInventory
									WHERE ItemInventoryID = @UsageItemID

									INSERT INTO s01tblItemHistory ( ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAVisitID, MAScheduleID, UsedBy, DateUsed, UnitPrice, UOMCode
														, OverrideID, OverrideNotes, SessionID, UCDM, BillingStatusID, ItemStatusID, UpdatedBy, LastActivityDate, ItemEventID, Qty, EntryTypeID ) 
									SELECT ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAVisitID, MAScheduleID, UsedBy, DateUsed, UnitPrice, UOMCode, @OverrideID, @OverrideNotes, @SessionID 
														, UCDM, BillingStatusID, NULL, @UserID, @CurrentUTCTime, @ItemEventID, @BarCodeQty, @EntryTypeID 
									FROM s01tblItemUsage 
									WHERE ItemUsageID = @ItemUsageID

									SELECT @ItemHistoryID = SCOPE_IDENTITY()
									INSERT INTO s01tblItemHistoryExtn (ItemHistoryID, LocationID ) 
									SELECT ItemHistoryID, @LocationID 
									FROM s01tblItemHistory 
									WHERE ItemHistoryID = @ItemHistoryID

									INSERT INTO tblInventoryLog ( ItemHistoryID, InvntoryID, ProcessStatus, ClusterID, LocationID, UpdatedBy, EntryTypeID, EnteredDate )
									SELECT ItemHistoryID, UsageItemID, 'P', @DeviceID, @LocationID, @UserID, @EntryTypeID, @CurrentUTCTime 
									FROM s01tblItemHistory 
									WHERE ItemHistoryID = @ItemHistoryID

									END
									ELSE
									BEGIN
										INSERT INTO s01tblItemUsage ( MAPatientID, MAVisitID, MAScheduleID, UsedBy, DateUsed, Qty, UnitPrice, UOMCode, BillingStatusID, OverrideID, OverrideNotes
																, UpdatedBy, LastActivityDate, UsageTypeID, UsageItemID, SessionID, EntryTypeID )
										SELECT @MAPatientID, @MAVisitID, @MAScheduleID, @UserID, @CurrentUtcTime, @BarCodeQty, [Ref Price], UOMCode, 1, @OverrideID, @OverrideNotes, @UserID
																, @CurrentUTCTime, 2, @ItemInventoryID, @SessionID, @EntryTypeID 
										FROM s01tblProducts 
										WHERE ProductID = @ProductID

								

									SELECT @ItemUsageID = SCOPE_IDENTITY()
									SELECT @UsageItemID = UsageItemID 
									FROM s01tblItemUsage 
									WHERE ItemUsageID = @ItemUsageID

									UPDATE s01tblItemInventory 
									SET Qty = Qty - @BarCodeQty, UpdatedBy = @UserID, QtyLastUpdated = @CurrentUTCTime 
									WHERE ItemInventoryID = @UsageItemID 
								
									INSERT INTO s01tblUpdateBarcodeItemInventoryLog( ItemInventoryID, QtyInHand, QtyLastUpdated, QtyUsed 
															, QtyReturned, QtyDeleted, QtyStocked, QtyWasted, CutOffTime, UpdatedQtyInHand )
									SELECT ItemInventoryID, Qty + @BarCodeQty, QtyLastUpdated, @BarCodeQty, 0, 0, 0, 0, @CurrentUTCTime, Qty 
									FROM s01tblItemInventory
									WHERE ItemInventoryID = @UsageItemID

									INSERT INTO s01tblItemHistory ( ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAVisitID, MAScheduleID, UsedBy, DateUsed, UnitPrice, UOMCode
														, OverrideID, OverrideNotes, SessionID, UCDM, BillingStatusID, ItemStatusID, UpdatedBy, LastActivityDate, ItemEventID, Qty, EntryTypeID ) 
									SELECT ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAVisitID, MAScheduleID, UsedBy, DateUsed, UnitPrice, UOMCode, @OverrideID, @OverrideNotes, @SessionID 
														, UCDM, BillingStatusID, NULL, @UserID, @CurrentUTCTime, @ItemEventID, @BarCodeQty, @EntryTypeID 
									FROM s01tblItemUsage 
									WHERE ItemUsageID = @ItemUsageID

									SELECT @ItemHistoryID = SCOPE_IDENTITY()
									INSERT INTO s01tblItemHistoryExtn (ItemHistoryID, LocationID ) 
									SELECT ItemHistoryID, @LocationID 
									FROM s01tblItemHistory 
									WHERE ItemHistoryID = @ItemHistoryID

									INSERT INTO tblInventoryLog ( ItemHistoryID, InvntoryID, ProcessStatus, ClusterID, LocationID, UpdatedBy, EntryTypeID, EnteredDate )
									SELECT ItemHistoryID, UsageItemID, 'P', @DeviceID, @LocationID, @UserID, @EntryTypeID, @CurrentUTCTime 
									FROM s01tblItemHistory 
									WHERE ItemHistoryID = @ItemHistoryID
									END
								END
							END
							IF ( @CurrentPatientID IS NULL AND @MAPatientID IS NOT NULL )
							BEGIN

									INSERT INTO s01tblItemUsage ( MAPatientID, MAVisitID, MAScheduleID, UsedBy, DateUsed, Qty, UnitPrice, UOMCode, BillingStatusID, OverrideID, OverrideNotes
															, UpdatedBy, LastActivityDate, UsageTypeID, UsageItemID, SessionID, EntryTypeID )
									SELECT @MAPatientID, @MAVisitID, @MAScheduleID, @UserID, @CurrentUtcTime, @BarCodeQty, [Ref Price], UOMCode, 1, @OverrideID, @OverrideNotes, @UserID
															, @CurrentUTCTime, 2, @ItemInventoryID, @SessionID, @EntryTypeID 
									FROM s01tblProducts 
									WHERE ProductID = @ProductID

									SELECT @ItemUsageID = SCOPE_IDENTITY()
									SELECT @UsageItemID = UsageItemID 
									FROM s01tblItemUsage 
									WHERE ItemUsageID = @ItemUsageID
							
									UPDATE s01tblItemInventory 
									SET Qty = Qty - @BarCodeQty, UpdatedBy = @UserID, QtyLastUpdated = @CurrentUTCTime 
									WHERE ItemInventoryID = @UsageItemID 

									INSERT INTO s01tblUpdateBarcodeItemInventoryLog( ItemInventoryID, QtyInHand, QtyLastUpdated, QtyUsed 
															, QtyReturned, QtyDeleted, QtyStocked, QtyWasted, CutOffTime, UpdatedQtyInHand )
									SELECT ItemInventoryID, Qty + @BarCodeQty, QtyLastUpdated, @BarCodeQty, 0, 0, 0, 0, @CurrentUTCTime, Qty 
									FROM s01tblItemInventory
									WHERE ItemInventoryID = @UsageItemID

									INSERT INTO s01tblItemHistory ( ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAVisitID, MAScheduleID, UsedBy, DateUsed, UnitPrice, UOMCode
														, OverrideID, OverrideNotes, SessionID, UCDM, BillingStatusID, ItemStatusID, UpdatedBy, LastActivityDate, ItemEventID, Qty, EntryTypeID ) 
									SELECT ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAVisitID, MAScheduleID, UsedBy, DateUsed, UnitPrice, UOMCode, @OverrideID, @OverrideNotes, @SessionID 
														, UCDM, BillingStatusID, NULL, @UserID, @CurrentUTCTime, @ItemEventID, @BarCodeQty, @EntryTypeID 
									FROM s01tblItemUsage 
									WHERE ItemUsageID = @ItemUsageID

									SELECT @ItemHistoryID = SCOPE_IDENTITY()
									INSERT INTO s01tblItemHistoryExtn (ItemHistoryID, LocationID ) 
									SELECT ItemHistoryID, @LocationID 
									FROM s01tblItemHistory 
									WHERE ItemHistoryID = @ItemHistoryID

									INSERT INTO tblInventoryLog ( ItemHistoryID, InvntoryID, ProcessStatus, ClusterID, LocationID, UpdatedBy, EntryTypeID, EnteredDate )
									SELECT ItemHistoryID, UsageItemID, 'P', @DeviceID, @LocationID, @UserID, @EntryTypeID, @CurrentUTCTime 
									FROM s01tblItemHistory 
									WHERE ItemHistoryID = @ItemHistoryID	
								--END
							END

							IF ( @MAPatientID IS NULL )
							BEGIN
								PRINT 'FALSE'
								IF EXISTS ( SELECT 1 
											FROM s01tblItemInventory I 
												INNER JOIN s01tblProducts P ON I.ProductID = P.ProductID 
												INNER JOIN s01tblItemUsage U ON I.ItemInventoryID = U.UsageItemID
											WHERE I.ProductID = @ProductID 
												AND I.LocationID = @LocationID 
												AND U.SessionID = @SessionID 
												AND MAPatientID IS NULL )
								BEGIN
									SELECT @QTY = U.Qty+1, @ItemInventoryID = I.ItemInventoryID  
									FROM s01tblItemUsage U 
										INNER JOIN s01tblItemInventory I ON I.ItemInventoryID = U.UsageItemID 
									WHERE U.SessionID = @SessionID 
										AND I.ProductID = @ProductID

									UPDATE s01tblItemUsage 
									SET Qty = @Qty, UpdatedBy = @UserID, LastActivityDate = @CurrentUTCTime, OverrideID = @OverrideID, OverrideNotes = @OverrideNotes
									WHERE UsageItemID = @ItemInventoryID AND SessionID = @SessionID	AND MAPatientID IS NULL 
							
									UPDATE s01tblItemInventory 
									SET Qty = Qty - @BarCodeQty, UpdatedBy = @UserID, QtyLastUpdated = @CurrentUTCTime 
									WHERE ItemInventoryID = @ItemInventoryID 

									INSERT INTO s01tblUpdateBarcodeItemInventoryLog( ItemInventoryID, QtyInHand, QtyLastUpdated, QtyUsed 
															, QtyReturned, QtyDeleted, QtyStocked, QtyWasted, CutOffTime, UpdatedQtyInHand )
									SELECT ItemInventoryID, Qty + @BarCodeQty, QtyLastUpdated, @BarCodeQty, 0, 0, 0, 0, @CurrentUTCTime, Qty 
									FROM s01tblItemInventory
									WHERE ItemInventoryID = @ItemInventoryID

									SELECT @ItemUsageID = ItemUsageID 
									FROM s01tblItemUsage 
									WHERE UsageItemID = @ItemInventoryID AND SessionID = @SessionID

									INSERT INTO s01tblItemHistory ( ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAVisitID, MAScheduleID, UsedBy, DateUsed, UnitPrice, UOMCode
														, OverrideID, OverrideNotes, SessionID, UCDM, BillingStatusID, ItemStatusID, UpdatedBy, LastActivityDate, ItemEventID, Qty, EntryTypeID ) 
									SELECT ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAVisitID, MAScheduleID, UsedBy, DateUsed, UnitPrice, UOMCode, @OverrideID, @OverrideNotes, @SessionID 
														, UCDM, BillingStatusID, NULL, @UserID, @CurrentUTCTime, @ItemEventID, @BarCodeQty, @EntryTypeID 
									FROM s01tblItemUsage 
									WHERE ItemUsageID = @ItemUsageID
					
									SELECT @ItemHistoryID = SCOPE_IDENTITY()
									INSERT INTO s01tblItemHistoryExtn (ItemHistoryID, LocationID ) 
									SELECT ItemHistoryID, @LocationID 
									FROM s01tblItemHistory 
									WHERE ItemHistoryID = @ItemHistoryID

									INSERT INTO tblInventoryLog ( ItemHistoryID, InvntoryID, ProcessStatus, ClusterID, LocationID, UpdatedBy, EntryTypeID, EnteredDate )
									SELECT ItemHistoryID, UsageItemID, 'P', @DeviceID, @LocationID, @UserID, @EntryTypeID, @CurrentUTCTime 
									FROM s01tblItemHistory 
									WHERE ItemHistoryID = @ItemHistoryID	
								END
								IF NOT EXISTS ( SELECT 1 
												FROM s01tblItemInventory I 
													INNER JOIN s01tblProducts P ON I.ProductID = P.ProductID 
													INNER JOIN s01tblItemUsage U ON I.ItemInventoryID = U.UsageItemID
												WHERE I.ProductID = @ProductID 
													AND I.LocationID = @LocationID 
													AND U.SessionID = @SessionID 
													AND MAPatientID IS NULL ) 				
								BEGIN
									INSERT INTO s01tblItemUsage ( MAPatientID, MAVisitID, MAScheduleID, UsedBy, DateUsed, Qty, UnitPrice, UOMCode, BillingStatusID, OverrideID, OverrideNotes
															, UpdatedBy, LastActivityDate, UsageTypeID, UsageItemID, SessionID, EntryTypeID )
									SELECT @MAPatientID, @MAVisitID, @MAScheduleID, @UserID, @CurrentUtcTime, 1, [Ref Price], UOMCode, 1, @OverrideID, @OverrideNotes, @UserID
															, @CurrentUTCTime, 2, @ItemInventoryID, @SessionID, @EntryTypeID 
									FROM s01tblProducts 
									WHERE ProductID = @ProductID

									SELECT @ItemUsageID = SCOPE_IDENTITY()

									SELECT @UsageItemID = UsageItemID 
									FROM s01tblItemUsage 
									WHERE ItemUsageID = @ItemUsageID

									UPDATE s01tblItemInventory 
									SET Qty = Qty - 1, UpdatedBy = @UserID, QtyLastUpdated = @CurrentUTCTime 
									WHERE ItemInventoryID = @UsageItemID 

									INSERT INTO s01tblUpdateBarcodeItemInventoryLog( ItemInventoryID, QtyInHand, QtyLastUpdated, QtyUsed 
															, QtyReturned, QtyDeleted, QtyStocked, QtyWasted, CutOffTime, UpdatedQtyInHand )
									SELECT ItemInventoryID, Qty + 1, QtyLastUpdated, 1, 0, 0, 0, 0, @CurrentUTCTime, Qty 
									FROM s01tblItemInventory
									WHERE ItemInventoryID = @UsageItemID

									INSERT INTO s01tblItemHistory ( ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAVisitID, MAScheduleID, UsedBy, DateUsed, UnitPrice, UOMCode
														, OverrideID, OverrideNotes, SessionID, UCDM, BillingStatusID, ItemStatusID, UpdatedBy, LastActivityDate, ItemEventID, Qty, EntryTypeID ) 
									SELECT ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAVisitID, MAScheduleID, UsedBy, DateUsed, UnitPrice, UOMCode, @OverrideID, @OverrideNotes, @SessionID 
														, UCDM, BillingStatusID, NULL, @UserID, @CurrentUTCTime, @ItemEventID, 1, @EntryTypeID 
									FROM s01tblItemUsage 
									WHERE ItemUsageID = @ItemUsageID

									SELECT @ItemHistoryID = SCOPE_IDENTITY()
									INSERT INTO s01tblItemHistoryExtn (ItemHistoryID, LocationID ) 
									SELECT ItemHistoryID, @LocationID 
									FROM s01tblItemHistory 
									WHERE ItemHistoryID = @ItemHistoryID

									INSERT INTO tblInventoryLog ( ItemHistoryID, InvntoryID, ProcessStatus, ClusterID, LocationID, UpdatedBy, EntryTypeID, EnteredDate )
									SELECT ItemHistoryID, UsageItemID, 'P', @DeviceID, @LocationID, @UserID, @EntryTypeID, @CurrentUTCTime 
									FROM s01tblItemHistory 
									WHERE ItemHistoryID = @ItemHistoryID	
								END
							END
						END
						PRINT 'Executing task iteration ' + CAST(@Counter AS VARCHAR(10));

						-- Increment the counter
						SET @Counter = @Counter + 1;
					END
					SELECT @DataSetName AS DataSetName, ItemInventoryID, [Ref Price], UOMCode, [Cat No], [Description 1], Qty, ProdUnitID, PCDM, LocationID ,[Barcode No]
					FROM qry703WebConsoleProductCatalog 
					WHERE [Cat No] = @CatNo 
						AND LocationTypeID = @LocationTypeID 
						AND LocationID = @LocationID
				END

				IF ( @EventType = 'ReturnBarcodeInventory' )
				BEGIN

					--PRINT 'Barcode Return'

					if exists (select top 1 * from s01tblitemusage where usageitemid=@UsageItemID)
					begin
						set @IsUsageExists='Available';
					end
	
					IF @IsUsageExists IS NULL
					BEGIN
						SELECT 'Fail' AS DataSetName, 'Item already stocked in cabinet.' AS MSG
					RETURN
					END

					EXECUTE  usp827WebConsoleReturnBarcodeItems @Event = @Event
							, @udtSystemData = @udtSystemData
							, @udtEventData = @udtEventData
							, @ProductID = @ProductID
							, @ItemEventID = @ItemEventID
							, @OverrideID = @OverrideID
		
				END
				

			--COMMIT TRANSACTION	
	END TRY
	BEGIN CATCH
		--IF XACT_STATE() <> 0
			--ROLLBACK TRANSACTION
			SET @Msg = 'Error in updating Barcode Inventory';
		EXECUTE s01uspLogError
	END CATCH
	SET NOCOUNT OFF
GO

