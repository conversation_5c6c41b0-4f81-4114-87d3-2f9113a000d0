/**
 * Copyright (c) Tiny Technologies, Inc. All rights reserved.
 * Licensed under the LGPL or a commercial license.
 * For LGPL see License.txt in the project root for license information.
 * For commercial licenses see https://www.tiny.cloud/
 *
 * Version: 5.3.2 (2020-06-10)
 */
!function(y){"use strict";var e,t,n,r,p=function(e){var t=e;return{get:function(){return t},set:function(e){t=e}}},c=tinymce.util.Tools.resolve("tinymce.PluginManager"),i=function(e,t){return e.fire("PastePlainTextToggle",{state:t})},f=function(n,r){n.addCommand("mceTogglePlainTextPaste",function(){var e,t;e=n,"text"===(t=r).pasteFormat.get()?(t.pasteFormat.set("html"),i(e,!1)):(t.pasteFormat.set("text"),i(e,!0)),e.focus()}),n.addCommand("mceInsertClipboardContent",function(e,t){t.content&&r.pasteHtml(t.content,t.internal),t.text&&r.pasteText(t.text)})},o=function(e){return function(){return e}},a=o(!1),s=o(!0),u=function(){return l},l=(e=function(e){return e.isNone()},{fold:function(e,t){return e()},is:a,isSome:a,isNone:s,getOr:n=function(e){return e},getOrThunk:t=function(e){return e()},getOrDie:function(e){throw new Error(e||"error: getOrDie called on none.")},getOrNull:o(null),getOrUndefined:o(undefined),or:n,orThunk:t,map:u,each:function(){},bind:u,exists:a,forall:s,filter:u,equals:e,equals_:e,toArray:function(){return[]},toString:o("none()")}),d=function(n){var e=o(n),t=function(){return i},r=function(e){return e(n)},i={fold:function(e,t){return t(n)},is:function(e){return n===e},isSome:s,isNone:a,getOr:e,getOrThunk:e,getOrDie:e,getOrNull:e,getOrUndefined:e,or:t,orThunk:t,map:function(e){return d(e(n))},each:function(e){e(n)},bind:r,exists:r,forall:r,filter:function(e){return e(n)?i:l},toArray:function(){return[n]},toString:function(){return"some("+n+")"},equals:function(e){return e.is(n)},equals_:function(e,t){return e.fold(a,function(e){return t(n,e)})}};return i},g={some:d,none:u,from:function(e){return null===e||e===undefined?l:d(e)}},m=(r="function",function(e){return typeof e===r}),v=Array.prototype.slice,h=function(e,t){for(var n=e.length,r=new Array(n),i=0;i<n;i++){var o=e[i];r[i]=t(o,i)}return r},b=function(e,t){for(var n=0,r=e.length;n<r;n++){t(e[n],n)}},x=m(Array.from)?Array.from:function(e){return v.call(e)},w=tinymce.util.Tools.resolve("tinymce.Env"),P=tinymce.util.Tools.resolve("tinymce.util.Delay"),_=tinymce.util.Tools.resolve("tinymce.util.Promise"),T=tinymce.util.Tools.resolve("tinymce.util.Tools"),C=tinymce.util.Tools.resolve("tinymce.util.VK"),D="x-tinymce/html",k="\x3c!-- "+D+" --\x3e",S=function(e){return-1!==e.indexOf(k)},O=tinymce.util.Tools.resolve("tinymce.html.Entities"),R=function(e,t,n){var r=e.split(/\n\n/),i=function(e,t){var n,r=[],i="<"+e;if("object"==typeof t){for(n in t)t.hasOwnProperty(n)&&r.push(n+'="'+O.encodeAllRaw(t[n])+'"');r.length&&(i+=" "+r.join(" "))}return i+">"}(t,n),o="</"+t+">",a=T.map(r,function(e){return e.split(/\n/).join("<br />")});return 1===a.length?a[0]:T.map(a,function(e){return i+e+o}).join("")},A=tinymce.util.Tools.resolve("tinymce.html.DomParser"),I=tinymce.util.Tools.resolve("tinymce.html.Serializer"),E=tinymce.util.Tools.resolve("tinymce.html.Node"),F=tinymce.util.Tools.resolve("tinymce.html.Schema"),M=function(e){return e.getParam("paste_data_images",!1)},N=function(e){return e.getParam("paste_retain_style_properties")},B="\xa0";function $(t,e){return T.each(e,function(e){t=e.constructor===RegExp?t.replace(e,""):t.replace(e[0],e[1])}),t}function H(e){var t=F(),n=A({},t),r="",i=t.getShortEndedElements(),o=T.makeMap("script noscript style textarea video audio iframe object"," "),a=t.getBlockElements();return e=$(e,[/<!\[[^\]]+\]>/g]),function s(e){var t=e.name,n=e;if("br"!==t){if("wbr"!==t)if(i[t]&&(r+=" "),o[t])r+=" ";else{if(3===e.type&&(r+=e.value),!e.shortEnded&&(e=e.firstChild))for(;s(e),e=e.next;);a[t]&&n.next&&(r+="\n","p"===t&&(r+="\n"))}}else r+="\n"}(n.parse(e)),r}function j(e){return e=$(e,[/^[\s\S]*<body[^>]*>\s*|\s*<\/body[^>]*>[\s\S]*$/gi,/<!--StartFragment-->|<!--EndFragment-->/g,[/( ?)<span class="Apple-converted-space">\u00a0<\/span>( ?)/g,function r(e,t,n){return t||n?B:" "}],/<br class="Apple-interchange-newline">/g,/<br>$/i])}var L=function(){return-1!==y.navigator.userAgent.indexOf(" Edge/")};function z(e){return/<font face="Times New Roman"|class="?Mso|style="[^"]*\bmso-|style='[^'']*\bmso-|w:WordDocument/i.test(e)||/class="OutlineElement/.test(e)||/id="?docs\-internal\-guid\-/.test(e)}function U(t){var n,e;return e=[/^[IVXLMCD]{1,2}\.[ \u00a0]/,/^[ivxlmcd]{1,2}\.[ \u00a0]/,/^[a-z]{1,2}[\.\)][ \u00a0]/,/^[A-Z]{1,2}[\.\)][ \u00a0]/,/^[0-9]+\.[ \u00a0]/,/^[\u3007\u4e00\u4e8c\u4e09\u56db\u4e94\u516d\u4e03\u516b\u4e5d]+\.[ \u00a0]/,/^[\u58f1\u5f10\u53c2\u56db\u4f0d\u516d\u4e03\u516b\u4e5d\u62fe]+\.[ \u00a0]/],t=t.replace(/^[\u00a0 ]+/,""),T.each(e,function(e){if(e.test(t))return!(n=!0)}),n}function q(e){var o,a,s=1;function n(e){var t="";if(3===e.type)return e.value;if(e=e.firstChild)for(;t+=n(e),e=e.next;);return t}function u(e,t){if(3!==e.type||!t.test(e.value)){if(e=e.firstChild)do{if(!u(e,t))return}while(e=e.next);return 1}e.value=e.value.replace(t,"")}function t(e,t,n){var r=e._listLevel||s;r!==s&&(o=r<s?o&&o.parent.parent:(a=o,null)),o&&o.name===t?o.append(e):(a=a||o,o=new E(t,1),1<n&&o.attr("start",""+n),e.wrap(o)),e.name="li",s<r&&a&&a.lastChild.append(o),s=r,function i(e){if(e._listIgnore)e.remove();else if(e=e.firstChild)for(;i(e),e=e.next;);}(e),u(e,/^\u00a0+/),u(e,/^\s*([\u2022\u00b7\u00a7\u25CF]|\w+\.)/),u(e,/^\u00a0+/)}for(var r=[],i=e.firstChild;null!=i;)if(r.push(i),null!==(i=i.walk()))for(;void 0!==i&&i.parent!==e;)i=i.walk();for(var l=0;l<r.length;l++)if("p"===(e=r[l]).name&&e.firstChild){var c=n(e);if(/^[\s\u00a0]*[\u2022\u00b7\u00a7\u25CF]\s*/.test(c)){t(e,"ul");continue}if(U(c)){var f=/([0-9]+)\./.exec(c),d=1;f&&(d=parseInt(f[1],10)),t(e,"ol",d);continue}if(e._listLevel){t(e,"ul",1);continue}o=null}else a=o,o=null}function V(n,r,i,o){var a,s={},e=n.dom.parseStyle(o);return T.each(e,function(e,t){switch(t){case"mso-list":(a=/\w+ \w+([0-9]+)/i.exec(o))&&(i._listLevel=parseInt(a[1],10)),/Ignore/i.test(e)&&i.firstChild&&(i._listIgnore=!0,i.firstChild._listIgnore=!0);break;case"horiz-align":t="text-align";break;case"vert-align":t="vertical-align";break;case"font-color":case"mso-foreground":t="color";break;case"mso-background":case"mso-highlight":t="background";break;case"font-weight":case"font-style":return void("normal"!==e&&(s[t]=e));case"mso-element":if(/^(comment|comment-list)$/i.test(e))return void i.remove()}0!==t.indexOf("mso-comment")?0!==t.indexOf("mso-")&&("all"===N(n)||r&&r[t])&&(s[t]=e):i.remove()}),/(bold)/i.test(s["font-weight"])&&(delete s["font-weight"],i.wrap(new E("b",1))),/(italic)/i.test(s["font-style"])&&(delete s["font-style"],i.wrap(new E("i",1))),(s=n.dom.serializeStyle(s,i.name))||null}var K=function(e,t){return e.getParam("paste_enable_default_filters",!0)?function(r,e){var t,i;(t=N(r))&&(i=T.makeMap(t.split(/[, ]/))),e=$(e,[/<br class="?Apple-interchange-newline"?>/gi,/<b[^>]+id="?docs-internal-[^>]*>/gi,/<!--[\s\S]+?-->/gi,/<(!|script[^>]*>.*?<\/script(?=[>\s])|\/?(\?xml(:\w+)?|img|meta|link|style|\w:\w+)(?=[\s\/>]))[^>]*>/gi,[/<(\/?)s>/gi,"<$1strike>"],[/&nbsp;/gi,B],[/<span\s+style\s*=\s*"\s*mso-spacerun\s*:\s*yes\s*;?\s*"\s*>([\s\u00a0]*)<\/span>/gi,function(e,t){return 0<t.length?t.replace(/./," ").slice(Math.floor(t.length/2)).split("").join(B):""}]]);var n=r.getParam("paste_word_valid_elements","-strong/b,-em/i,-u,-span,-p,-ol,-ul,-li,-h1,-h2,-h3,-h4,-h5,-h6,-p/div,-a[href|name],sub,sup,strike,br,del,table[width],tr,td[colspan|rowspan|width],th[colspan|rowspan|width],thead,tfoot,tbody"),o=F({valid_elements:n,valid_children:"-li[p]"});T.each(o.elements,function(e){e.attributes["class"]||(e.attributes["class"]={},e.attributesOrder.push("class")),e.attributes.style||(e.attributes.style={},e.attributesOrder.push("style"))});var a=A({},o);a.addAttributeFilter("style",function(e){for(var t,n=e.length;n--;)(t=e[n]).attr("style",V(r,i,t,t.attr("style"))),"span"===t.name&&t.parent&&!t.attributes.length&&t.unwrap()}),a.addAttributeFilter("class",function(e){for(var t,n,r=e.length;r--;)n=(t=e[r]).attr("class"),/^(MsoCommentReference|MsoCommentText|msoDel)$/i.test(n)&&t.remove(),t.attr("class",null)}),a.addNodeFilter("del",function(e){for(var t=e.length;t--;)e[t].remove()}),a.addNodeFilter("a",function(e){for(var t,n,r,i=e.length;i--;)if(n=(t=e[i]).attr("href"),r=t.attr("name"),n&&-1!==n.indexOf("#_msocom_"))t.remove();else if(n&&0===n.indexOf("file://")&&(n=(n=n.split("#")[1])&&"#"+n),n||r){if(r&&!/^_?(?:toc|edn|ftn)/i.test(r)){t.unwrap();continue}t.attr({href:n,name:r})}else t.unwrap()});var s=a.parse(e);return r.getParam("paste_convert_word_fake_lists",!0)&&q(s),e=I({validate:r.settings.validate},o).serialize(s)}(e,t):t},X=function(e,t){return{content:e,cancelled:t}},W=function(e,t,n,r){var i,o,a,s,u,l,c,f,d,m,p,g,v=(i=t,o=n,a=r,e.fire("PastePreProcess",{content:i,internal:o,wordContent:a})),h=function(e,t){var n=A({},e.schema);n.addNodeFilter("meta",function(e){T.each(e,function(e){return e.remove()})});var r=n.parse(t,{forced_root_block:!1,isRootContent:!0});return I({validate:e.settings.validate},e.schema).serialize(r)}(e,v.content);return e.hasEventListeners("PastePostProcess")&&!v.isDefaultPrevented()?(u=h,l=n,c=r,p=(s=e).dom.create("div",{style:"display:none"},u),f=p,d=l,m=c,g=s.fire("PastePostProcess",{node:f,internal:d,wordContent:m}),X(g.node.innerHTML,g.isDefaultPrevented())):X(h,v.isDefaultPrevented())},Y=function(e,t){return e.insertContent(t,{merge:e.getParam("paste_merge_formats",!0),paste:!0}),!0},Z=function(e){return/^https?:\/\/[\w\?\-\/+=.&%@~#]+$/i.test(e)},G=function(e){return Z(e)&&/.(gif|jpe?g|png)$/.test(e)},J=function(e,t,n){return!(!1!==e.selection.isCollapsed()||!Z(t))&&(i=t,o=n,(r=e).undoManager.extra(function(){o(r,i)},function(){r.execCommand("mceInsertLink",!1,i)}),!0);var r,i,o},Q=function(e,t,n){return!!G(t)&&(i=t,o=n,(r=e).undoManager.extra(function(){o(r,i)},function(){r.insertContent('<img src="'+i+'">')}),!0);var r,i,o},ee=function(e,t,n){var r,i;n||!1===e.getParam("smart_paste",!0)?Y(e,t):(r=e,i=t,T.each([J,Q,Y],function(e){return!0!==e(r,i,Y)}))},te=function(e){return"\n"===e||"\r"===e},ne=function(i){var t,n;return(n={pcIsSpace:!(t=function(e,t){return-1!==" \f\t\x0B".indexOf(t)||t===B?e.pcIsSpace||""===e.str||e.str.length===i.length-1||(n=i,(r=e.str.length+1)<n.length&&0<=r&&te(n[r]))?{pcIsSpace:!1,str:e.str+B}:{pcIsSpace:!0,str:e.str+" "}:{pcIsSpace:te(t),str:e.str+t};var n,r}),str:""},b(i,function(e){n=t(n,e)}),n).str},re=function(e,t,n,r){var i,o,a,s,u,l=(i=e,a=n,s=z(o=t),u=s?K(i,o):o,W(i,u,a,s));!1===l.cancelled&&ee(e,l.content,r)},ie=function(e,t,n){var r=n||S(t);re(e,t.replace(k,""),r,!1)},oe=function(e,t){var n,r,i,o=e.dom.encode(t).replace(/\r\n/g,"\n"),a=ne(o),s=(n=a,r=e.settings.forced_root_block,i=e.settings.forced_root_block_attrs,r?R(n,!0===r?"p":r,i):n.replace(/\r?\n/g,"<br>"));re(e,s,!1,!0)},ae=function(e){var t={};if(e){if(e.getData){var n=e.getData("Text");n&&0<n.length&&-1===n.indexOf("data:text/mce-internal,")&&(t["text/plain"]=n)}if(e.types)for(var r=0;r<e.types.length;r++){var i=e.types[r];try{t[i]=e.getData(i)}catch(o){t[i]=""}}}return t},se=function(e,t){return t in e&&0<e[t].length},ue=function(e){return se(e,"text/html")||se(e,"text/plain")},le=function Be(e){var t=0;return function(){return e+t++}}("mceclip"),ce=function(e,t){var n,r,i,o,a,s,u,l=(n=t.uri,(r=/data:([^;]+);base64,([a-z0-9\+\/=]+)/i.exec(n))?{type:r[1],data:decodeURIComponent(r[2])}:{type:null,data:null}),c=l.data,f=l.type,d=le(),m=e.settings.images_reuse_filename&&t.blob.name?(i=e,o=t.blob.name,(a=o.match(/([\s\S]+?)\.(?:jpeg|jpg|png|gif)$/i))?i.dom.encode(a[1]):null):d,p=new y.Image;if(p.src=t.uri,s=e.settings,u=p,!s.images_dataimg_filter||s.images_dataimg_filter(u)){var g=e.editorUpload.blobCache,v=void 0,h=g.getByData(c,f);h?v=h:(v=g.create(d,t.blob,c,m),g.add(v)),ie(e,'<img src="'+v.blobUri()+'">',!1)}else ie(e,'<img src="'+t.uri+'">',!1)},fe=function(t,e,n){var r,i,o,a,s="paste"===e.type?e.clipboardData:e.dataTransfer;if(t.settings.paste_data_images&&s){var u=(o=(i=s).items?h(x(i.items),function(e){return e.getAsFile()}):[],a=i.files?x(i.files):[],function(e,t){for(var n=[],r=0,i=e.length;r<i;r++){var o=e[r];t(o,r)&&n.push(o)}return n}(0<o.length?o:a,function(e){return/^image\/(jpeg|png|gif|bmp)$/.test(e.type)}));if(0<u.length)return e.preventDefault(),r=u,_.all(h(r,function(r){return new _(function(e){var t=r.getAsFile?r.getAsFile():r,n=new window.FileReader;n.onload=function(){e({blob:t,uri:n.result})},n.readAsDataURL(t)})})).then(function(e){n&&t.selection.setRng(n),b(e,function(e){ce(t,e)})}),!0}return!1},de=function(e){return C.metaKeyPressed(e)&&86===e.keyCode||e.shiftKey&&45===e.keyCode},me=function(u,l,c){var t,f,d=(t=p(g.none()),{clear:function(){t.set(g.none())},set:function(e){t.set(g.some(e))},isSet:function(){return t.get().isSome()},on:function(e){t.get().each(e)}});function m(e,t,n,r){var i,o,a;se(e,"text/html")?i=e["text/html"]:(i=l.getHtml(),r=r||S(i),l.isDefaultContent(i)&&(n=!0)),i=j(i),l.remove(),o=!1===r&&!/<(?:\/?(?!(?:div|p|br|span)>)\w+|(?:(?!(?:span style="white-space:\s?pre;?">)|br\s?\/>))\w+\s[^>]+)>/i.test(i),a=G(i),i.length&&(!o||a)||(n=!0),(n||a)&&(i=se(e,"text/plain")&&o?e["text/plain"]:H(i)),l.isDefaultContent(i)?t||u.windowManager.alert("Please use Ctrl+V/Cmd+V keyboard shortcuts to paste contents."):n?oe(u,i):ie(u,i,r)}u.on("keydown",function(e){function t(e){de(e)&&!e.isDefaultPrevented()&&l.remove()}if(de(e)&&!e.isDefaultPrevented()){if((f=e.shiftKey&&86===e.keyCode)&&w.webkit&&-1!==y.navigator.userAgent.indexOf("Version/"))return;if(e.stopImmediatePropagation(),d.set(e),window.setTimeout(function(){d.clear()},100),w.ie&&f)return e.preventDefault(),n=!0,void u.fire("paste",{ieFake:n});l.remove(),l.create(),u.once("keyup",t),u.once("paste",function(){u.off("keyup",t)})}var n});u.on("paste",function(e){var t,n,r,i=d.isSet(),o=(t=u,n=ae(e.clipboardData||t.getDoc().dataTransfer),L()?T.extend(n,{"text/html":""}):n),a="text"===c.get()||f,s=se(o,D);(f=!1,e.isDefaultPrevented()||(r=e.clipboardData,-1!==y.navigator.userAgent.indexOf("Android")&&r&&r.items&&0===r.items.length))?l.remove():ue(o)||!fe(u,e,l.getLastRng()||u.selection.getRng())?(i||e.preventDefault(),!w.ie||i&&!e.ieFake||se(o,"text/html")||(l.create(),u.dom.bind(l.getEl(),"paste",function(e){e.stopPropagation()}),u.getDoc().execCommand("Paste",!1,null),o["text/html"]=l.getHtml()),se(o,"text/html")?(e.preventDefault(),s=s||S(o["text/html"]),m(o,i,a,s)):P.setEditorTimeout(u,function(){m(o,i,a,s)},0)):l.remove()})},pe=function(a,e,t){var s;me(a,e,t),a.parser.addNodeFilter("img",function(e,t,n){var r,i=function(e){e.attr("data-mce-object")||s===w.transparentSrc||e.remove()};if(!a.settings.paste_data_images&&((r=n).data&&!0===r.data.paste))for(var o=e.length;o--;)(s=e[o].attr("src"))&&(0!==s.indexOf("webkit-fake-url")&&(a.settings.allow_html_data_urls||0!==s.indexOf("data:"))||i(e[o]))})},ge=function(e){return w.ie&&e.inline?y.document.body:e.getBody()},ve=function(t,e,n){var r;ge(r=t)!==r.getBody()&&t.dom.bind(e,"paste keyup",function(e){be(t,n)||t.fire("paste")})},he=function(e){return e.dom.get("mcepastebin")},ye=function(e,t){return t===e},be=function(e,t){var n,r=he(e);return(n=r)&&"mcepastebin"===n.id&&ye(t,r.innerHTML)},xe=function(a){var s=p(null),u="%MCEPASTEBIN%";return{create:function(){return t=s,n=u,i=(e=a).dom,o=e.getBody(),t.set(e.selection.getRng()),r=e.dom.add(ge(e),"div",{id:"mcepastebin","class":"mce-pastebin",contentEditable:!0,"data-mce-bogus":"all",style:"position: fixed; top: 50%; width: 10px; height: 10px; overflow: hidden; opacity: 0"},n),(w.ie||w.gecko)&&i.setStyle(r,"left","rtl"===i.getStyle(o,"direction",!0)?65535:-65535),i.bind(r,"beforedeactivate focusin focusout",function(e){e.stopPropagation()}),ve(e,r,n),r.focus(),void e.selection.select(r,!0);var e,t,n,r,i,o},remove:function(){return function(e,t){if(he(e)){for(var n=void 0,r=t.get();n=e.dom.get("mcepastebin");)e.dom.remove(n),e.dom.unbind(n);r&&e.selection.setRng(r)}t.set(null)}(a,s)},getEl:function(){return he(a)},getHtml:function(){return function(n){var t,e,r,i,o,a=function(e,t){e.appendChild(t),n.dom.remove(t,!0)};for(e=T.grep(ge(n).childNodes,function(e){return"mcepastebin"===e.id}),t=e.shift(),T.each(e,function(e){a(t,e)}),r=(i=n.dom.select("div[id=mcepastebin]",t)).length-1;0<=r;r--)o=n.dom.create("div"),t.insertBefore(o,i[r]),a(o,i[r]);return t?t.innerHTML:""}(a)},getLastRng:function(){return s.get()},isDefault:function(){return be(a,u)},isDefaultContent:function(e){return e===u}}},we=function(e,t,n){if(r=e,!1!==w.iOS||r===undefined||"function"!=typeof r.setData||!0===L())return!1;try{return e.clearData(),e.setData("text/html",t),e.setData("text/plain",n),e.setData(D,t),!0}catch(i){return!1}var r},Pe=function(e,t,n,r){we(e.clipboardData,t.html,t.text)?(e.preventDefault(),r()):n(t.html,r)},_e=function(s){return function(e,t){var n=k+e,r=s.dom.create("div",{contenteditable:"false","data-mce-bogus":"all"}),i=s.dom.create("div",{contenteditable:"true"},n);s.dom.setStyles(r,{position:"fixed",top:"0",left:"-3000px",width:"1000px",overflow:"hidden"}),r.appendChild(i),s.dom.add(s.getBody(),r);var o=s.selection.getRng();i.focus();var a=s.dom.createRng();a.selectNodeContents(i),s.selection.setRng(a),P.setTimeout(function(){s.selection.setRng(o),r.parentNode.removeChild(r),t()},0)}},Te=function(e){return{html:e.selection.getContent({contextual:!0}),text:e.selection.getContent({format:"text"})}},Ce=function(e){return!e.selection.isCollapsed()||!!(t=e).dom.getParent(t.selection.getStart(),"td[data-mce-selected],th[data-mce-selected]",t.getBody());var t},De=function(e){var t,n;e.on("cut",(t=e,function(e){Ce(t)&&Pe(e,Te(t),_e(t),function(){if(w.browser.isChrome()){var e=t.selection.getRng();P.setEditorTimeout(t,function(){t.selection.setRng(e),t.execCommand("Delete")},0)}else t.execCommand("Delete")})})),e.on("copy",(n=e,function(e){Ce(n)&&Pe(e,Te(n),_e(n),function(){})}))},ke=tinymce.util.Tools.resolve("tinymce.dom.RangeUtils"),Se=function(e,t){return ke.getCaretRangeFromPoint(t.clientX,t.clientY,e.getDoc())},Oe=function(e,t){e.focus(),e.selection.setRng(t)},Re=function(a,s,u){a.getParam("paste_block_drop",!1)&&a.on("dragend dragover draggesture dragdrop drop drag",function(e){e.preventDefault(),e.stopPropagation()}),M(a)||a.on("drop",function(e){var t=e.dataTransfer;t&&t.files&&0<t.files.length&&e.preventDefault()}),a.on("drop",function(e){var t,n;if(n=Se(a,e),!e.isDefaultPrevented()&&!u.get()){t=s.getDataTransferItems(e.dataTransfer);var r,i=s.hasContentType(t,D);if(s.hasHtmlOrText(t)&&(!(r=t["text/plain"])||0!==r.indexOf("file://"))||!s.pasteImageData(e,n))if(n&&a.getParam("paste_filter_drop",!0)){var o=t["mce-internal"]||t["text/html"]||t["text/plain"];o&&(e.preventDefault(),P.setEditorTimeout(a,function(){a.undoManager.transact(function(){t["mce-internal"]&&a.execCommand("Delete"),Oe(a,n),o=j(o),t["text/html"]?s.pasteHtml(o,i):s.pasteText(o)})}))}}}),a.on("dragstart",function(e){u.set(!0)}),a.on("dragover dragend",function(e){M(a)&&!1===u.get()&&(e.preventDefault(),Oe(a,Se(a,e))),"dragend"===e.type&&u.set(!1)})};function Ae(t,n){t.on("PastePreProcess",function(e){e.content=n(t,e.content,e.internal,e.wordContent)})}function Ie(e,t){if(!z(t))return t;var n=[];return T.each(e.schema.getBlockElements(),function(e,t){n.push(t)}),t=$(t,[[new RegExp("(?:<br>&nbsp;[\\s\\r\\n]+|<br>)*(<\\/?("+n.join("|")+")[^>]*>)(?:<br>&nbsp;[\\s\\r\\n]+|<br>)*","g"),"$1"]]),t=$(t,[[/<br><br>/g,"<BR><BR>"],[/<br>/g," "],[/<BR><BR>/g,"<br>"]])}function Ee(e,t,n,r){if(r||n)return t;var l,i=e.getParam("paste_webkit_styles");if(!1===e.getParam("paste_remove_styles_if_webkit",!0)||"all"===i)return t;if(i&&(l=i.split(/[, ]/)),l){var c=e.dom,f=e.selection.getNode();t=t.replace(/(<[^>]+) style="([^"]*)"([^>]*>)/gi,function(e,t,n,r){var i=c.parseStyle(c.decode(n)),o={};if("none"===l)return t+r;for(var a=0;a<l.length;a++){var s=i[l[a]],u=c.getStyle(f,l[a],!0);/color/.test(l[a])&&(s=c.toHex(s),u=c.toHex(u)),u!==s&&(o[l[a]]=s)}return(o=c.serializeStyle(o,"span"))?t+' style="'+o+'"'+r:t+r})}else t=t.replace(/(<[^>]+) style="([^"]*)"([^>]*>)/gi,"$1$3");return t=t.replace(/(<[^>]+) data-mce-style="([^"]+)"([^>]*>)/gi,function(e,t,n,r){return t+' style="'+n+'"'+r})}function Fe(n,e){n.$("a",e).find("font,u").each(function(e,t){n.dom.remove(t,!0)})}var Me=function(e){w.webkit&&Ae(e,Ee),w.ie&&(Ae(e,Ie),function r(t,n){t.on("PastePostProcess",function(e){n(t,e.node)})}(e,Fe))},Ne=function(n,r){return function(t){t.setActive("text"===r.pasteFormat.get());var e=function(e){return t.setActive(e.state)};return n.on("PastePlainTextToggle",e),function(){return n.off("PastePlainTextToggle",e)}}};!function $e(){c.add("paste",function(e){if(!1==!(!/(^|[ ,])powerpaste([, ]|$)/.test(e.settings.plugins)||!c.get("powerpaste")||("undefined"!=typeof y.window.console&&y.window.console.log&&y.window.console.log("PowerPaste is incompatible with Paste plugin! Remove 'paste' from the 'plugins' option."),0))){var t=p(!1),n=p(e.getParam("paste_as_text",!1)?"text":"html"),r=(u=n,l=xe(s=e),s.on("PreInit",function(){return pe(s,l,u)}),{pasteFormat:u,pasteHtml:function(e,t){return ie(s,e,t)},pasteText:function(e){return oe(s,e)},pasteImageData:function(e,t){return fe(s,e,t)},getDataTransferItems:ae,hasHtmlOrText:ue,hasContentType:se}),i=Me(e);return a=r,(o=e).ui.registry.addToggleButton("pastetext",{active:!1,icon:"paste-text",tooltip:"Paste as text",onAction:function(){return o.execCommand("mceTogglePlainTextPaste")},onSetup:Ne(o,a)}),o.ui.registry.addToggleMenuItem("pastetext",{text:"Paste as text",icon:"paste-text",onAction:function(){return o.execCommand("mceTogglePlainTextPaste")},onSetup:Ne(o,a)}),f(e,r),function(e){var t=e.plugins.paste,n=e.getParam("paste_preprocess");n&&e.on("PastePreProcess",function(e){n.call(t,t,e)});var r=e.getParam("paste_postprocess");r&&e.on("PastePostProcess",function(e){r.call(t,t,e)})}(e),De(e),Re(e,r,t),{clipboard:r,quirks:i}}var o,a,s,u,l})}()}(window);