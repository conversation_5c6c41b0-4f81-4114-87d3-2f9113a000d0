USE [MA_SUP_SITENAME_IRISHUBDB]
GO
SET IDENTITY_INSERT [dbo].[tblAppEventLog] ON 
GO
INSERT [dbo].[tblAppEventLog] ([AppEventLogID], [UserID], [User Name], [RFID], [EventID], [EntryTypeID], [Cluster Name], [Cabinet Name], [Compartment Name], [PatientID], [Patient Name], [PhysicianID], [Physician Name], [Data Upload], [Computer Name], [Domain], [Log Date], [AlertSent], [OverrideNotes], [LogMsg], [SessionID], [Door Status], [Lock Status], [Switch Status], [Button Events]) VALUES (1, 2, N'Super User', N'1', 94, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, N'MAILPTP103', N'maspects.com', CAST(N'2025-01-08T20:53:22.233' AS DateTime), NULL, NULL, N'Login Successfully', N'b131a11f-be8f-433f-8ebe-d12aa0765aef', NULL, NULL, NULL, NULL)
GO
INSERT [dbo].[tblAppEventLog] ([AppEventLogID], [UserID], [User Name], [RFID], [EventID], [EntryTypeID], [Cluster Name], [Cabinet Name], [Compartment Name], [PatientID], [Patient Name], [PhysicianID], [Physician Name], [Data Upload], [Computer Name], [Domain], [Log Date], [AlertSent], [OverrideNotes], [LogMsg], [SessionID], [Door Status], [Lock Status], [Switch Status], [Button Events]) VALUES (2, 2, N'Super User', N'E004010836DDC2B1', 97, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, N'MAILPTP103', N'maspects.com', CAST(N'2025-01-08T21:24:40.230' AS DateTime), NULL, NULL, N'Item removed Successfully', N'20250109022322053', NULL, NULL, NULL, NULL)
GO
INSERT [dbo].[tblAppEventLog] ([AppEventLogID], [UserID], [User Name], [RFID], [EventID], [EntryTypeID], [Cluster Name], [Cabinet Name], [Compartment Name], [PatientID], [Patient Name], [PhysicianID], [Physician Name], [Data Upload], [Computer Name], [Domain], [Log Date], [AlertSent], [OverrideNotes], [LogMsg], [SessionID], [Door Status], [Lock Status], [Switch Status], [Button Events]) VALUES (3, 2, N'Super User', NULL, 95, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, N'MAILPTP103', N'maspects.com', CAST(N'2025-01-08T21:25:19.760' AS DateTime), NULL, NULL, N'Logout Successfully', N'b131a11f-be8f-433f-8ebe-d12aa0765aef', NULL, NULL, NULL, NULL)
GO
INSERT [dbo].[tblAppEventLog] ([AppEventLogID], [UserID], [User Name], [RFID], [EventID], [EntryTypeID], [Cluster Name], [Cabinet Name], [Compartment Name], [PatientID], [Patient Name], [PhysicianID], [Physician Name], [Data Upload], [Computer Name], [Domain], [Log Date], [AlertSent], [OverrideNotes], [LogMsg], [SessionID], [Door Status], [Lock Status], [Switch Status], [Button Events]) VALUES (4, 2, N'Super User', N'1', 94, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, N'MAILPTP103', N'maspects.com', CAST(N'2025-01-08T21:28:42.810' AS DateTime), NULL, NULL, N'Login Successfully', N'32215526-fc4f-4c2d-9e1e-c864812e977e', NULL, NULL, NULL, NULL)
GO
INSERT [dbo].[tblAppEventLog] ([AppEventLogID], [UserID], [User Name], [RFID], [EventID], [EntryTypeID], [Cluster Name], [Cabinet Name], [Compartment Name], [PatientID], [Patient Name], [PhysicianID], [Physician Name], [Data Upload], [Computer Name], [Domain], [Log Date], [AlertSent], [OverrideNotes], [LogMsg], [SessionID], [Door Status], [Lock Status], [Switch Status], [Button Events]) VALUES (5, 2, N'Super User', N'1', 94, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, N'MAILPTP103', N'maspects.com', CAST(N'2025-01-08T21:33:21.650' AS DateTime), NULL, NULL, N'Login Successfully', N'6f3d6dd6-0f0a-416e-9afd-5c91dfa28c2a', NULL, NULL, NULL, NULL)
GO
INSERT [dbo].[tblAppEventLog] ([AppEventLogID], [UserID], [User Name], [RFID], [EventID], [EntryTypeID], [Cluster Name], [Cabinet Name], [Compartment Name], [PatientID], [Patient Name], [PhysicianID], [Physician Name], [Data Upload], [Computer Name], [Domain], [Log Date], [AlertSent], [OverrideNotes], [LogMsg], [SessionID], [Door Status], [Lock Status], [Switch Status], [Button Events]) VALUES (6, 2, N'Super User', N'04546540664440', 97, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, N'MAILPTP103', N'maspects.com', CAST(N'2025-01-08T21:33:36.020' AS DateTime), NULL, NULL, N'Item removed Successfully', N'20250109030321563', NULL, NULL, NULL, NULL)
GO
INSERT [dbo].[tblAppEventLog] ([AppEventLogID], [UserID], [User Name], [RFID], [EventID], [EntryTypeID], [Cluster Name], [Cabinet Name], [Compartment Name], [PatientID], [Patient Name], [PhysicianID], [Physician Name], [Data Upload], [Computer Name], [Domain], [Log Date], [AlertSent], [OverrideNotes], [LogMsg], [SessionID], [Door Status], [Lock Status], [Switch Status], [Button Events]) VALUES (7, 2, N'Super User', N'04546540664440', 97, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, N'MAILPTP103', N'maspects.com', CAST(N'2025-01-08T21:33:48.180' AS DateTime), NULL, NULL, N'Item removed Successfully', N'20250109030321563', NULL, NULL, NULL, NULL)
GO
INSERT [dbo].[tblAppEventLog] ([AppEventLogID], [UserID], [User Name], [RFID], [EventID], [EntryTypeID], [Cluster Name], [Cabinet Name], [Compartment Name], [PatientID], [Patient Name], [PhysicianID], [Physician Name], [Data Upload], [Computer Name], [Domain], [Log Date], [AlertSent], [OverrideNotes], [LogMsg], [SessionID], [Door Status], [Lock Status], [Switch Status], [Button Events]) VALUES (8, 2, N'Super User', N'10886982146595', 97, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, N'MAILPTP103', N'maspects.com', CAST(N'2025-01-08T21:34:54.797' AS DateTime), NULL, NULL, N'Item removed Successfully', N'20250109030321563', NULL, NULL, NULL, NULL)
GO
INSERT [dbo].[tblAppEventLog] ([AppEventLogID], [UserID], [User Name], [RFID], [EventID], [EntryTypeID], [Cluster Name], [Cabinet Name], [Compartment Name], [PatientID], [Patient Name], [PhysicianID], [Physician Name], [Data Upload], [Computer Name], [Domain], [Log Date], [AlertSent], [OverrideNotes], [LogMsg], [SessionID], [Door Status], [Lock Status], [Switch Status], [Button Events]) VALUES (9, 2, N'Super User', N'10886982146595', 97, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, N'MAILPTP103', N'maspects.com', CAST(N'2025-01-08T21:35:07.543' AS DateTime), NULL, NULL, N'Item removed Successfully', N'20250109030321563', NULL, NULL, NULL, NULL)
GO
INSERT [dbo].[tblAppEventLog] ([AppEventLogID], [UserID], [User Name], [RFID], [EventID], [EntryTypeID], [Cluster Name], [Cabinet Name], [Compartment Name], [PatientID], [Patient Name], [PhysicianID], [Physician Name], [Data Upload], [Computer Name], [Domain], [Log Date], [AlertSent], [OverrideNotes], [LogMsg], [SessionID], [Door Status], [Lock Status], [Switch Status], [Button Events]) VALUES (10, 2, N'Super User', N'10886982146595', 97, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, N'MAILPTP103', N'maspects.com', CAST(N'2025-01-08T22:01:16.533' AS DateTime), NULL, NULL, N'Item removed Successfully', N'20250109030321563', NULL, NULL, NULL, NULL)
GO
INSERT [dbo].[tblAppEventLog] ([AppEventLogID], [UserID], [User Name], [RFID], [EventID], [EntryTypeID], [Cluster Name], [Cabinet Name], [Compartment Name], [PatientID], [Patient Name], [PhysicianID], [Physician Name], [Data Upload], [Computer Name], [Domain], [Log Date], [AlertSent], [OverrideNotes], [LogMsg], [SessionID], [Door Status], [Lock Status], [Switch Status], [Button Events]) VALUES (11, 2, N'Super User', N'1', 94, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, N'MAILPTP103', N'maspects.com', CAST(N'2025-01-08T22:21:21.157' AS DateTime), NULL, NULL, N'Login Successfully', N'a3c9d290-4458-4822-8da8-a21e25b3dddf', NULL, NULL, NULL, NULL)
GO
INSERT [dbo].[tblAppEventLog] ([AppEventLogID], [UserID], [User Name], [RFID], [EventID], [EntryTypeID], [Cluster Name], [Cabinet Name], [Compartment Name], [PatientID], [Patient Name], [PhysicianID], [Physician Name], [Data Upload], [Computer Name], [Domain], [Log Date], [AlertSent], [OverrideNotes], [LogMsg], [SessionID], [Door Status], [Lock Status], [Switch Status], [Button Events]) VALUES (12, 2, N'Super User', N'H6792048850', 97, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, N'MAILPTP103', N'maspects.com', CAST(N'2025-01-08T22:32:45.753' AS DateTime), NULL, NULL, N'Item removed Successfully', N'20250109035121057', NULL, NULL, NULL, NULL)
GO
INSERT [dbo].[tblAppEventLog] ([AppEventLogID], [UserID], [User Name], [RFID], [EventID], [EntryTypeID], [Cluster Name], [Cabinet Name], [Compartment Name], [PatientID], [Patient Name], [PhysicianID], [Physician Name], [Data Upload], [Computer Name], [Domain], [Log Date], [AlertSent], [OverrideNotes], [LogMsg], [SessionID], [Door Status], [Lock Status], [Switch Status], [Button Events]) VALUES (13, 2, N'Super User', N'H6792048850', 97, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, N'MAILPTP103', N'maspects.com', CAST(N'2025-01-08T22:33:18.117' AS DateTime), NULL, NULL, N'Item removed Successfully', N'20250109035121057', NULL, NULL, NULL, NULL)
GO
INSERT [dbo].[tblAppEventLog] ([AppEventLogID], [UserID], [User Name], [RFID], [EventID], [EntryTypeID], [Cluster Name], [Cabinet Name], [Compartment Name], [PatientID], [Patient Name], [PhysicianID], [Physician Name], [Data Upload], [Computer Name], [Domain], [Log Date], [AlertSent], [OverrideNotes], [LogMsg], [SessionID], [Door Status], [Lock Status], [Switch Status], [Button Events]) VALUES (14, 2, N'Super User', N'H6792048850', 97, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, N'MAILPTP103', N'maspects.com', CAST(N'2025-01-08T22:33:36.913' AS DateTime), NULL, NULL, N'Item removed Successfully', N'20250109035121057', NULL, NULL, NULL, NULL)
GO
INSERT [dbo].[tblAppEventLog] ([AppEventLogID], [UserID], [User Name], [RFID], [EventID], [EntryTypeID], [Cluster Name], [Cabinet Name], [Compartment Name], [PatientID], [Patient Name], [PhysicianID], [Physician Name], [Data Upload], [Computer Name], [Domain], [Log Date], [AlertSent], [OverrideNotes], [LogMsg], [SessionID], [Door Status], [Lock Status], [Switch Status], [Button Events]) VALUES (15, 2, N'Super User', N'10886982151988', 97, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, N'MAILPTP103', N'maspects.com', CAST(N'2025-01-08T22:35:01.220' AS DateTime), NULL, NULL, N'Item removed Successfully', N'20250109035121057', NULL, NULL, NULL, NULL)
GO
INSERT [dbo].[tblAppEventLog] ([AppEventLogID], [UserID], [User Name], [RFID], [EventID], [EntryTypeID], [Cluster Name], [Cabinet Name], [Compartment Name], [PatientID], [Patient Name], [PhysicianID], [Physician Name], [Data Upload], [Computer Name], [Domain], [Log Date], [AlertSent], [OverrideNotes], [LogMsg], [SessionID], [Door Status], [Lock Status], [Switch Status], [Button Events]) VALUES (16, 2, N'Super User', N'10886982151988', 97, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, N'MAILPTP103', N'maspects.com', CAST(N'2025-01-08T22:35:11.270' AS DateTime), NULL, NULL, N'Item removed Successfully', N'20250109035121057', NULL, NULL, NULL, NULL)
GO
INSERT [dbo].[tblAppEventLog] ([AppEventLogID], [UserID], [User Name], [RFID], [EventID], [EntryTypeID], [Cluster Name], [Cabinet Name], [Compartment Name], [PatientID], [Patient Name], [PhysicianID], [Physician Name], [Data Upload], [Computer Name], [Domain], [Log Date], [AlertSent], [OverrideNotes], [LogMsg], [SessionID], [Door Status], [Lock Status], [Switch Status], [Button Events]) VALUES (17, 2, N'Super User', N'10886982151988', 97, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, N'MAILPTP103', N'maspects.com', CAST(N'2025-01-08T22:35:51.880' AS DateTime), NULL, NULL, N'Item removed Successfully', N'20250109035121057', NULL, NULL, NULL, NULL)
GO
INSERT [dbo].[tblAppEventLog] ([AppEventLogID], [UserID], [User Name], [RFID], [EventID], [EntryTypeID], [Cluster Name], [Cabinet Name], [Compartment Name], [PatientID], [Patient Name], [PhysicianID], [Physician Name], [Data Upload], [Computer Name], [Domain], [Log Date], [AlertSent], [OverrideNotes], [LogMsg], [SessionID], [Door Status], [Lock Status], [Switch Status], [Button Events]) VALUES (18, 2, N'Super User', N'10886982151988', 97, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, N'MAILPTP103', N'maspects.com', CAST(N'2025-01-08T22:37:09.933' AS DateTime), NULL, NULL, N'Item removed Successfully', N'20250109035121057', NULL, NULL, NULL, NULL)
GO
INSERT [dbo].[tblAppEventLog] ([AppEventLogID], [UserID], [User Name], [RFID], [EventID], [EntryTypeID], [Cluster Name], [Cabinet Name], [Compartment Name], [PatientID], [Patient Name], [PhysicianID], [Physician Name], [Data Upload], [Computer Name], [Domain], [Log Date], [AlertSent], [OverrideNotes], [LogMsg], [SessionID], [Door Status], [Lock Status], [Switch Status], [Button Events]) VALUES (19, 2, N'Super User', N'10886982151988', 97, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, N'MAILPTP103', N'maspects.com', CAST(N'2025-01-08T22:38:15.563' AS DateTime), NULL, NULL, N'Item removed Successfully', N'20250109035121057', NULL, NULL, NULL, NULL)
GO
INSERT [dbo].[tblAppEventLog] ([AppEventLogID], [UserID], [User Name], [RFID], [EventID], [EntryTypeID], [Cluster Name], [Cabinet Name], [Compartment Name], [PatientID], [Patient Name], [PhysicianID], [Physician Name], [Data Upload], [Computer Name], [Domain], [Log Date], [AlertSent], [OverrideNotes], [LogMsg], [SessionID], [Door Status], [Lock Status], [Switch Status], [Button Events]) VALUES (20, 2, N'Super User', N'10886982151988', 97, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, N'MAILPTP103', N'maspects.com', CAST(N'2025-01-08T22:38:27.230' AS DateTime), NULL, NULL, N'Item removed Successfully', N'20250109035121057', NULL, NULL, NULL, NULL)
GO
INSERT [dbo].[tblAppEventLog] ([AppEventLogID], [UserID], [User Name], [RFID], [EventID], [EntryTypeID], [Cluster Name], [Cabinet Name], [Compartment Name], [PatientID], [Patient Name], [PhysicianID], [Physician Name], [Data Upload], [Computer Name], [Domain], [Log Date], [AlertSent], [OverrideNotes], [LogMsg], [SessionID], [Door Status], [Lock Status], [Switch Status], [Button Events]) VALUES (21, 0, NULL, NULL, 95, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, N'MAILPTP103', N'maspects.com', CAST(N'2025-01-09T07:52:11.003' AS DateTime), NULL, NULL, N'Logout Successfully', NULL, NULL, NULL, NULL, NULL)
GO
SET IDENTITY_INSERT [dbo].[tblAppEventLog] OFF
GO
INSERT [dbo].[tblAppEvents] ([EventID], [Event], [EventDesc], [SPName], [SPDesc], [LogMsg], [EnteredDate], [AppID]) VALUES (10, N'ActiveDirectoryLogin', N'User Login with Active Directory enabled', N'uspActiveDirectoryUserLogin', N'User can Insert/Update the info using Active Directory Login', N'User added/Updated with Active Directory Login Successfully', CAST(N'2020-04-23T13:43:29.733' AS DateTime), 3)
GO
INSERT [dbo].[tblAppEvents] ([EventID], [Event], [EventDesc], [SPName], [SPDesc], [LogMsg], [EnteredDate], [AppID]) VALUES (11, N'UploadProductDocument', N'Uploading Product Doucument through Web App', N'uspUploadProductDocuments', N'User to see AND add the Document details in Web page', N'Product document Uploaded/Deleted Successfully', CAST(N'2020-04-23T13:43:29.743' AS DateTime), 3)
GO
INSERT [dbo].[tblAppEvents] ([EventID], [Event], [EventDesc], [SPName], [SPDesc], [LogMsg], [EnteredDate], [AppID]) VALUES (12, N'FetchProductDocument', N'Fetching Product document details in web', N'uspFetchProductDocuments', N'User can see the document details in Web page', N'Product Document fetched Successfully', CAST(N'2020-04-23T13:43:29.750' AS DateTime), 3)
GO
INSERT [dbo].[tblAppEvents] ([EventID], [Event], [EventDesc], [SPName], [SPDesc], [LogMsg], [EnteredDate], [AppID]) VALUES (15, N'FetchProductDocumentExtn', N'Fetching the product Document extension for on App', N'uspFetchProductDocumentExtn', N'User can fetch the supported Product Document Extension', N'Fetched Product Document Extension Successfully', CAST(N'2020-04-23T13:43:29.753' AS DateTime), 3)
GO
INSERT [dbo].[tblAppEvents] ([EventID], [Event], [EventDesc], [SPName], [SPDesc], [LogMsg], [EnteredDate], [AppID]) VALUES (18, N'FetchSolution', N'Fetching Solutions on Web', N'uspFetchSolution', N'User to Fetch the Active Solution in Web', N'Solution fetched Successfully', CAST(N'2020-04-23T13:43:29.760' AS DateTime), 3)
GO
INSERT [dbo].[tblAppEvents] ([EventID], [Event], [EventDesc], [SPName], [SPDesc], [LogMsg], [EnteredDate], [AppID]) VALUES (19, N'AddSolutionDetails', N'Adding solution details against a used item FROM Web', N'uspTissueReconstitution', N'User to Reconstitute Tissue items', N'Added Solution Details Successfully', CAST(N'2020-04-23T13:43:29.763' AS DateTime), 3)
GO
INSERT [dbo].[tblAppEvents] ([EventID], [Event], [EventDesc], [SPName], [SPDesc], [LogMsg], [EnteredDate], [AppID]) VALUES (20, N'EditSolutionDetails', N'Edit solution details against a used item FROM Web', N'uspTissueReconstitution', N'User to Reconstitute Tissue items', N'Edited Solution Deatils Successfully', CAST(N'2020-04-23T13:43:29.767' AS DateTime), 3)
GO
INSERT [dbo].[tblAppEvents] ([EventID], [Event], [EventDesc], [SPName], [SPDesc], [LogMsg], [EnteredDate], [AppID]) VALUES (21, N'RemoveSolutionDetails', N'Remove solution detail against a used item FROM Web', N'uspTissueReconstitution', N'User to Reconstitute Tissue items', N'Removed Solution Details Successfully', CAST(N'2020-04-23T13:43:29.773' AS DateTime), 3)
GO
INSERT [dbo].[tblAppEvents] ([EventID], [Event], [EventDesc], [SPName], [SPDesc], [LogMsg], [EnteredDate], [AppID]) VALUES (22, N'FetchSolutionDetails', N'Solution Detail fetching at web', N'uspFetchSolutionDetails', N'User to the Active Solution in Web', N'Fetched Solution Details Successfully', CAST(N'2020-04-23T13:43:29.780' AS DateTime), 3)
GO
INSERT [dbo].[tblAppEvents] ([EventID], [Event], [EventDesc], [SPName], [SPDesc], [LogMsg], [EnteredDate], [AppID]) VALUES (29, N'FetchPurchaseOrder', N'Purchase Order Details fetching at web', N'uspFetchPurchaseOrder', N'User to view the Purchase order', N'Fetched Purchase Order Details Successfully', CAST(N'2020-04-23T13:43:29.800' AS DateTime), 3)
GO
INSERT [dbo].[tblAppEvents] ([EventID], [Event], [EventDesc], [SPName], [SPDesc], [LogMsg], [EnteredDate], [AppID]) VALUES (30, N'AddPurchaseOrder', N'To Add Purchase Order Details through web', N'uspAddPurchaseOrder', N'User can add Purchase order', N'New purchase order have been added successfully', CAST(N'2020-04-23T13:43:29.800' AS DateTime), 3)
GO
INSERT [dbo].[tblAppEvents] ([EventID], [Event], [EventDesc], [SPName], [SPDesc], [LogMsg], [EnteredDate], [AppID]) VALUES (31, N'EditPurchaseOrder', N'To Edit Purchase Order Details through web', N'uspAddPurchaseOrder', N'User can edit Purchase order', N'Purchase order details have been edited successfully', CAST(N'2020-04-23T13:43:29.800' AS DateTime), 3)
GO
INSERT [dbo].[tblAppEvents] ([EventID], [Event], [EventDesc], [SPName], [SPDesc], [LogMsg], [EnteredDate], [AppID]) VALUES (32, N'SearchPurchaseOrder', N'To Search Purchase Order Details through web', N'uspSearchPurchaseOrder', N'User can Search Purchase order', N'Purchase Order Details searched Successfully', CAST(N'2020-04-23T13:43:29.803' AS DateTime), 3)
GO
INSERT [dbo].[tblAppEvents] ([EventID], [Event], [EventDesc], [SPName], [SPDesc], [LogMsg], [EnteredDate], [AppID]) VALUES (36, N'FetchProductList', N'Product List fetching at web', N'uspFetchProductList', N'User to view product list in Web', N'Fetched Product List Successfully', CAST(N'2020-04-23T13:43:29.803' AS DateTime), 4)
GO
INSERT [dbo].[tblAppEvents] ([EventID], [Event], [EventDesc], [SPName], [SPDesc], [LogMsg], [EnteredDate], [AppID]) VALUES (47, N'FetchDropdownList', N'Dropdown List fetching in web', N'uspFetchDropdownList', N'User to view all the dropdown lists in Web', N'Fetched dropdown List Successfully', CAST(N'2020-04-23T13:43:29.840' AS DateTime), 4)
GO
INSERT [dbo].[tblAppEvents] ([EventID], [Event], [EventDesc], [SPName], [SPDesc], [LogMsg], [EnteredDate], [AppID]) VALUES (58, N'FetchOrderDetails', N'To Fetch order details', N'uspManageOrders', N'To manage order details and material messages', N'Order details fetched successfully', CAST(N'2020-04-23T14:41:19.073' AS DateTime), 3)
GO
INSERT [dbo].[tblAppEvents] ([EventID], [Event], [EventDesc], [SPName], [SPDesc], [LogMsg], [EnteredDate], [AppID]) VALUES (59, N'SearchOrderDetails', N'To search order details', N'uspManageOrders', N'To manage order details and material messages', N'Order details searched successfully', CAST(N'2020-04-23T14:41:19.083' AS DateTime), 3)
GO
INSERT [dbo].[tblAppEvents] ([EventID], [Event], [EventDesc], [SPName], [SPDesc], [LogMsg], [EnteredDate], [AppID]) VALUES (60, N'UpdateOrderDetails', N'To Fetch order details', N'uspManageOrders', N'To manage order details and material messages', N'Orders Fetched successfully', CAST(N'2020-04-23T14:41:19.097' AS DateTime), 3)
GO
INSERT [dbo].[tblAppEvents] ([EventID], [Event], [EventDesc], [SPName], [SPDesc], [LogMsg], [EnteredDate], [AppID]) VALUES (61, N'CreateOrder', N'To create order', N'uspManageOrders', N'To manage order details and material messages', N'Order details inserted successfully', CAST(N'2020-04-23T14:41:19.100' AS DateTime), 3)
GO
INSERT [dbo].[tblAppEvents] ([EventID], [Event], [EventDesc], [SPName], [SPDesc], [LogMsg], [EnteredDate], [AppID]) VALUES (62, N'DeleteOrderDetails', N'To delete order details', N'uspManageOrders', N'To manage order details and material messages', N'Order details deleted successfully', CAST(N'2020-04-23T14:41:19.110' AS DateTime), 3)
GO
INSERT [dbo].[tblAppEvents] ([EventID], [Event], [EventDesc], [SPName], [SPDesc], [LogMsg], [EnteredDate], [AppID]) VALUES (63, N'FetchOrderStatusTypes', N'To Fetch order Status types', N'uspManageOrders', N'To manage order details and material messages', N'Order Status types Fetched successfully', CAST(N'2020-04-23T14:41:19.113' AS DateTime), 3)
GO
INSERT [dbo].[tblAppEvents] ([EventID], [Event], [EventDesc], [SPName], [SPDesc], [LogMsg], [EnteredDate], [AppID]) VALUES (64, N'FetchMaterialMsgDetails', N'To Fetch material message details', N'uspManageOrders', N'To manage order details and material messages', N'Material Message details Fetched successfully', CAST(N'2020-04-23T14:41:19.120' AS DateTime), 3)
GO
INSERT [dbo].[tblAppEvents] ([EventID], [Event], [EventDesc], [SPName], [SPDesc], [LogMsg], [EnteredDate], [AppID]) VALUES (65, N'SearchMaterialMsgDetails', N'To search for material message details', N'uspManageOrders', N'To manage order details and material messages', N'Material Message details searched successfully', CAST(N'2020-04-23T14:41:19.130' AS DateTime), 3)
GO
INSERT [dbo].[tblAppEvents] ([EventID], [Event], [EventDesc], [SPName], [SPDesc], [LogMsg], [EnteredDate], [AppID]) VALUES (66, N'UpdateMaterialMsgDetails', N'To update material message details', N'uspManageOrders', N'To manage order details and material messages', N'Material Message details updated successfully', CAST(N'2020-04-23T14:41:19.130' AS DateTime), 3)
GO
INSERT [dbo].[tblAppEvents] ([EventID], [Event], [EventDesc], [SPName], [SPDesc], [LogMsg], [EnteredDate], [AppID]) VALUES (67, N'CreateMaterialMsg', N'To create Material message', N'uspManageOrders', N'To manage order details and material messages', N'Material message details inserted successfully', CAST(N'2020-04-23T14:41:19.130' AS DateTime), 3)
GO
INSERT [dbo].[tblAppEvents] ([EventID], [Event], [EventDesc], [SPName], [SPDesc], [LogMsg], [EnteredDate], [AppID]) VALUES (68, N'DeleteMaterialMsgDetails', N'To delete material message details', N'uspManageOrders', N'To manage order details and material messages', N'Material message details deleted successfully', CAST(N'2020-04-23T14:41:19.130' AS DateTime), 3)
GO
INSERT [dbo].[tblAppEvents] ([EventID], [Event], [EventDesc], [SPName], [SPDesc], [LogMsg], [EnteredDate], [AppID]) VALUES (69, N'SearchMaterialMsgItems', N'To search material message Items details', N'uspManageOrders', N'To manage order details and material messages', N'Material Message Items searched successfully', CAST(N'2020-04-23T14:41:19.140' AS DateTime), 3)
GO
INSERT [dbo].[tblAppEvents] ([EventID], [Event], [EventDesc], [SPName], [SPDesc], [LogMsg], [EnteredDate], [AppID]) VALUES (70, N'SearchOrdersParLevel', N'To search for orders par levels', N'uspManageOrders', N'To manage order details and material messages', N'Orders par level serched successfully', CAST(N'2020-04-23T14:41:19.143' AS DateTime), 3)
GO
INSERT [dbo].[tblAppEvents] ([EventID], [Event], [EventDesc], [SPName], [SPDesc], [LogMsg], [EnteredDate], [AppID]) VALUES (71, N'SearchConfigs', N'To Fetch config details', N'uspFetchConfigs', N'To fetch configs/objects enabled/disabled in IrisWebDB', N'Configs details searched successfully', CAST(N'2020-04-23T14:41:19.150' AS DateTime), 3)
GO
INSERT [dbo].[tblAppEvents] ([EventID], [Event], [EventDesc], [SPName], [SPDesc], [LogMsg], [EnteredDate], [AppID]) VALUES (72, N'SearchLawsonLocation', N'To search for Lawson Location details', N'uspFetchLawsonLocation', N'To fetch lawson location details', N'Lawson location details searched successfully', CAST(N'2020-04-23T14:41:19.153' AS DateTime), 3)
GO
INSERT [dbo].[tblAppEvents] ([EventID], [Event], [EventDesc], [SPName], [SPDesc], [LogMsg], [EnteredDate], [AppID]) VALUES (73, N'FetchSupplierDetails', N'To Fetch supplier details', N'uspFetchSuppliers', N'To Fetch supplier details', N'Supplier details fetched successfully', CAST(N'2020-04-23T14:41:19.160' AS DateTime), 3)
GO
INSERT [dbo].[tblAppEvents] ([EventID], [Event], [EventDesc], [SPName], [SPDesc], [LogMsg], [EnteredDate], [AppID]) VALUES (74, N'SearchSupplierDetails', N'To search for supplier details', N'uspFetchSuppliers', N'To Fetch supplier details', N'Supplier details searched successfully', CAST(N'2020-04-23T14:41:19.163' AS DateTime), 3)
GO
INSERT [dbo].[tblAppEvents] ([EventID], [Event], [EventDesc], [SPName], [SPDesc], [LogMsg], [EnteredDate], [AppID]) VALUES (75, N'SearchSettings', N'To search setting details', N'uspFetchSettings', N'To fetch settings list from MA_SUP_TJUHOR_IRISDB', N'Setting details searched successfully', CAST(N'2020-04-23T14:41:19.170' AS DateTime), 3)
GO
INSERT [dbo].[tblAppEvents] ([EventID], [Event], [EventDesc], [SPName], [SPDesc], [LogMsg], [EnteredDate], [AppID]) VALUES (76, N'FetchItemsUsageDetails', N'To fetch Items Usage details', N'uspManageItemUsage', N'To manage Items usage and usage history details', N'Items Usage details fetched successfully', CAST(N'2020-04-23T14:41:19.173' AS DateTime), 3)
GO
INSERT [dbo].[tblAppEvents] ([EventID], [Event], [EventDesc], [SPName], [SPDesc], [LogMsg], [EnteredDate], [AppID]) VALUES (77, N'SearchItemsUsageDetails', N'To search Items usage detials', N'uspManageItemUsage', N'To manage Items usage and usage history details', N'Items Usage details searched successfully', CAST(N'2020-04-23T14:41:19.180' AS DateTime), 3)
GO
INSERT [dbo].[tblAppEvents] ([EventID], [Event], [EventDesc], [SPName], [SPDesc], [LogMsg], [EnteredDate], [AppID]) VALUES (78, N'SearchItemHistoryDetails', N'To search Item usage history details', N'uspManageItemUsage', N'To manage Items usage and usage history details', N'Items Usage History details searched successfully', CAST(N'2020-04-23T14:41:19.183' AS DateTime), 3)
GO
INSERT [dbo].[tblAppEvents] ([EventID], [Event], [EventDesc], [SPName], [SPDesc], [LogMsg], [EnteredDate], [AppID]) VALUES (79, N'FetchInCabinetItemList', N'User can fetch the Cabinet Item List', N'uspFetchInCabinetItemKist', N'User can see Incabinet Item List on Web Page', N'In Cabinet ItemList Fetched successfully', CAST(N'2020-04-23T14:42:03.550' AS DateTime), 4)
GO
INSERT [dbo].[tblAppEvents] ([EventID], [Event], [EventDesc], [SPName], [SPDesc], [LogMsg], [EnteredDate], [AppID]) VALUES (81, N'SearchItemDetails', N'Search item details in web', N'uspFetchItemDetails', N'User to search item details', N'Fetched item Details Successfully', CAST(N'2022-05-19T18:55:59.740' AS DateTime), 4)
GO
INSERT [dbo].[tblAppEvents] ([EventID], [Event], [EventDesc], [SPName], [SPDesc], [LogMsg], [EnteredDate], [AppID]) VALUES (92, N'SearchProductList', N'User can Search the Product List', N'uspFetchProductList', N'User can see the searched Product list on Web Page', N'Product List Searched successfully', CAST(N'2020-04-23T14:54:35.373' AS DateTime), 4)
GO
INSERT [dbo].[tblAppEvents] ([EventID], [Event], [EventDesc], [SPName], [SPDesc], [LogMsg], [EnteredDate], [AppID]) VALUES (93, N'UpdateProductList', N'User can update the Product List', N'uspFetchProductList', N'User can see the updated Product on Web Page', N'Product List Updated successfully', CAST(N'2020-04-23T14:54:35.377' AS DateTime), 4)
GO
INSERT [dbo].[tblAppEvents] ([EventID], [Event], [EventDesc], [SPName], [SPDesc], [LogMsg], [EnteredDate], [AppID]) VALUES (94, N'Login', N'Loign to App', NULL, NULL, N'Login Successfully', CAST(N'2023-06-21T08:28:48.623' AS DateTime), 3)
GO
INSERT [dbo].[tblAppEvents] ([EventID], [Event], [EventDesc], [SPName], [SPDesc], [LogMsg], [EnteredDate], [AppID]) VALUES (95, N'LogOut', N'Logout from App', NULL, NULL, N'Logout Successfully', CAST(N'2023-06-21T08:28:48.860' AS DateTime), 3)
GO
INSERT [dbo].[tblAppEvents] ([EventID], [Event], [EventDesc], [SPName], [SPDesc], [LogMsg], [EnteredDate], [AppID]) VALUES (96, N'ReturnItem', N'Return a Item with/without patient selection', N'usp822WebConsoleProcessItems', NULL, N'Item returned Successfully', CAST(N'2023-06-21T08:28:49.110' AS DateTime), 1)
GO
INSERT [dbo].[tblAppEvents] ([EventID], [Event], [EventDesc], [SPName], [SPDesc], [LogMsg], [EnteredDate], [AppID]) VALUES (97, N'RemoveItem', N'Removed Items with/without Patient Selection', N'usp822WebConsoleProcessItems', NULL, N'Item removed Successfully', CAST(N'2023-06-21T08:28:49.360' AS DateTime), 1)
GO
INSERT [dbo].[tblAppEvents] ([EventID], [Event], [EventDesc], [SPName], [SPDesc], [LogMsg], [EnteredDate], [AppID]) VALUES (98, N'View', N'User viewed the page', NULL, NULL, N'User viewed the page', CAST(N'2023-06-21T08:28:49.623' AS DateTime), 3)
GO
INSERT [dbo].[tblAppEvents] ([EventID], [Event], [EventDesc], [SPName], [SPDesc], [LogMsg], [EnteredDate], [AppID]) VALUES (99, N'Invalid Scan', N'Scanning invalid label in the workflow', NULL, NULL, N'Scanned invalid label in the workflow', CAST(N'2023-06-21T08:28:49.873' AS DateTime), 3)
GO
INSERT [dbo].[tblAppEvents] ([EventID], [Event], [EventDesc], [SPName], [SPDesc], [LogMsg], [EnteredDate], [AppID]) VALUES (100, N'WebActivity', N'To log web events', NULL, NULL, N'Transaction completed', CAST(N'2023-06-21T08:28:50.123' AS DateTime), 3)
GO
INSERT [dbo].[tblAppEvents] ([EventID], [Event], [EventDesc], [SPName], [SPDesc], [LogMsg], [EnteredDate], [AppID]) VALUES (101, N'UpdateBarcodeInventory', N'Update Barcode Inventory', N'uspScanBarcodeInventory', N'To fetch/Update barcode inventory for barcode scanning in cabinet screen ', N'Barcode Invnetory Updated Successfully', CAST(N'2022-08-29T23:11:34.067' AS DateTime), 1)
GO
INSERT [dbo].[tblAppEvents] ([EventID], [Event], [EventDesc], [SPName], [SPDesc], [LogMsg], [EnteredDate], [AppID]) VALUES (102, N'FetchBillOnlyItemDetails', N'Fetch Item Details for Bill Only One Time Use', N'uspFetchOneTimeUseDetails', N'Fetch Item Details for Bill Only One Time Use', NULL, CAST(N'2024-11-29T01:49:21.770' AS DateTime), 1)
GO
INSERT [dbo].[tblAppEvents] ([EventID], [Event], [EventDesc], [SPName], [SPDesc], [LogMsg], [EnteredDate], [AppID]) VALUES (103, N'UpdateOTUItemDetails', N'Update FDA Item Details into Database', N'uspSaveOneTimeUseDetails', N'Update FDA Item Details into Database', NULL, CAST(N'2024-12-03T01:03:05.593' AS DateTime), 1)
GO
INSERT [dbo].[tblAppEvents] ([EventID], [Event], [EventDesc], [SPName], [SPDesc], [LogMsg], [EnteredDate], [AppID]) VALUES (104, N'FetchItemDetailsByCatNo', N'Fetch Item Details Searched by Catalog Number', N'uspFetchItemDetailsByCatNo', N'Fetch Item Details Searched by Catalog Number like search', NULL, CAST(N'2024-12-19T17:31:50.280' AS DateTime), 1)
GO
SET IDENTITY_INSERT [dbo].[tblInventoryLog] ON 
GO
INSERT [dbo].[tblInventoryLog] ([InvHistoryID], [ItemHistoryID], [InvntoryID], [ProcessStatus], [ClusterID], [LocationID], [UpdatedBy], [EntryTypeID], [EnteredDate]) VALUES (1, 475006, 3530, N'P', 0, 1, 2, 1, CAST(N'2025-01-09T03:03:35.950' AS DateTime))
GO
INSERT [dbo].[tblInventoryLog] ([InvHistoryID], [ItemHistoryID], [InvntoryID], [ProcessStatus], [ClusterID], [LocationID], [UpdatedBy], [EntryTypeID], [EnteredDate]) VALUES (2, 475007, 3530, N'P', 0, 1, 2, 1, CAST(N'2025-01-09T03:03:48.127' AS DateTime))
GO
INSERT [dbo].[tblInventoryLog] ([InvHistoryID], [ItemHistoryID], [InvntoryID], [ProcessStatus], [ClusterID], [LocationID], [UpdatedBy], [EntryTypeID], [EnteredDate]) VALUES (3, 475008, 1660, N'P', 0, 1, 2, 1, CAST(N'2025-01-09T03:04:54.743' AS DateTime))
GO
INSERT [dbo].[tblInventoryLog] ([InvHistoryID], [ItemHistoryID], [InvntoryID], [ProcessStatus], [ClusterID], [LocationID], [UpdatedBy], [EntryTypeID], [EnteredDate]) VALUES (4, 475009, 1660, N'P', 0, 1, 2, 1, CAST(N'2025-01-09T03:05:07.500' AS DateTime))
GO
INSERT [dbo].[tblInventoryLog] ([InvHistoryID], [ItemHistoryID], [InvntoryID], [ProcessStatus], [ClusterID], [LocationID], [UpdatedBy], [EntryTypeID], [EnteredDate]) VALUES (5, 475014, NULL, N'P', 0, 1, 2, 1, CAST(N'2025-01-09T03:15:41.267' AS DateTime))
GO
INSERT [dbo].[tblInventoryLog] ([InvHistoryID], [ItemHistoryID], [InvntoryID], [ProcessStatus], [ClusterID], [LocationID], [UpdatedBy], [EntryTypeID], [EnteredDate]) VALUES (6, 475017, NULL, N'P', 0, 1, 2, 1, CAST(N'2025-01-09T03:17:58.470' AS DateTime))
GO
INSERT [dbo].[tblInventoryLog] ([InvHistoryID], [ItemHistoryID], [InvntoryID], [ProcessStatus], [ClusterID], [LocationID], [UpdatedBy], [EntryTypeID], [EnteredDate]) VALUES (7, 475018, NULL, N'P', 0, 1, 2, 1, CAST(N'2025-01-09T03:28:31.347' AS DateTime))
GO
INSERT [dbo].[tblInventoryLog] ([InvHistoryID], [ItemHistoryID], [InvntoryID], [ProcessStatus], [ClusterID], [LocationID], [UpdatedBy], [EntryTypeID], [EnteredDate]) VALUES (8, 475019, 1660, N'P', 0, 1, 2, 1, CAST(N'2025-01-09T03:31:16.470' AS DateTime))
GO
INSERT [dbo].[tblInventoryLog] ([InvHistoryID], [ItemHistoryID], [InvntoryID], [ProcessStatus], [ClusterID], [LocationID], [UpdatedBy], [EntryTypeID], [EnteredDate]) VALUES (9, 475020, 1660, N'P', 0, 1, 2, 1, CAST(N'2025-01-09T04:02:45.653' AS DateTime))
GO
INSERT [dbo].[tblInventoryLog] ([InvHistoryID], [ItemHistoryID], [InvntoryID], [ProcessStatus], [ClusterID], [LocationID], [UpdatedBy], [EntryTypeID], [EnteredDate]) VALUES (10, 475021, 1660, N'P', 0, 1, 2, 1, CAST(N'2025-01-09T04:03:18.083' AS DateTime))
GO
INSERT [dbo].[tblInventoryLog] ([InvHistoryID], [ItemHistoryID], [InvntoryID], [ProcessStatus], [ClusterID], [LocationID], [UpdatedBy], [EntryTypeID], [EnteredDate]) VALUES (11, 475022, 1660, N'P', 0, 1, 2, 1, CAST(N'2025-01-09T04:03:36.873' AS DateTime))
GO
INSERT [dbo].[tblInventoryLog] ([InvHistoryID], [ItemHistoryID], [InvntoryID], [ProcessStatus], [ClusterID], [LocationID], [UpdatedBy], [EntryTypeID], [EnteredDate]) VALUES (12, 475023, 1827, N'P', 0, 1, 2, 1, CAST(N'2025-01-09T04:05:01.153' AS DateTime))
GO
INSERT [dbo].[tblInventoryLog] ([InvHistoryID], [ItemHistoryID], [InvntoryID], [ProcessStatus], [ClusterID], [LocationID], [UpdatedBy], [EntryTypeID], [EnteredDate]) VALUES (13, 475024, 1827, N'P', 0, 1, 2, 1, CAST(N'2025-01-09T04:05:11.237' AS DateTime))
GO
INSERT [dbo].[tblInventoryLog] ([InvHistoryID], [ItemHistoryID], [InvntoryID], [ProcessStatus], [ClusterID], [LocationID], [UpdatedBy], [EntryTypeID], [EnteredDate]) VALUES (14, 475025, 1827, N'P', 0, 1, 2, 1, CAST(N'2025-01-09T04:05:51.847' AS DateTime))
GO
INSERT [dbo].[tblInventoryLog] ([InvHistoryID], [ItemHistoryID], [InvntoryID], [ProcessStatus], [ClusterID], [LocationID], [UpdatedBy], [EntryTypeID], [EnteredDate]) VALUES (15, 475026, 1827, N'P', 0, 1, 2, 1, CAST(N'2025-01-09T04:07:09.903' AS DateTime))
GO
INSERT [dbo].[tblInventoryLog] ([InvHistoryID], [ItemHistoryID], [InvntoryID], [ProcessStatus], [ClusterID], [LocationID], [UpdatedBy], [EntryTypeID], [EnteredDate]) VALUES (16, 475027, 1827, N'P', 0, 1, 2, 1, CAST(N'2025-01-09T04:08:15.530' AS DateTime))
GO
INSERT [dbo].[tblInventoryLog] ([InvHistoryID], [ItemHistoryID], [InvntoryID], [ProcessStatus], [ClusterID], [LocationID], [UpdatedBy], [EntryTypeID], [EnteredDate]) VALUES (17, 475028, 1827, N'P', 0, 1, 2, 1, CAST(N'2025-01-09T04:08:27.200' AS DateTime))
GO
SET IDENTITY_INSERT [dbo].[tblInventoryLog] OFF
GO
SET IDENTITY_INSERT [dbo].[tblModules] ON 
GO
INSERT [dbo].[tblModules] ([MODULE_ID], [MODULE_NAME], [DESCRIPTION], [LINK], [PARENT_MODULE_ID], [PROJECT_ID], [ORDER_ID], [MODULE_ICON], [ISENABLED]) VALUES (1, N'Admin', N'Admin Module', N'#', 0, 1, 3, N'fas fa-user-cog', 1)
GO
INSERT [dbo].[tblModules] ([MODULE_ID], [MODULE_NAME], [DESCRIPTION], [LINK], [PARENT_MODULE_ID], [PROJECT_ID], [ORDER_ID], [MODULE_ICON], [ISENABLED]) VALUES (2, N'Manage Devices', N'Manage Devices Module', N'/Managedevices', 1, 1, 2, NULL, 1)
GO
INSERT [dbo].[tblModules] ([MODULE_ID], [MODULE_NAME], [DESCRIPTION], [LINK], [PARENT_MODULE_ID], [PROJECT_ID], [ORDER_ID], [MODULE_ICON], [ISENABLED]) VALUES (3, N'Reports', N'Reports', N'/ManageReports', 0, 1, 2, N'fas fa-file-invoice', 1)
GO
INSERT [dbo].[tblModules] ([MODULE_ID], [MODULE_NAME], [DESCRIPTION], [LINK], [PARENT_MODULE_ID], [PROJECT_ID], [ORDER_ID], [MODULE_ICON], [ISENABLED]) VALUES (4, N'Manage Patients', N'Patients Module', N'/ManagePatients/AddNewPatient', 0, 1, 1, N'fas fa-procedures', 1)
GO
INSERT [dbo].[tblModules] ([MODULE_ID], [MODULE_NAME], [DESCRIPTION], [LINK], [PARENT_MODULE_ID], [PROJECT_ID], [ORDER_ID], [MODULE_ICON], [ISENABLED]) VALUES (5, N'Use Items', N'Use Items', N'/ManagePatients', 0, 1, 1, N'fas fa-calendar-minus', 1)
GO
INSERT [dbo].[tblModules] ([MODULE_ID], [MODULE_NAME], [DESCRIPTION], [LINK], [PARENT_MODULE_ID], [PROJECT_ID], [ORDER_ID], [MODULE_ICON], [ISENABLED]) VALUES (6, N'Manage Roles', N'Manage Roles', N'/ManageRoles', 1, 1, 1, NULL, 1)
GO
INSERT [dbo].[tblModules] ([MODULE_ID], [MODULE_NAME], [DESCRIPTION], [LINK], [PARENT_MODULE_ID], [PROJECT_ID], [ORDER_ID], [MODULE_ICON], [ISENABLED]) VALUES (7, N'Manage Features', N'Manage Features', N'/ManageFeatures', 1, 1, 2, NULL, 1)
GO
INSERT [dbo].[tblModules] ([MODULE_ID], [MODULE_NAME], [DESCRIPTION], [LINK], [PARENT_MODULE_ID], [PROJECT_ID], [ORDER_ID], [MODULE_ICON], [ISENABLED]) VALUES (8, N'Manage Users', N'Manage Users', N'/ManageUsers', 1, 1, 3, NULL, 1)
GO
INSERT [dbo].[tblModules] ([MODULE_ID], [MODULE_NAME], [DESCRIPTION], [LINK], [PARENT_MODULE_ID], [PROJECT_ID], [ORDER_ID], [MODULE_ICON], [ISENABLED]) VALUES (9, N'Remove Item from Patient Record', N'Remove Item from Patient Record', N'/ReturnItem', 0, 1, 1, N'fas fa-calendar-plus', 1)
GO
INSERT [dbo].[tblModules] ([MODULE_ID], [MODULE_NAME], [DESCRIPTION], [LINK], [PARENT_MODULE_ID], [PROJECT_ID], [ORDER_ID], [MODULE_ICON], [ISENABLED]) VALUES (10, N'Print RFID', N'Print RFID', N'/index/irisprinter', 0, 1, 2, N'fas fa-print', 0)
GO
INSERT [dbo].[tblModules] ([MODULE_ID], [MODULE_NAME], [DESCRIPTION], [LINK], [PARENT_MODULE_ID], [PROJECT_ID], [ORDER_ID], [MODULE_ICON], [ISENABLED]) VALUES (11, N'Two Factor Authentication', N'Two Factor Authentication module', N'#', 0, 1, 1, NULL, 0)
GO
SET IDENTITY_INSERT [dbo].[tblModules] OFF
GO
SET IDENTITY_INSERT [dbo].[tblReportRBAC] ON 
GO
INSERT [dbo].[tblReportRBAC] ([ReportRBACID], [ReportID], [UserID], [Status], [rowguid]) VALUES (1, 1, 523, 1, N'b6d7fb81-c414-44dc-a0a2-bcfff9d7756f')
GO
INSERT [dbo].[tblReportRBAC] ([ReportRBACID], [ReportID], [UserID], [Status], [rowguid]) VALUES (2, 2, 523, 1, N'751b8cd3-4382-45cc-98d6-97f637ec9892')
GO
INSERT [dbo].[tblReportRBAC] ([ReportRBACID], [ReportID], [UserID], [Status], [rowguid]) VALUES (1009, 1, 639, 1, N'402daaaf-5c5e-491c-8641-c70c95a86694')
GO
INSERT [dbo].[tblReportRBAC] ([ReportRBACID], [ReportID], [UserID], [Status], [rowguid]) VALUES (1015, 1, 647, 1, N'beb8f84f-2cd6-4161-9429-ee6b849b61ef')
GO
INSERT [dbo].[tblReportRBAC] ([ReportRBACID], [ReportID], [UserID], [Status], [rowguid]) VALUES (1016, 1, 0, 1, N'413752e9-6c9b-4610-ac1e-5c6816c05169')
GO
INSERT [dbo].[tblReportRBAC] ([ReportRBACID], [ReportID], [UserID], [Status], [rowguid]) VALUES (1017, 2, 0, 1, N'6a2434d5-45cd-413b-9162-2b7aa3147cd8')
GO
INSERT [dbo].[tblReportRBAC] ([ReportRBACID], [ReportID], [UserID], [Status], [rowguid]) VALUES (1018, 3, 0, 1, N'76e76283-6edb-4b68-84c8-466356b071a3')
GO
INSERT [dbo].[tblReportRBAC] ([ReportRBACID], [ReportID], [UserID], [Status], [rowguid]) VALUES (1019, 4, 0, 1, N'3dffa444-3fc1-440b-9592-8b402416fb99')
GO
SET IDENTITY_INSERT [dbo].[tblReportRBAC] OFF
GO
INSERT [dbo].[tblReportStatus] ([ID], [Status]) VALUES (0, N'Inactive')
GO
INSERT [dbo].[tblReportStatus] ([ID], [Status]) VALUES (1, N'Active')
GO
SET IDENTITY_INSERT [dbo].[tblStandardTimeZone] ON 
GO
INSERT [dbo].[tblStandardTimeZone] ([StandardTimeZoneID], [TimezoneDescription], [Abbreviation], [StandardTimeZone], [UTCStandard], [Country], [EnteredDate]) VALUES (1, N'Coordinated Universal Time', N'UTC', N'UTC', N'UTC+00', N'Bouvet Island', CAST(N'2022-08-29T23:11:32.537' AS DateTime))
GO
INSERT [dbo].[tblStandardTimeZone] ([StandardTimeZoneID], [TimezoneDescription], [Abbreviation], [StandardTimeZone], [UTCStandard], [Country], [EnteredDate]) VALUES (2, N'Eastern Time (US & Canada)', N'EST', N'Eastern Standard Time', N'UTC-14400', N'Canada', CAST(N'2022-08-29T23:11:32.550' AS DateTime))
GO
INSERT [dbo].[tblStandardTimeZone] ([StandardTimeZoneID], [TimezoneDescription], [Abbreviation], [StandardTimeZone], [UTCStandard], [Country], [EnteredDate]) VALUES (3, N'Abu Dhabi, Muscat', N'AST', N'Arabian Standard Time', N'UTC+10800', N'Oman', CAST(N'2022-08-29T23:11:32.550' AS DateTime))
GO
INSERT [dbo].[tblStandardTimeZone] ([StandardTimeZoneID], [TimezoneDescription], [Abbreviation], [StandardTimeZone], [UTCStandard], [Country], [EnteredDate]) VALUES (4, N'Chennai, Kolkata, Mumbai, New Delhi', N'IST', N'Indian Standard Time', N'UTC+19800', N'India', CAST(N'2022-08-29T23:11:32.550' AS DateTime))
GO
SET IDENTITY_INSERT [dbo].[tblStandardTimeZone] OFF
GO
SET IDENTITY_INSERT [dbo].[tblUserBookMarks] ON 
GO
INSERT [dbo].[tblUserBookMarks] ([BookmarkID], [UserID], [Bookmark], [BookmarkURL], [DateAdded]) VALUES (1, 2, N'Manage Patients', N'/ManagePatients/AddNewPatient', CAST(N'2024-11-14T19:05:54.907' AS DateTime))
GO
SET IDENTITY_INSERT [dbo].[tblUserBookMarks] OFF
GO
SET IDENTITY_INSERT [dbo].[tblUserRBAC] ON 
GO
INSERT [dbo].[tblUserRBAC] ([UserRBACID], [UserGroupID], [ModuleID], [Status], [rowguid]) VALUES (1, 3, 4, 1, N'c28bd842-bcb6-4bed-adf3-b63317ca3092')
GO
INSERT [dbo].[tblUserRBAC] ([UserRBACID], [UserGroupID], [ModuleID], [Status], [rowguid]) VALUES (2, 3, 5, 1, N'48fe6417-60d5-4c12-b356-6fc10db8f61c')
GO
INSERT [dbo].[tblUserRBAC] ([UserRBACID], [UserGroupID], [ModuleID], [Status], [rowguid]) VALUES (3, 3, 9, 1, N'223c16c4-d5fc-4ced-8f9a-5771dc5045a6')
GO
INSERT [dbo].[tblUserRBAC] ([UserRBACID], [UserGroupID], [ModuleID], [Status], [rowguid]) VALUES (4, 1, 1, 1, N'3850f28a-ade3-493b-ae53-3b76a1b11d05')
GO
INSERT [dbo].[tblUserRBAC] ([UserRBACID], [UserGroupID], [ModuleID], [Status], [rowguid]) VALUES (5, 1, 2, 0, N'c9dd7ed1-0e34-4931-8768-8190f661ff42')
GO
INSERT [dbo].[tblUserRBAC] ([UserRBACID], [UserGroupID], [ModuleID], [Status], [rowguid]) VALUES (6, 1, 6, 1, N'958a8b3a-ce3d-4d84-9f8a-e608dc188c03')
GO
INSERT [dbo].[tblUserRBAC] ([UserRBACID], [UserGroupID], [ModuleID], [Status], [rowguid]) VALUES (1002, 3, 1, 0, N'612d8035-2249-4fef-918d-bc35cf3a1059')
GO
INSERT [dbo].[tblUserRBAC] ([UserRBACID], [UserGroupID], [ModuleID], [Status], [rowguid]) VALUES (1003, 3, 2, 0, N'60e68cca-4673-49b5-b40d-b65cd0114dd8')
GO
INSERT [dbo].[tblUserRBAC] ([UserRBACID], [UserGroupID], [ModuleID], [Status], [rowguid]) VALUES (1004, 3, 6, 0, N'65503e70-d289-44e0-8825-95cf113ac412')
GO
INSERT [dbo].[tblUserRBAC] ([UserRBACID], [UserGroupID], [ModuleID], [Status], [rowguid]) VALUES (1005, 3, 7, 0, N'9f238275-c87a-40b4-b488-fa5a05e2e0a9')
GO
INSERT [dbo].[tblUserRBAC] ([UserRBACID], [UserGroupID], [ModuleID], [Status], [rowguid]) VALUES (1006, 3, 8, 0, N'a7024741-7086-441e-96d6-282adea7af8f')
GO
INSERT [dbo].[tblUserRBAC] ([UserRBACID], [UserGroupID], [ModuleID], [Status], [rowguid]) VALUES (1007, 3, 3, 0, N'0cbbefc5-8efb-4a3d-b98f-8ecec1c4287d')
GO
INSERT [dbo].[tblUserRBAC] ([UserRBACID], [UserGroupID], [ModuleID], [Status], [rowguid]) VALUES (7, 1, 7, 1, N'08fe1ac1-d62b-4e4c-a2b7-81196fe3ba5c')
GO
INSERT [dbo].[tblUserRBAC] ([UserRBACID], [UserGroupID], [ModuleID], [Status], [rowguid]) VALUES (8, 1, 8, 1, N'8ce4bd64-b729-474b-988d-0f7d47d7a1c8')
GO
INSERT [dbo].[tblUserRBAC] ([UserRBACID], [UserGroupID], [ModuleID], [Status], [rowguid]) VALUES (9, 1, 3, 1, N'e4eff23d-e784-49db-819e-09f4727893f8')
GO
INSERT [dbo].[tblUserRBAC] ([UserRBACID], [UserGroupID], [ModuleID], [Status], [rowguid]) VALUES (10, 1, 4, 1, N'294cd73c-495a-4104-b810-cf042bc2b614')
GO
INSERT [dbo].[tblUserRBAC] ([UserRBACID], [UserGroupID], [ModuleID], [Status], [rowguid]) VALUES (11, 1, 5, 1, N'bdbf45fa-3e42-4f02-b60b-fea6a91b12a4')
GO
INSERT [dbo].[tblUserRBAC] ([UserRBACID], [UserGroupID], [ModuleID], [Status], [rowguid]) VALUES (12, 1, 9, 1, N'8e38bcc5-4a29-4ef1-a765-************')
GO
INSERT [dbo].[tblUserRBAC] ([UserRBACID], [UserGroupID], [ModuleID], [Status], [rowguid]) VALUES (1008, 1, 10, 1, N'7131062c-e5e5-4be3-bccc-8f4fc4ad9a66')
GO
INSERT [dbo].[tblUserRBAC] ([UserRBACID], [UserGroupID], [ModuleID], [Status], [rowguid]) VALUES (1009, 3, 10, 1, N'64fdb047-26a9-4174-b2db-b881678e7520')
GO
SET IDENTITY_INSERT [dbo].[tblUserRBAC] OFF
GO
SET IDENTITY_INSERT [dbo].[tblUsersRecentHistory] ON 
GO
INSERT [dbo].[tblUsersRecentHistory] ([ID], [UserID], [ModuleName], [ModuleLink], [LastActivityDate]) VALUES (49, 2, N'Manage Roles', N'/ManageRoles', CAST(N'2024-11-14T18:59:58.897' AS DateTime))
GO
INSERT [dbo].[tblUsersRecentHistory] ([ID], [UserID], [ModuleName], [ModuleLink], [LastActivityDate]) VALUES (51, 2, N'Manage Features', N'/ManageFeatures', CAST(N'2024-11-14T19:00:17.963' AS DateTime))
GO
INSERT [dbo].[tblUsersRecentHistory] ([ID], [UserID], [ModuleName], [ModuleLink], [LastActivityDate]) VALUES (60, 2, N'Manage Users', N'/ManageUsers', CAST(N'2024-11-14T19:05:46.417' AS DateTime))
GO
INSERT [dbo].[tblUsersRecentHistory] ([ID], [UserID], [ModuleName], [ModuleLink], [LastActivityDate]) VALUES (61, 2, N'Manage Devices', N'/Managedevices', CAST(N'2024-11-14T19:05:49.040' AS DateTime))
GO
INSERT [dbo].[tblUsersRecentHistory] ([ID], [UserID], [ModuleName], [ModuleLink], [LastActivityDate]) VALUES (217, 18, N'Use Items', N'/ManagePatients', CAST(N'2024-11-20T21:20:22.730' AS DateTime))
GO
INSERT [dbo].[tblUsersRecentHistory] ([ID], [UserID], [ModuleName], [ModuleLink], [LastActivityDate]) VALUES (218, 597, N'Use Items', N'/ManagePatients', CAST(N'2024-11-20T21:21:19.110' AS DateTime))
GO
INSERT [dbo].[tblUsersRecentHistory] ([ID], [UserID], [ModuleName], [ModuleLink], [LastActivityDate]) VALUES (220, 0, N'Use Items', N'/ManagePatients', CAST(N'2024-11-20T21:21:48.363' AS DateTime))
GO
INSERT [dbo].[tblUsersRecentHistory] ([ID], [UserID], [ModuleName], [ModuleLink], [LastActivityDate]) VALUES (224, 36, N'Use Items', N'/ManagePatients', CAST(N'2024-11-21T16:03:41.623' AS DateTime))
GO
INSERT [dbo].[tblUsersRecentHistory] ([ID], [UserID], [ModuleName], [ModuleLink], [LastActivityDate]) VALUES (280, 3, N'Use Items', N'/ManagePatients', CAST(N'2024-11-26T13:26:23.513' AS DateTime))
GO
INSERT [dbo].[tblUsersRecentHistory] ([ID], [UserID], [ModuleName], [ModuleLink], [LastActivityDate]) VALUES (1825, 2, N'Reports', N'/ManageReports', CAST(N'2024-12-17T22:30:02.430' AS DateTime))
GO
INSERT [dbo].[tblUsersRecentHistory] ([ID], [UserID], [ModuleName], [ModuleLink], [LastActivityDate]) VALUES (2098, 831, N'Use Items', N'/ManagePatients', CAST(N'2024-12-19T11:37:51.560' AS DateTime))
GO
INSERT [dbo].[tblUsersRecentHistory] ([ID], [UserID], [ModuleName], [ModuleLink], [LastActivityDate]) VALUES (2217, 832, N'Use Items', N'/ManagePatients', CAST(N'2024-12-19T15:16:58.200' AS DateTime))
GO
INSERT [dbo].[tblUsersRecentHistory] ([ID], [UserID], [ModuleName], [ModuleLink], [LastActivityDate]) VALUES (2218, 833, N'Use Items', N'/ManagePatients', CAST(N'2024-12-19T15:18:12.870' AS DateTime))
GO
INSERT [dbo].[tblUsersRecentHistory] ([ID], [UserID], [ModuleName], [ModuleLink], [LastActivityDate]) VALUES (2219, 834, N'Use Items', N'/ManagePatients', CAST(N'2024-12-19T15:25:38.730' AS DateTime))
GO
INSERT [dbo].[tblUsersRecentHistory] ([ID], [UserID], [ModuleName], [ModuleLink], [LastActivityDate]) VALUES (2238, 836, N'Use Items', N'/ManagePatients', CAST(N'2024-12-19T19:39:30.260' AS DateTime))
GO
INSERT [dbo].[tblUsersRecentHistory] ([ID], [UserID], [ModuleName], [ModuleLink], [LastActivityDate]) VALUES (3358, 2, N'Manage Patients', N'/ManagePatients/AddNewPatient', CAST(N'2024-12-20T18:35:17.593' AS DateTime))
GO
INSERT [dbo].[tblUsersRecentHistory] ([ID], [UserID], [ModuleName], [ModuleLink], [LastActivityDate]) VALUES (4145, 2, N'Use Items', N'/ManagePatients', CAST(N'2025-01-08T22:35:45.803' AS DateTime))
GO
SET IDENTITY_INSERT [dbo].[tblUsersRecentHistory] OFF
GO
