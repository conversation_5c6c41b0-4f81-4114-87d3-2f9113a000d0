
/*
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing,
* software distributed under the License is distributed on an
* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
* KIND, either express or implied.  See the License for the
* specific language governing permissions and limitations
* under the License.
*/


!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e(t.echarts={})}(this,function(t){"use strict";var e=2311,i=function(){return e++},v="object"==typeof wx&&"function"==typeof wx.getSystemInfoSync?{browser:{},os:{},node:!1,wxa:!0,canvasSupported:!0,svgSupported:!1,touchEventsSupported:!0,domSupported:!1}:"undefined"==typeof document&&"undefined"!=typeof self?{browser:{},os:{},node:!1,worker:!0,canvasSupported:!0,domSupported:!1}:"undefined"==typeof navigator?{browser:{},os:{},node:!0,worker:!1,canvasSupported:!0,svgSupported:!0,domSupported:!1}:function(t){var e={},n=t.match(/Firefox\/([\d.]+)/),i=t.match(/MSIE\s([\d.]+)/)||t.match(/Trident\/.+?rv:(([\d.]+))/),r=t.match(/Edge\/([\d.]+)/),o=/micromessenger/i.test(t);n&&(e.firefox=!0,e.version=n[1]);i&&(e.ie=!0,e.version=i[1]);r&&(e.edge=!0,e.version=r[1]);o&&(e.weChat=!0);return{browser:e,os:{},node:!1,canvasSupported:!!document.createElement("canvas").getContext,svgSupported:"undefined"!=typeof SVGRect,touchEventsSupported:"ontouchstart"in window&&!e.ie&&!e.edge,pointerEventsSupported:"onpointerdown"in window&&(e.edge||e.ie&&11<=e.version),domSupported:"undefined"!=typeof document}}(navigator.userAgent);var s={"[object Function]":1,"[object RegExp]":1,"[object Date]":1,"[object Error]":1,"[object CanvasGradient]":1,"[object CanvasPattern]":1,"[object Image]":1,"[object Canvas]":1},l={"[object Int8Array]":1,"[object Uint8Array]":1,"[object Uint8ClampedArray]":1,"[object Int16Array]":1,"[object Uint16Array]":1,"[object Int32Array]":1,"[object Uint32Array]":1,"[object Float32Array]":1,"[object Float64Array]":1},u=Object.prototype.toString,n=Array.prototype,a=n.forEach,h=n.filter,r=n.slice,c=n.map,d=n.reduce,o={};function f(t,e){"createCanvas"===t&&(g=null),o[t]=e}function b(t){if(null==t||"object"!=typeof t)return t;var e=t,n=u.call(t);if("[object Array]"===n){if(!$(t)){e=[];for(var i=0,r=t.length;i<r;i++)e[i]=b(t[i])}}else if(l[n]){if(!$(t)){var o=t.constructor;if(t.constructor.from)e=o.from(t);else{e=new o(t.length);for(i=0,r=t.length;i<r;i++)e[i]=b(t[i])}}}else if(!s[n]&&!$(t)&&!V(t))for(var a in e={},t)t.hasOwnProperty(a)&&(e[a]=b(t[a]));return e}function m(t,e,n){if(!N(e)||!N(t))return n?b(e):t;for(var i in e)if(e.hasOwnProperty(i)){var r=t[i],o=e[i];!N(o)||!N(r)||O(o)||O(r)||V(o)||V(r)||R(o)||R(r)||$(o)||$(r)?!n&&i in t||(t[i]=b(e[i])):m(r,o,n)}return t}function p(t,e){for(var n=t[0],i=1,r=t.length;i<r;i++)n=m(n,t[i],e);return n}function k(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n]);return t}function A(t,e,n){for(var i in e)e.hasOwnProperty(i)&&(n?null!=e[i]:null==t[i])&&(t[i]=e[i]);return t}function y(){return o.createCanvas()}var g;function _(){return g=g||y().getContext("2d")}function x(t,e){if(t){if(t.indexOf)return t.indexOf(e);for(var n=0,i=t.length;n<i;n++)if(t[n]===e)return n}return-1}function w(t,e){var n=t.prototype;function i(){}for(var r in i.prototype=e.prototype,t.prototype=new i,n)n.hasOwnProperty(r)&&(t.prototype[r]=n[r]);(t.prototype.constructor=t).superClass=e}function S(t,e,n){A(t="prototype"in t?t.prototype:t,e="prototype"in e?e.prototype:e,n)}function L(t){if(t)return"string"!=typeof t&&"number"==typeof t.length}function D(t,e,n){if(t&&e)if(t.forEach&&t.forEach===a)t.forEach(e,n);else if(t.length===+t.length)for(var i=0,r=t.length;i<r;i++)e.call(n,t[i],i,t);else for(var o in t)t.hasOwnProperty(o)&&e.call(n,t[o],o,t)}function P(t,e,n){if(t&&e){if(t.map&&t.map===c)return t.map(e,n);for(var i=[],r=0,o=t.length;r<o;r++)i.push(e.call(n,t[r],r,t));return i}}function M(t,e,n,i){if(t&&e){if(t.reduce&&t.reduce===d)return t.reduce(e,n,i);for(var r=0,o=t.length;r<o;r++)n=e.call(i,n,t[r],r,t);return n}}function I(t,e,n){if(t&&e){if(t.filter&&t.filter===h)return t.filter(e,n);for(var i=[],r=0,o=t.length;r<o;r++)e.call(n,t[r],r,t)&&i.push(t[r]);return i}}function C(t,e){var n=r.call(arguments,2);return function(){return t.apply(e,n.concat(r.call(arguments)))}}function T(t){var e=r.call(arguments,1);return function(){return t.apply(this,e.concat(r.call(arguments)))}}function O(t){return"[object Array]"===u.call(t)}function E(t){return"function"==typeof t}function z(t){return"[object String]"===u.call(t)}function N(t){var e=typeof t;return"function"==e||!!t&&"object"==e}function R(t){return!!s[u.call(t)]}function B(t){return!!l[u.call(t)]}function V(t){return"object"==typeof t&&"number"==typeof t.nodeType&&"object"==typeof t.ownerDocument}function F(t){return t!=t}function H(t){for(var e=0,n=arguments.length;e<n;e++)if(null!=arguments[e])return arguments[e]}function W(t,e){return null!=t?t:e}function G(t,e,n){return null!=t?t:null!=e?e:n}function Z(){return Function.call.apply(r,arguments)}function U(t){if("number"==typeof t)return[t,t,t,t];var e=t.length;return 2===e?[t[0],t[1],t[0],t[1]]:3===e?[t[0],t[1],t[2],t[1]]:t}function X(t,e){if(!t)throw new Error(e)}function Y(t){return null==t?null:"function"==typeof t.trim?t.trim():t.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")}o.createCanvas=function(){return document.createElement("canvas")};var j="__ec_primitive__";function q(t){t[j]=!0}function $(t){return t[j]}function K(t){var n=O(t);this.data={};var i=this;function e(t,e){n?i.set(t,e):i.set(e,t)}t instanceof K?t.each(e):t&&D(t,e)}function Q(t){return new K(t)}function J(){}K.prototype={constructor:K,get:function(t){return this.data.hasOwnProperty(t)?this.data[t]:null},set:function(t,e){return this.data[t]=e},each:function(t,e){for(var n in void 0!==e&&(t=C(t,e)),this.data)this.data.hasOwnProperty(n)&&t(this.data[n],n)},removeKey:function(t){delete this.data[t]}};var tt=(Object.freeze||Object)({$override:f,clone:b,merge:m,mergeAll:p,extend:k,defaults:A,createCanvas:y,getContext:_,indexOf:x,inherits:w,mixin:S,isArrayLike:L,each:D,map:P,reduce:M,filter:I,find:function(t,e,n){if(t&&e)for(var i=0,r=t.length;i<r;i++)if(e.call(n,t[i],i,t))return t[i]},bind:C,curry:T,isArray:O,isFunction:E,isString:z,isObject:N,isBuiltInObject:R,isTypedArray:B,isDom:V,eqNaN:F,retrieve:H,retrieve2:W,retrieve3:G,slice:Z,normalizeCssArray:U,assert:X,trim:Y,setAsPrimitive:q,isPrimitive:$,createHashMap:Q,concatArray:function(t,e){for(var n=new t.constructor(t.length+e.length),i=0;i<t.length;i++)n[i]=t[i];var r=t.length;for(i=0;i<e.length;i++)n[i+r]=e[i];return n},noop:J}),et="undefined"==typeof Float32Array?Array:Float32Array;function nt(t,e){var n=new et(2);return null==t&&(t=0),null==e&&(e=0),n[0]=t,n[1]=e,n}function it(t,e){return t[0]=e[0],t[1]=e[1],t}function rt(t){var e=new et(2);return e[0]=t[0],e[1]=t[1],e}function ot(t,e,n){return t[0]=e[0]+n[0],t[1]=e[1]+n[1],t}function at(t,e,n,i){return t[0]=e[0]+n[0]*i,t[1]=e[1]+n[1]*i,t}function st(t,e,n){return t[0]=e[0]-n[0],t[1]=e[1]-n[1],t}function lt(t){return Math.sqrt(ht(t))}var ut=lt;function ht(t){return t[0]*t[0]+t[1]*t[1]}var ct=ht;function dt(t,e,n){return t[0]=e[0]*n,t[1]=e[1]*n,t}function ft(t,e){var n=lt(e);return 0===n?(t[0]=0,t[1]=0):(t[0]=e[0]/n,t[1]=e[1]/n),t}function pt(t,e){return Math.sqrt((t[0]-e[0])*(t[0]-e[0])+(t[1]-e[1])*(t[1]-e[1]))}var gt=pt;function mt(t,e){return(t[0]-e[0])*(t[0]-e[0])+(t[1]-e[1])*(t[1]-e[1])}var vt=mt;function yt(t,e,n){var i=e[0],r=e[1];return t[0]=n[0]*i+n[2]*r+n[4],t[1]=n[1]*i+n[3]*r+n[5],t}function _t(t,e,n){return t[0]=Math.min(e[0],n[0]),t[1]=Math.min(e[1],n[1]),t}function xt(t,e,n){return t[0]=Math.max(e[0],n[0]),t[1]=Math.max(e[1],n[1]),t}var wt=(Object.freeze||Object)({create:nt,copy:it,clone:rt,set:function(t,e,n){return t[0]=e,t[1]=n,t},add:ot,scaleAndAdd:at,sub:st,len:lt,length:ut,lenSquare:ht,lengthSquare:ct,mul:function(t,e,n){return t[0]=e[0]*n[0],t[1]=e[1]*n[1],t},div:function(t,e,n){return t[0]=e[0]/n[0],t[1]=e[1]/n[1],t},dot:function(t,e){return t[0]*e[0]+t[1]*e[1]},scale:dt,normalize:ft,distance:pt,dist:gt,distanceSquare:mt,distSquare:vt,negate:function(t,e){return t[0]=-e[0],t[1]=-e[1],t},lerp:function(t,e,n,i){return t[0]=e[0]+i*(n[0]-e[0]),t[1]=e[1]+i*(n[1]-e[1]),t},applyTransform:yt,min:_t,max:xt});function bt(){this.on("mousedown",this._dragStart,this),this.on("mousemove",this._drag,this),this.on("mouseup",this._dragEnd,this)}function St(t,e){return{target:t,topTarget:e&&e.topTarget}}bt.prototype={constructor:bt,_dragStart:function(t){for(var e=t.target;e&&!e.draggable;)e=e.parent;e&&((this._draggingTarget=e).dragging=!0,this._x=t.offsetX,this._y=t.offsetY,this.dispatchToElement(St(e,t),"dragstart",t.event))},_drag:function(t){var e=this._draggingTarget;if(e){var n=t.offsetX,i=t.offsetY,r=n-this._x,o=i-this._y;this._x=n,this._y=i,e.drift(r,o,t),this.dispatchToElement(St(e,t),"drag",t.event);var a=this.findHover(n,i,e).target,s=this._dropTarget;e!==(this._dropTarget=a)&&(s&&a!==s&&this.dispatchToElement(St(s,t),"dragleave",t.event),a&&a!==s&&this.dispatchToElement(St(a,t),"dragenter",t.event))}},_dragEnd:function(t){var e=this._draggingTarget;e&&(e.dragging=!1),this.dispatchToElement(St(e,t),"dragend",t.event),this._dropTarget&&this.dispatchToElement(St(this._dropTarget,t),"drop",t.event),this._draggingTarget=null,this._dropTarget=null}};var Mt=Array.prototype.slice,It=function(t){this._$handlers={},this._$eventProcessor=t};function Ct(t,e,n,i,r,o){var a=t._$handlers;if("function"==typeof n&&(r=i,i=n,n=null),!i||!e)return t;n=function(t,e){var n=t._$eventProcessor;return null!=e&&n&&n.normalizeQuery&&(e=n.normalizeQuery(e)),e}(t,n),a[e]||(a[e]=[]);for(var s=0;s<a[e].length;s++)if(a[e][s].h===i)return t;var l={h:i,one:o,query:n,ctx:r||t,callAtLast:i.zrEventfulCallAtLast},u=a[e].length-1,h=a[e][u];return h&&h.callAtLast?a[e].splice(u,0,l):a[e].push(l),t}It.prototype={constructor:It,one:function(t,e,n,i){return Ct(this,t,e,n,i,!0)},on:function(t,e,n,i){return Ct(this,t,e,n,i,!1)},isSilent:function(t){var e=this._$handlers;return!e[t]||!e[t].length},off:function(t,e){var n=this._$handlers;if(!t)return this._$handlers={},this;if(e){if(n[t]){for(var i=[],r=0,o=n[t].length;r<o;r++)n[t][r].h!==e&&i.push(n[t][r]);n[t]=i}n[t]&&0===n[t].length&&delete n[t]}else delete n[t];return this},trigger:function(t){var e=this._$handlers[t],n=this._$eventProcessor;if(e){var i=arguments,r=i.length;3<r&&(i=Mt.call(i,1));for(var o=e.length,a=0;a<o;){var s=e[a];if(n&&n.filter&&null!=s.query&&!n.filter(t,s.query))a++;else{switch(r){case 1:s.h.call(s.ctx);break;case 2:s.h.call(s.ctx,i[1]);break;case 3:s.h.call(s.ctx,i[1],i[2]);break;default:s.h.apply(s.ctx,i)}s.one?(e.splice(a,1),o--):a++}}}return n&&n.afterTrigger&&n.afterTrigger(t),this},triggerWithContext:function(t){var e=this._$handlers[t],n=this._$eventProcessor;if(e){var i=arguments,r=i.length;4<r&&(i=Mt.call(i,1,i.length-1));for(var o=i[i.length-1],a=e.length,s=0;s<a;){var l=e[s];if(n&&n.filter&&null!=l.query&&!n.filter(t,l.query))s++;else{switch(r){case 1:l.h.call(o);break;case 2:l.h.call(o,i[1]);break;case 3:l.h.call(o,i[1],i[2]);break;default:l.h.apply(o,i)}l.one?(e.splice(s,1),a--):s++}}}return n&&n.afterTrigger&&n.afterTrigger(t),this}};var Tt=Math.log(2);function At(t,e,n,i,r,o){var a=i+"-"+r,s=t.length;if(o.hasOwnProperty(a))return o[a];if(1===e){var l=Math.round(Math.log((1<<s)-1&~r)/Tt);return t[n][l]}for(var u=i|1<<n,h=n+1;i&1<<h;)h++;for(var c=0,d=0,f=0;d<s;d++){var p=1<<d;p&r||(c+=(f%2?-1:1)*t[n][d]*At(t,e-1,h,u,r|p,o),f++)}return o[a]=c}function Dt(t,e){var n=[[t[0],t[1],1,0,0,0,-e[0]*t[0],-e[0]*t[1]],[0,0,0,t[0],t[1],1,-e[1]*t[0],-e[1]*t[1]],[t[2],t[3],1,0,0,0,-e[2]*t[2],-e[2]*t[3]],[0,0,0,t[2],t[3],1,-e[3]*t[2],-e[3]*t[3]],[t[4],t[5],1,0,0,0,-e[4]*t[4],-e[4]*t[5]],[0,0,0,t[4],t[5],1,-e[5]*t[4],-e[5]*t[5]],[t[6],t[7],1,0,0,0,-e[6]*t[6],-e[6]*t[7]],[0,0,0,t[6],t[7],1,-e[7]*t[6],-e[7]*t[7]]],i={},r=At(n,8,0,0,0,i);if(0!==r){for(var o=[],a=0;a<8;a++)for(var s=0;s<8;s++)null==o[s]&&(o[s]=0),o[s]+=((a+s)%2?-1:1)*At(n,7,0===a?1:0,1<<a,1<<s,i)/r*e[a];return function(t,e,n){var i=e*o[6]+n*o[7]+1;t[0]=(e*o[0]+n*o[1]+o[2])/i,t[1]=(e*o[3]+n*o[4]+o[5])/i}}}var kt="___zrEVENTSAVED",Pt=[];function Lt(t,e,n,i,r){if(e.getBoundingClientRect&&v.domSupported&&!Ot(e)){var o=e[kt]||(e[kt]={}),a=function(t,e,n){for(var i=n?"invTrans":"trans",r=e[i],o=e.srcCoords,a=!0,s=[],l=[],u=0;u<4;u++){var h=t[u].getBoundingClientRect(),c=2*u,d=h.left,f=h.top;s.push(d,f),a=a&&o&&d===o[c]&&f===o[1+c],l.push(t[u].offsetLeft,t[u].offsetTop)}return a&&r?r:(e.srcCoords=s,e[i]=n?Dt(l,s):Dt(s,l))}(function(t,e){var n=e.markers;if(n)return n;n=e.markers=[];for(var i=["left","right"],r=["top","bottom"],o=0;o<4;o++){var a=document.createElement("div"),s=a.style,l=o%2,u=(o>>1)%2;s.cssText=["position: absolute","visibility: hidden","padding: 0","margin: 0","border-width: 0","user-select: none","width:0","height:0",i[l]+":0",r[u]+":0",i[1-l]+":auto",r[1-u]+":auto",""].join("!important;"),t.appendChild(a),n.push(a)}return n}(e,o),o,r);if(a)return a(t,n,i),!0}return!1}function Ot(t){return"CANVAS"===t.nodeName.toUpperCase()}var Et="undefined"!=typeof window&&!!window.addEventListener,zt=/^(?:mouse|pointer|contextmenu|drag|drop)|click/,Nt=[];function Rt(t,e,n,i){return n=n||{},i||!v.canvasSupported?Bt(t,e,n):v.browser.firefox&&null!=e.layerX&&e.layerX!==e.offsetX?(n.zrX=e.layerX,n.zrY=e.layerY):null!=e.offsetX?(n.zrX=e.offsetX,n.zrY=e.offsetY):Bt(t,e,n),n}function Bt(t,e,n){if(v.domSupported&&t.getBoundingClientRect){var i=e.clientX,r=e.clientY;if(Ot(t)){var o=t.getBoundingClientRect();return n.zrX=i-o.left,void(n.zrY=r-o.top)}if(Lt(Nt,t,i,r))return n.zrX=Nt[0],void(n.zrY=Nt[1])}n.zrX=n.zrY=0}function Vt(t){return t||window.event}function Ft(t,e,n){if(null!=(e=Vt(e)).zrX)return e;var i=e.type;if(i&&0<=i.indexOf("touch")){var r="touchend"!==i?e.targetTouches[0]:e.changedTouches[0];r&&Rt(t,r,e,n)}else Rt(t,e,e,n),e.zrDelta=e.wheelDelta?e.wheelDelta/120:-(e.detail||0)/3;var o=e.button;return null==e.which&&void 0!==o&&zt.test(e.type)&&(e.which=1&o?1:2&o?3:4&o?2:0),e}function Ht(t,e,n,i){Et?t.addEventListener(e,n,i):t.attachEvent("on"+e,n)}var Wt=Et?function(t){t.preventDefault(),t.stopPropagation(),t.cancelBubble=!0}:function(t){t.returnValue=!1,t.cancelBubble=!0};function Gt(t){return 2===t.which||3===t.which}function Zt(){this._track=[]}function Ut(t){var e=t[1][0]-t[0][0],n=t[1][1]-t[0][1];return Math.sqrt(e*e+n*n)}Zt.prototype={constructor:Zt,recognize:function(t,e,n){return this._doTrack(t,e,n),this._recognize(t)},clear:function(){return this._track.length=0,this},_doTrack:function(t,e,n){var i=t.touches;if(i){for(var r={points:[],touches:[],target:e,event:t},o=0,a=i.length;o<a;o++){var s=i[o],l=Rt(n,s,{});r.points.push([l.zrX,l.zrY]),r.touches.push(s)}this._track.push(r)}},_recognize:function(t){for(var e in Xt)if(Xt.hasOwnProperty(e)){var n=Xt[e](this._track,t);if(n)return n}}};var Xt={pinch:function(t,e){var n=t.length;if(n){var i=(t[n-1]||{}).points,r=(t[n-2]||{}).points||i;if(r&&1<r.length&&i&&1<i.length){var o=Ut(i)/Ut(r);isFinite(o)||(o=1),e.pinchScale=o;var a=function(t){return[(t[0][0]+t[1][0])/2,(t[0][1]+t[1][1])/2]}(i);return e.pinchX=a[0],e.pinchY=a[1],{type:"pinch",target:t[0].target,event:e}}}}},Yt="silent";function jt(){Wt(this.event)}function qt(){}qt.prototype.dispose=function(){};function $t(t,e,n,i){It.call(this),this.storage=t,this.painter=e,this.painterRoot=i,n=n||new qt,this.proxy=null,this._hovered={},this._lastTouchMoment,this._lastX,this._lastY,this._gestureMgr,bt.call(this),this.setHandlerProxy(n)}var Kt=["click","dblclick","mousewheel","mouseout","mouseup","mousedown","mousemove","contextmenu"];function Qt(t,e,n){if(t[t.rectHover?"rectContain":"contain"](e,n)){for(var i,r=t;r;){if(r.clipPath&&!r.clipPath.contain(e,n))return!1;r.silent&&(i=!0),r=r.parent}return!i||Yt}return!1}function Jt(t,e,n){var i=t.painter;return e<0||e>i.getWidth()||n<0||n>i.getHeight()}$t.prototype={constructor:$t,setHandlerProxy:function(e){this.proxy&&this.proxy.dispose(),e&&(D(Kt,function(t){e.on&&e.on(t,this[t],this)},this),e.handler=this),this.proxy=e},mousemove:function(t){var e=t.zrX,n=t.zrY,i=Jt(this,e,n),r=this._hovered,o=r.target;o&&!o.__zr&&(o=(r=this.findHover(r.x,r.y)).target);var a=this._hovered=i?{x:e,y:n}:this.findHover(e,n),s=a.target,l=this.proxy;l.setCursor&&l.setCursor(s?s.cursor:"default"),o&&s!==o&&this.dispatchToElement(r,"mouseout",t),this.dispatchToElement(a,"mousemove",t),s&&s!==o&&this.dispatchToElement(a,"mouseover",t)},mouseout:function(t){var e=t.zrEventControl,n=t.zrIsToLocalDOM;"only_globalout"!==e&&this.dispatchToElement(this._hovered,"mouseout",t),"no_globalout"!==e&&(n||this.trigger("globalout",{type:"globalout",event:t}))},resize:function(t){this._hovered={}},dispatch:function(t,e){var n=this[t];n&&n.call(this,e)},dispose:function(){this.proxy.dispose(),this.storage=this.proxy=this.painter=null},setCursorStyle:function(t){var e=this.proxy;e.setCursor&&e.setCursor(t)},dispatchToElement:function(t,e,n){var i=(t=t||{}).target;if(!i||!i.silent){for(var r="on"+e,o=function(t,e,n){return{type:t,event:n,target:e.target,topTarget:e.topTarget,cancelBubble:!1,offsetX:n.zrX,offsetY:n.zrY,gestureEvent:n.gestureEvent,pinchX:n.pinchX,pinchY:n.pinchY,pinchScale:n.pinchScale,wheelDelta:n.zrDelta,zrByTouch:n.zrByTouch,which:n.which,stop:jt}}(e,t,n);i&&(i[r]&&(o.cancelBubble=i[r].call(i,o)),i.trigger(e,o),i=i.parent,!o.cancelBubble););o.cancelBubble||(this.trigger(e,o),this.painter&&this.painter.eachOtherLayer(function(t){"function"==typeof t[r]&&t[r].call(t,o),t.trigger&&t.trigger(e,o)}))}},findHover:function(t,e,n){for(var i=this.storage.getDisplayList(),r={x:t,y:e},o=i.length-1;0<=o;o--){var a;if(i[o]!==n&&!i[o].ignore&&(a=Qt(i[o],t,e))&&(r.topTarget||(r.topTarget=i[o]),a!==Yt)){r.target=i[o];break}}return r},processGesture:function(t,e){this._gestureMgr||(this._gestureMgr=new Zt);var n=this._gestureMgr;"start"===e&&n.clear();var i=n.recognize(t,this.findHover(t.zrX,t.zrY,null).target,this.proxy.dom);if("end"===e&&n.clear(),i){var r=i.type;t.gestureEvent=r,this.dispatchToElement({target:i.target},r,i.event)}}},D(["click","mousedown","mouseup","mousewheel","dblclick","contextmenu"],function(a){$t.prototype[a]=function(t){var e,n,i=t.zrX,r=t.zrY,o=Jt(this,i,r);if("mouseup"===a&&o||(n=(e=this.findHover(i,r)).target),"mousedown"===a)this._downEl=n,this._downPoint=[t.zrX,t.zrY],this._upEl=n;else if("mouseup"===a)this._upEl=n;else if("click"===a){if(this._downEl!==this._upEl||!this._downPoint||4<gt(this._downPoint,[t.zrX,t.zrY]))return;this._downPoint=null}this.dispatchToElement(e,a,t)}}),S($t,It),S($t,bt);var te="undefined"==typeof Float32Array?Array:Float32Array;function ee(){var t=new te(6);return ne(t),t}function ne(t){return t[0]=1,t[1]=0,t[2]=0,t[3]=1,t[4]=0,t[5]=0,t}function ie(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4],t[5]=e[5],t}function re(t,e,n){var i=e[0]*n[0]+e[2]*n[1],r=e[1]*n[0]+e[3]*n[1],o=e[0]*n[2]+e[2]*n[3],a=e[1]*n[2]+e[3]*n[3],s=e[0]*n[4]+e[2]*n[5]+e[4],l=e[1]*n[4]+e[3]*n[5]+e[5];return t[0]=i,t[1]=r,t[2]=o,t[3]=a,t[4]=s,t[5]=l,t}function oe(t,e,n){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4]+n[0],t[5]=e[5]+n[1],t}function ae(t,e,n){var i=e[0],r=e[2],o=e[4],a=e[1],s=e[3],l=e[5],u=Math.sin(n),h=Math.cos(n);return t[0]=i*h+a*u,t[1]=-i*u+a*h,t[2]=r*h+s*u,t[3]=-r*u+h*s,t[4]=h*o+u*l,t[5]=h*l-u*o,t}function se(t,e,n){var i=n[0],r=n[1];return t[0]=e[0]*i,t[1]=e[1]*r,t[2]=e[2]*i,t[3]=e[3]*r,t[4]=e[4]*i,t[5]=e[5]*r,t}function le(t,e){var n=e[0],i=e[2],r=e[4],o=e[1],a=e[3],s=e[5],l=n*a-o*i;return l?(l=1/l,t[0]=a*l,t[1]=-o*l,t[2]=-i*l,t[3]=n*l,t[4]=(i*s-a*r)*l,t[5]=(o*r-n*s)*l,t):null}var ue=(Object.freeze||Object)({create:ee,identity:ne,copy:ie,mul:re,translate:oe,rotate:ae,scale:se,invert:le,clone:function(t){var e=ee();return ie(e,t),e}}),he=ne;function ce(t){return 5e-5<t||t<-5e-5}var de=function(t){(t=t||{}).position||(this.position=[0,0]),null==t.rotation&&(this.rotation=0),t.scale||(this.scale=[1,1]),this.origin=this.origin||null},fe=de.prototype;fe.transform=null,fe.needLocalTransform=function(){return ce(this.rotation)||ce(this.position[0])||ce(this.position[1])||ce(this.scale[0]-1)||ce(this.scale[1]-1)};var pe=[];fe.updateTransform=function(){var t=this.parent,e=t&&t.transform,n=this.needLocalTransform(),i=this.transform;if(n||e){i=i||ee(),n?this.getLocalTransform(i):he(i),e&&(n?re(i,t.transform,i):ie(i,t.transform)),this.transform=i;var r=this.globalScaleRatio;if(null!=r&&1!==r){this.getGlobalScale(pe);var o=pe[0]<0?-1:1,a=pe[1]<0?-1:1,s=((pe[0]-o)*r+o)/pe[0]||0,l=((pe[1]-a)*r+a)/pe[1]||0;i[0]*=s,i[1]*=s,i[2]*=l,i[3]*=l}this.invTransform=this.invTransform||ee(),le(this.invTransform,i)}else i&&he(i)},fe.getLocalTransform=function(t){return de.getLocalTransform(this,t)},fe.setTransform=function(t){var e=this.transform,n=t.dpr||1;e?t.setTransform(n*e[0],n*e[1],n*e[2],n*e[3],n*e[4],n*e[5]):t.setTransform(n,0,0,n,0,0)},fe.restoreTransform=function(t){var e=t.dpr||1;t.setTransform(e,0,0,e,0,0)};var ge=[],me=ee();fe.setLocalTransform=function(t){if(t){var e=t[0]*t[0]+t[1]*t[1],n=t[2]*t[2]+t[3]*t[3],i=this.position,r=this.scale;ce(e-1)&&(e=Math.sqrt(e)),ce(n-1)&&(n=Math.sqrt(n)),t[0]<0&&(e=-e),t[3]<0&&(n=-n),i[0]=t[4],i[1]=t[5],r[0]=e,r[1]=n,this.rotation=Math.atan2(-t[1]/n,t[0]/e)}},fe.decomposeTransform=function(){if(this.transform){var t=this.parent,e=this.transform;t&&t.transform&&(re(ge,t.invTransform,e),e=ge);var n=this.origin;n&&(n[0]||n[1])&&(me[4]=n[0],me[5]=n[1],re(ge,e,me),ge[4]-=n[0],ge[5]-=n[1],e=ge),this.setLocalTransform(e)}},fe.getGlobalScale=function(t){var e=this.transform;return t=t||[],e?(t[0]=Math.sqrt(e[0]*e[0]+e[1]*e[1]),t[1]=Math.sqrt(e[2]*e[2]+e[3]*e[3]),e[0]<0&&(t[0]=-t[0]),e[3]<0&&(t[1]=-t[1])):(t[0]=1,t[1]=1),t},fe.transformCoordToLocal=function(t,e){var n=[t,e],i=this.invTransform;return i&&yt(n,n,i),n},fe.transformCoordToGlobal=function(t,e){var n=[t,e],i=this.transform;return i&&yt(n,n,i),n},de.getLocalTransform=function(t,e){he(e=e||[]);var n=t.origin,i=t.scale||[1,1],r=t.rotation||0,o=t.position||[0,0];return n&&(e[4]-=n[0],e[5]-=n[1]),se(e,e,i),r&&ae(e,e,r),n&&(e[4]+=n[0],e[5]+=n[1]),e[4]+=o[0],e[5]+=o[1],e};var ve={linear:function(t){return t},quadraticIn:function(t){return t*t},quadraticOut:function(t){return t*(2-t)},quadraticInOut:function(t){return(t*=2)<1?.5*t*t:-.5*(--t*(t-2)-1)},cubicIn:function(t){return t*t*t},cubicOut:function(t){return--t*t*t+1},cubicInOut:function(t){return(t*=2)<1?.5*t*t*t:.5*((t-=2)*t*t+2)},quarticIn:function(t){return t*t*t*t},quarticOut:function(t){return 1- --t*t*t*t},quarticInOut:function(t){return(t*=2)<1?.5*t*t*t*t:-.5*((t-=2)*t*t*t-2)},quinticIn:function(t){return t*t*t*t*t},quinticOut:function(t){return--t*t*t*t*t+1},quinticInOut:function(t){return(t*=2)<1?.5*t*t*t*t*t:.5*((t-=2)*t*t*t*t+2)},sinusoidalIn:function(t){return 1-Math.cos(t*Math.PI/2)},sinusoidalOut:function(t){return Math.sin(t*Math.PI/2)},sinusoidalInOut:function(t){return.5*(1-Math.cos(Math.PI*t))},exponentialIn:function(t){return 0===t?0:Math.pow(1024,t-1)},exponentialOut:function(t){return 1===t?1:1-Math.pow(2,-10*t)},exponentialInOut:function(t){return 0===t?0:1===t?1:(t*=2)<1?.5*Math.pow(1024,t-1):.5*(2-Math.pow(2,-10*(t-1)))},circularIn:function(t){return 1-Math.sqrt(1-t*t)},circularOut:function(t){return Math.sqrt(1- --t*t)},circularInOut:function(t){return(t*=2)<1?-.5*(Math.sqrt(1-t*t)-1):.5*(Math.sqrt(1-(t-=2)*t)+1)},elasticIn:function(t){var e,n=.1;return 0===t?0:1===t?1:(e=!n||n<1?(n=1,.1):.4*Math.asin(1/n)/(2*Math.PI),-n*Math.pow(2,10*(t-=1))*Math.sin((t-e)*(2*Math.PI)/.4))},elasticOut:function(t){var e,n=.1;return 0===t?0:1===t?1:(e=!n||n<1?(n=1,.1):.4*Math.asin(1/n)/(2*Math.PI),n*Math.pow(2,-10*t)*Math.sin((t-e)*(2*Math.PI)/.4)+1)},elasticInOut:function(t){var e,n=.1;return 0===t?0:1===t?1:(e=!n||n<1?(n=1,.1):.4*Math.asin(1/n)/(2*Math.PI),(t*=2)<1?n*Math.pow(2,10*(t-=1))*Math.sin((t-e)*(2*Math.PI)/.4)*-.5:n*Math.pow(2,-10*(t-=1))*Math.sin((t-e)*(2*Math.PI)/.4)*.5+1)},backIn:function(t){return t*t*(2.70158*t-1.70158)},backOut:function(t){return--t*t*(2.70158*t+1.70158)+1},backInOut:function(t){var e=2.5949095;return(t*=2)<1?t*t*((1+e)*t-e)*.5:.5*((t-=2)*t*((1+e)*t+e)+2)},bounceIn:function(t){return 1-ve.bounceOut(1-t)},bounceOut:function(t){return t<1/2.75?7.5625*t*t:t<2/2.75?7.5625*(t-=1.5/2.75)*t+.75:t<2.5/2.75?7.5625*(t-=2.25/2.75)*t+.9375:7.5625*(t-=2.625/2.75)*t+.984375},bounceInOut:function(t){return t<.5?.5*ve.bounceIn(2*t):.5*ve.bounceOut(2*t-1)+.5}};function ye(t){this._target=t.target,this._life=t.life||1e3,this._delay=t.delay||0,this._initialized=!1,this.loop=null!=t.loop&&t.loop,this.gap=t.gap||0,this.easing=t.easing||"Linear",this.onframe=t.onframe,this.ondestroy=t.ondestroy,this.onrestart=t.onrestart,this._pausedTime=0,this._paused=!1}ye.prototype={constructor:ye,step:function(t,e){if(this._initialized||(this._startTime=t+this._delay,this._initialized=!0),this._paused)this._pausedTime+=e;else{var n=(t-this._startTime-this._pausedTime)/this._life;if(!(n<0)){n=Math.min(n,1);var i=this.easing,r="string"==typeof i?ve[i]:i,o="function"==typeof r?r(n):n;return this.fire("frame",o),1===n?this.loop?(this.restart(t),"restart"):(this._needsRemove=!0,"destroy"):null}}},restart:function(t){var e=(t-this._startTime-this._pausedTime)%this._life;this._startTime=t-e+this.gap,this._pausedTime=0,this._needsRemove=!1},fire:function(t,e){this[t="on"+t]&&this[t](this._target,e)},pause:function(){this._paused=!0},resume:function(){this._paused=!1}};function _e(){this.head=null,this.tail=null,this._len=0}var xe=_e.prototype;xe.insert=function(t){var e=new be(t);return this.insertEntry(e),e},xe.insertEntry=function(t){this.head?((this.tail.next=t).prev=this.tail,t.next=null,this.tail=t):this.head=this.tail=t,this._len++},xe.remove=function(t){var e=t.prev,n=t.next;e?e.next=n:this.head=n,n?n.prev=e:this.tail=e,t.next=t.prev=null,this._len--},xe.len=function(){return this._len},xe.clear=function(){this.head=this.tail=null,this._len=0};function we(t){this._list=new _e,this._map={},this._maxSize=t||10,this._lastRemovedEntry=null}var be=function(t){this.value=t,this.next,this.prev},Se=we.prototype;Se.put=function(t,e){var n=this._list,i=this._map,r=null;if(null==i[t]){var o=n.len(),a=this._lastRemovedEntry;if(o>=this._maxSize&&0<o){var s=n.head;n.remove(s),delete i[s.key],r=s.value,this._lastRemovedEntry=s}a?a.value=e:a=new be(e),a.key=t,n.insertEntry(a),i[t]=a}return r},Se.get=function(t){var e=this._map[t],n=this._list;if(null!=e)return e!==n.tail&&(n.remove(e),n.insertEntry(e)),e.value},Se.clear=function(){this._list.clear(),this._map={}};var Me={transparent:[0,0,0,0],aliceblue:[240,248,255,1],antiquewhite:[250,235,215,1],aqua:[0,255,255,1],aquamarine:[127,255,212,1],azure:[240,255,255,1],beige:[245,245,220,1],bisque:[255,228,196,1],black:[0,0,0,1],blanchedalmond:[255,235,205,1],blue:[0,0,255,1],blueviolet:[138,43,226,1],brown:[165,42,42,1],burlywood:[222,184,135,1],cadetblue:[95,158,160,1],chartreuse:[127,255,0,1],chocolate:[210,105,30,1],coral:[255,127,80,1],cornflowerblue:[100,149,237,1],cornsilk:[255,248,220,1],crimson:[220,20,60,1],cyan:[0,255,255,1],darkblue:[0,0,139,1],darkcyan:[0,139,139,1],darkgoldenrod:[184,134,11,1],darkgray:[169,169,169,1],darkgreen:[0,100,0,1],darkgrey:[169,169,169,1],darkkhaki:[189,183,107,1],darkmagenta:[139,0,139,1],darkolivegreen:[85,107,47,1],darkorange:[255,140,0,1],darkorchid:[153,50,204,1],darkred:[139,0,0,1],darksalmon:[233,150,122,1],darkseagreen:[143,188,143,1],darkslateblue:[72,61,139,1],darkslategray:[47,79,79,1],darkslategrey:[47,79,79,1],darkturquoise:[0,206,209,1],darkviolet:[148,0,211,1],deeppink:[255,20,147,1],deepskyblue:[0,191,255,1],dimgray:[105,105,105,1],dimgrey:[105,105,105,1],dodgerblue:[30,144,255,1],firebrick:[178,34,34,1],floralwhite:[255,250,240,1],forestgreen:[34,139,34,1],fuchsia:[255,0,255,1],gainsboro:[220,220,220,1],ghostwhite:[248,248,255,1],gold:[255,215,0,1],goldenrod:[218,165,32,1],gray:[128,128,128,1],green:[0,128,0,1],greenyellow:[173,255,47,1],grey:[128,128,128,1],honeydew:[240,255,240,1],hotpink:[255,105,180,1],indianred:[205,92,92,1],indigo:[75,0,130,1],ivory:[255,255,240,1],khaki:[240,230,140,1],lavender:[230,230,250,1],lavenderblush:[255,240,245,1],lawngreen:[124,252,0,1],lemonchiffon:[255,250,205,1],lightblue:[173,216,230,1],lightcoral:[240,128,128,1],lightcyan:[224,255,255,1],lightgoldenrodyellow:[250,250,210,1],lightgray:[211,211,211,1],lightgreen:[144,238,144,1],lightgrey:[211,211,211,1],lightpink:[255,182,193,1],lightsalmon:[255,160,122,1],lightseagreen:[32,178,170,1],lightskyblue:[135,206,250,1],lightslategray:[119,136,153,1],lightslategrey:[119,136,153,1],lightsteelblue:[176,196,222,1],lightyellow:[255,255,224,1],lime:[0,255,0,1],limegreen:[50,205,50,1],linen:[250,240,230,1],magenta:[255,0,255,1],maroon:[128,0,0,1],mediumaquamarine:[102,205,170,1],mediumblue:[0,0,205,1],mediumorchid:[186,85,211,1],mediumpurple:[147,112,219,1],mediumseagreen:[60,179,113,1],mediumslateblue:[123,104,238,1],mediumspringgreen:[0,250,154,1],mediumturquoise:[72,209,204,1],mediumvioletred:[199,21,133,1],midnightblue:[25,25,112,1],mintcream:[245,255,250,1],mistyrose:[255,228,225,1],moccasin:[255,228,181,1],navajowhite:[255,222,173,1],navy:[0,0,128,1],oldlace:[253,245,230,1],olive:[128,128,0,1],olivedrab:[107,142,35,1],orange:[255,165,0,1],orangered:[255,69,0,1],orchid:[218,112,214,1],palegoldenrod:[238,232,170,1],palegreen:[152,251,152,1],paleturquoise:[175,238,238,1],palevioletred:[219,112,147,1],papayawhip:[255,239,213,1],peachpuff:[255,218,185,1],peru:[205,133,63,1],pink:[255,192,203,1],plum:[221,160,221,1],powderblue:[176,224,230,1],purple:[128,0,128,1],red:[255,0,0,1],rosybrown:[188,143,143,1],royalblue:[65,105,225,1],saddlebrown:[139,69,19,1],salmon:[250,128,114,1],sandybrown:[244,164,96,1],seagreen:[46,139,87,1],seashell:[255,245,238,1],sienna:[160,82,45,1],silver:[192,192,192,1],skyblue:[135,206,235,1],slateblue:[106,90,205,1],slategray:[112,128,144,1],slategrey:[112,128,144,1],snow:[255,250,250,1],springgreen:[0,255,127,1],steelblue:[70,130,180,1],tan:[210,180,140,1],teal:[0,128,128,1],thistle:[216,191,216,1],tomato:[255,99,71,1],turquoise:[64,224,208,1],violet:[238,130,238,1],wheat:[245,222,179,1],white:[255,255,255,1],whitesmoke:[245,245,245,1],yellow:[255,255,0,1],yellowgreen:[154,205,50,1]};function Ie(t){return(t=Math.round(t))<0?0:255<t?255:t}function Ce(t){return t<0?0:1<t?1:t}function Te(t){return t.length&&"%"===t.charAt(t.length-1)?Ie(parseFloat(t)/100*255):Ie(parseInt(t,10))}function Ae(t){return t.length&&"%"===t.charAt(t.length-1)?Ce(parseFloat(t)/100):Ce(parseFloat(t))}function De(t,e,n){return n<0?n+=1:1<n&&(n-=1),6*n<1?t+(e-t)*n*6:2*n<1?e:3*n<2?t+(e-t)*(2/3-n)*6:t}function ke(t,e,n){return t+(e-t)*n}function Pe(t,e,n,i,r){return t[0]=e,t[1]=n,t[2]=i,t[3]=r,t}function Le(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t}var Oe=new we(20),Ee=null;function ze(t,e){Ee&&Le(Ee,e),Ee=Oe.put(t,Ee||e.slice())}function Ne(t,e){if(t){e=e||[];var n=Oe.get(t);if(n)return Le(e,n);var i,r=(t+="").replace(/ /g,"").toLowerCase();if(r in Me)return Le(e,Me[r]),ze(t,e),e;if("#"===r.charAt(0))return 4===r.length?0<=(i=parseInt(r.substr(1),16))&&i<=4095?(Pe(e,(3840&i)>>4|(3840&i)>>8,240&i|(240&i)>>4,15&i|(15&i)<<4,1),ze(t,e),e):void Pe(e,0,0,0,1):7===r.length?0<=(i=parseInt(r.substr(1),16))&&i<=16777215?(Pe(e,(16711680&i)>>16,(65280&i)>>8,255&i,1),ze(t,e),e):void Pe(e,0,0,0,1):void 0;var o=r.indexOf("("),a=r.indexOf(")");if(-1!==o&&a+1===r.length){var s=r.substr(0,o),l=r.substr(o+1,a-(o+1)).split(","),u=1;switch(s){case"rgba":if(4!==l.length)return void Pe(e,0,0,0,1);u=Ae(l.pop());case"rgb":return 3!==l.length?void Pe(e,0,0,0,1):(Pe(e,Te(l[0]),Te(l[1]),Te(l[2]),u),ze(t,e),e);case"hsla":return 4!==l.length?void Pe(e,0,0,0,1):(l[3]=Ae(l[3]),Re(l,e),ze(t,e),e);case"hsl":return 3!==l.length?void Pe(e,0,0,0,1):(Re(l,e),ze(t,e),e);default:return}}Pe(e,0,0,0,1)}}function Re(t,e){var n=(parseFloat(t[0])%360+360)%360/360,i=Ae(t[1]),r=Ae(t[2]),o=r<=.5?r*(i+1):r+i-r*i,a=2*r-o;return Pe(e=e||[],Ie(255*De(a,o,n+1/3)),Ie(255*De(a,o,n)),Ie(255*De(a,o,n-1/3)),1),4===t.length&&(e[3]=t[3]),e}function Be(t,e){var n=Ne(t);if(n){for(var i=0;i<3;i++)n[i]=e<0?n[i]*(1-e)|0:(255-n[i])*e+n[i]|0,255<n[i]?n[i]=255:t[i]<0&&(n[i]=0);return Ue(n,4===n.length?"rgba":"rgb")}}function Ve(t){var e=Ne(t);if(e)return((1<<24)+(e[0]<<16)+(e[1]<<8)+ +e[2]).toString(16).slice(1)}function Fe(t,e,n){if(e&&e.length&&0<=t&&t<=1){n=n||[];var i=t*(e.length-1),r=Math.floor(i),o=Math.ceil(i),a=e[r],s=e[o],l=i-r;return n[0]=Ie(ke(a[0],s[0],l)),n[1]=Ie(ke(a[1],s[1],l)),n[2]=Ie(ke(a[2],s[2],l)),n[3]=Ce(ke(a[3],s[3],l)),n}}var He=Fe;function We(t,e,n){if(e&&e.length&&0<=t&&t<=1){var i=t*(e.length-1),r=Math.floor(i),o=Math.ceil(i),a=Ne(e[r]),s=Ne(e[o]),l=i-r,u=Ue([Ie(ke(a[0],s[0],l)),Ie(ke(a[1],s[1],l)),Ie(ke(a[2],s[2],l)),Ce(ke(a[3],s[3],l))],"rgba");return n?{color:u,leftIndex:r,rightIndex:o,value:i}:u}}var Ge=We;function Ze(t,e){if((t=Ne(t))&&null!=e)return t[3]=Ce(e),Ue(t,"rgba")}function Ue(t,e){if(t&&t.length){var n=t[0]+","+t[1]+","+t[2];return"rgba"!==e&&"hsva"!==e&&"hsla"!==e||(n+=","+t[3]),e+"("+n+")"}}var Xe=(Object.freeze||Object)({parse:Ne,lift:Be,toHex:Ve,fastLerp:Fe,fastMapToColor:He,lerp:We,mapToColor:Ge,modifyHSL:function(t,e,n,i){if(t=Ne(t))return t=function(t){if(t){var e,n,i=t[0]/255,r=t[1]/255,o=t[2]/255,a=Math.min(i,r,o),s=Math.max(i,r,o),l=s-a,u=(s+a)/2;if(0==l)n=e=0;else{n=u<.5?l/(s+a):l/(2-s-a);var h=((s-i)/6+l/2)/l,c=((s-r)/6+l/2)/l,d=((s-o)/6+l/2)/l;i===s?e=d-c:r===s?e=1/3+h-d:o===s&&(e=2/3+c-h),e<0&&(e+=1),1<e&&(e-=1)}var f=[360*e,n,u];return null!=t[3]&&f.push(t[3]),f}}(t),null!=e&&(t[0]=function(t){return(t=Math.round(t))<0?0:360<t?360:t}(e)),null!=n&&(t[1]=Ae(n)),null!=i&&(t[2]=Ae(i)),Ue(Re(t),"rgba")},modifyAlpha:Ze,stringify:Ue}),Ye=Array.prototype.slice;function je(t,e){return t[e]}function qe(t,e,n){t[e]=n}function $e(t,e,n){return(e-t)*n+t}function Ke(t,e,n){return.5<n?e:t}function Qe(t,e,n,i,r){var o=t.length;if(1===r)for(var a=0;a<o;a++)i[a]=$e(t[a],e[a],n);else{var s=o&&t[0].length;for(a=0;a<o;a++)for(var l=0;l<s;l++)i[a][l]=$e(t[a][l],e[a][l],n)}}function Je(t,e,n){var i=t.length,r=e.length;if(i!==r)if(r<i)t.length=r;else for(var o=i;o<r;o++)t.push(1===n?e[o]:Ye.call(e[o]));var a=t[0]&&t[0].length;for(o=0;o<t.length;o++)if(1===n)isNaN(t[o])&&(t[o]=e[o]);else for(var s=0;s<a;s++)isNaN(t[o][s])&&(t[o][s]=e[o][s])}function tn(t,e,n){if(t===e)return!0;var i=t.length;if(i!==e.length)return!1;if(1===n){for(var r=0;r<i;r++)if(t[r]!==e[r])return!1}else{var o=t[0].length;for(r=0;r<i;r++)for(var a=0;a<o;a++)if(t[r][a]!==e[r][a])return!1}return!0}function en(t,e,n,i,r,o,a,s,l){var u=t.length;if(1===l)for(var h=0;h<u;h++)s[h]=nn(t[h],e[h],n[h],i[h],r,o,a);else{var c=t[0].length;for(h=0;h<u;h++)for(var d=0;d<c;d++)s[h][d]=nn(t[h][d],e[h][d],n[h][d],i[h][d],r,o,a)}}function nn(t,e,n,i,r,o,a){var s=.5*(n-t),l=.5*(i-e);return(2*(e-n)+s+l)*a+(-3*(e-n)-2*s-l)*o+s*r+e}function rn(t){if(L(t)){var e=t.length;if(L(t[0])){for(var n=[],i=0;i<e;i++)n.push(Ye.call(t[i]));return n}return Ye.call(t)}return t}function on(t){return t[0]=Math.floor(t[0]),t[1]=Math.floor(t[1]),t[2]=Math.floor(t[2]),"rgba("+t.join(",")+")"}function an(t,e,n,i,o,r){var a=t._getter,s=t._setter,l="spline"===e,u=i.length;if(u){var h,c=L(i[0].value),d=!1,f=!1,p=c?function(t){var e=t[t.length-1].value;return L(e&&e[0])?2:1}(i):0;i.sort(function(t,e){return t.time-e.time}),h=i[u-1].time;for(var g=[],m=[],v=i[0].value,y=!0,_=0;_<u;_++){g.push(i[_].time/h);var x=i[_].value;if(c&&tn(x,v,p)||!c&&x===v||(y=!1),"string"==typeof(v=x)){var w=Ne(x);w?(x=w,d=!0):f=!0}m.push(x)}if(r||!y){var b=m[u-1];for(_=0;_<u-1;_++)c?Je(m[_],b,p):!isNaN(m[_])||isNaN(b)||f||d||(m[_]=b);c&&Je(a(t._target,o),b,p);var S,M,I,C,T,A=0,D=0;if(d)var k=[0,0,0,0];var P=new ye({target:t._target,life:h,loop:t._loop,delay:t._delay,onframe:function(t,e){var n;if(e<0)n=0;else if(e<D){for(n=Math.min(A+1,u-1);0<=n&&!(g[n]<=e);n--);n=Math.min(n,u-2)}else{for(n=A;n<u&&!(g[n]>e);n++);n=Math.min(n-1,u-2)}D=e;var i=g[(A=n)+1]-g[n];if(0!=i)if(S=(e-g[n])/i,l)if(I=m[n],M=m[0===n?n:n-1],C=m[u-2<n?u-1:n+1],T=m[u-3<n?u-1:n+2],c)en(M,I,C,T,S,S*S,S*S*S,a(t,o),p);else{if(d)r=en(M,I,C,T,S,S*S,S*S*S,k,1),r=on(k);else{if(f)return Ke(I,C,S);r=nn(M,I,C,T,S,S*S,S*S*S)}s(t,o,r)}else if(c)Qe(m[n],m[n+1],S,a(t,o),p);else{var r;if(d)Qe(m[n],m[n+1],S,k,1),r=on(k);else{if(f)return Ke(m[n],m[n+1],S);r=$e(m[n],m[n+1],S)}s(t,o,r)}},ondestroy:n});return e&&"spline"!==e&&(P.easing=e),P}}}function sn(t,e,n,i){this._tracks={},this._target=t,this._loop=e||!1,this._getter=n||je,this._setter=i||qe,this._clipCount=0,this._delay=0,this._doneList=[],this._onframeList=[],this._clipList=[]}sn.prototype={when:function(t,e){var n=this._tracks;for(var i in e)if(e.hasOwnProperty(i)){if(!n[i]){n[i]=[];var r=this._getter(this._target,i);if(null==r)continue;0!==t&&n[i].push({time:0,value:rn(r)})}n[i].push({time:t,value:e[i]})}return this},during:function(t){return this._onframeList.push(t),this},pause:function(){for(var t=0;t<this._clipList.length;t++)this._clipList[t].pause();this._paused=!0},resume:function(){for(var t=0;t<this._clipList.length;t++)this._clipList[t].resume();this._paused=!1},isPaused:function(){return!!this._paused},_doneCallback:function(){this._tracks={},this._clipList.length=0;for(var t=this._doneList,e=t.length,n=0;n<e;n++)t[n].call(this)},start:function(t,e){function n(){--o||r._doneCallback()}var i,r=this,o=0;for(var a in this._tracks)if(this._tracks.hasOwnProperty(a)){var s=an(this,t,n,this._tracks[a],a,e);s&&(this._clipList.push(s),o++,this.animation&&this.animation.addClip(s),i=s)}if(i){var l=i.onframe;i.onframe=function(t,e){l(t,e);for(var n=0;n<r._onframeList.length;n++)r._onframeList[n](t,e)}}return o||this._doneCallback(),this},stop:function(t){for(var e=this._clipList,n=this.animation,i=0;i<e.length;i++){var r=e[i];t&&r.onframe(this._target,1),n&&n.removeClip(r)}e.length=0},delay:function(t){return this._delay=t,this},done:function(t){return t&&this._doneList.push(t),this},getClips:function(){return this._clipList}};var ln=1;"undefined"!=typeof window&&(ln=Math.max(window.devicePixelRatio||1,1));var un=ln,hn=function(){};function cn(){this.animators=[]}var dn=hn;function fn(t,e,n,i,r,o,a,s){z(i)?(o=r,r=i,i=0):E(r)?(o=r,r="linear",i=0):E(i)?(o=i,i=0):n=E(n)?(o=n,500):n||500,t.stopAnimation(),function t(e,n,i,r,o,a,s){var l={};var u=0;for(var h in r)r.hasOwnProperty(h)&&(null!=i[h]?N(r[h])&&!L(r[h])?t(e,n?n+"."+h:h,i[h],r[h],o,a,s):(s?(l[h]=i[h],pn(e,n,h,r[h])):l[h]=r[h],u++):null==r[h]||s||pn(e,n,h,r[h]));0<u&&e.animate(n,!1).when(null==o?500:o,l).delay(a||0)}(t,"",t,e,n,i,s);var l=t.animators.slice(),u=l.length;function h(){--u||o&&o()}u||o&&o();for(var c=0;c<l.length;c++)l[c].done(h).start(r,a)}function pn(t,e,n,i){if(e){var r={};r[e]={},r[e][n]=i,t.attr(r)}else t.attr(n,i)}cn.prototype={constructor:cn,animate:function(t,e){var n,i=!1,r=this,o=this.__zr;if(t){var a=t.split("."),s=r;i="shape"===a[0];for(var l=0,u=a.length;l<u;l++)s=s&&s[a[l]];s&&(n=s)}else n=r;if(n){var h=r.animators,c=new sn(n,e);return c.during(function(t){r.dirty(i)}).done(function(){h.splice(x(h,c),1)}),h.push(c),o&&o.animation.addAnimator(c),c}dn('Property "'+t+'" is not existed in element '+r.id)},stopAnimation:function(t){for(var e=this.animators,n=e.length,i=0;i<n;i++)e[i].stop(t);return e.length=0,this},animateTo:function(t,e,n,i,r,o){fn(this,t,e,n,i,r,o)},animateFrom:function(t,e,n,i,r,o){fn(this,t,e,n,i,r,o,!0)}};var gn=function(t){de.call(this,t),It.call(this,t),cn.call(this,t),this.id=t.id||i()};gn.prototype={type:"element",name:"",__zr:null,ignore:!1,clipPath:null,isGroup:!1,drift:function(t,e){switch(this.draggable){case"horizontal":e=0;break;case"vertical":t=0}var n=this.transform;(n=n||(this.transform=[1,0,0,1,0,0]))[4]+=t,n[5]+=e,this.decomposeTransform(),this.dirty(!1)},beforeUpdate:function(){},afterUpdate:function(){},update:function(){this.updateTransform()},traverse:function(t,e){},attrKV:function(t,e){if("position"===t||"scale"===t||"origin"===t){if(e){var n=this[t];(n=n||(this[t]=[]))[0]=e[0],n[1]=e[1]}}else this[t]=e},hide:function(){this.ignore=!0,this.__zr&&this.__zr.refresh()},show:function(){this.ignore=!1,this.__zr&&this.__zr.refresh()},attr:function(t,e){if("string"==typeof t)this.attrKV(t,e);else if(N(t))for(var n in t)t.hasOwnProperty(n)&&this.attrKV(n,t[n]);return this.dirty(!1),this},setClipPath:function(t){var e=this.__zr;e&&t.addSelfToZr(e),this.clipPath&&this.clipPath!==t&&this.removeClipPath(),(this.clipPath=t).__zr=e,(t.__clipTarget=this).dirty(!1)},removeClipPath:function(){var t=this.clipPath;t&&(t.__zr&&t.removeSelfFromZr(t.__zr),t.__zr=null,t.__clipTarget=null,this.clipPath=null,this.dirty(!1))},addSelfToZr:function(t){this.__zr=t;var e=this.animators;if(e)for(var n=0;n<e.length;n++)t.animation.addAnimator(e[n]);this.clipPath&&this.clipPath.addSelfToZr(t)},removeSelfFromZr:function(t){this.__zr=null;var e=this.animators;if(e)for(var n=0;n<e.length;n++)t.animation.removeAnimator(e[n]);this.clipPath&&this.clipPath.removeSelfFromZr(t)}},S(gn,cn),S(gn,de),S(gn,It);var mn,vn,yn,_n,xn=yt,wn=Math.min,bn=Math.max;function Sn(t,e,n,i){n<0&&(t+=n,n=-n),i<0&&(e+=i,i=-i),this.x=t,this.y=e,this.width=n,this.height=i}Sn.prototype={constructor:Sn,union:function(t){var e=wn(t.x,this.x),n=wn(t.y,this.y);this.width=bn(t.x+t.width,this.x+this.width)-e,this.height=bn(t.y+t.height,this.y+this.height)-n,this.x=e,this.y=n},applyTransform:(mn=[],vn=[],yn=[],_n=[],function(t){if(t){mn[0]=yn[0]=this.x,mn[1]=_n[1]=this.y,vn[0]=_n[0]=this.x+this.width,vn[1]=yn[1]=this.y+this.height,xn(mn,mn,t),xn(vn,vn,t),xn(yn,yn,t),xn(_n,_n,t),this.x=wn(mn[0],vn[0],yn[0],_n[0]),this.y=wn(mn[1],vn[1],yn[1],_n[1]);var e=bn(mn[0],vn[0],yn[0],_n[0]),n=bn(mn[1],vn[1],yn[1],_n[1]);this.width=e-this.x,this.height=n-this.y}}),calculateTransform:function(t){var e=t.width/this.width,n=t.height/this.height,i=ee();return oe(i,i,[-this.x,-this.y]),se(i,i,[e,n]),oe(i,i,[t.x,t.y]),i},intersect:function(t){if(!t)return!1;t instanceof Sn||(t=Sn.create(t));var e=this,n=e.x,i=e.x+e.width,r=e.y,o=e.y+e.height,a=t.x,s=t.x+t.width,l=t.y,u=t.y+t.height;return!(i<a||s<n||o<l||u<r)},contain:function(t,e){var n=this;return t>=n.x&&t<=n.x+n.width&&e>=n.y&&e<=n.y+n.height},clone:function(){return new Sn(this.x,this.y,this.width,this.height)},copy:function(t){this.x=t.x,this.y=t.y,this.width=t.width,this.height=t.height},plain:function(){return{x:this.x,y:this.y,width:this.width,height:this.height}}},Sn.create=function(t){return new Sn(t.x,t.y,t.width,t.height)};var Mn=function(t){for(var e in t=t||{},gn.call(this,t),t)t.hasOwnProperty(e)&&(this[e]=t[e]);this._children=[],this.__storage=null,this.__dirty=!0};Mn.prototype={constructor:Mn,isGroup:!0,type:"group",silent:!1,children:function(){return this._children.slice()},childAt:function(t){return this._children[t]},childOfName:function(t){for(var e=this._children,n=0;n<e.length;n++)if(e[n].name===t)return e[n]},childCount:function(){return this._children.length},add:function(t){return t&&t!==this&&t.parent!==this&&(this._children.push(t),this._doAdd(t)),this},addBefore:function(t,e){if(t&&t!==this&&t.parent!==this&&e&&e.parent===this){var n=this._children,i=n.indexOf(e);0<=i&&(n.splice(i,0,t),this._doAdd(t))}return this},_doAdd:function(t){t.parent&&t.parent.remove(t);var e=(t.parent=this).__storage,n=this.__zr;e&&e!==t.__storage&&(e.addToStorage(t),t instanceof Mn&&t.addChildrenToStorage(e)),n&&n.refresh()},remove:function(t){var e=this.__zr,n=this.__storage,i=this._children,r=x(i,t);return r<0||(i.splice(r,1),t.parent=null,n&&(n.delFromStorage(t),t instanceof Mn&&t.delChildrenFromStorage(n)),e&&e.refresh()),this},removeAll:function(){var t,e,n=this._children,i=this.__storage;for(e=0;e<n.length;e++)t=n[e],i&&(i.delFromStorage(t),t instanceof Mn&&t.delChildrenFromStorage(i)),t.parent=null;return n.length=0,this},eachChild:function(t,e){for(var n=this._children,i=0;i<n.length;i++){var r=n[i];t.call(e,r,i)}return this},traverse:function(t,e){for(var n=0;n<this._children.length;n++){var i=this._children[n];t.call(e,i),"group"===i.type&&i.traverse(t,e)}return this},addChildrenToStorage:function(t){for(var e=0;e<this._children.length;e++){var n=this._children[e];t.addToStorage(n),n instanceof Mn&&n.addChildrenToStorage(t)}},delChildrenFromStorage:function(t){for(var e=0;e<this._children.length;e++){var n=this._children[e];t.delFromStorage(n),n instanceof Mn&&n.delChildrenFromStorage(t)}},dirty:function(){return this.__dirty=!0,this.__zr&&this.__zr.refresh(),this},getBoundingRect:function(t){for(var e=null,n=new Sn(0,0,0,0),i=t||this._children,r=[],o=0;o<i.length;o++){var a=i[o];if(!a.ignore&&!a.invisible){var s=a.getBoundingRect(),l=a.getLocalTransform(r);l?(n.copy(s),n.applyTransform(l),(e=e||n.clone()).union(n)):(e=e||s.clone()).union(s)}}return e||n}},w(Mn,gn);var In=32,Cn=7;function Tn(t,e,n,i){var r=e+1;if(r===n)return 1;if(i(t[r++],t[e])<0){for(;r<n&&i(t[r],t[r-1])<0;)r++;!function(t,e,n){n--;for(;e<n;){var i=t[e];t[e++]=t[n],t[n--]=i}}(t,e,r)}else for(;r<n&&0<=i(t[r],t[r-1]);)r++;return r-e}function An(t,e,n,i,r){for(i===e&&i++;i<n;i++){for(var o,a=t[i],s=e,l=i;s<l;)r(a,t[o=s+l>>>1])<0?l=o:s=1+o;var u=i-s;switch(u){case 3:t[s+3]=t[s+2];case 2:t[s+2]=t[s+1];case 1:t[s+1]=t[s];break;default:for(;0<u;)t[s+u]=t[s+u-1],u--}t[s]=a}}function Dn(t,e,n,i,r,o){var a=0,s=0,l=1;if(0<o(t,e[n+r])){for(s=i-r;l<s&&0<o(t,e[n+r+l]);)(l=1+((a=l)<<1))<=0&&(l=s);s<l&&(l=s),a+=r,l+=r}else{for(s=r+1;l<s&&o(t,e[n+r-l])<=0;)(l=1+((a=l)<<1))<=0&&(l=s);s<l&&(l=s);var u=a;a=r-l,l=r-u}for(a++;a<l;){var h=a+(l-a>>>1);0<o(t,e[n+h])?a=h+1:l=h}return l}function kn(t,e,n,i,r,o){var a=0,s=0,l=1;if(o(t,e[n+r])<0){for(s=r+1;l<s&&o(t,e[n+r-l])<0;)(l=1+((a=l)<<1))<=0&&(l=s);s<l&&(l=s);var u=a;a=r-l,l=r-u}else{for(s=i-r;l<s&&0<=o(t,e[n+r+l]);)(l=1+((a=l)<<1))<=0&&(l=s);s<l&&(l=s),a+=r,l+=r}for(a++;a<l;){var h=a+(l-a>>>1);o(t,e[n+h])<0?l=h:a=h+1}return l}function Pn(p,g){var a,s,m=Cn,l=0,v=[];function e(t){var e=a[t],n=s[t],i=a[t+1],r=s[t+1];s[t]=n+r,t===l-3&&(a[t+1]=a[t+2],s[t+1]=s[t+2]),l--;var o=kn(p[i],p,e,n,0,g);e+=o,0!==(n-=o)&&0!==(r=Dn(p[e+n-1],p,i,r,r-1,g))&&(n<=r?function(t,e,n,i){var r=0;for(r=0;r<e;r++)v[r]=p[t+r];var o=0,a=n,s=t;if(p[s++]=p[a++],0==--i){for(r=0;r<e;r++)p[s+r]=v[o+r];return}if(1===e){for(r=0;r<i;r++)p[s+r]=p[a+r];return p[s+i]=v[o]}var l,u,h,c=m;for(;;){u=l=0,h=!1;do{if(g(p[a],v[o])<0){if(p[s++]=p[a++],u++,(l=0)==--i){h=!0;break}}else if(p[s++]=v[o++],l++,u=0,1==--e){h=!0;break}}while((l|u)<c);if(h)break;do{if(0!==(l=kn(p[a],v,o,e,0,g))){for(r=0;r<l;r++)p[s+r]=v[o+r];if(s+=l,o+=l,(e-=l)<=1){h=!0;break}}if(p[s++]=p[a++],0==--i){h=!0;break}if(0!==(u=Dn(v[o],p,a,i,0,g))){for(r=0;r<u;r++)p[s+r]=p[a+r];if(s+=u,a+=u,0===(i-=u)){h=!0;break}}if(p[s++]=v[o++],1==--e){h=!0;break}c--}while(Cn<=l||Cn<=u);if(h)break;c<0&&(c=0),c+=2}if((m=c)<1&&(m=1),1===e){for(r=0;r<i;r++)p[s+r]=p[a+r];p[s+i]=v[o]}else{if(0===e)throw new Error;for(r=0;r<e;r++)p[s+r]=v[o+r]}}(e,n,i,r):function(t,e,n,i){var r=0;for(r=0;r<i;r++)v[r]=p[n+r];var o=t+e-1,a=i-1,s=n+i-1,l=0,u=0;if(p[s--]=p[o--],0==--e){for(l=s-(i-1),r=0;r<i;r++)p[l+r]=v[r];return}if(1===i){for(u=(s-=e)+1,l=(o-=e)+1,r=e-1;0<=r;r--)p[u+r]=p[l+r];return p[s]=v[a]}var h=m;for(;;){var c=0,d=0,f=!1;do{if(g(v[a],p[o])<0){if(p[s--]=p[o--],c++,(d=0)==--e){f=!0;break}}else if(p[s--]=v[a--],d++,c=0,1==--i){f=!0;break}}while((c|d)<h);if(f)break;do{if(0!==(c=e-kn(v[a],p,t,e,e-1,g))){for(e-=c,u=(s-=c)+1,l=(o-=c)+1,r=c-1;0<=r;r--)p[u+r]=p[l+r];if(0===e){f=!0;break}}if(p[s--]=v[a--],1==--i){f=!0;break}if(0!==(d=i-Dn(p[o],v,0,i,i-1,g))){for(i-=d,u=(s-=d)+1,l=(a-=d)+1,r=0;r<d;r++)p[u+r]=v[l+r];if(i<=1){f=!0;break}}if(p[s--]=p[o--],0==--e){f=!0;break}h--}while(Cn<=c||Cn<=d);if(f)break;h<0&&(h=0),h+=2}(m=h)<1&&(m=1);if(1===i){for(u=(s-=e)+1,l=(o-=e)+1,r=e-1;0<=r;r--)p[u+r]=p[l+r];p[s]=v[a]}else{if(0===i)throw new Error;for(l=s-(i-1),r=0;r<i;r++)p[l+r]=v[r]}}(e,n,i,r))}a=[],s=[],this.mergeRuns=function(){for(;1<l;){var t=l-2;if(1<=t&&s[t-1]<=s[t]+s[t+1]||2<=t&&s[t-2]<=s[t]+s[t-1])s[t-1]<s[t+1]&&t--;else if(s[t]>s[t+1])break;e(t)}},this.forceMergeRuns=function(){for(;1<l;){var t=l-2;0<t&&s[t-1]<s[t+1]&&t--,e(t)}},this.pushRun=function(t,e){a[l]=t,s[l]=e,l+=1}}function Ln(t,e,n,i){n=n||0;var r=(i=i||t.length)-n;if(!(r<2)){var o=0;if(r<In)An(t,n,i,n+(o=Tn(t,n,i,e)),e);else{var a=new Pn(t,e),s=function(t){for(var e=0;In<=t;)e|=1&t,t>>=1;return t+e}(r);do{if((o=Tn(t,n,i,e))<s){var l=r;s<l&&(l=s),An(t,n,n+l,n+o,e),o=l}a.pushRun(n,o),a.mergeRuns(),r-=o,n+=o}while(0!==r);a.forceMergeRuns()}}}function On(t,e){return t.zlevel===e.zlevel?t.z===e.z?t.z2-e.z2:t.z-e.z:t.zlevel-e.zlevel}function En(){this._roots=[],this._displayList=[],this._displayListLen=0}En.prototype={constructor:En,traverse:function(t,e){for(var n=0;n<this._roots.length;n++)this._roots[n].traverse(t,e)},getDisplayList:function(t,e){return e=e||!1,t&&this.updateDisplayList(e),this._displayList},updateDisplayList:function(t){this._displayListLen=0;for(var e=this._roots,n=this._displayList,i=0,r=e.length;i<r;i++)this._updateAndAddDisplayable(e[i],null,t);n.length=this._displayListLen,v.canvasSupported&&Ln(n,On)},_updateAndAddDisplayable:function(t,e,n){if(!t.ignore||n){t.beforeUpdate(),t.__dirty&&t.update(),t.afterUpdate();var i=t.clipPath;if(i){e=e?e.slice():[];for(var r=i,o=t;r;)r.parent=o,r.updateTransform(),e.push(r),r=(o=r).clipPath}if(t.isGroup){for(var a=t._children,s=0;s<a.length;s++){var l=a[s];t.__dirty&&(l.__dirty=!0),this._updateAndAddDisplayable(l,e,n)}t.__dirty=!1}else t.__clipPaths=e,this._displayList[this._displayListLen++]=t}},addRoot:function(t){t.__storage!==this&&(t instanceof Mn&&t.addChildrenToStorage(this),this.addToStorage(t),this._roots.push(t))},delRoot:function(t){if(null==t){for(var e=0;e<this._roots.length;e++){var n=this._roots[e];n instanceof Mn&&n.delChildrenFromStorage(this)}return this._roots=[],this._displayList=[],void(this._displayListLen=0)}if(t instanceof Array){e=0;for(var i=t.length;e<i;e++)this.delRoot(t[e])}else{var r=x(this._roots,t);0<=r&&(this.delFromStorage(t),this._roots.splice(r,1),t instanceof Mn&&t.delChildrenFromStorage(this))}},addToStorage:function(t){return t&&(t.__storage=this,t.dirty(!1)),this},delFromStorage:function(t){return t&&(t.__storage=null),this},dispose:function(){this._renderList=this._roots=null},displayableSortFunc:On};var zn={shadowBlur:1,shadowOffsetX:1,shadowOffsetY:1,textShadowBlur:1,textShadowOffsetX:1,textShadowOffsetY:1,textBoxShadowBlur:1,textBoxShadowOffsetX:1,textBoxShadowOffsetY:1},Nn=function(t,e,n){return zn.hasOwnProperty(e)?n*t.dpr:n},Rn={NONE:0,STYLE_BIND:1,PLAIN_TEXT:2},Bn=9,Vn=[["shadowBlur",0],["shadowOffsetX",0],["shadowOffsetY",0],["shadowColor","#000"],["lineCap","butt"],["lineJoin","miter"],["miterLimit",10]],Fn=function(t){this.extendFrom(t,!1)};function Hn(t,e,n){var i=null==e.x?0:e.x,r=null==e.x2?1:e.x2,o=null==e.y?0:e.y,a=null==e.y2?0:e.y2;return e.global||(i=i*n.width+n.x,r=r*n.width+n.x,o=o*n.height+n.y,a=a*n.height+n.y),i=isNaN(i)?0:i,r=isNaN(r)?1:r,o=isNaN(o)?0:o,a=isNaN(a)?0:a,t.createLinearGradient(i,o,r,a)}function Wn(t,e,n){var i=n.width,r=n.height,o=Math.min(i,r),a=null==e.x?.5:e.x,s=null==e.y?.5:e.y,l=null==e.r?.5:e.r;return e.global||(a=a*i+n.x,s=s*r+n.y,l*=o),t.createRadialGradient(a,s,0,a,s,l)}Fn.prototype={constructor:Fn,fill:"#000",stroke:null,opacity:1,fillOpacity:null,strokeOpacity:null,lineDash:null,lineDashOffset:0,shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,lineWidth:1,strokeNoScale:!1,text:null,font:null,textFont:null,fontStyle:null,fontWeight:null,fontSize:null,fontFamily:null,textTag:null,textFill:"#000",textStroke:null,textWidth:null,textHeight:null,textStrokeWidth:0,textLineHeight:null,textPosition:"inside",textRect:null,textOffset:null,textAlign:null,textVerticalAlign:null,textDistance:5,textShadowColor:"transparent",textShadowBlur:0,textShadowOffsetX:0,textShadowOffsetY:0,textBoxShadowColor:"transparent",textBoxShadowBlur:0,textBoxShadowOffsetX:0,textBoxShadowOffsetY:0,transformText:!1,textRotation:0,textOrigin:null,textBackgroundColor:null,textBorderColor:null,textBorderWidth:0,textBorderRadius:0,textPadding:null,rich:null,truncate:null,blend:null,bind:function(t,e,n){var i=this,r=n&&n.style,o=!r||t.__attrCachedBy!==Rn.STYLE_BIND;t.__attrCachedBy=Rn.STYLE_BIND;for(var a=0;a<Vn.length;a++){var s=Vn[a],l=s[0];!o&&i[l]===r[l]||(t[l]=Nn(t,l,i[l]||s[1]))}if(!o&&i.fill===r.fill||(t.fillStyle=i.fill),!o&&i.stroke===r.stroke||(t.strokeStyle=i.stroke),!o&&i.opacity===r.opacity||(t.globalAlpha=null==i.opacity?1:i.opacity),!o&&i.blend===r.blend||(t.globalCompositeOperation=i.blend||"source-over"),this.hasStroke()){var u=i.lineWidth;t.lineWidth=u/(this.strokeNoScale&&e&&e.getLineScale?e.getLineScale():1)}},hasFill:function(){var t=this.fill;return null!=t&&"none"!==t},hasStroke:function(){var t=this.stroke;return null!=t&&"none"!==t&&0<this.lineWidth},extendFrom:function(t,e){if(t)for(var n in t)!t.hasOwnProperty(n)||!0!==e&&(!1===e?this.hasOwnProperty(n):null==t[n])||(this[n]=t[n])},set:function(t,e){"string"==typeof t?this[t]=e:this.extendFrom(t,!0)},clone:function(){var t=new this.constructor;return t.extendFrom(this,!0),t},getGradient:function(t,e,n){for(var i=("radial"===e.type?Wn:Hn)(t,e,n),r=e.colorStops,o=0;o<r.length;o++)i.addColorStop(r[o].offset,r[o].color);return i}};for(var Gn=Fn.prototype,Zn=0;Zn<Vn.length;Zn++){var Un=Vn[Zn];Un[0]in Gn||(Gn[Un[0]]=Un[1])}Fn.getGradient=Gn.getGradient;function Xn(t,e){this.image=t,this.repeat=e,this.type="pattern"}function Yn(){return!1}function jn(t,e,n){var i=y(),r=e.getWidth(),o=e.getHeight(),a=i.style;return a&&(a.position="absolute",a.left=0,a.top=0,a.width=r+"px",a.height=o+"px",i.setAttribute("data-zr-dom-id",t)),i.width=r*n,i.height=o*n,i}function qn(t,e,n){var i;n=n||un,"string"==typeof t?i=jn(t,e,n):N(t)&&(t=(i=t).id),this.id=t;var r=(this.dom=i).style;r&&(i.onselectstart=Yn,r["-webkit-user-select"]="none",r["user-select"]="none",r["-webkit-touch-callout"]="none",r["-webkit-tap-highlight-color"]="rgba(0,0,0,0)",r.padding=0,r.margin=0,r["border-width"]=0),this.domBack=null,this.ctxBack=null,this.painter=e,this.config=null,this.clearColor=0,this.motionBlur=!1,this.lastFrameAlpha=.7,this.dpr=n}qn.prototype={constructor:qn,__dirty:!0,__used:!(Xn.prototype.getCanvasPattern=function(t){return t.createPattern(this.image,this.repeat||"repeat")}),__drawIndex:0,__startIndex:0,__endIndex:0,incremental:!1,getElementCount:function(){return this.__endIndex-this.__startIndex},initContext:function(){this.ctx=this.dom.getContext("2d"),this.ctx.dpr=this.dpr},createBackBuffer:function(){var t=this.dpr;this.domBack=jn("back-"+this.id,this.painter,t),this.ctxBack=this.domBack.getContext("2d"),1!==t&&this.ctxBack.scale(t,t)},resize:function(t,e){var n=this.dpr,i=this.dom,r=i.style,o=this.domBack;r&&(r.width=t+"px",r.height=e+"px"),i.width=t*n,i.height=e*n,o&&(o.width=t*n,o.height=e*n,1!==n&&this.ctxBack.scale(n,n))},clear:function(t,e){var n,i=this.dom,r=this.ctx,o=i.width,a=i.height,s=(e=e||this.clearColor,this.motionBlur&&!t),l=this.lastFrameAlpha,u=this.dpr;s&&(this.domBack||this.createBackBuffer(),this.ctxBack.globalCompositeOperation="copy",this.ctxBack.drawImage(i,0,0,o/u,a/u)),r.clearRect(0,0,o,a),e&&"transparent"!==e&&(e.colorStops?(n=e.__canvasGradient||Fn.getGradient(r,e,{x:0,y:0,width:o,height:a}),e.__canvasGradient=n):e.image&&(n=Xn.prototype.getCanvasPattern.call(e,r)),r.save(),r.fillStyle=n||e,r.fillRect(0,0,o,a),r.restore());if(s){var h=this.domBack;r.save(),r.globalAlpha=l,r.drawImage(h,0,0,o,a),r.restore()}}};var $n="undefined"!=typeof window&&(window.requestAnimationFrame&&window.requestAnimationFrame.bind(window)||window.msRequestAnimationFrame&&window.msRequestAnimationFrame.bind(window)||window.mozRequestAnimationFrame||window.webkitRequestAnimationFrame)||function(t){setTimeout(t,16)},Kn=new we(50);function Qn(t){if("string"!=typeof t)return t;var e=Kn.get(t);return e&&e.image}function Jn(t,e,n,i,r){if(t){if("string"!=typeof t)return t;if(e&&e.__zrImageSrc===t||!n)return e;var o=Kn.get(t),a={hostEl:n,cb:i,cbPayload:r};return o?ei(e=o.image)||o.pending.push(a):((e=new Image).onload=e.onerror=ti,Kn.put(t,e.__cachedImgObj={image:e,pending:[a]}),e.src=e.__zrImageSrc=t),e}return e}function ti(){var t=this.__cachedImgObj;this.onload=this.onerror=this.__cachedImgObj=null;for(var e=0;e<t.pending.length;e++){var n=t.pending[e],i=n.cb;i&&i(this,n.cbPayload),n.hostEl.dirty()}t.pending.length=0}function ei(t){return t&&t.width&&t.height}var ni={},ii=0,ri=5e3,oi=/\{([a-zA-Z0-9_]+)\|([^}]*)\}/g,ai="12px sans-serif",si={};function li(t,e){var n=t+":"+(e=e||ai);if(ni[n])return ni[n];for(var i,r,o=(t+"").split("\n"),a=0,s=0,l=o.length;s<l;s++)a=Math.max((i=o[s],r=e,si.measureText(i,r)).width,a);return ri<ii&&(ii=0,ni={}),ii++,ni[n]=a}function ui(t,e,n,i,r,o,a,s){return a?function(t,e,n,i,r,o,a,s){var l=_i(t,{rich:a,truncate:s,font:e,textAlign:n,textPadding:r,textLineHeight:o}),u=l.outerWidth,h=l.outerHeight,c=hi(0,u,n),d=ci(0,h,i);return new Sn(c,d,u,h)}(t,e,n,i,r,o,a,s):function(t,e,n,i,r,o,a){var s=yi(t,e,r,o,a),l=li(t,e);r&&(l+=r[1]+r[3]);var u=s.outerHeight,h=hi(0,l,n),c=ci(0,u,i),d=new Sn(h,c,l,u);return d.lineHeight=s.lineHeight,d}(t,e,n,i,r,o,s)}function hi(t,e,n){return"right"===n?t-=e:"center"===n&&(t-=e/2),t}function ci(t,e,n){return"middle"===n?t-=e/2:"bottom"===n&&(t-=e),t}function di(t,e,n){var i=e.textPosition,r=e.textDistance,o=n.x,a=n.y;r=r||0;var s=n.height,l=n.width,u=s/2,h="left",c="top";switch(i){case"left":o-=r,a+=u,h="right",c="middle";break;case"right":o+=r+l,a+=u,c="middle";break;case"top":o+=l/2,a-=r,h="center",c="bottom";break;case"bottom":o+=l/2,a+=s+r,h="center";break;case"inside":o+=l/2,a+=u,h="center",c="middle";break;case"insideLeft":o+=r,a+=u,c="middle";break;case"insideRight":o+=l-r,a+=u,h="right",c="middle";break;case"insideTop":o+=l/2,a+=r,h="center";break;case"insideBottom":o+=l/2,a+=s-r,h="center",c="bottom";break;case"insideTopLeft":o+=r,a+=r;break;case"insideTopRight":o+=l-r,a+=r,h="right";break;case"insideBottomLeft":o+=r,a+=s-r,c="bottom";break;case"insideBottomRight":o+=l-r,a+=s-r,h="right",c="bottom"}return(t=t||{}).x=o,t.y=a,t.textAlign=h,t.textVerticalAlign=c,t}function fi(t,e,n,i,r){if(!e)return"";var o=(t+"").split("\n");r=pi(e,n,i,r);for(var a=0,s=o.length;a<s;a++)o[a]=gi(o[a],r);return o.join("\n")}function pi(t,e,n,i){(i=k({},i)).font=e;n=W(n,"...");i.maxIterations=W(i.maxIterations,2);var r=i.minChar=W(i.minChar,0);i.cnCharWidth=li("国",e);var o=i.ascCharWidth=li("a",e);i.placeholder=W(i.placeholder,"");for(var a=t=Math.max(0,t-1),s=0;s<r&&o<=a;s++)a-=o;var l=li(n,e);return a<l&&(n="",l=0),a=t-l,i.ellipsis=n,i.ellipsisWidth=l,i.contentWidth=a,i.containerWidth=t,i}function gi(t,e){var n=e.containerWidth,i=e.font,r=e.contentWidth;if(!n)return"";var o=li(t,i);if(o<=n)return t;for(var a=0;;a++){if(o<=r||a>=e.maxIterations){t+=e.ellipsis;break}var s=0===a?mi(t,r,e.ascCharWidth,e.cnCharWidth):0<o?Math.floor(t.length*r/o):0;o=li(t=t.substr(0,s),i)}return""===t&&(t=e.placeholder),t}function mi(t,e,n,i){for(var r=0,o=0,a=t.length;o<a&&r<e;o++){var s=t.charCodeAt(o);r+=0<=s&&s<=127?n:i}return o}function vi(t){return li("国",t)}function yi(t,e,n,i,r){null!=t&&(t+="");var o=W(i,vi(e)),a=t?t.split("\n"):[],s=a.length*o,l=s,u=!0;if(n&&(l+=n[0]+n[2]),t&&r){u=!1;var h=r.outerHeight,c=r.outerWidth;if(null!=h&&h<l)t="",a=[];else if(null!=c)for(var d=pi(c-(n?n[1]+n[3]:0),e,r.ellipsis,{minChar:r.minChar,placeholder:r.placeholder}),f=0,p=a.length;f<p;f++)a[f]=gi(a[f],d)}return{lines:a,height:s,outerHeight:l,lineHeight:o,canCacheByTextString:u}}function _i(t,e){var n={lines:[],width:0,height:0};if(null!=t&&(t+=""),!t)return n;for(var i,r=oi.lastIndex=0;null!=(i=oi.exec(t));){var o=i.index;r<o&&xi(n,t.substring(r,o)),xi(n,i[2],i[1]),r=oi.lastIndex}r<t.length&&xi(n,t.substring(r,t.length));var a=n.lines,s=0,l=0,u=[],h=e.textPadding,c=e.truncate,d=c&&c.outerWidth,f=c&&c.outerHeight;h&&(null!=d&&(d-=h[1]+h[3]),null!=f&&(f-=h[0]+h[2]));for(var p=0;p<a.length;p++){for(var g=a[p],m=0,v=0,y=0;y<g.tokens.length;y++){var _=(D=g.tokens[y]).styleName&&e.rich[D.styleName]||{},x=D.textPadding=_.textPadding,w=D.font=_.font||e.font,b=D.textHeight=W(_.textHeight,vi(w));if(x&&(b+=x[0]+x[2]),D.height=b,D.lineHeight=G(_.textLineHeight,e.textLineHeight,b),D.textAlign=_&&_.textAlign||e.textAlign,D.textVerticalAlign=_&&_.textVerticalAlign||"middle",null!=f&&s+D.lineHeight>f)return{lines:[],width:0,height:0};D.textWidth=li(D.text,w);var S=_.textWidth,M=null==S||"auto"===S;if("string"==typeof S&&"%"===S.charAt(S.length-1))D.percentWidth=S,u.push(D),S=0;else{if(M){S=D.textWidth;var I=_.textBackgroundColor,C=I&&I.image;C&&ei(C=Qn(C))&&(S=Math.max(S,C.width*b/C.height))}var T=x?x[1]+x[3]:0;S+=T;var A=null!=d?d-v:null;null!=A&&A<S&&(!M||A<T?(D.text="",D.textWidth=S=0):(D.text=fi(D.text,A-T,w,c.ellipsis,{minChar:c.minChar}),D.textWidth=li(D.text,w),S=D.textWidth+T))}v+=D.width=S,_&&(m=Math.max(m,D.lineHeight))}g.width=v,s+=g.lineHeight=m,l=Math.max(l,v)}n.outerWidth=n.width=W(e.textWidth,l),n.outerHeight=n.height=W(e.textHeight,s),h&&(n.outerWidth+=h[1]+h[3],n.outerHeight+=h[0]+h[2]);for(p=0;p<u.length;p++){var D,k=(D=u[p]).percentWidth;D.width=parseInt(k,10)/100*l}return n}function xi(t,e,n){for(var i=""===e,r=e.split("\n"),o=t.lines,a=0;a<r.length;a++){var s=r[a],l={styleName:n,text:s,isLineHolder:!s&&!i};if(a)o.push({tokens:[l]});else{var u=(o[o.length-1]||(o[0]={tokens:[]})).tokens,h=u.length;1===h&&u[0].isLineHolder?u[0]=l:!s&&h&&!i||u.push(l)}}}function wi(t){var e=(t.fontSize||t.fontFamily)&&[t.fontStyle,t.fontWeight,(t.fontSize||12)+"px",t.fontFamily||"sans-serif"].join(" ");return e&&Y(e)||t.textFont||t.font}function bi(t,e){var n,i,r,o,a,s=e.x,l=e.y,u=e.width,h=e.height,c=e.r;u<0&&(s+=u,u=-u),h<0&&(l+=h,h=-h),"number"==typeof c?n=i=r=o=c:c instanceof Array?1===c.length?n=i=r=o=c[0]:2===c.length?(n=r=c[0],i=o=c[1]):3===c.length?(n=c[0],i=o=c[1],r=c[2]):(n=c[0],i=c[1],r=c[2],o=c[3]):n=i=r=o=0,u<n+i&&(n*=u/(a=n+i),i*=u/a),u<r+o&&(r*=u/(a=r+o),o*=u/a),h<i+r&&(i*=h/(a=i+r),r*=h/a),h<n+o&&(n*=h/(a=n+o),o*=h/a),t.moveTo(s+n,l),t.lineTo(s+u-i,l),0!==i&&t.arc(s+u-i,l+i,i,-Math.PI/2,0),t.lineTo(s+u,l+h-r),0!==r&&t.arc(s+u-r,l+h-r,r,0,Math.PI/2),t.lineTo(s+o,l+h),0!==o&&t.arc(s+o,l+h-o,o,Math.PI/2,Math.PI),t.lineTo(s,l+n),0!==n&&t.arc(s+n,l+n,n,Math.PI,1.5*Math.PI)}si.measureText=function(t,e){var n=_();return n.font=e||ai,n.measureText(t)};var Si=ai,Mi={left:1,right:1,center:1},Ii={top:1,bottom:1,middle:1},Ci=[["textShadowBlur","shadowBlur",0],["textShadowOffsetX","shadowOffsetX",0],["textShadowOffsetY","shadowOffsetY",0],["textShadowColor","shadowColor","transparent"]],Ti={},Ai={};function Di(t){return ki(t),D(t.rich,ki),t}function ki(t){if(t){t.font=wi(t);var e=t.textAlign;"middle"===e&&(e="center"),t.textAlign=null==e||Mi[e]?e:"left";var n=t.textVerticalAlign||t.textBaseline;"center"===n&&(n="middle"),t.textVerticalAlign=null==n||Ii[n]?n:"top",t.textPadding&&(t.textPadding=U(t.textPadding))}}function Pi(t,e,n,i,r,o){i.rich?function(t,e,n,i,r,o){o!==Bn&&(e.__attrCachedBy=Rn.NONE);var a=t.__textCotentBlock;a&&!t.__dirtyText||(a=t.__textCotentBlock=_i(n,i));!function(t,e,n,i,r){var o=n.width,a=n.outerWidth,s=n.outerHeight,l=i.textPadding,u=Ri(Ai,t,i,r),h=u.baseX,c=u.baseY,d=u.textAlign,f=u.textVerticalAlign;Li(e,i,r,h,c);var p=hi(h,a,d),g=ci(c,s,f),m=p,v=g;l&&(m+=l[3],v+=l[0]);var y=m+o;Ei(i)&&zi(t,e,i,p,g,a,s);for(var _=0;_<n.lines.length;_++){for(var x,w=n.lines[_],b=w.tokens,S=b.length,M=w.lineHeight,I=w.width,C=0,T=m,A=y,D=S-1;C<S&&(!(x=b[C]).textAlign||"left"===x.textAlign);)Oi(t,e,x,i,M,v,T,"left"),I-=x.width,T+=x.width,C++;for(;0<=D&&"right"===(x=b[D]).textAlign;)Oi(t,e,x,i,M,v,A,"right"),I-=x.width,A-=x.width,D--;for(T+=(o-(T-m)-(y-A)-I)/2;C<=D;)x=b[C],Oi(t,e,x,i,M,v,T+x.width/2,"center"),T+=x.width,C++;v+=M}}(t,e,a,i,r)}(t,e,n,i,r,o):function(t,e,n,i,r,o){var a,s=Ei(i),l=!1,u=e.__attrCachedBy===Rn.PLAIN_TEXT;o!==Bn?(o&&(a=o.style,l=!s&&u&&a),e.__attrCachedBy=s?Rn.NONE:Rn.PLAIN_TEXT):u&&(e.__attrCachedBy=Rn.NONE);var h=i.font||Si;l&&h===(a.font||Si)||(e.font=h);var c=t.__computedFont;t.__styleFont!==h&&(t.__styleFont=h,c=t.__computedFont=e.font);var d=i.textPadding,f=i.textLineHeight,p=t.__textCotentBlock;p&&!t.__dirtyText||(p=t.__textCotentBlock=yi(n,c,d,f,i.truncate));var g=p.outerHeight,m=p.lines,v=p.lineHeight,y=Ri(Ai,t,i,r),_=y.baseX,x=y.baseY,w=y.textAlign||"left",b=y.textVerticalAlign;Li(e,i,r,_,x);var S=ci(x,g,b),M=_,I=S;if(s||d){var C=li(n,c);d&&(C+=d[1]+d[3]);var T=hi(_,C,w);s&&zi(t,e,i,T,S,C,g),d&&(M=Wi(_,w,d),I+=d[0])}e.textAlign=w,e.textBaseline="middle",e.globalAlpha=i.opacity||1;for(var A=0;A<Ci.length;A++){var D=Ci[A],k=D[0],P=D[1],L=i[k];l&&L===a[k]||(e[P]=Nn(e,P,L||D[2]))}I+=v/2;var O=i.textStrokeWidth,E=l?a.textStrokeWidth:null,z=!l||O!==E,N=!l||z||i.textStroke!==a.textStroke,R=Vi(i.textStroke,O),B=Fi(i.textFill);R&&(z&&(e.lineWidth=O),N&&(e.strokeStyle=R));B&&(l&&i.textFill===a.textFill||(e.fillStyle=B));if(1===m.length)R&&e.strokeText(m[0],M,I),B&&e.fillText(m[0],M,I);else for(A=0;A<m.length;A++)R&&e.strokeText(m[A],M,I),B&&e.fillText(m[A],M,I),I+=v}(t,e,n,i,r,o)}function Li(t,e,n,i,r){if(n&&e.textRotation){var o=e.textOrigin;"center"===o?(i=n.width/2+n.x,r=n.height/2+n.y):o&&(i=o[0]+n.x,r=o[1]+n.y),t.translate(i,r),t.rotate(-e.textRotation),t.translate(-i,-r)}}function Oi(t,e,n,i,r,o,a,s){var l=i.rich[n.styleName]||{};l.text=n.text;var u=n.textVerticalAlign,h=o+r/2;"top"===u?h=o+n.height/2:"bottom"===u&&(h=o+r-n.height/2),!n.isLineHolder&&Ei(l)&&zi(t,e,l,"right"===s?a-n.width:"center"===s?a-n.width/2:a,h-n.height/2,n.width,n.height);var c=n.textPadding;c&&(a=Wi(a,s,c),h-=n.height/2-c[2]-n.textHeight/2),Bi(e,"shadowBlur",G(l.textShadowBlur,i.textShadowBlur,0)),Bi(e,"shadowColor",l.textShadowColor||i.textShadowColor||"transparent"),Bi(e,"shadowOffsetX",G(l.textShadowOffsetX,i.textShadowOffsetX,0)),Bi(e,"shadowOffsetY",G(l.textShadowOffsetY,i.textShadowOffsetY,0)),Bi(e,"textAlign",s),Bi(e,"textBaseline","middle"),Bi(e,"font",n.font||Si);var d=Vi(l.textStroke||i.textStroke,p),f=Fi(l.textFill||i.textFill),p=W(l.textStrokeWidth,i.textStrokeWidth);d&&(Bi(e,"lineWidth",p),Bi(e,"strokeStyle",d),e.strokeText(n.text,a,h)),f&&(Bi(e,"fillStyle",f),e.fillText(n.text,a,h))}function Ei(t){return!!(t.textBackgroundColor||t.textBorderWidth&&t.textBorderColor)}function zi(t,e,n,i,r,o,a){var s=n.textBackgroundColor,l=n.textBorderWidth,u=n.textBorderColor,h=z(s);if(Bi(e,"shadowBlur",n.textBoxShadowBlur||0),Bi(e,"shadowColor",n.textBoxShadowColor||"transparent"),Bi(e,"shadowOffsetX",n.textBoxShadowOffsetX||0),Bi(e,"shadowOffsetY",n.textBoxShadowOffsetY||0),h||l&&u){e.beginPath();var c=n.textBorderRadius;c?bi(e,{x:i,y:r,width:o,height:a,r:c}):e.rect(i,r,o,a),e.closePath()}if(h)if(Bi(e,"fillStyle",s),null!=n.fillOpacity){var d=e.globalAlpha;e.globalAlpha=n.fillOpacity*n.opacity,e.fill(),e.globalAlpha=d}else e.fill();else if(N(s)){var f=s.image;(f=Jn(f,null,t,Ni,s))&&ei(f)&&e.drawImage(f,i,r,o,a)}if(l&&u)if(Bi(e,"lineWidth",l),Bi(e,"strokeStyle",u),null!=n.strokeOpacity){d=e.globalAlpha;e.globalAlpha=n.strokeOpacity*n.opacity,e.stroke(),e.globalAlpha=d}else e.stroke()}function Ni(t,e){e.image=t}function Ri(t,e,n,i){var r=n.x||0,o=n.y||0,a=n.textAlign,s=n.textVerticalAlign;if(i){var l=n.textPosition;if(l instanceof Array)r=i.x+Hi(l[0],i.width),o=i.y+Hi(l[1],i.height);else{var u=e&&e.calculateTextPosition?e.calculateTextPosition(Ti,n,i):di(Ti,n,i);r=u.x,o=u.y,a=a||u.textAlign,s=s||u.textVerticalAlign}var h=n.textOffset;h&&(r+=h[0],o+=h[1])}return(t=t||{}).baseX=r,t.baseY=o,t.textAlign=a,t.textVerticalAlign=s,t}function Bi(t,e,n){return t[e]=Nn(t,e,n),t[e]}function Vi(t,e){return null==t||e<=0||"transparent"===t||"none"===t?null:t.image||t.colorStops?"#000":t}function Fi(t){return null==t||"none"===t?null:t.image||t.colorStops?"#000":t}function Hi(t,e){return"string"==typeof t?0<=t.lastIndexOf("%")?parseFloat(t)/100*e:parseFloat(t):t}function Wi(t,e,n){return"right"===e?t-n[1]:"center"===e?t+n[3]/2-n[1]/2:t+n[3]}function Gi(t,e){return null!=t&&(t||e.textBackgroundColor||e.textBorderWidth&&e.textBorderColor||e.textPadding)}function Zi(){}var Ui=new Sn;function Xi(t){for(var e in t=t||{},gn.call(this,t),t)t.hasOwnProperty(e)&&"style"!==e&&(this[e]=t[e]);this.style=new Fn(t.style,this),this._rect=null,this.__clipPaths=null}function Yi(t){Xi.call(this,t)}Xi.prototype={constructor:Xi,type:"displayable",__dirty:!0,invisible:!(Zi.prototype={constructor:Zi,drawRectText:function(t,e){var n=this.style;e=n.textRect||e,this.__dirty&&Di(n);var i=n.text;if(null!=i&&(i+=""),Gi(i,n)){t.save();var r=this.transform;n.transformText?this.setTransform(t):r&&(Ui.copy(e),Ui.applyTransform(r),e=Ui),Pi(this,t,i,n,e,Bn),t.restore()}}}),z:0,z2:0,zlevel:0,draggable:!1,dragging:!1,silent:!1,culling:!1,cursor:"pointer",rectHover:!1,progressive:!1,incremental:!1,globalScaleRatio:1,beforeBrush:function(t){},afterBrush:function(t){},brush:function(t,e){},getBoundingRect:function(){},contain:function(t,e){return this.rectContain(t,e)},traverse:function(t,e){t.call(e,this)},rectContain:function(t,e){var n=this.transformCoordToLocal(t,e);return this.getBoundingRect().contain(n[0],n[1])},dirty:function(){this.__dirty=this.__dirtyText=!0,this._rect=null,this.__zr&&this.__zr.refresh()},animateStyle:function(t){return this.animate("style",t)},attrKV:function(t,e){"style"!==t?gn.prototype.attrKV.call(this,t,e):this.style.set(e)},setStyle:function(t,e){return this.style.set(t,e),this.dirty(!1),this},useStyle:function(t){return this.style=new Fn(t,this),this.dirty(!1),this},calculateTextPosition:null},w(Xi,gn),S(Xi,Zi),Yi.prototype={constructor:Yi,type:"image",brush:function(t,e){var n=this.style,i=n.image;n.bind(t,this,e);var r=this._image=Jn(i,this._image,this,this.onload);if(r&&ei(r)){var o=n.x||0,a=n.y||0,s=n.width,l=n.height,u=r.width/r.height;if(null==s&&null!=l?s=l*u:null==l&&null!=s?l=s/u:null==s&&null==l&&(s=r.width,l=r.height),this.setTransform(t),n.sWidth&&n.sHeight){var h=n.sx||0,c=n.sy||0;t.drawImage(r,h,c,n.sWidth,n.sHeight,o,a,s,l)}else if(n.sx&&n.sy){var d=s-(h=n.sx),f=l-(c=n.sy);t.drawImage(r,h,c,d,f,o,a,s,l)}else t.drawImage(r,o,a,s,l);null!=n.text&&(this.restoreTransform(t),this.drawRectText(t,this.getBoundingRect()))}},getBoundingRect:function(){var t=this.style;return this._rect||(this._rect=new Sn(t.x||0,t.y||0,t.width||0,t.height||0)),this._rect}},w(Yi,Xi);var ji=314159;function qi(t){return parseInt(t,10)}var $i=new Sn(0,0,0,0),Ki=new Sn(0,0,0,0);function Qi(t,e,n){this.type="canvas";var i=!t.nodeName||"CANVAS"===t.nodeName.toUpperCase();this._opts=n=k({},n||{}),this.dpr=n.devicePixelRatio||un,this._singleCanvas=i;var r=(this.root=t).style;r&&(r["-webkit-tap-highlight-color"]="transparent",r["-webkit-user-select"]=r["user-select"]=r["-webkit-touch-callout"]="none",t.innerHTML=""),this.storage=e;var o=this._zlevelList=[],a=this._layers={};if(this._layerConfig={},this._needsManuallyCompositing=!1,i){var s=t.width,l=t.height;null!=n.width&&(s=n.width),null!=n.height&&(l=n.height),this.dpr=n.devicePixelRatio||1,t.width=s*this.dpr,t.height=l*this.dpr,this._width=s,this._height=l;var u=new qn(t,this,this.dpr);u.__builtin__=!0,u.initContext(),(a[ji]=u).zlevel=ji,o.push(ji),this._domRoot=t}else{this._width=this._getSize(0),this._height=this._getSize(1);var h=this._domRoot=function(t,e){var n=document.createElement("div");return n.style.cssText=["position:relative","width:"+t+"px","height:"+e+"px","padding:0","margin:0","border-width:0"].join(";")+";",n}(this._width,this._height);t.appendChild(h)}this._hoverlayer=null,this._hoverElements=[]}Qi.prototype={constructor:Qi,getType:function(){return"canvas"},isSingleCanvas:function(){return this._singleCanvas},getViewportRoot:function(){return this._domRoot},getViewportRootOffset:function(){var t=this.getViewportRoot();if(t)return{offsetLeft:t.offsetLeft||0,offsetTop:t.offsetTop||0}},refresh:function(t){var e=this.storage.getDisplayList(!0),n=this._zlevelList;this._redrawId=Math.random(),this._paintList(e,t,this._redrawId);for(var i=0;i<n.length;i++){var r=n[i],o=this._layers[r];if(!o.__builtin__&&o.refresh){var a=0===i?this._backgroundColor:null;o.refresh(a)}}return this.refreshHover(),this},addHover:function(t,e){if(!t.__hoverMir){var n=new t.constructor({style:t.style,shape:t.shape,z:t.z,z2:t.z2,silent:t.silent});return(n.__from=t).__hoverMir=n,e&&n.setStyle(e),this._hoverElements.push(n),n}},removeHover:function(t){var e=t.__hoverMir,n=this._hoverElements,i=x(n,e);0<=i&&n.splice(i,1),t.__hoverMir=null},clearHover:function(t){for(var e=this._hoverElements,n=0;n<e.length;n++){var i=e[n].__from;i&&(i.__hoverMir=null)}e.length=0},refreshHover:function(){var t=this._hoverElements,e=t.length,n=this._hoverlayer;if(n&&n.clear(),e){Ln(t,this.storage.displayableSortFunc);var i={};(n=n||(this._hoverlayer=this.getLayer(1e5))).ctx.save();for(var r=0;r<e;){var o=t[r],a=o.__from;a&&a.__zr?(r++,a.invisible||(o.transform=a.transform,o.invTransform=a.invTransform,o.__clipPaths=a.__clipPaths,this._doPaintEl(o,n,!0,i))):(t.splice(r,1),a.__hoverMir=null,e--)}n.ctx.restore()}},getHoverLayer:function(){return this.getLayer(1e5)},_paintList:function(t,e,n){if(this._redrawId===n){e=e||!1,this._updateLayerStatus(t);var i=this._doPaintList(t,e);if(this._needsManuallyCompositing&&this._compositeManually(),!i){var r=this;$n(function(){r._paintList(t,e,n)})}}},_compositeManually:function(){var e=this.getLayer(ji).ctx,n=this._domRoot.width,i=this._domRoot.height;e.clearRect(0,0,n,i),this.eachBuiltinLayer(function(t){t.virtual&&e.drawImage(t.dom,0,0,n,i)})},_doPaintList:function(t,e){for(var n=[],i=0;i<this._zlevelList.length;i++){var r=this._zlevelList[i];(s=this._layers[r]).__builtin__&&s!==this._hoverlayer&&(s.__dirty||e)&&n.push(s)}for(var o=!0,a=0;a<n.length;a++){var s,l=(s=n[a]).ctx,u={};l.save();var h=e?s.__startIndex:s.__drawIndex,c=!e&&s.incremental&&Date.now,d=c&&Date.now(),f=s.zlevel===this._zlevelList[0]?this._backgroundColor:null;if(s.__startIndex===s.__endIndex)s.clear(!1,f);else if(h===s.__startIndex){var p=t[h];p.incremental&&p.notClear&&!e||s.clear(!1,f)}-1===h&&(console.error("For some unknown reason. drawIndex is -1"),h=s.__startIndex);for(var g=h;g<s.__endIndex;g++){var m=t[g];if(this._doPaintEl(m,s,e,u),m.__dirty=m.__dirtyText=!1,c)if(15<Date.now()-d)break}s.__drawIndex=g,s.__drawIndex<s.__endIndex&&(o=!1),u.prevElClipPaths&&l.restore(),l.restore()}return v.wxa&&D(this._layers,function(t){t&&t.ctx&&t.ctx.draw&&t.ctx.draw()}),o},_doPaintEl:function(t,e,n,i){var r=e.ctx,o=t.transform;if((e.__dirty||n)&&!t.invisible&&0!==t.style.opacity&&(!o||o[0]||o[3])&&(!t.culling||!function(t,e,n){return $i.copy(t.getBoundingRect()),t.transform&&$i.applyTransform(t.transform),Ki.width=e,Ki.height=n,!$i.intersect(Ki)}(t,this._width,this._height))){var a=t.__clipPaths,s=i.prevElClipPaths;s&&!function(t,e){if(t===e)return!1;if(!t||!e||t.length!==e.length)return!0;for(var n=0;n<t.length;n++)if(t[n]!==e[n])return!0;return!1}(a,s)||(s&&(r.restore(),i.prevElClipPaths=null,i.prevEl=null),a&&(r.save(),function(t,e){for(var n=0;n<t.length;n++){var i=t[n];i.setTransform(e),e.beginPath(),i.buildPath(e,i.shape),e.clip(),i.restoreTransform(e)}}(a,r),i.prevElClipPaths=a)),t.beforeBrush&&t.beforeBrush(r),t.brush(r,i.prevEl||null),(i.prevEl=t).afterBrush&&t.afterBrush(r)}},getLayer:function(t,e){this._singleCanvas&&!this._needsManuallyCompositing&&(t=ji);var n=this._layers[t];return n||((n=new qn("zr_"+t,this,this.dpr)).zlevel=t,n.__builtin__=!0,this._layerConfig[t]?m(n,this._layerConfig[t],!0):this._layerConfig[t-.01]&&m(n,this._layerConfig[t-.01],!0),e&&(n.virtual=e),this.insertLayer(t,n),n.initContext()),n},insertLayer:function(t,e){var n=this._layers,i=this._zlevelList,r=i.length,o=null,a=-1,s=this._domRoot;if(n[t])dn("ZLevel "+t+" has been used already");else if(function(t){return!!t&&(!!t.__builtin__||"function"==typeof t.resize&&"function"==typeof t.refresh)}(e)){if(0<r&&t>i[0]){for(a=0;a<r-1&&!(i[a]<t&&i[a+1]>t);a++);o=n[i[a]]}if(i.splice(a+1,0,t),!(n[t]=e).virtual)if(o){var l=o.dom;l.nextSibling?s.insertBefore(e.dom,l.nextSibling):s.appendChild(e.dom)}else s.firstChild?s.insertBefore(e.dom,s.firstChild):s.appendChild(e.dom)}else dn("Layer of zlevel "+t+" is not valid")},eachLayer:function(t,e){var n,i,r=this._zlevelList;for(i=0;i<r.length;i++)n=r[i],t.call(e,this._layers[n],n)},eachBuiltinLayer:function(t,e){var n,i,r,o=this._zlevelList;for(r=0;r<o.length;r++)i=o[r],(n=this._layers[i]).__builtin__&&t.call(e,n,i)},eachOtherLayer:function(t,e){var n,i,r,o=this._zlevelList;for(r=0;r<o.length;r++)i=o[r],(n=this._layers[i]).__builtin__||t.call(e,n,i)},getLayers:function(){return this._layers},_updateLayerStatus:function(t){function e(t){r&&(r.__endIndex!==t&&(r.__dirty=!0),r.__endIndex=t)}if(this.eachBuiltinLayer(function(t,e){t.__dirty=t.__used=!1}),this._singleCanvas)for(var n=1;n<t.length;n++){if((a=t[n]).zlevel!==t[n-1].zlevel||a.incremental){this._needsManuallyCompositing=!0;break}}var i,r=null,o=0;for(n=0;n<t.length;n++){var a,s,l=(a=t[n]).zlevel;i!==l&&(i=l,o=0),a.incremental?((s=this.getLayer(l+.001,this._needsManuallyCompositing)).incremental=!0,o=1):s=this.getLayer(l+(0<o?.01:0),this._needsManuallyCompositing),s.__builtin__||dn("ZLevel "+l+" has been used by unkown layer "+s.id),s!==r&&(s.__used=!0,s.__startIndex!==n&&(s.__dirty=!0),s.__startIndex=n,s.incremental?s.__drawIndex=-1:s.__drawIndex=n,e(n),r=s),a.__dirty&&(s.__dirty=!0,s.incremental&&s.__drawIndex<0&&(s.__drawIndex=n))}e(n),this.eachBuiltinLayer(function(t,e){!t.__used&&0<t.getElementCount()&&(t.__dirty=!0,t.__startIndex=t.__endIndex=t.__drawIndex=0),t.__dirty&&t.__drawIndex<0&&(t.__drawIndex=t.__startIndex)})},clear:function(){return this.eachBuiltinLayer(this._clearLayer),this},_clearLayer:function(t){t.clear()},setBackgroundColor:function(t){this._backgroundColor=t},configLayer:function(t,e){if(e){var n=this._layerConfig;n[t]?m(n[t],e,!0):n[t]=e;for(var i=0;i<this._zlevelList.length;i++){var r=this._zlevelList[i];if(r===t||r===t+.01)m(this._layers[r],n[t],!0)}}},delLayer:function(t){var e=this._layers,n=this._zlevelList,i=e[t];i&&(i.dom.parentNode.removeChild(i.dom),delete e[t],n.splice(x(n,t),1))},resize:function(e,n){if(this._domRoot.style){var t=this._domRoot;t.style.display="none";var i=this._opts;if(null!=e&&(i.width=e),null!=n&&(i.height=n),e=this._getSize(0),n=this._getSize(1),t.style.display="",this._width!==e||n!==this._height){for(var r in t.style.width=e+"px",t.style.height=n+"px",this._layers)this._layers.hasOwnProperty(r)&&this._layers[r].resize(e,n);D(this._progressiveLayers,function(t){t.resize(e,n)}),this.refresh(!0)}this._width=e,this._height=n}else{if(null==e||null==n)return;this._width=e,this._height=n,this.getLayer(ji).resize(e,n)}return this},clearLayer:function(t){var e=this._layers[t];e&&e.clear()},dispose:function(){this.root.innerHTML="",this.root=this.storage=this._domRoot=this._layers=null},getRenderedCanvas:function(t){if(t=t||{},this._singleCanvas&&!this._compositeManually)return this._layers[ji].dom;var e=new qn("image",this,t.pixelRatio||this.dpr);if(e.initContext(),e.clear(!1,t.backgroundColor||this._backgroundColor),t.pixelRatio<=this.dpr){this.refresh();var n=e.dom.width,i=e.dom.height,r=e.ctx;this.eachLayer(function(t){t.__builtin__?r.drawImage(t.dom,0,0,n,i):t.renderToCanvas&&(e.ctx.save(),t.renderToCanvas(e.ctx),e.ctx.restore())})}else for(var o={},a=this.storage.getDisplayList(!0),s=0;s<a.length;s++){var l=a[s];this._doPaintEl(l,e,!0,o)}return e.dom},getWidth:function(){return this._width},getHeight:function(){return this._height},_getSize:function(t){var e=this._opts,n=["width","height"][t],i=["clientWidth","clientHeight"][t],r=["paddingLeft","paddingTop"][t],o=["paddingRight","paddingBottom"][t];if(null!=e[n]&&"auto"!==e[n])return parseFloat(e[n]);var a=this.root,s=document.defaultView.getComputedStyle(a);return(a[i]||qi(s[n])||qi(a.style[n]))-(qi(s[r])||0)-(qi(s[o])||0)|0},pathToImage:function(t,e){e=e||this.dpr;var n=document.createElement("canvas"),i=n.getContext("2d"),r=t.getBoundingRect(),o=t.style,a=o.shadowBlur*e,s=o.shadowOffsetX*e,l=o.shadowOffsetY*e,u=o.hasStroke()?o.lineWidth:0,h=Math.max(u/2,a-s),c=Math.max(u/2,s+a),d=Math.max(u/2,a-l),f=Math.max(u/2,l+a),p=r.width+h+c,g=r.height+d+f;n.width=p*e,n.height=g*e,i.scale(e,e),i.clearRect(0,0,p,g),i.dpr=e;var m={position:t.position,rotation:t.rotation,scale:t.scale};t.position=[h-r.x,d-r.y],t.rotation=0,t.scale=[1,1],t.updateTransform(),t&&t.brush(i);var v=new Yi({style:{x:0,y:0,image:n}});return null!=m.position&&(v.position=t.position=m.position),null!=m.rotation&&(v.rotation=t.rotation=m.rotation),null!=m.scale&&(v.scale=t.scale=m.scale),v}};function Ji(t){t=t||{},this.stage=t.stage||{},this.onframe=t.onframe||function(){},this._clips=[],this._running=!1,this._time,this._pausedTime,this._pauseStart,this._paused=!1,It.call(this)}Ji.prototype={constructor:Ji,addClip:function(t){this._clips.push(t)},addAnimator:function(t){t.animation=this;for(var e=t.getClips(),n=0;n<e.length;n++)this.addClip(e[n])},removeClip:function(t){var e=x(this._clips,t);0<=e&&this._clips.splice(e,1)},removeAnimator:function(t){for(var e=t.getClips(),n=0;n<e.length;n++)this.removeClip(e[n]);t.animation=null},_update:function(){for(var t=(new Date).getTime()-this._pausedTime,e=t-this._time,n=this._clips,i=n.length,r=[],o=[],a=0;a<i;a++){var s=n[a],l=s.step(t,e);l&&(r.push(l),o.push(s))}for(a=0;a<i;)n[a]._needsRemove?(n[a]=n[i-1],n.pop(),i--):a++;i=r.length;for(a=0;a<i;a++)o[a].fire(r[a]);this._time=t,this.onframe(e),this.trigger("frame",e),this.stage.update&&this.stage.update()},_startLoop:function(){var e=this;this._running=!0,$n(function t(){e._running&&($n(t),e._paused||e._update())})},start:function(){this._time=(new Date).getTime(),this._pausedTime=0,this._startLoop()},stop:function(){this._running=!1},pause:function(){this._paused||(this._pauseStart=(new Date).getTime(),this._paused=!0)},resume:function(){this._paused&&(this._pausedTime+=(new Date).getTime()-this._pauseStart,this._paused=!1)},clear:function(){this._clips=[]},isFinished:function(){return!this._clips.length},animate:function(t,e){var n=new sn(t,(e=e||{}).loop,e.getter,e.setter);return this.addAnimator(n),n}},S(Ji,It);var tr,er,nr=v.domSupported,ir=(er={pointerdown:1,pointerup:1,pointermove:1,pointerout:1},{mouse:tr=["click","dblclick","mousewheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],touch:["touchstart","touchend","touchmove"],pointer:P(tr,function(t){var e=t.replace("mouse","pointer");return er.hasOwnProperty(e)?e:t})}),rr={mouse:["mousemove","mouseup"],pointer:["pointermove","pointerup"]};function or(t){return"mousewheel"===t&&v.browser.firefox?"DOMMouseScroll":t}function ar(t){var e=t.pointerType;return"pen"===e||"touch"===e}function sr(t){t&&(t.zrByTouch=!0)}function lr(t,e){for(var n=e,i=!1;n&&9!==n.nodeType&&!(i=n.domBelongToZr||n!==e&&n===t.painterRoot);)n=n.parentNode;return i}function ur(t,e){this.type=e.type,this.target=this.currentTarget=t.dom,this.pointerType=e.pointerType,this.clientX=e.clientX,this.clientY=e.clientY}var hr=ur.prototype;hr.stopPropagation=hr.stopImmediatePropagation=hr.preventDefault=J;var cr={mousedown:function(t){t=Ft(this.dom,t),this._mayPointerCapture=[t.zrX,t.zrY],this.trigger("mousedown",t)},mousemove:function(t){t=Ft(this.dom,t);var e=this._mayPointerCapture;!e||t.zrX===e[0]&&t.zrY===e[1]||vr(this,!0),this.trigger("mousemove",t)},mouseup:function(t){t=Ft(this.dom,t),vr(this,!1),this.trigger("mouseup",t)},mouseout:function(t){t=Ft(this.dom,t),this._pointerCapturing&&(t.zrEventControl="no_globalout");var e=t.toElement||t.relatedTarget;t.zrIsToLocalDOM=lr(this,e),this.trigger("mouseout",t)},touchstart:function(t){sr(t=Ft(this.dom,t)),this._lastTouchMoment=new Date,this.handler.processGesture(t,"start"),cr.mousemove.call(this,t),cr.mousedown.call(this,t)},touchmove:function(t){sr(t=Ft(this.dom,t)),this.handler.processGesture(t,"change"),cr.mousemove.call(this,t)},touchend:function(t){sr(t=Ft(this.dom,t)),this.handler.processGesture(t,"end"),cr.mouseup.call(this,t),+new Date-this._lastTouchMoment<300&&cr.click.call(this,t)},pointerdown:function(t){cr.mousedown.call(this,t)},pointermove:function(t){ar(t)||cr.mousemove.call(this,t)},pointerup:function(t){cr.mouseup.call(this,t)},pointerout:function(t){ar(t)||cr.mouseout.call(this,t)}};D(["click","mousewheel","dblclick","contextmenu"],function(e){cr[e]=function(t){t=Ft(this.dom,t),this.trigger(e,t)}});var dr={pointermove:function(t){ar(t)||dr.mousemove.call(this,t)},pointerup:function(t){dr.mouseup.call(this,t)},mousemove:function(t){this.trigger("mousemove",t)},mouseup:function(t){var e=this._pointerCapturing;vr(this,!1),this.trigger("mouseup",t),e&&(t.zrEventControl="only_globalout",this.trigger("mouseout",t))}};function fr(n,i){var r=i.domHandlers;v.pointerEventsSupported?D(ir.pointer,function(e){gr(i,e,function(t){r[e].call(n,t)})}):(v.touchEventsSupported&&D(ir.touch,function(e){gr(i,e,function(t){r[e].call(n,t),function(t){t.touching=!0,null!=t.touchTimer&&(clearTimeout(t.touchTimer),t.touchTimer=null),t.touchTimer=setTimeout(function(){t.touching=!1,t.touchTimer=null},700)}(i)})}),D(ir.mouse,function(e){gr(i,e,function(t){t=Vt(t),i.touching||r[e].call(n,t)})}))}function pr(n,i){function t(e){gr(i,e,function(t){t=Vt(t),lr(n,t.target)||(t=function(t,e){return Ft(t.dom,new ur(t,e),!0)}(n,t),i.domHandlers[e].call(n,t))},{capture:!0})}v.pointerEventsSupported?D(rr.pointer,t):v.touchEventsSupported||D(rr.mouse,t)}function gr(t,e,n,i){t.mounted[e]=n,t.listenerOpts[e]=i,Ht(t.domTarget,or(e),n,i)}function mr(t){var e,n,i,r,o=t.mounted;for(var a in o)o.hasOwnProperty(a)&&(e=t.domTarget,n=or(a),i=o[a],r=t.listenerOpts[a],Et?e.removeEventListener(n,i,r):e.detachEvent("on"+n,i));t.mounted={}}function vr(t,e){if(t._mayPointerCapture=null,nr&&t._pointerCapturing^e){t._pointerCapturing=e;var n=t._globalHandlerScope;e?pr(t,n):mr(n)}}function yr(t,e){this.domTarget=t,this.domHandlers=e,this.mounted={},this.listenerOpts={},this.touchTimer=null,this.touching=!1}function _r(t,e){It.call(this),this.dom=t,this.painterRoot=e,this._localHandlerScope=new yr(t,cr),nr&&(this._globalHandlerScope=new yr(document,dr)),this._pointerCapturing=!1,this._mayPointerCapture=null,fr(this,this._localHandlerScope)}var xr=_r.prototype;xr.dispose=function(){mr(this._localHandlerScope),nr&&mr(this._globalHandlerScope)},xr.setCursor=function(t){this.dom.style&&(this.dom.style.cursor=t||"default")},S(_r,It);var wr=!v.canvasSupported,br={canvas:Qi},Sr={};function Mr(t,e){var n=new Cr(i(),t,e);return Sr[n.id]=n}function Ir(t,e){br[t]=e}var Cr=function(t,e,n){n=n||{},this.dom=e,this.id=t;var i=this,r=new En,o=n.renderer;if(wr){if(!br.vml)throw new Error("You need to require 'zrender/vml/vml' to support IE8");o="vml"}else o&&br[o]||(o="canvas");var a=new br[o](e,r,n,t);this.storage=r,this.painter=a;var s=v.node||v.worker?null:new _r(a.getViewportRoot(),a.root);this.handler=new $t(r,a,s,a.root),this.animation=new Ji({stage:{update:C(this.flush,this)}}),this.animation.start(),this._needsRefresh;var l=r.delFromStorage,u=r.addToStorage;r.delFromStorage=function(t){l.call(r,t),t&&t.removeSelfFromZr(i)},r.addToStorage=function(t){u.call(r,t),t.addSelfToZr(i)}};Cr.prototype={constructor:Cr,getId:function(){return this.id},add:function(t){this.storage.addRoot(t),this._needsRefresh=!0},remove:function(t){this.storage.delRoot(t),this._needsRefresh=!0},configLayer:function(t,e){this.painter.configLayer&&this.painter.configLayer(t,e),this._needsRefresh=!0},setBackgroundColor:function(t){this.painter.setBackgroundColor&&this.painter.setBackgroundColor(t),this._needsRefresh=!0},refreshImmediately:function(){this._needsRefresh=this._needsRefreshHover=!1,this.painter.refresh(),this._needsRefresh=this._needsRefreshHover=!1},refresh:function(){this._needsRefresh=!0},flush:function(){var t;this._needsRefresh&&(t=!0,this.refreshImmediately()),this._needsRefreshHover&&(t=!0,this.refreshHoverImmediately()),t&&this.trigger("rendered")},addHover:function(t,e){if(this.painter.addHover){var n=this.painter.addHover(t,e);return this.refreshHover(),n}},removeHover:function(t){this.painter.removeHover&&(this.painter.removeHover(t),this.refreshHover())},clearHover:function(){this.painter.clearHover&&(this.painter.clearHover(),this.refreshHover())},refreshHover:function(){this._needsRefreshHover=!0},refreshHoverImmediately:function(){this._needsRefreshHover=!1,this.painter.refreshHover&&this.painter.refreshHover()},resize:function(t){t=t||{},this.painter.resize(t.width,t.height),this.handler.resize()},clearAnimation:function(){this.animation.clear()},getWidth:function(){return this.painter.getWidth()},getHeight:function(){return this.painter.getHeight()},pathToImage:function(t,e){return this.painter.pathToImage(t,e)},setCursorStyle:function(t){this.handler.setCursorStyle(t)},findHover:function(t,e){return this.handler.findHover(t,e)},on:function(t,e,n){this.handler.on(t,e,n)},off:function(t,e){this.handler.off(t,e)},trigger:function(t,e){this.handler.trigger(t,e)},clear:function(){this.storage.delRoot(),this.painter.clear()},dispose:function(){this.animation.stop(),this.clear(),this.storage.dispose(),this.painter.dispose(),this.handler.dispose(),this.animation=this.storage=this.painter=this.handler=null,function(t){delete Sr[t]}(this.id)}};var Tr=(Object.freeze||Object)({version:"4.3.1",init:Mr,dispose:function(t){if(t)t.dispose();else{for(var e in Sr)Sr.hasOwnProperty(e)&&Sr[e].dispose();Sr={}}return this},getInstance:function(t){return Sr[t]},registerPainter:Ir}),Ar=D,Dr=N,kr=O,Pr="series\0";function Lr(t){return t instanceof Array?t:null==t?[]:[t]}function Or(t,e,n){if(t){t[e]=t[e]||{},t.emphasis=t.emphasis||{},t.emphasis[e]=t.emphasis[e]||{};for(var i=0,r=n.length;i<r;i++){var o=n[i];!t.emphasis[e].hasOwnProperty(o)&&t[e].hasOwnProperty(o)&&(t.emphasis[e][o]=t[e][o])}}}var Er=["fontStyle","fontWeight","fontSize","fontFamily","rich","tag","color","textBorderColor","textBorderWidth","width","height","lineHeight","align","verticalAlign","baseline","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY","textShadowColor","textShadowBlur","textShadowOffsetX","textShadowOffsetY","backgroundColor","borderColor","borderWidth","borderRadius","padding"];function zr(t){return!Dr(t)||kr(t)||t instanceof Date?t:t.value}function Nr(t,r){r=(r||[]).slice();var o=P(t||[],function(t,e){return{exist:t}});return Ar(r,function(t,e){if(Dr(t)){for(var n=0;n<o.length;n++)if(!o[n].option&&null!=t.id&&o[n].exist.id===t.id+"")return o[n].option=t,void(r[e]=null);for(n=0;n<o.length;n++){var i=o[n].exist;if(!(o[n].option||null!=i.id&&null!=t.id||null==t.name||Vr(t)||Vr(i)||i.name!==t.name+""))return o[n].option=t,void(r[e]=null)}}}),Ar(r,function(t,e){if(Dr(t)){for(var n=0;n<o.length;n++){var i=o[n].exist;if(!o[n].option&&!Vr(i)&&null==t.id){o[n].option=t;break}}n>=o.length&&o.push({option:t})}}),o}function Rr(t){var a=Q();Ar(t,function(t,e){var n=t.exist;n&&a.set(n.id,t)}),Ar(t,function(t,e){var n=t.option;X(!n||null==n.id||!a.get(n.id)||a.get(n.id)===t,"id duplicates: "+(n&&n.id)),n&&null!=n.id&&a.set(n.id,t),t.keyInfo||(t.keyInfo={})}),Ar(t,function(t,e){var n=t.exist,i=t.option,r=t.keyInfo;if(Dr(i)){if(r.name=null!=i.name?i.name+"":n?n.name:Pr+e,n)r.id=n.id;else if(null!=i.id)r.id=i.id+"";else for(var o=0;r.id="\0"+r.name+"\0"+o++,a.get(r.id););a.set(r.id,t)}})}function Br(t){var e=t.name;return!(!e||!e.indexOf(Pr))}function Vr(t){return Dr(t)&&t.id&&0===(t.id+"").indexOf("\0_ec_\0")}function Fr(e,t){return null!=t.dataIndexInside?t.dataIndexInside:null!=t.dataIndex?O(t.dataIndex)?P(t.dataIndex,function(t){return e.indexOfRawIndex(t)}):e.indexOfRawIndex(t.dataIndex):null!=t.name?O(t.name)?P(t.name,function(t){return e.indexOfName(t)}):e.indexOfName(t.name):void 0}function Hr(){var e="__\0ec_inner_"+Wr+++"_"+Math.random().toFixed(5);return function(t){return t[e]||(t[e]={})}}var Wr=0;function Gr(s,l,u){if(z(l)){var t={};t[l+"Index"]=0,l=t}var e=u&&u.defaultMainType;!e||Zr(l,e+"Index")||Zr(l,e+"Id")||Zr(l,e+"Name")||(l[e+"Index"]=0);var h={};return Ar(l,function(t,e){t=l[e];if("dataIndex"!==e&&"dataIndexInside"!==e){var n=e.match(/^(\w+)(Index|Id|Name)$/)||[],i=n[1],r=(n[2]||"").toLowerCase();if(!(!i||!r||null==t||"index"===r&&"none"===t||u&&u.includeMainTypes&&x(u.includeMainTypes,i)<0)){var o={mainType:i};"index"===r&&"all"===t||(o[r]=t);var a=s.queryComponents(o);h[i+"Models"]=a,h[i+"Model"]=a[0]}}else h[e]=t}),h}function Zr(t,e){return t&&t.hasOwnProperty(e)}function Ur(t,e,n){t.setAttribute?t.setAttribute(e,n):t[e]=n}function Xr(t){return"auto"===t?v.domSupported?"html":"richText":t||"html"}var Yr=".",jr="___EC__COMPONENT__CONTAINER___";function qr(t){var e={main:"",sub:""};return t&&(t=t.split(Yr),e.main=t[0]||"",e.sub=t[1]||""),e}function $r(t){(t.$constructor=t).extend=function(t){function e(){t.$constructor?t.$constructor.apply(this,arguments):n.apply(this,arguments)}var n=this;return k(e.prototype,t),e.extend=this.extend,e.superCall=Jr,e.superApply=to,w(e,this),e.superClass=n,e}}var Kr=0;function Qr(t){var e=["__\0is_clz",Kr++,Math.random().toFixed(3)].join("_");t.prototype[e]=!0,t.isInstance=function(t){return!(!t||!t[e])}}function Jr(t,e){var n=Z(arguments,2);return this.superClass.prototype[e].apply(t,n)}function to(t,e,n){return this.superClass.prototype[e].apply(t,n)}function eo(n,t){t=t||{};var r={};if(n.registerClass=function(t,e){if(e)if(function(t){X(/^[a-zA-Z0-9_]+([.][a-zA-Z0-9_]+)?$/.test(t),'componentType "'+t+'" illegal')}(e),(e=qr(e)).sub){if(e.sub!==jr){(function(t){var e=r[t.main];e&&e[jr]||((e=r[t.main]={})[jr]=!0);return e})(e)[e.sub]=t}}else r[e.main]=t;return t},n.getClass=function(t,e,n){var i=r[t];if(i&&i[jr]&&(i=e?i[e]:null),n&&!i)throw new Error(e?"Component "+t+"."+(e||"")+" not exists. Load it first.":t+".type should be specified.");return i},n.getClassesByMainType=function(t){t=qr(t);var n=[],e=r[t.main];return e&&e[jr]?D(e,function(t,e){e!==jr&&n.push(t)}):n.push(e),n},n.hasClass=function(t){return t=qr(t),!!r[t.main]},n.getAllClassMainTypes=function(){var n=[];return D(r,function(t,e){n.push(e)}),n},n.hasSubTypes=function(t){t=qr(t);var e=r[t.main];return e&&e[jr]},n.parseClassType=qr,t.registerWhenExtend){var i=n.extend;i&&(n.extend=function(t){var e=i.call(this,t);return n.registerClass(e,t.type)})}return n}function no(s){for(var t=0;t<s.length;t++)s[t][1]||(s[t][1]=s[t][0]);return function(t,e,n){for(var i={},r=0;r<s.length;r++){var o=s[r][1];if(!(e&&0<=x(e,o)||n&&x(n,o)<0)){var a=t.getShallow(o);null!=a&&(i[s[r][0]]=a)}}return i}}var io=no([["lineWidth","width"],["stroke","color"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"]]),ro={getLineStyle:function(t){var e=io(this,t);return e.lineDash=this.getLineDash(e.lineWidth),e},getLineDash:function(t){null==t&&(t=1);var e=this.get("type"),n=Math.max(t,2),i=4*t;return"solid"!==e&&null!=e&&("dashed"===e?[i,i]:[n,n])}},oo=no([["fill","color"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["opacity"],["shadowColor"]]),ao={getAreaStyle:function(t,e){return oo(this,t,e)}},so=Math.pow,lo=Math.sqrt,uo=1e-8,ho=1e-4,co=lo(3),fo=1/3,po=nt(),go=nt(),mo=nt();function vo(t){return-uo<t&&t<uo}function yo(t){return uo<t||t<-uo}function _o(t,e,n,i,r){var o=1-r;return o*o*(o*t+3*r*e)+r*r*(r*i+3*o*n)}function xo(t,e,n,i,r){var o=1-r;return 3*(((e-t)*o+2*(n-e)*r)*o+(i-n)*r*r)}function wo(t,e,n,i,r){var o=6*n-12*e+6*t,a=9*e+3*i-3*t-9*n,s=3*e-3*t,l=0;if(vo(a)){if(yo(o))0<=(h=-s/o)&&h<=1&&(r[l++]=h)}else{var u=o*o-4*a*s;if(vo(u))r[0]=-o/(2*a);else if(0<u){var h,c=lo(u),d=(-o-c)/(2*a);0<=(h=(-o+c)/(2*a))&&h<=1&&(r[l++]=h),0<=d&&d<=1&&(r[l++]=d)}}return l}function bo(t,e,n,i,r,o){var a=(e-t)*r+t,s=(n-e)*r+e,l=(i-n)*r+n,u=(s-a)*r+a,h=(l-s)*r+s,c=(h-u)*r+u;o[0]=t,o[1]=a,o[2]=u,o[3]=c,o[4]=c,o[5]=h,o[6]=l,o[7]=i}function So(t,e,n,i){var r=1-i;return r*(r*t+2*i*e)+i*i*n}function Mo(t,e,n,i){return 2*((1-i)*(e-t)+i*(n-e))}function Io(t,e,n){var i=t+n-2*e;return 0==i?.5:(t-e)/i}function Co(t,e,n,i,r){var o=(e-t)*i+t,a=(n-e)*i+e,s=(a-o)*i+o;r[0]=t,r[1]=o,r[2]=s,r[3]=s,r[4]=a,r[5]=n}var To=Math.min,Ao=Math.max,Do=Math.sin,ko=Math.cos,Po=2*Math.PI,Lo=nt(),Oo=nt(),Eo=nt();function zo(t,e,n){if(0!==t.length){var i,r=t[0],o=r[0],a=r[0],s=r[1],l=r[1];for(i=1;i<t.length;i++)r=t[i],o=To(o,r[0]),a=Ao(a,r[0]),s=To(s,r[1]),l=Ao(l,r[1]);e[0]=o,e[1]=s,n[0]=a,n[1]=l}}function No(t,e,n,i,r,o){r[0]=To(t,n),r[1]=To(e,i),o[0]=Ao(t,n),o[1]=Ao(e,i)}var Ro=[],Bo=[];function Vo(t,e,n,i,r,o,a,s,l,u){var h,c=wo,d=_o,f=c(t,n,r,a,Ro);for(l[0]=1/0,l[1]=1/0,u[0]=-1/0,u[1]=-1/0,h=0;h<f;h++){var p=d(t,n,r,a,Ro[h]);l[0]=To(p,l[0]),u[0]=Ao(p,u[0])}for(f=c(e,i,o,s,Bo),h=0;h<f;h++){var g=d(e,i,o,s,Bo[h]);l[1]=To(g,l[1]),u[1]=Ao(g,u[1])}l[0]=To(t,l[0]),u[0]=Ao(t,u[0]),l[0]=To(a,l[0]),u[0]=Ao(a,u[0]),l[1]=To(e,l[1]),u[1]=Ao(e,u[1]),l[1]=To(s,l[1]),u[1]=Ao(s,u[1])}function Fo(t,e,n,i,r,o,a,s,l){var u=_t,h=xt,c=Math.abs(r-o);if(c%Po<1e-4&&1e-4<c)return s[0]=t-n,s[1]=e-i,l[0]=t+n,void(l[1]=e+i);if(Lo[0]=ko(r)*n+t,Lo[1]=Do(r)*i+e,Oo[0]=ko(o)*n+t,Oo[1]=Do(o)*i+e,u(s,Lo,Oo),h(l,Lo,Oo),(r%=Po)<0&&(r+=Po),(o%=Po)<0&&(o+=Po),o<r&&!a?o+=Po:r<o&&a&&(r+=Po),a){var d=o;o=r,r=d}for(var f=0;f<o;f+=Math.PI/2)r<f&&(Eo[0]=ko(f)*n+t,Eo[1]=Do(f)*i+e,u(s,Eo,s),h(l,Eo,l))}var Ho={M:1,L:2,C:3,Q:4,A:5,Z:6,R:7},Wo=[],Go=[],Zo=[],Uo=[],Xo=Math.min,Yo=Math.max,jo=Math.cos,qo=Math.sin,$o=Math.sqrt,Ko=Math.abs,Qo="undefined"!=typeof Float32Array,Jo=function(t){this._saveData=!t,this._saveData&&(this.data=[]),this._ctx=null};function ta(t,e,n,i,r,o,a){if(0===r)return!1;var s=r,l=0;if(e+s<a&&i+s<a||a<e-s&&a<i-s||t+s<o&&n+s<o||o<t-s&&o<n-s)return!1;if(t===n)return Math.abs(o-t)<=s/2;var u=(l=(e-i)/(t-n))*o-a+(t*i-n*e)/(t-n);return u*u/(l*l+1)<=s/2*s/2}function ea(t,e,n,i,r,o,a,s,l,u,h){if(0===l)return!1;var c=l;return!(e+c<h&&i+c<h&&o+c<h&&s+c<h||h<e-c&&h<i-c&&h<o-c&&h<s-c||t+c<u&&n+c<u&&r+c<u&&a+c<u||u<t-c&&u<n-c&&u<r-c&&u<a-c)&&function(t,e,n,i,r,o,a,s,l,u,h){var c,d,f,p,g,m=.005,v=1/0;po[0]=l,po[1]=u;for(var y=0;y<1;y+=.05)go[0]=_o(t,n,r,a,y),go[1]=_o(e,i,o,s,y),(p=vt(po,go))<v&&(c=y,v=p);v=1/0;for(var _=0;_<32&&!(m<ho);_++)d=c-m,f=c+m,go[0]=_o(t,n,r,a,d),go[1]=_o(e,i,o,s,d),p=vt(go,po),0<=d&&p<v?(c=d,v=p):(mo[0]=_o(t,n,r,a,f),mo[1]=_o(e,i,o,s,f),g=vt(mo,po),f<=1&&g<v?(c=f,v=g):m*=.5);return h&&(h[0]=_o(t,n,r,a,c),h[1]=_o(e,i,o,s,c)),lo(v)}(t,e,n,i,r,o,a,s,u,h,null)<=c/2}function na(t,e,n,i,r,o,a,s,l){if(0===a)return!1;var u=a;return!(e+u<l&&i+u<l&&o+u<l||l<e-u&&l<i-u&&l<o-u||t+u<s&&n+u<s&&r+u<s||s<t-u&&s<n-u&&s<r-u)&&function(t,e,n,i,r,o,a,s,l){var u,h=.005,c=1/0;po[0]=a,po[1]=s;for(var d=0;d<1;d+=.05){go[0]=So(t,n,r,d),go[1]=So(e,i,o,d),(m=vt(po,go))<c&&(u=d,c=m)}c=1/0;for(var f=0;f<32&&!(h<ho);f++){var p=u-h,g=u+h;go[0]=So(t,n,r,p),go[1]=So(e,i,o,p);var m=vt(go,po);if(0<=p&&m<c)u=p,c=m;else{mo[0]=So(t,n,r,g),mo[1]=So(e,i,o,g);var v=vt(mo,po);g<=1&&v<c?(u=g,c=v):h*=.5}}return l&&(l[0]=So(t,n,r,u),l[1]=So(e,i,o,u)),lo(c)}(t,e,n,i,r,o,s,l,null)<=u/2}Jo.prototype={constructor:Jo,_xi:0,_yi:0,_x0:0,_y0:0,_ux:0,_uy:0,_len:0,_lineDash:null,_dashOffset:0,_dashIdx:0,_dashSum:0,setScale:function(t,e,n){n=n||0,this._ux=Ko(n/un/t)||0,this._uy=Ko(n/un/e)||0},getContext:function(){return this._ctx},beginPath:function(t){return(this._ctx=t)&&t.beginPath(),t&&(this.dpr=t.dpr),this._saveData&&(this._len=0),this._lineDash&&(this._lineDash=null,this._dashOffset=0),this},moveTo:function(t,e){return this.addData(Ho.M,t,e),this._ctx&&this._ctx.moveTo(t,e),this._x0=t,this._y0=e,this._xi=t,this._yi=e,this},lineTo:function(t,e){var n=Ko(t-this._xi)>this._ux||Ko(e-this._yi)>this._uy||this._len<5;return this.addData(Ho.L,t,e),this._ctx&&n&&(this._needsDash()?this._dashedLineTo(t,e):this._ctx.lineTo(t,e)),n&&(this._xi=t,this._yi=e),this},bezierCurveTo:function(t,e,n,i,r,o){return this.addData(Ho.C,t,e,n,i,r,o),this._ctx&&(this._needsDash()?this._dashedBezierTo(t,e,n,i,r,o):this._ctx.bezierCurveTo(t,e,n,i,r,o)),this._xi=r,this._yi=o,this},quadraticCurveTo:function(t,e,n,i){return this.addData(Ho.Q,t,e,n,i),this._ctx&&(this._needsDash()?this._dashedQuadraticTo(t,e,n,i):this._ctx.quadraticCurveTo(t,e,n,i)),this._xi=n,this._yi=i,this},arc:function(t,e,n,i,r,o){return this.addData(Ho.A,t,e,n,n,i,r-i,0,o?0:1),this._ctx&&this._ctx.arc(t,e,n,i,r,o),this._xi=jo(r)*n+t,this._yi=qo(r)*n+e,this},arcTo:function(t,e,n,i,r){return this._ctx&&this._ctx.arcTo(t,e,n,i,r),this},rect:function(t,e,n,i){return this._ctx&&this._ctx.rect(t,e,n,i),this.addData(Ho.R,t,e,n,i),this},closePath:function(){this.addData(Ho.Z);var t=this._ctx,e=this._x0,n=this._y0;return t&&(this._needsDash()&&this._dashedLineTo(e,n),t.closePath()),this._xi=e,this._yi=n,this},fill:function(t){t&&t.fill(),this.toStatic()},stroke:function(t){t&&t.stroke(),this.toStatic()},setLineDash:function(t){if(t instanceof Array){this._lineDash=t;for(var e=this._dashIdx=0,n=0;n<t.length;n++)e+=t[n];this._dashSum=e}return this},setLineDashOffset:function(t){return this._dashOffset=t,this},len:function(){return this._len},setData:function(t){var e=t.length;this.data&&this.data.length===e||!Qo||(this.data=new Float32Array(e));for(var n=0;n<e;n++)this.data[n]=t[n];this._len=e},appendPath:function(t){t instanceof Array||(t=[t]);for(var e=t.length,n=0,i=this._len,r=0;r<e;r++)n+=t[r].len();Qo&&this.data instanceof Float32Array&&(this.data=new Float32Array(i+n));for(r=0;r<e;r++)for(var o=t[r].data,a=0;a<o.length;a++)this.data[i++]=o[a];this._len=i},addData:function(t){if(this._saveData){var e=this.data;this._len+arguments.length>e.length&&(this._expandData(),e=this.data);for(var n=0;n<arguments.length;n++)e[this._len++]=arguments[n];this._prevCmd=t}},_expandData:function(){if(!(this.data instanceof Array)){for(var t=[],e=0;e<this._len;e++)t[e]=this.data[e];this.data=t}},_needsDash:function(){return this._lineDash},_dashedLineTo:function(t,e){var n,i,r=this._dashSum,o=this._dashOffset,a=this._lineDash,s=this._ctx,l=this._xi,u=this._yi,h=t-l,c=e-u,d=$o(h*h+c*c),f=l,p=u,g=a.length;for(o<0&&(o=r+o),f-=(o%=r)*(h/=d),p-=o*(c/=d);0<h&&f<=t||h<0&&t<=f||0===h&&(0<c&&p<=e||c<0&&e<=p);)f+=h*(n=a[i=this._dashIdx]),p+=c*n,this._dashIdx=(i+1)%g,0<h&&f<l||h<0&&l<f||0<c&&p<u||c<0&&u<p||s[i%2?"moveTo":"lineTo"](0<=h?Xo(f,t):Yo(f,t),0<=c?Xo(p,e):Yo(p,e));h=f-t,c=p-e,this._dashOffset=-$o(h*h+c*c)},_dashedBezierTo:function(t,e,n,i,r,o){var a,s,l,u,h,c=this._dashSum,d=this._dashOffset,f=this._lineDash,p=this._ctx,g=this._xi,m=this._yi,v=_o,y=0,_=this._dashIdx,x=f.length,w=0;for(d<0&&(d=c+d),d%=c,a=0;a<1;a+=.1)s=v(g,t,n,r,a+.1)-v(g,t,n,r,a),l=v(m,e,i,o,a+.1)-v(m,e,i,o,a),y+=$o(s*s+l*l);for(;_<x&&!(d<(w+=f[_]));_++);for(a=(w-d)/y;a<=1;)u=v(g,t,n,r,a),h=v(m,e,i,o,a),_%2?p.moveTo(u,h):p.lineTo(u,h),a+=f[_]/y,_=(_+1)%x;_%2!=0&&p.lineTo(r,o),s=r-u,l=o-h,this._dashOffset=-$o(s*s+l*l)},_dashedQuadraticTo:function(t,e,n,i){var r=n,o=i;n=(n+2*t)/3,i=(i+2*e)/3,t=(this._xi+2*t)/3,e=(this._yi+2*e)/3,this._dashedBezierTo(t,e,n,i,r,o)},toStatic:function(){var t=this.data;t instanceof Array&&(t.length=this._len,Qo&&(this.data=new Float32Array(t)))},getBoundingRect:function(){Wo[0]=Wo[1]=Zo[0]=Zo[1]=Number.MAX_VALUE,Go[0]=Go[1]=Uo[0]=Uo[1]=-Number.MAX_VALUE;for(var t,e,n,i,r,o,a,s,l,u,h,c,d,f,p=this.data,g=0,m=0,v=0,y=0,_=0;_<p.length;){var x=p[_++];switch(1===_&&(v=g=p[_],y=m=p[_+1]),x){case Ho.M:g=v=p[_++],m=y=p[_++],Zo[0]=v,Zo[1]=y,Uo[0]=v,Uo[1]=y;break;case Ho.L:No(g,m,p[_],p[_+1],Zo,Uo),g=p[_++],m=p[_++];break;case Ho.C:Vo(g,m,p[_++],p[_++],p[_++],p[_++],p[_],p[_+1],Zo,Uo),g=p[_++],m=p[_++];break;case Ho.Q:t=g,e=m,n=p[_++],i=p[_++],r=p[_],o=p[_+1],a=Zo,s=Uo,u=l=void 0,u=So,h=Ao(To((l=Io)(t,n,r),1),0),c=Ao(To(l(e,i,o),1),0),d=u(t,n,r,h),f=u(e,i,o,c),a[0]=To(t,r,d),a[1]=To(e,o,f),s[0]=Ao(t,r,d),s[1]=Ao(e,o,f),g=p[_++],m=p[_++];break;case Ho.A:var w=p[_++],b=p[_++],S=p[_++],M=p[_++],I=p[_++],C=p[_++]+I;_+=1;var T=1-p[_++];1===_&&(v=jo(I)*S+w,y=qo(I)*M+b),Fo(w,b,S,M,I,C,T,Zo,Uo),g=jo(C)*S+w,m=qo(C)*M+b;break;case Ho.R:No(v=g=p[_++],y=m=p[_++],v+p[_++],y+p[_++],Zo,Uo);break;case Ho.Z:g=v,m=y}_t(Wo,Wo,Zo),xt(Go,Go,Uo)}return 0===_&&(Wo[0]=Wo[1]=Go[0]=Go[1]=0),new Sn(Wo[0],Wo[1],Go[0]-Wo[0],Go[1]-Wo[1])},rebuildPath:function(t){for(var e,n,i,r,o,a,s=this.data,l=this._ux,u=this._uy,h=this._len,c=0;c<h;){var d=s[c++];switch(1===c&&(e=i=s[c],n=r=s[c+1]),d){case Ho.M:e=i=s[c++],n=r=s[c++],t.moveTo(i,r);break;case Ho.L:o=s[c++],a=s[c++],(Ko(o-i)>l||Ko(a-r)>u||c===h-1)&&(t.lineTo(o,a),i=o,r=a);break;case Ho.C:t.bezierCurveTo(s[c++],s[c++],s[c++],s[c++],s[c++],s[c++]),i=s[c-2],r=s[c-1];break;case Ho.Q:t.quadraticCurveTo(s[c++],s[c++],s[c++],s[c++]),i=s[c-2],r=s[c-1];break;case Ho.A:var f=s[c++],p=s[c++],g=s[c++],m=s[c++],v=s[c++],y=s[c++],_=s[c++],x=s[c++],w=m<g?g:m,b=m<g?1:g/m,S=m<g?m/g:1,M=v+y;.001<Math.abs(g-m)?(t.translate(f,p),t.rotate(_),t.scale(b,S),t.arc(0,0,w,v,M,1-x),t.scale(1/b,1/S),t.rotate(-_),t.translate(-f,-p)):t.arc(f,p,w,v,M,1-x),1===c&&(e=jo(v)*g+f,n=qo(v)*m+p),i=jo(M)*g+f,r=qo(M)*m+p;break;case Ho.R:e=i=s[c],n=r=s[c+1],t.rect(s[c++],s[c++],s[c++],s[c++]);break;case Ho.Z:t.closePath(),i=e,r=n}}}},Jo.CMD=Ho;var ia=2*Math.PI;function ra(t){return(t%=ia)<0&&(t+=ia),t}var oa=2*Math.PI;function aa(t,e,n,i,r,o,a,s,l){if(0===a)return!1;var u=a;s-=t,l-=e;var h=Math.sqrt(s*s+l*l);if(n<h-u||h+u<n)return!1;if(Math.abs(i-r)%oa<1e-4)return!0;if(o){var c=i;i=ra(r),r=ra(c)}else i=ra(i),r=ra(r);r<i&&(r+=oa);var d=Math.atan2(l,s);return d<0&&(d+=oa),i<=d&&d<=r||i<=d+oa&&d+oa<=r}function sa(t,e,n,i,r,o){if(e<o&&i<o||o<e&&o<i)return 0;if(i===e)return 0;var a=i<e?1:-1,s=(o-e)/(i-e);1!=s&&0!=s||(a=i<e?.5:-.5);var l=s*(n-t)+t;return l===r?1/0:r<l?a:0}var la=Jo.CMD,ua=2*Math.PI,ha=1e-4;var ca=[-1,-1,-1],da=[-1,-1];function fa(t,e,n,i,r,o,a,s,l,u){if(e<u&&i<u&&o<u&&s<u||u<e&&u<i&&u<o&&u<s)return 0;var h,c=function(t,e,n,i,r,o){var a=i+3*(e-n)-t,s=3*(n-2*e+t),l=3*(e-t),u=t-r,h=s*s-3*a*l,c=s*l-9*a*u,d=l*l-3*s*u,f=0;if(vo(h)&&vo(c)){if(vo(s))o[0]=0;else 0<=(M=-l/s)&&M<=1&&(o[f++]=M)}else{var p=c*c-4*h*d;if(vo(p)){var g=c/h,m=-g/2;0<=(M=-s/a+g)&&M<=1&&(o[f++]=M),0<=m&&m<=1&&(o[f++]=m)}else if(0<p){var v=lo(p),y=h*s+1.5*a*(-c+v),_=h*s+1.5*a*(-c-v);0<=(M=(-s-((y=y<0?-so(-y,fo):so(y,fo))+(_=_<0?-so(-_,fo):so(_,fo))))/(3*a))&&M<=1&&(o[f++]=M)}else{var x=(2*h*s-3*a*c)/(2*lo(h*h*h)),w=Math.acos(x)/3,b=lo(h),S=Math.cos(w),M=(-s-2*b*S)/(3*a),I=(m=(-s+b*(S+co*Math.sin(w)))/(3*a),(-s+b*(S-co*Math.sin(w)))/(3*a));0<=M&&M<=1&&(o[f++]=M),0<=m&&m<=1&&(o[f++]=m),0<=I&&I<=1&&(o[f++]=I)}}return f}(e,i,o,s,u,ca);if(0===c)return 0;for(var d,f,p=0,g=-1,m=0;m<c;m++){var v=ca[m],y=0===v||1===v?.5:1;_o(t,n,r,a,v)<l||(g<0&&(g=wo(e,i,o,s,da),da[1]<da[0]&&1<g&&(void 0,h=da[0],da[0]=da[1],da[1]=h),d=_o(e,i,o,s,da[0]),1<g&&(f=_o(e,i,o,s,da[1]))),2===g?v<da[0]?p+=d<e?y:-y:v<da[1]?p+=f<d?y:-y:p+=s<f?y:-y:v<da[0]?p+=d<e?y:-y:p+=s<d?y:-y)}return p}function pa(t,e,n,i,r,o,a,s){if(e<s&&i<s&&o<s||s<e&&s<i&&s<o)return 0;var l=function(t,e,n,i,r){var o=t-2*e+n,a=2*(e-t),s=t-i,l=0;if(vo(o)){if(yo(a))0<=(h=-s/a)&&h<=1&&(r[l++]=h)}else{var u=a*a-4*o*s;if(vo(u))0<=(h=-a/(2*o))&&h<=1&&(r[l++]=h);else if(0<u){var h,c=lo(u),d=(-a-c)/(2*o);0<=(h=(-a+c)/(2*o))&&h<=1&&(r[l++]=h),0<=d&&d<=1&&(r[l++]=d)}}return l}(e,i,o,s,ca);if(0===l)return 0;var u=Io(e,i,o);if(0<=u&&u<=1){for(var h=0,c=So(e,i,o,u),d=0;d<l;d++){var f=0===ca[d]||1===ca[d]?.5:1;So(t,n,r,ca[d])<a||(ca[d]<u?h+=c<e?f:-f:h+=o<c?f:-f)}return h}f=0===ca[0]||1===ca[0]?.5:1;return So(t,n,r,ca[0])<a?0:o<e?f:-f}function ga(t,e,n,i,r,o,a,s){if(n<(s-=e)||s<-n)return 0;var l=Math.sqrt(n*n-s*s);ca[0]=-l,ca[1]=l;var u=Math.abs(i-r);if(u<1e-4)return 0;if(u%ua<1e-4){r=ua;var h=o?1:-1;return a>=ca[i=0]+t&&a<=ca[1]+t?h:0}if(o){l=i;i=ra(r),r=ra(l)}else i=ra(i),r=ra(r);r<i&&(r+=ua);for(var c=0,d=0;d<2;d++){var f=ca[d];if(a<f+t){var p=Math.atan2(s,f);h=o?1:-1;p<0&&(p=ua+p),(i<=p&&p<=r||i<=p+ua&&p+ua<=r)&&(p>Math.PI/2&&p<1.5*Math.PI&&(h=-h),c+=h)}}return c}function ma(t,e,n,i,r){for(var o=0,a=0,s=0,l=0,u=0,h=0;h<t.length;){var c=t[h++];switch(c===la.M&&1<h&&(n||(o+=sa(a,s,l,u,i,r))),1===h&&(l=a=t[h],u=s=t[h+1]),c){case la.M:a=l=t[h++],s=u=t[h++];break;case la.L:if(n){if(ta(a,s,t[h],t[h+1],e,i,r))return!0}else o+=sa(a,s,t[h],t[h+1],i,r)||0;a=t[h++],s=t[h++];break;case la.C:if(n){if(ea(a,s,t[h++],t[h++],t[h++],t[h++],t[h],t[h+1],e,i,r))return!0}else o+=fa(a,s,t[h++],t[h++],t[h++],t[h++],t[h],t[h+1],i,r)||0;a=t[h++],s=t[h++];break;case la.Q:if(n){if(na(a,s,t[h++],t[h++],t[h],t[h+1],e,i,r))return!0}else o+=pa(a,s,t[h++],t[h++],t[h],t[h+1],i,r)||0;a=t[h++],s=t[h++];break;case la.A:var d=t[h++],f=t[h++],p=t[h++],g=t[h++],m=t[h++],v=t[h++];h+=1;var y=1-t[h++],_=Math.cos(m)*p+d,x=Math.sin(m)*g+f;1<h?o+=sa(a,s,_,x,i,r):(l=_,u=x);var w=(i-d)*g/p+d;if(n){if(aa(d,f,g,m,m+v,y,e,w,r))return!0}else o+=ga(d,f,g,m,m+v,y,w,r);a=Math.cos(m+v)*p+d,s=Math.sin(m+v)*g+f;break;case la.R:l=a=t[h++],u=s=t[h++];_=l+t[h++],x=u+t[h++];if(n){if(ta(l,u,_,u,e,i,r)||ta(_,u,_,x,e,i,r)||ta(_,x,l,x,e,i,r)||ta(l,x,l,u,e,i,r))return!0}else o+=sa(_,u,_,x,i,r),o+=sa(l,x,l,u,i,r);break;case la.Z:if(n){if(ta(a,s,l,u,e,i,r))return!0}else o+=sa(a,s,l,u,i,r);a=l,s=u}}return n||function(t,e){return Math.abs(t-e)<ha}(s,u)||(o+=sa(a,s,l,u,i,r)||0),0!==o}var va=Xn.prototype.getCanvasPattern,ya=Math.abs,_a=new Jo(!0);function xa(t){Xi.call(this,t),this.path=null}xa.prototype={constructor:xa,type:"path",__dirtyPath:!0,strokeContainThreshold:5,segmentIgnoreThreshold:0,subPixelOptimize:!1,brush:function(t,e){var n,i=this.style,r=this.path||_a,o=i.hasStroke(),a=i.hasFill(),s=i.fill,l=i.stroke,u=a&&!!s.colorStops,h=o&&!!l.colorStops,c=a&&!!s.image,d=o&&!!l.image;i.bind(t,this,e),this.setTransform(t),this.__dirty&&(u&&(n=n||this.getBoundingRect(),this._fillGradient=i.getGradient(t,s,n)),h&&(n=n||this.getBoundingRect(),this._strokeGradient=i.getGradient(t,l,n)));u?t.fillStyle=this._fillGradient:c&&(t.fillStyle=va.call(s,t)),h?t.strokeStyle=this._strokeGradient:d&&(t.strokeStyle=va.call(l,t));var f=i.lineDash,p=i.lineDashOffset,g=!!t.setLineDash,m=this.getGlobalScale();if(r.setScale(m[0],m[1],this.segmentIgnoreThreshold),this.__dirtyPath||f&&!g&&o?(r.beginPath(t),f&&!g&&(r.setLineDash(f),r.setLineDashOffset(p)),this.buildPath(r,this.shape,!1),this.path&&(this.__dirtyPath=!1)):(t.beginPath(),this.path.rebuildPath(t)),a)if(null!=i.fillOpacity){var v=t.globalAlpha;t.globalAlpha=i.fillOpacity*i.opacity,r.fill(t),t.globalAlpha=v}else r.fill(t);if(f&&g&&(t.setLineDash(f),t.lineDashOffset=p),o)if(null!=i.strokeOpacity){v=t.globalAlpha;t.globalAlpha=i.strokeOpacity*i.opacity,r.stroke(t),t.globalAlpha=v}else r.stroke(t);f&&g&&t.setLineDash([]),null!=i.text&&(this.restoreTransform(t),this.drawRectText(t,this.getBoundingRect()))},buildPath:function(t,e,n){},createPathProxy:function(){this.path=new Jo},getBoundingRect:function(){var t=this._rect,e=this.style,n=!t;if(n){var i=this.path;i=i||(this.path=new Jo),this.__dirtyPath&&(i.beginPath(),this.buildPath(i,this.shape,!1)),t=i.getBoundingRect()}if(this._rect=t,e.hasStroke()){var r=this._rectWithStroke||(this._rectWithStroke=t.clone());if(this.__dirty||n){r.copy(t);var o=e.lineWidth,a=e.strokeNoScale?this.getLineScale():1;e.hasFill()||(o=Math.max(o,this.strokeContainThreshold||4)),1e-10<a&&(r.width+=o/a,r.height+=o/a,r.x-=o/a/2,r.y-=o/a/2)}return r}return t},contain:function(t,e){var n=this.transformCoordToLocal(t,e),i=this.getBoundingRect(),r=this.style;if(t=n[0],e=n[1],i.contain(t,e)){var o=this.path.data;if(r.hasStroke()){var a=r.lineWidth,s=r.strokeNoScale?this.getLineScale():1;if(1e-10<s&&(r.hasFill()||(a=Math.max(a,this.strokeContainThreshold)),function(t,e,n,i){return ma(t,e,!0,n,i)}(o,a/s,t,e)))return!0}if(r.hasFill())return function(t,e,n){return ma(t,0,!1,e,n)}(o,t,e)}return!1},dirty:function(t){null==t&&(t=!0),t&&(this.__dirtyPath=t,this._rect=null),this.__dirty=this.__dirtyText=!0,this.__zr&&this.__zr.refresh(),this.__clipTarget&&this.__clipTarget.dirty()},animateShape:function(t){return this.animate("shape",t)},attrKV:function(t,e){"shape"===t?(this.setShape(e),this.__dirtyPath=!0,this._rect=null):Xi.prototype.attrKV.call(this,t,e)},setShape:function(t,e){var n=this.shape;if(n){if(N(t))for(var i in t)t.hasOwnProperty(i)&&(n[i]=t[i]);else n[t]=e;this.dirty(!0)}return this},getLineScale:function(){var t=this.transform;return t&&1e-10<ya(t[0]-1)&&1e-10<ya(t[3]-1)?Math.sqrt(ya(t[0]*t[3]-t[2]*t[1])):1}},xa.extend=function(r){function t(t){xa.call(this,t),r.style&&this.style.extendFrom(r.style,!1);var e=r.shape;if(e){this.shape=this.shape||{};var n=this.shape;for(var i in e)!n.hasOwnProperty(i)&&e.hasOwnProperty(i)&&(n[i]=e[i])}r.init&&r.init.call(this,t)}for(var e in w(t,xa),r)"style"!==e&&"shape"!==e&&(t.prototype[e]=r[e]);return t},w(xa,Xi);function wa(t){return Math.sqrt(t[0]*t[0]+t[1]*t[1])}var ba=Jo.CMD,Sa=[[],[],[]],Ma=Math.sqrt,Ia=Math.atan2,Ca=function(t,e){var n,i,r,o,a,s=t.data,l=ba.M,u=ba.C,h=ba.L,c=ba.R,d=ba.A,f=ba.Q;for(o=r=0;r<s.length;){switch(n=s[r++],o=r,i=0,n){case l:case h:i=1;break;case u:i=3;break;case f:i=2;break;case d:var p=e[4],g=e[5],m=Ma(e[0]*e[0]+e[1]*e[1]),v=Ma(e[2]*e[2]+e[3]*e[3]),y=Ia(-e[1]/v,e[0]/m);s[r]*=m,s[r++]+=p,s[r]*=v,s[r++]+=g,s[r++]*=m,s[r++]*=v,s[r++]+=y,s[r++]+=y,o=r+=2;break;case c:_[0]=s[r++],_[1]=s[r++],yt(_,_,e),s[o++]=_[0],s[o++]=_[1],_[0]+=s[r++],_[1]+=s[r++],yt(_,_,e),s[o++]=_[0],s[o++]=_[1]}for(a=0;a<i;a++){var _;(_=Sa[a])[0]=s[r++],_[1]=s[r++],yt(_,_,e),s[o++]=_[0],s[o++]=_[1]}}},Ta=Math.sqrt,Aa=Math.sin,Da=Math.cos,ka=Math.PI,Pa=function(t,e){return(t[0]*e[0]+t[1]*e[1])/(wa(t)*wa(e))},La=function(t,e){return(t[0]*e[1]<t[1]*e[0]?-1:1)*Math.acos(Pa(t,e))};function Oa(t,e,n,i,r,o,a,s,l,u,h){var c=l*(ka/180),d=Da(c)*(t-n)/2+Aa(c)*(e-i)/2,f=-1*Aa(c)*(t-n)/2+Da(c)*(e-i)/2,p=d*d/(a*a)+f*f/(s*s);1<p&&(a*=Ta(p),s*=Ta(p));var g=(r===o?-1:1)*Ta((a*a*(s*s)-a*a*(f*f)-s*s*(d*d))/(a*a*(f*f)+s*s*(d*d)))||0,m=g*a*f/s,v=g*-s*d/a,y=(t+n)/2+Da(c)*m-Aa(c)*v,_=(e+i)/2+Aa(c)*m+Da(c)*v,x=La([1,0],[(d-m)/a,(f-v)/s]),w=[(d-m)/a,(f-v)/s],b=[(-1*d-m)/a,(-1*f-v)/s],S=La(w,b);Pa(w,b)<=-1&&(S=ka),1<=Pa(w,b)&&(S=0),0===o&&0<S&&(S-=2*ka),1===o&&S<0&&(S+=2*ka),h.addData(u,y,_,a,s,x,S,c,o)}var Ea=/([mlvhzcqtsa])([^mlvhzcqtsa]*)/gi,za=/-?([0-9]*\.)?[0-9]+([eE]-?[0-9]+)?/g;function Na(t,e){var n=function(t){if(!t)return new Jo;for(var e,n=0,i=0,r=n,o=i,a=new Jo,s=Jo.CMD,l=t.match(Ea),u=0;u<l.length;u++){for(var h,c=l[u],d=c.charAt(0),f=c.match(za)||[],p=f.length,g=0;g<p;g++)f[g]=parseFloat(f[g]);for(var m=0;m<p;){var v,y,_,x,w,b,S,M=n,I=i;switch(d){case"l":n+=f[m++],i+=f[m++],h=s.L,a.addData(h,n,i);break;case"L":n=f[m++],i=f[m++],h=s.L,a.addData(h,n,i);break;case"m":n+=f[m++],i+=f[m++],h=s.M,a.addData(h,n,i),r=n,o=i,d="l";break;case"M":n=f[m++],i=f[m++],h=s.M,a.addData(h,n,i),r=n,o=i,d="L";break;case"h":n+=f[m++],h=s.L,a.addData(h,n,i);break;case"H":n=f[m++],h=s.L,a.addData(h,n,i);break;case"v":i+=f[m++],h=s.L,a.addData(h,n,i);break;case"V":i=f[m++],h=s.L,a.addData(h,n,i);break;case"C":h=s.C,a.addData(h,f[m++],f[m++],f[m++],f[m++],f[m++],f[m++]),n=f[m-2],i=f[m-1];break;case"c":h=s.C,a.addData(h,f[m++]+n,f[m++]+i,f[m++]+n,f[m++]+i,f[m++]+n,f[m++]+i),n+=f[m-2],i+=f[m-1];break;case"S":v=n,y=i;var C=a.len(),T=a.data;e===s.C&&(v+=n-T[C-4],y+=i-T[C-3]),h=s.C,M=f[m++],I=f[m++],n=f[m++],i=f[m++],a.addData(h,v,y,M,I,n,i);break;case"s":v=n,y=i;C=a.len(),T=a.data;e===s.C&&(v+=n-T[C-4],y+=i-T[C-3]),h=s.C,M=n+f[m++],I=i+f[m++],n+=f[m++],i+=f[m++],a.addData(h,v,y,M,I,n,i);break;case"Q":M=f[m++],I=f[m++],n=f[m++],i=f[m++],h=s.Q,a.addData(h,M,I,n,i);break;case"q":M=f[m++]+n,I=f[m++]+i,n+=f[m++],i+=f[m++],h=s.Q,a.addData(h,M,I,n,i);break;case"T":v=n,y=i;C=a.len(),T=a.data;e===s.Q&&(v+=n-T[C-4],y+=i-T[C-3]),n=f[m++],i=f[m++],h=s.Q,a.addData(h,v,y,n,i);break;case"t":v=n,y=i;C=a.len(),T=a.data;e===s.Q&&(v+=n-T[C-4],y+=i-T[C-3]),n+=f[m++],i+=f[m++],h=s.Q,a.addData(h,v,y,n,i);break;case"A":_=f[m++],x=f[m++],w=f[m++],b=f[m++],S=f[m++],Oa(M=n,I=i,n=f[m++],i=f[m++],b,S,_,x,w,h=s.A,a);break;case"a":_=f[m++],x=f[m++],w=f[m++],b=f[m++],S=f[m++],Oa(M=n,I=i,n+=f[m++],i+=f[m++],b,S,_,x,w,h=s.A,a)}}"z"!==d&&"Z"!==d||(h=s.Z,a.addData(h),n=r,i=o),e=h}return a.toStatic(),a}(t);return(e=e||{}).buildPath=function(t){if(t.setData){t.setData(n.data),(e=t.getContext())&&t.rebuildPath(e)}else{var e=t;n.rebuildPath(e)}},e.applyTransform=function(t){Ca(n,t),this.dirty(!0)},e}function Ra(t,e){return new xa(Na(t,e))}var Ba=function(t){Xi.call(this,t)};Ba.prototype={constructor:Ba,type:"text",brush:function(t,e){var n=this.style;this.__dirty&&Di(n),n.fill=n.stroke=n.shadowBlur=n.shadowColor=n.shadowOffsetX=n.shadowOffsetY=null;var i=n.text;null!=i&&(i+=""),Gi(i,n)?(this.setTransform(t),Pi(this,t,i,n,null,e),this.restoreTransform(t)):t.__attrCachedBy=Rn.NONE},getBoundingRect:function(){var t=this.style;if(this.__dirty&&Di(t),!this._rect){var e=t.text;null!=e?e+="":e="";var n=ui(t.text+"",t.font,t.textAlign,t.textVerticalAlign,t.textPadding,t.textLineHeight,t.rich);if(n.x+=t.x||0,n.y+=t.y||0,Vi(t.textStroke,t.textStrokeWidth)){var i=t.textStrokeWidth;n.x-=i/2,n.y-=i/2,n.width+=i,n.height+=i}this._rect=n}return this._rect}},w(Ba,Xi);function Va(l){return v.browser.ie&&11<=v.browser.version?function(){var t,e=this.__clipPaths,n=this.style;if(e)for(var i=0;i<e.length;i++){var r=e[i],o=r&&r.shape,a=r&&r.type;if(o&&("sector"===a&&o.startAngle===o.endAngle||"rect"===a&&(!o.width||!o.height))){for(var s=0;s<Ha.length;s++)Ha[s][2]=n[Ha[s][0]],n[Ha[s][0]]=Ha[s][1];t=!0;break}}if(l.apply(this,arguments),t)for(s=0;s<Ha.length;s++)n[Ha[s][0]]=Ha[s][2]}:l}var Fa=xa.extend({type:"circle",shape:{cx:0,cy:0,r:0},buildPath:function(t,e,n){n&&t.moveTo(e.cx+e.r,e.cy),t.arc(e.cx,e.cy,e.r,0,2*Math.PI,!0)}}),Ha=[["shadowBlur",0],["shadowColor","#000"],["shadowOffsetX",0],["shadowOffsetY",0]],Wa=xa.extend({type:"sector",shape:{cx:0,cy:0,r0:0,r:0,startAngle:0,endAngle:2*Math.PI,clockwise:!0},brush:Va(xa.prototype.brush),buildPath:function(t,e){var n=e.cx,i=e.cy,r=Math.max(e.r0||0,0),o=Math.max(e.r,0),a=e.startAngle,s=e.endAngle,l=e.clockwise,u=Math.cos(a),h=Math.sin(a);t.moveTo(u*r+n,h*r+i),t.lineTo(u*o+n,h*o+i),t.arc(n,i,o,a,s,!l),t.lineTo(Math.cos(s)*r+n,Math.sin(s)*r+i),0!==r&&t.arc(n,i,r,s,a,l),t.closePath()}}),Ga=xa.extend({type:"ring",shape:{cx:0,cy:0,r:0,r0:0},buildPath:function(t,e){var n=e.cx,i=e.cy,r=2*Math.PI;t.moveTo(n+e.r,i),t.arc(n,i,e.r,0,r,!1),t.moveTo(n+e.r0,i),t.arc(n,i,e.r0,0,r,!0)}});function Za(t,e,n,i,r,o,a){var s=.5*(n-t),l=.5*(i-e);return(2*(e-n)+s+l)*a+(-3*(e-n)-2*s-l)*o+s*r+e}function Ua(t,e,n){var i=e.points,r=e.smooth;if(i&&2<=i.length){if(r&&"spline"!==r){var o=function(t,e,n,i){var r,o,a,s,l=[],u=[],h=[],c=[];if(i){a=[1/0,1/0],s=[-1/0,-1/0];for(var d=0,f=t.length;d<f;d++)_t(a,a,t[d]),xt(s,s,t[d]);_t(a,a,i[0]),xt(s,s,i[1])}for(d=0,f=t.length;d<f;d++){var p=t[d];if(n)r=t[d?d-1:f-1],o=t[(d+1)%f];else{if(0===d||d===f-1){l.push(rt(t[d]));continue}r=t[d-1],o=t[d+1]}st(u,o,r),dt(u,u,e);var g=pt(p,r),m=pt(p,o),v=g+m;0!==v&&(g/=v,m/=v),dt(h,u,-g),dt(c,u,m);var y=ot([],p,h),_=ot([],p,c);i&&(xt(y,y,a),_t(y,y,s),xt(_,_,a),_t(_,_,s)),l.push(y),l.push(_)}return n&&l.push(l.shift()),l}(i,r,n,e.smoothConstraint);t.moveTo(i[0][0],i[0][1]);for(var a=i.length,s=0;s<(n?a:a-1);s++){var l=o[2*s],u=o[2*s+1],h=i[(s+1)%a];t.bezierCurveTo(l[0],l[1],u[0],u[1],h[0],h[1])}}else{"spline"===r&&(i=function(t,e){for(var n=t.length,i=[],r=0,o=1;o<n;o++)r+=pt(t[o-1],t[o]);var a=r/2;a=a<n?n:a;for(o=0;o<a;o++){var s,l,u,h=o/(a-1)*(e?n:n-1),c=Math.floor(h),d=h-c,f=t[c%n];u=e?(s=t[(c-1+n)%n],l=t[(c+1)%n],t[(c+2)%n]):(s=t[0===c?c:c-1],l=t[n-2<c?n-1:c+1],t[n-3<c?n-1:c+2]);var p=d*d,g=d*p;i.push([Za(s[0],f[0],l[0],u[0],d,p,g),Za(s[1],f[1],l[1],u[1],d,p,g)])}return i}(i,n)),t.moveTo(i[0][0],i[0][1]);s=1;for(var c=i.length;s<c;s++)t.lineTo(i[s][0],i[s][1])}n&&t.closePath()}}var Xa=xa.extend({type:"polygon",shape:{points:null,smooth:!1,smoothConstraint:null},buildPath:function(t,e){Ua(t,e,!0)}}),Ya=xa.extend({type:"polyline",shape:{points:null,smooth:!1,smoothConstraint:null},style:{stroke:"#000",fill:null},buildPath:function(t,e){Ua(t,e,!1)}}),ja=Math.round;function qa(t,e,n){if(e){var i=e.x1,r=e.x2,o=e.y1,a=e.y2;t.x1=i,t.x2=r,t.y1=o,t.y2=a;var s=n&&n.lineWidth;s&&(ja(2*i)===ja(2*r)&&(t.x1=t.x2=Ka(i,s,!0)),ja(2*o)===ja(2*a)&&(t.y1=t.y2=Ka(o,s,!0)))}}function $a(t,e,n){if(e){var i=e.x,r=e.y,o=e.width,a=e.height;t.x=i,t.y=r,t.width=o,t.height=a;var s=n&&n.lineWidth;s&&(t.x=Ka(i,s,!0),t.y=Ka(r,s,!0),t.width=Math.max(Ka(i+o,s,!1)-t.x,0===o?0:1),t.height=Math.max(Ka(r+a,s,!1)-t.y,0===a?0:1))}}function Ka(t,e,n){if(!e)return t;var i=ja(2*t);return(i+ja(e))%2==0?i/2:(i+(n?1:-1))/2}var Qa={},Ja=xa.extend({type:"rect",shape:{r:0,x:0,y:0,width:0,height:0},buildPath:function(t,e){var n,i,r,o;this.subPixelOptimize?($a(Qa,e,this.style),n=Qa.x,i=Qa.y,r=Qa.width,o=Qa.height,Qa.r=e.r,e=Qa):(n=e.x,i=e.y,r=e.width,o=e.height),e.r?bi(t,e):t.rect(n,i,r,o),t.closePath()}}),ts={},es=xa.extend({type:"line",shape:{x1:0,y1:0,x2:0,y2:0,percent:1},style:{stroke:"#000",fill:null},buildPath:function(t,e){var n,i,r,o;o=this.subPixelOptimize?(qa(ts,e,this.style),n=ts.x1,i=ts.y1,r=ts.x2,ts.y2):(n=e.x1,i=e.y1,r=e.x2,e.y2);var a=e.percent;0!==a&&(t.moveTo(n,i),a<1&&(r=n*(1-a)+r*a,o=i*(1-a)+o*a),t.lineTo(r,o))},pointAt:function(t){var e=this.shape;return[e.x1*(1-t)+e.x2*t,e.y1*(1-t)+e.y2*t]}}),ns=[];function is(t,e,n){var i=t.cpx2,r=t.cpy2;return null===i||null===r?[(n?xo:_o)(t.x1,t.cpx1,t.cpx2,t.x2,e),(n?xo:_o)(t.y1,t.cpy1,t.cpy2,t.y2,e)]:[(n?Mo:So)(t.x1,t.cpx1,t.x2,e),(n?Mo:So)(t.y1,t.cpy1,t.y2,e)]}function rs(t){this.colorStops=t||[]}var os=xa.extend({type:"bezier-curve",shape:{x1:0,y1:0,x2:0,y2:0,cpx1:0,cpy1:0,percent:1},style:{stroke:"#000",fill:null},buildPath:function(t,e){var n=e.x1,i=e.y1,r=e.x2,o=e.y2,a=e.cpx1,s=e.cpy1,l=e.cpx2,u=e.cpy2,h=e.percent;0!==h&&(t.moveTo(n,i),null==l||null==u?(h<1&&(Co(n,a,r,h,ns),a=ns[1],r=ns[2],Co(i,s,o,h,ns),s=ns[1],o=ns[2]),t.quadraticCurveTo(a,s,r,o)):(h<1&&(bo(n,a,l,r,h,ns),a=ns[1],l=ns[2],r=ns[3],bo(i,s,u,o,h,ns),s=ns[1],u=ns[2],o=ns[3]),t.bezierCurveTo(a,s,l,u,r,o)))},pointAt:function(t){return is(this.shape,t,!1)},tangentAt:function(t){var e=is(this.shape,t,!0);return ft(e,e)}}),as=xa.extend({type:"arc",shape:{cx:0,cy:0,r:0,startAngle:0,endAngle:2*Math.PI,clockwise:!0},style:{stroke:"#000",fill:null},buildPath:function(t,e){var n=e.cx,i=e.cy,r=Math.max(e.r,0),o=e.startAngle,a=e.endAngle,s=e.clockwise,l=Math.cos(o),u=Math.sin(o);t.moveTo(l*r+n,u*r+i),t.arc(n,i,r,o,a,!s)}}),ss=xa.extend({type:"compound",shape:{paths:null},_updatePathDirty:function(){for(var t=this.__dirtyPath,e=this.shape.paths,n=0;n<e.length;n++)t=t||e[n].__dirtyPath;this.__dirtyPath=t,this.__dirty=this.__dirty||t},beforeBrush:function(){this._updatePathDirty();for(var t=this.shape.paths||[],e=this.getGlobalScale(),n=0;n<t.length;n++)t[n].path||t[n].createPathProxy(),t[n].path.setScale(e[0],e[1],t[n].segmentIgnoreThreshold)},buildPath:function(t,e){for(var n=e.paths||[],i=0;i<n.length;i++)n[i].buildPath(t,n[i].shape,!0)},afterBrush:function(){for(var t=this.shape.paths||[],e=0;e<t.length;e++)t[e].__dirtyPath=!1},getBoundingRect:function(){return this._updatePathDirty(),xa.prototype.getBoundingRect.call(this)}});rs.prototype={constructor:rs,addColorStop:function(t,e){this.colorStops.push({offset:t,color:e})}};function ls(t,e,n,i,r,o){this.x=null==t?0:t,this.y=null==e?0:e,this.x2=null==n?1:n,this.y2=null==i?0:i,this.type="linear",this.global=o||!1,rs.call(this,r)}ls.prototype={constructor:ls},w(ls,rs);function us(t,e,n,i,r){this.x=null==t?.5:t,this.y=null==e?.5:e,this.r=null==n?.5:n,this.type="radial",this.global=r||!1,rs.call(this,i)}function hs(t){Xi.call(this,t),this._displayables=[],this._temporaryDisplayables=[],this._cursor=0,this.notClear=!0}us.prototype={constructor:us},w(us,rs),hs.prototype.incremental=!0,hs.prototype.clearDisplaybles=function(){this._displayables=[],this._temporaryDisplayables=[],this._cursor=0,this.dirty(),this.notClear=!1},hs.prototype.addDisplayable=function(t,e){e?this._temporaryDisplayables.push(t):this._displayables.push(t),this.dirty()},hs.prototype.addDisplayables=function(t,e){e=e||!1;for(var n=0;n<t.length;n++)this.addDisplayable(t[n],e)},hs.prototype.eachPendingDisplayable=function(t){for(var e=this._cursor;e<this._displayables.length;e++)t&&t(this._displayables[e]);for(e=0;e<this._temporaryDisplayables.length;e++)t&&t(this._temporaryDisplayables[e])},hs.prototype.update=function(){this.updateTransform();for(var t=this._cursor;t<this._displayables.length;t++){(e=this._displayables[t]).parent=this,e.update(),e.parent=null}for(t=0;t<this._temporaryDisplayables.length;t++){var e;(e=this._temporaryDisplayables[t]).parent=this,e.update(),e.parent=null}},hs.prototype.brush=function(t,e){for(var n=this._cursor;n<this._displayables.length;n++){(i=this._displayables[n]).beforeBrush&&i.beforeBrush(t),i.brush(t,n===this._cursor?null:this._displayables[n-1]),i.afterBrush&&i.afterBrush(t)}this._cursor=n;for(n=0;n<this._temporaryDisplayables.length;n++){var i;(i=this._temporaryDisplayables[n]).beforeBrush&&i.beforeBrush(t),i.brush(t,0===n?null:this._temporaryDisplayables[n-1]),i.afterBrush&&i.afterBrush(t)}this._temporaryDisplayables=[],this.notClear=!0};var cs=[];hs.prototype.getBoundingRect=function(){if(!this._rect){for(var t=new Sn(1/0,1/0,-1/0,-1/0),e=0;e<this._displayables.length;e++){var n=this._displayables[e],i=n.getBoundingRect().clone();n.needLocalTransform()&&i.applyTransform(n.getLocalTransform(cs)),t.union(i)}this._rect=t}return this._rect},hs.prototype.contain=function(t,e){var n=this.transformCoordToLocal(t,e);if(this.getBoundingRect().contain(n[0],n[1]))for(var i=0;i<this._displayables.length;i++){if(this._displayables[i].contain(t,e))return!0}return!1},w(hs,Xi);var ds=Math.max,fs=Math.min,ps={},gs=1,ms="emphasis",vs="normal",ys=1,_s={},xs={};function ws(t){return xa.extend(t)}function bs(t,e){xs[t]=e}function Ss(t){if(xs.hasOwnProperty(t))return xs[t]}function Ms(t,e,n,i){var r=Ra(t,e);return n&&("center"===i&&(n=Cs(n,r.getBoundingRect())),As(r,n)),r}function Is(t,n,i){var r=new Yi({style:{image:t,x:n.x,y:n.y,width:n.width,height:n.height},onload:function(t){if("center"===i){var e={width:t.width,height:t.height};r.setStyle(Cs(n,e))}}});return r}function Cs(t,e){var n,i=e.width/e.height,r=t.height*i;return n=r<=t.width?t.height:(r=t.width)/i,{x:t.x+t.width/2-r/2,y:t.y+t.height/2-n/2,width:r,height:n}}function Ts(t,e){for(var n=[],i=t.length,r=0;r<i;r++){var o=t[r];o.path||o.createPathProxy(),o.__dirtyPath&&o.buildPath(o.path,o.shape,!0),n.push(o.path)}var a=new xa(e);return a.createPathProxy(),a.buildPath=function(t){t.appendPath(n);var e=t.getContext();e&&t.rebuildPath(e)},a}function As(t,e){if(t.applyTransform){var n=t.getBoundingRect().calculateTransform(e);t.applyTransform(n)}}var Ds=Ka;function ks(t){return null!=t&&"none"!==t}var Ps=Q(),Ls=0;function Os(t){var e=t.__hoverStl;if(e&&!t.__highlighted){var n=t.__zr,i=t.useHoverLayer&&n&&"canvas"===n.painter.type;if(t.__highlighted=i?"layer":"plain",!(t.isGroup||!n&&t.useHoverLayer)){var r=t,o=t.style;i&&(o=(r=n.addHover(t)).style),Js(o),i||function(t){if(t.__hoverStlDirty){t.__hoverStlDirty=!1;var e=t.__hoverStl;if(e){var n=t.__cachedNormalStl={};t.__cachedNormalZ2=t.z2;var i=t.style;for(var r in e)null!=e[r]&&(n[r]=i[r]);n.fill=i.fill,n.stroke=i.stroke}else t.__cachedNormalStl=t.__cachedNormalZ2=null}}(r),o.extendFrom(e),Es(o,e,"fill"),Es(o,e,"stroke"),Qs(o),i||(t.dirty(!1),t.z2+=gs)}}}function Es(t,e,n){!ks(e[n])&&ks(t[n])&&(t[n]=function(t){if("string"!=typeof t)return t;var e=Ps.get(t);return e||(e=Be(t,-.1),Ls<1e4&&(Ps.set(t,e),Ls++)),e}(t[n]))}function zs(t){var e=t.__highlighted;if(e&&(t.__highlighted=!1,!t.isGroup))if("layer"===e)t.__zr&&t.__zr.removeHover(t);else{var n=t.style,i=t.__cachedNormalStl;i&&(Js(n),t.setStyle(i),Qs(n));var r=t.__cachedNormalZ2;null!=r&&t.z2-r===gs&&(t.z2=r)}}function Ns(t,e,n){var i,r=vs,o=vs;t.__highlighted&&(r=ms,i=!0),e(t,n),t.__highlighted&&(o=ms,i=!0),t.isGroup&&t.traverse(function(t){t.isGroup||e(t,n)}),i&&t.__highDownOnUpdate&&t.__highDownOnUpdate(r,o)}function Rs(t,e){e=t.__hoverStl=!1!==e&&(t.hoverStyle||e||{}),t.__hoverStlDirty=!0,t.__highlighted&&(t.__cachedNormalStl=null,zs(t),Os(t))}function Bs(t){Ws(this,t)||this.__highByOuter||Ns(this,Os)}function Vs(t){Ws(this,t)||this.__highByOuter||Ns(this,zs)}function Fs(t){this.__highByOuter|=1<<(t||0),Ns(this,Os)}function Hs(t){(this.__highByOuter&=~(1<<(t||0)))||Ns(this,zs)}function Ws(t,e){return t.__highDownSilentOnTouch&&e.zrByTouch}function Gs(t,e){Zs(t,!0),Ns(t,Rs,e)}function Zs(t,e){var n=!1===e;if(t.__highDownSilentOnTouch=t.highDownSilentOnTouch,t.__highDownOnUpdate=t.highDownOnUpdate,!n||t.__highDownDispatcher){var i=n?"off":"on";t[i]("mouseover",Bs)[i]("mouseout",Vs),t[i]("emphasis",Fs)[i]("normal",Hs),t.__highByOuter=t.__highByOuter||0,t.__highDownDispatcher=!n}}function Us(t){return!(!t||!t.__highDownDispatcher)}function Xs(t){var e=_s[t];return null==e&&ys<=32&&(e=_s[t]=ys++),e}function Ys(t,e,n,i,r,o,a){var s,l=(r=r||ps).labelFetcher,u=r.labelDataIndex,h=r.labelDimIndex,c=r.labelProp,d=n.getShallow("show"),f=i.getShallow("show");(d||f)&&(l&&(s=l.getFormattedLabel(u,"normal",null,h,c)),null==s&&(s=E(r.defaultText)?r.defaultText(u,r):r.defaultText));var p=d?s:null,g=f?W(l?l.getFormattedLabel(u,"emphasis",null,h,c):null,s):null;null==p&&null==g||(js(t,n,o,r),js(e,i,a,r,!0)),t.text=p,e.text=g}function js(t,e,n,i,r){return qs(t,e,i,r),n&&k(t,n),t}function qs(t,e,n,i){if((n=n||ps).isRectText){var r;n.getTextPosition?r=n.getTextPosition(e,i):"outside"===(r=e.getShallow("position")||(i?null:"inside"))&&(r="top"),t.textPosition=r,t.textOffset=e.getShallow("offset");var o=e.getShallow("rotate");null!=o&&(o*=Math.PI/180),t.textRotation=o,t.textDistance=W(e.getShallow("distance"),i?null:5)}var a,s=e.ecModel,l=s&&s.option.textStyle,u=function(t){var e;for(;t&&t!==t.ecModel;){var n=(t.option||ps).rich;if(n)for(var i in e=e||{},n)n.hasOwnProperty(i)&&(e[i]=1);t=t.parentModel}return e}(e);if(u)for(var h in a={},u)if(u.hasOwnProperty(h)){var c=e.getModel(["rich",h]);$s(a[h]={},c,l,n,i)}return t.rich=a,$s(t,e,l,n,i,!0),n.forceRich&&!n.textStyle&&(n.textStyle={}),t}function $s(t,e,n,i,r,o){n=!r&&n||ps,t.textFill=Ks(e.getShallow("color"),i)||n.color,t.textStroke=Ks(e.getShallow("textBorderColor"),i)||n.textBorderColor,t.textStrokeWidth=W(e.getShallow("textBorderWidth"),n.textBorderWidth),r||(o&&(t.insideRollbackOpt=i,Qs(t)),null==t.textFill&&(t.textFill=i.autoColor)),t.fontStyle=e.getShallow("fontStyle")||n.fontStyle,t.fontWeight=e.getShallow("fontWeight")||n.fontWeight,t.fontSize=e.getShallow("fontSize")||n.fontSize,t.fontFamily=e.getShallow("fontFamily")||n.fontFamily,t.textAlign=e.getShallow("align"),t.textVerticalAlign=e.getShallow("verticalAlign")||e.getShallow("baseline"),t.textLineHeight=e.getShallow("lineHeight"),t.textWidth=e.getShallow("width"),t.textHeight=e.getShallow("height"),t.textTag=e.getShallow("tag"),o&&i.disableBox||(t.textBackgroundColor=Ks(e.getShallow("backgroundColor"),i),t.textPadding=e.getShallow("padding"),t.textBorderColor=Ks(e.getShallow("borderColor"),i),t.textBorderWidth=e.getShallow("borderWidth"),t.textBorderRadius=e.getShallow("borderRadius"),t.textBoxShadowColor=e.getShallow("shadowColor"),t.textBoxShadowBlur=e.getShallow("shadowBlur"),t.textBoxShadowOffsetX=e.getShallow("shadowOffsetX"),t.textBoxShadowOffsetY=e.getShallow("shadowOffsetY")),t.textShadowColor=e.getShallow("textShadowColor")||n.textShadowColor,t.textShadowBlur=e.getShallow("textShadowBlur")||n.textShadowBlur,t.textShadowOffsetX=e.getShallow("textShadowOffsetX")||n.textShadowOffsetX,t.textShadowOffsetY=e.getShallow("textShadowOffsetY")||n.textShadowOffsetY}function Ks(t,e){return"auto"!==t?t:e&&e.autoColor?e.autoColor:null}function Qs(t){var e,n=t.textPosition,i=t.insideRollbackOpt;if(i&&null==t.textFill){var r=i.autoColor,o=i.isRectText,a=i.useInsideStyle,s=!1!==a&&(!0===a||o&&n&&"string"==typeof n&&0<=n.indexOf("inside")),l=!s&&null!=r;(s||l)&&(e={textFill:t.textFill,textStroke:t.textStroke,textStrokeWidth:t.textStrokeWidth}),s&&(t.textFill="#fff",null==t.textStroke&&(t.textStroke=r,null==t.textStrokeWidth&&(t.textStrokeWidth=2))),l&&(t.textFill=r)}t.insideRollback=e}function Js(t){var e=t.insideRollback;e&&(t.textFill=e.textFill,t.textStroke=e.textStroke,t.textStrokeWidth=e.textStrokeWidth,t.insideRollback=null)}function tl(t,e){var n=e&&e.getModel("textStyle");return Y([t.fontStyle||n&&n.getShallow("fontStyle")||"",t.fontWeight||n&&n.getShallow("fontWeight")||"",(t.fontSize||n&&n.getShallow("fontSize")||12)+"px",t.fontFamily||n&&n.getShallow("fontFamily")||"sans-serif"].join(" "))}function el(t,e,n,i,r,o){if("function"==typeof r&&(o=r,r=null),i&&i.isAnimationEnabled()){var a=t?"Update":"",s=i.getShallow("animationDuration"+a),l=i.getShallow("animationEasing"+a),u=i.getShallow("animationDelay"+a);"function"==typeof u&&(u=u(r,i.getAnimationDelayParams?i.getAnimationDelayParams(e,r):null)),"function"==typeof s&&(s=s(r)),0<s?e.animateTo(n,s,u||0,l,o,!!o):(e.stopAnimation(),e.attr(n),o&&o())}else e.stopAnimation(),e.attr(n),o&&o()}function nl(t,e,n,i,r){el(!0,t,e,n,i,r)}function il(t,e,n,i,r){el(!1,t,e,n,i,r)}function rl(t,e){for(var n=ne([]);t&&t!==e;)re(n,t.getLocalTransform(),n),t=t.parent;return n}function ol(t,e,n){return e&&!L(e)&&(e=de.getLocalTransform(e)),n&&(e=le([],e)),yt([],t,e)}function al(t,e,n){var i=0===e[4]||0===e[5]||0===e[0]?1:Math.abs(2*e[4]/e[0]),r=0===e[4]||0===e[5]||0===e[2]?1:Math.abs(2*e[4]/e[2]),o=["left"===t?-i:"right"===t?i:0,"top"===t?-r:"bottom"===t?r:0];return o=ol(o,e,n),Math.abs(o[0])>Math.abs(o[1])?0<o[0]?"right":"left":0<o[1]?"bottom":"top"}function sl(t,e,i,n){if(t&&e){var r,o=(r={},t.traverse(function(t){!t.isGroup&&t.anid&&(r[t.anid]=t)}),r);e.traverse(function(t){if(!t.isGroup&&t.anid){var e=o[t.anid];if(e){var n=a(t);t.attr(a(e)),nl(t,n,i,t.dataIndex)}}})}function a(t){var e={position:rt(t.position),rotation:t.rotation};return t.shape&&(e.shape=k({},t.shape)),e}}function ll(t,i){return P(t,function(t){var e=t[0];e=ds(e,i.x),e=fs(e,i.x+i.width);var n=t[1];return n=ds(n,i.y),[e,n=fs(n,i.y+i.height)]})}function ul(t,e,n){var i=(e=k({rectHover:!0},e)).style={strokeNoScale:!0};if(n=n||{x:-1,y:-1,width:2,height:2},t)return 0===t.indexOf("image://")?(i.image=t.slice(8),A(i,n),new Yi(e)):Ms(t.replace("path://",""),e,n,"center")}function hl(t,e,n,i,r,o,a,s){var l=n-t,u=i-e,h=a-r,c=s-o,d=cl(h,c,l,u);if(function(t){return t<=1e-6&&-1e-6<=t}(d))return!1;var f=t-r,p=e-o,g=cl(f,p,l,u)/d;if(g<0||1<g)return!1;var m=cl(f,p,h,c)/d;return!(m<0||1<m)}function cl(t,e,n,i){return t*i-n*e}bs("circle",Fa),bs("sector",Wa),bs("ring",Ga),bs("polygon",Xa),bs("polyline",Ya),bs("rect",Ja),bs("line",es),bs("bezierCurve",os),bs("arc",as);var dl=(Object.freeze||Object)({Z2_EMPHASIS_LIFT:gs,CACHED_LABEL_STYLE_PROPERTIES:{color:"textFill",textBorderColor:"textStroke",textBorderWidth:"textStrokeWidth"},extendShape:ws,extendPath:function(t,e){return function(t,e){return xa.extend(Na(t,e))}(t,e)},registerShape:bs,getShapeClass:Ss,makePath:Ms,makeImage:Is,mergePath:Ts,resizePath:As,subPixelOptimizeLine:function(t){return qa(t.shape,t.shape,t.style),t},subPixelOptimizeRect:function(t){return $a(t.shape,t.shape,t.style),t},subPixelOptimize:Ds,setElementHoverStyle:Rs,setHoverStyle:Gs,setAsHighDownDispatcher:Zs,isHighDownDispatcher:Us,getHighlightDigit:Xs,setLabelStyle:Ys,modifyLabelStyle:function(t,e,n){var i=t.style;e&&(Js(i),t.setStyle(e),Qs(i)),i=t.__hoverStl,n&&i&&(Js(i),k(i,n),Qs(i))},setTextStyle:js,setText:function(t,e,n){var i,r={isRectText:!0};!1===n?i=!0:r.autoColor=n,qs(t,e,r,i)},getFont:tl,updateProps:nl,initProps:il,getTransform:rl,applyTransform:ol,transformDirection:al,groupTransition:sl,clipPointsByRect:ll,clipRectByRect:function(t,e){var n=ds(t.x,e.x),i=fs(t.x+t.width,e.x+e.width),r=ds(t.y,e.y),o=fs(t.y+t.height,e.y+e.height);if(n<=i&&r<=o)return{x:n,y:r,width:i-n,height:o-r}},createIcon:ul,linePolygonIntersect:function(t,e,n,i,r){for(var o=0,a=r[r.length-1];o<r.length;o++){var s=r[o];if(hl(t,e,n,i,s[0],s[1],a[0],a[1]))return!0;a=s}},lineLineIntersect:hl,Group:Mn,Image:Yi,Text:Ba,Circle:Fa,Sector:Wa,Ring:Ga,Polygon:Xa,Polyline:Ya,Rect:Ja,Line:es,BezierCurve:os,Arc:as,IncrementalDisplayable:hs,CompoundPath:ss,LinearGradient:ls,RadialGradient:us,BoundingRect:Sn}),fl=["textStyle","color"],pl={getTextColor:function(t){var e=this.ecModel;return this.getShallow("color")||(!t&&e?e.get(fl):null)},getFont:function(){return tl({fontStyle:this.getShallow("fontStyle"),fontWeight:this.getShallow("fontWeight"),fontSize:this.getShallow("fontSize"),fontFamily:this.getShallow("fontFamily")},this.ecModel)},getTextRect:function(t){return ui(t,this.getFont(),this.getShallow("align"),this.getShallow("verticalAlign")||this.getShallow("baseline"),this.getShallow("padding"),this.getShallow("lineHeight"),this.getShallow("rich"),this.getShallow("truncateText"))}},gl=no([["fill","color"],["stroke","borderColor"],["lineWidth","borderWidth"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"],["textPosition"],["textAlign"]]),ml={getItemStyle:function(t,e){var n=gl(this,t,e),i=this.getBorderLineDash();return i&&(n.lineDash=i),n},getBorderLineDash:function(){var t=this.get("borderType");return"solid"===t||null==t?null:"dashed"===t?[5,5]:[1,1]}},vl=S,yl=Hr();function _l(t,e,n){this.parentModel=e,this.ecModel=n,this.option=t}function xl(t,e,n){for(var i=0;i<e.length&&(!e[i]||null!=(t=t&&"object"==typeof t?t[e[i]]:null));i++);return null==t&&n&&(t=n.get(e)),t}function wl(t,e){var n=yl(t).getParent;return n?n.call(t,e):t.parentModel}_l.prototype={constructor:_l,init:null,mergeOption:function(t){m(this.option,t,!0)},get:function(t,e){return null==t?this.option:xl(this.option,this.parsePath(t),!e&&wl(this,t))},getShallow:function(t,e){var n=this.option,i=null==n?n:n[t],r=!e&&wl(this,t);return null==i&&r&&(i=r.getShallow(t)),i},getModel:function(t,e){var n;return new _l(null==t?this.option:xl(this.option,t=this.parsePath(t)),e=e||(n=wl(this,t))&&n.getModel(t),this.ecModel)},isEmpty:function(){return null==this.option},restoreData:function(){},clone:function(){return new this.constructor(b(this.option))},setReadOnly:function(t){},parsePath:function(t){return"string"==typeof t&&(t=t.split(".")),t},customizeGetParent:function(t){yl(this).getParent=t},isAnimationEnabled:function(){if(!v.node){if(null!=this.option.animation)return!!this.option.animation;if(this.parentModel)return this.parentModel.isAnimationEnabled()}}},$r(_l),Qr(_l),vl(_l,ro),vl(_l,ao),vl(_l,pl),vl(_l,ml);var bl=0;function Sl(t){return[t||"",bl++,Math.random().toFixed(5)].join("_")}var Ml=1e-4;function Il(t,e,n,i){var r=e[1]-e[0],o=n[1]-n[0];if(0==r)return 0==o?n[0]:(n[0]+n[1])/2;if(i)if(0<r){if(t<=e[0])return n[0];if(t>=e[1])return n[1]}else{if(t>=e[0])return n[0];if(t<=e[1])return n[1]}else{if(t===e[0])return n[0];if(t===e[1])return n[1]}return(t-e[0])/r*o+n[0]}function Cl(t,e){switch(t){case"center":case"middle":t="50%";break;case"left":case"top":t="0%";break;case"right":case"bottom":t="100%"}return"string"==typeof t?function(t){return t.replace(/^\s+|\s+$/g,"")}(t).match(/%$/)?parseFloat(t)/100*e:parseFloat(t):null==t?NaN:+t}function Tl(t,e,n){return null==e&&(e=10),e=Math.min(Math.max(0,e),20),t=(+t).toFixed(e),n?t:+t}function Al(t){return t.sort(function(t,e){return t-e}),t}function Dl(t){if(t=+t,isNaN(t))return 0;for(var e=1,n=0;Math.round(t*e)/e!==t;)e*=10,n++;return n}function kl(t){var e=t.toString(),n=e.indexOf("e");if(0<n){var i=+e.slice(n+1);return i<0?-i:0}var r=e.indexOf(".");return r<0?0:e.length-1-r}function Pl(t,e){var n=Math.log,i=Math.LN10,r=Math.floor(n(t[1]-t[0])/i),o=Math.round(n(Math.abs(e[1]-e[0]))/i),a=Math.min(Math.max(-r+o,0),20);return isFinite(a)?a:20}function Ll(t,e,n){if(!t[e])return 0;var i=M(t,function(t,e){return t+(isNaN(e)?0:e)},0);if(0===i)return 0;for(var r=Math.pow(10,n),o=P(t,function(t){return(isNaN(t)?0:t)/i*r*100}),a=100*r,s=P(o,function(t){return Math.floor(t)}),l=M(s,function(t,e){return t+e},0),u=P(o,function(t,e){return t-s[e]});l<a;){for(var h=Number.NEGATIVE_INFINITY,c=null,d=0,f=u.length;d<f;++d)u[d]>h&&(h=u[d],c=d);++s[c],u[c]=0,++l}return s[e]/r}function Ol(t){var e=2*Math.PI;return(t%e+e)%e}function El(t){return-Ml<t&&t<Ml}var zl=/^(?:(\d{4})(?:[-\/](\d{1,2})(?:[-\/](\d{1,2})(?:[T ](\d{1,2})(?::(\d\d)(?::(\d\d)(?:[.,](\d+))?)?)?(Z|[\+\-]\d\d:?\d\d)?)?)?)?)?$/;function Nl(t){if(t instanceof Date)return t;if("string"!=typeof t)return null==t?new Date(NaN):new Date(Math.round(t));var e=zl.exec(t);if(!e)return new Date(NaN);if(e[8]){var n=+e[4]||0;return"Z"!==e[8].toUpperCase()&&(n-=e[8].slice(0,3)),new Date(Date.UTC(+e[1],+(e[2]||1)-1,+e[3]||1,n,+(e[5]||0),+e[6]||0,+e[7]||0))}return new Date(+e[1],+(e[2]||1)-1,+e[3]||1,+e[4]||0,+(e[5]||0),+e[6]||0,+e[7]||0)}function Rl(t){return Math.pow(10,Bl(t))}function Bl(t){if(0===t)return 0;var e=Math.floor(Math.log(t)/Math.LN10);return 10<=t/Math.pow(10,e)&&e++,e}function Vl(t,e){var n=Bl(t),i=Math.pow(10,n),r=t/i;return t=(e?r<1.5?1:r<2.5?2:r<4?3:r<7?5:10:r<1?1:r<2?2:r<3?3:r<5?5:10)*i,-20<=n?+t.toFixed(n<0?-n:0):t}var Fl=(Object.freeze||Object)({linearMap:Il,parsePercent:Cl,round:Tl,asc:Al,getPrecision:Dl,getPrecisionSafe:kl,getPixelPrecision:Pl,getPercentWithPrecision:Ll,MAX_SAFE_INTEGER:9007199254740991,remRadian:Ol,isRadianAroundZero:El,parseDate:Nl,quantity:Rl,quantityExponent:Bl,nice:Vl,quantile:function(t,e){var n=(t.length-1)*e+1,i=Math.floor(n),r=+t[i-1],o=n-i;return o?r+o*(t[i]-r):r},reformIntervals:function(t){t.sort(function(t,e){return function t(e,n,i){return e.interval[i]<n.interval[i]||e.interval[i]===n.interval[i]&&(e.close[i]-n.close[i]==(i?-1:1)||!i&&t(e,n,1))}(t,e,0)?-1:1});for(var e=-1/0,n=1,i=0;i<t.length;){for(var r=t[i].interval,o=t[i].close,a=0;a<2;a++)r[a]<=e&&(r[a]=e,o[a]=a?1:1-n),e=r[a],n=o[a];r[0]===r[1]&&o[0]*o[1]!=1?t.splice(i,1):i++}return t},isNumeric:function(t){return 0<=t-parseFloat(t)}});function Hl(t){return isNaN(t)?"-":(t=(t+"").split("."))[0].replace(/(\d{1,3})(?=(?:\d{3})+(?!\d))/g,"$1,")+(1<t.length?"."+t[1]:"")}function Wl(t,e){return t=(t||"").toLowerCase().replace(/-(.)/g,function(t,e){return e.toUpperCase()}),e&&t&&(t=t.charAt(0).toUpperCase()+t.slice(1)),t}var Gl=U,Zl=/([&<>"'])/g,Ul={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"};function Xl(t){return null==t?"":(t+"").replace(Zl,function(t,e){return Ul[e]})}function Yl(t,e){return"{"+t+(null==e?"":e)+"}"}var jl=["a","b","c","d","e","f","g"];function ql(t,e,n){O(e)||(e=[e]);var i=e.length;if(!i)return"";for(var r=e[0].$vars||[],o=0;o<r.length;o++){var a=jl[o];t=t.replace(Yl(a),Yl(a,0))}for(var s=0;s<i;s++)for(var l=0;l<r.length;l++){var u=e[s][r[l]];t=t.replace(Yl(jl[l],s),n?Xl(u):u)}return t}function $l(t,e){var n=(t=z(t)?{color:t,extraCssText:e}:t||{}).color,i=t.type,r=(e=t.extraCssText,t.renderMode||"html"),o=t.markerId||"X";return n?"html"===r?"subItem"===i?'<span style="display:inline-block;vertical-align:middle;margin-right:8px;margin-left:3px;border-radius:4px;width:4px;height:4px;background-color:'+Xl(n)+";"+(e||"")+'"></span>':'<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:'+Xl(n)+";"+(e||"")+'"></span>':{renderMode:r,content:"{marker"+o+"|}  ",style:{color:n}}:""}function Kl(t,e){return"0000".substr(0,e-(t+="").length)+t}function Ql(t,e,n){"week"!==t&&"month"!==t&&"quarter"!==t&&"half-year"!==t&&"year"!==t||(t="MM-dd\nyyyy");var i=Nl(e),r=n?"UTC":"",o=i["get"+r+"FullYear"](),a=i["get"+r+"Month"]()+1,s=i["get"+r+"Date"](),l=i["get"+r+"Hours"](),u=i["get"+r+"Minutes"](),h=i["get"+r+"Seconds"](),c=i["get"+r+"Milliseconds"]();return t=t.replace("MM",Kl(a,2)).replace("M",a).replace("yyyy",o).replace("yy",o%100).replace("dd",Kl(s,2)).replace("d",s).replace("hh",Kl(l,2)).replace("h",l).replace("mm",Kl(u,2)).replace("m",u).replace("ss",Kl(h,2)).replace("s",h).replace("SSS",Kl(c,3))}function Jl(t){return t?t.charAt(0).toUpperCase()+t.substr(1):t}var tu=fi;function eu(t,e){if("_blank"===e||"blank"===e){var n=window.open();n.opener=null,n.location=t}else window.open(t,e)}var nu=(Object.freeze||Object)({addCommas:Hl,toCamelCase:Wl,normalizeCssArray:Gl,encodeHTML:Xl,formatTpl:ql,formatTplSimple:function(n,t,i){return D(t,function(t,e){n=n.replace("{"+e+"}",i?Xl(t):t)}),n},getTooltipMarker:$l,formatTime:Ql,capitalFirst:Jl,truncateText:tu,getTextBoundingRect:function(t){return ui(t.text,t.font,t.textAlign,t.textVerticalAlign,t.textPadding,t.textLineHeight,t.rich,t.truncate)},getTextRect:function(t,e,n,i,r,o,a,s){return ui(t,e,n,i,r,s,o,a)},windowOpen:eu}),iu=D,ru=["left","right","top","bottom","width","height"],ou=[["width","left","right"],["height","top","bottom"]];function au(h,c,d,f,p){var g=0,m=0;null==f&&(f=1/0),null==p&&(p=1/0);var v=0;c.eachChild(function(t,e){var n,i,r=t.position,o=t.getBoundingRect(),a=c.childAt(e+1),s=a&&a.getBoundingRect();if("horizontal"===h){var l=o.width+(s?-s.x+o.x:0);v=f<(n=g+l)||t.newline?(g=0,n=l,m+=v+d,o.height):Math.max(v,o.height)}else{var u=o.height+(s?-s.y+o.y:0);v=p<(i=m+u)||t.newline?(g+=v+d,m=0,i=u,o.width):Math.max(v,o.width)}t.newline||(r[0]=g,r[1]=m,"horizontal"===h?g=n+d:m=i+d)})}var su=au;T(au,"vertical"),T(au,"horizontal");function lu(t,e,n){n=Gl(n||0);var i=e.width,r=e.height,o=Cl(t.left,i),a=Cl(t.top,r),s=Cl(t.right,i),l=Cl(t.bottom,r),u=Cl(t.width,i),h=Cl(t.height,r),c=n[2]+n[0],d=n[1]+n[3],f=t.aspect;switch(isNaN(u)&&(u=i-s-d-o),isNaN(h)&&(h=r-l-c-a),null!=f&&(isNaN(u)&&isNaN(h)&&(i/r<f?u=.8*i:h=.8*r),isNaN(u)&&(u=f*h),isNaN(h)&&(h=u/f)),isNaN(o)&&(o=i-s-u-d),isNaN(a)&&(a=r-l-h-c),t.left||t.right){case"center":o=i/2-u/2-n[3];break;case"right":o=i-u-d}switch(t.top||t.bottom){case"middle":case"center":a=r/2-h/2-n[0];break;case"bottom":a=r-h-c}o=o||0,a=a||0,isNaN(u)&&(u=i-d-o-(s||0)),isNaN(h)&&(h=r-c-a-(l||0));var p=new Sn(o+n[3],a+n[0],u,h);return p.margin=n,p}function uu(t,e,n,i,r){var o=!r||!r.hv||r.hv[0],a=!r||!r.hv||r.hv[1],s=r&&r.boundingMode||"all";if(o||a){var l;if("raw"===s)l="group"===t.type?new Sn(0,0,+e.width||0,+e.height||0):t.getBoundingRect();else if(l=t.getBoundingRect(),t.needLocalTransform()){var u=t.getLocalTransform();(l=l.clone()).applyTransform(u)}e=lu(A({width:l.width,height:l.height},e),n,i);var h=t.position,c=o?e.x-l.x:0,d=a?e.y-l.y:0;t.attr("position","raw"===s?[c,d]:[h[0]+c,h[1]+d])}}function hu(l,u,t){N(t)||(t={});var h=t.ignoreSize;O(h)||(h=[h,h]);var e=i(ou[0],0),n=i(ou[1],1);function i(t,e){var n={},i=0,r={},o=0;if(iu(t,function(t){r[t]=l[t]}),iu(t,function(t){c(u,t)&&(n[t]=r[t]=u[t]),d(n,t)&&i++,d(r,t)&&o++}),h[e])return d(u,t[1])?r[t[2]]=null:d(u,t[2])&&(r[t[1]]=null),r;if(2!==o&&i){if(2<=i)return n;for(var a=0;a<t.length;a++){var s=t[a];if(!c(n,s)&&c(l,s)){n[s]=l[s];break}}return n}return r}function c(t,e){return t.hasOwnProperty(e)}function d(t,e){return null!=t[e]&&"auto"!==t[e]}function r(t,e,n){iu(t,function(t){e[t]=n[t]})}r(ou[0],l,e),r(ou[1],l,n)}function cu(t){return du({},t)}function du(e,n){return n&&e&&iu(ru,function(t){n.hasOwnProperty(t)&&(e[t]=n[t])}),e}var fu,pu,gu,mu=Hr(),vu=_l.extend({type:"component",id:"",name:"",mainType:"",subType:"",componentIndex:0,defaultOption:null,ecModel:null,dependentModels:[],uid:null,layoutMode:null,$constructor:function(t,e,n,i){_l.call(this,t,e,n,i),this.uid=Sl("ec_cpt_model")},init:function(t,e,n,i){this.mergeDefaultAndTheme(t,n)},mergeDefaultAndTheme:function(t,e){var n=this.layoutMode,i=n?cu(t):{};m(t,e.getTheme().get(this.mainType)),m(t,this.getDefaultOption()),n&&hu(t,i,n)},mergeOption:function(t,e){m(this.option,t,!0);var n=this.layoutMode;n&&hu(this.option,t,n)},optionUpdated:function(t,e){},getDefaultOption:function(){var t=mu(this);if(!t.defaultOption){for(var e=[],n=this.constructor;n;){var i=n.prototype.defaultOption;i&&e.push(i),n=n.superClass}for(var r={},o=e.length-1;0<=o;o--)r=m(r,e[o],!0);t.defaultOption=r}return t.defaultOption},getReferringComponents:function(t){return this.ecModel.queryComponents({mainType:t,index:this.get(t+"Index",!0),id:this.get(t+"Id",!0)})}});function yu(t,e){return t[e]||(t[e]={predecessor:[],successor:[]}),t[e]}eo(vu,{registerWhenExtend:!0}),pu={},(fu=vu).registerSubTypeDefaulter=function(t,e){t=qr(t),pu[t.main]=e},fu.determineSubType=function(t,e){var n=e.type;if(!n){var i=qr(t).main;fu.hasSubTypes(t)&&pu[i]&&(n=pu[i](e))}return n},gu=function(t){var e=[];D(vu.getClassesByMainType(t),function(t){e=e.concat(t.prototype.dependencies||[])}),e=P(e,function(t){return qr(t).main}),"dataset"!==t&&x(e,"dataset")<=0&&e.unshift("dataset");return e},vu.topologicalTravel=function(t,e,n,i){if(t.length){var r=function(e){var r={},o=[];return D(e,function(n){var i=yu(r,n),t=function(t,e){var n=[];return D(t,function(t){0<=x(e,t)&&n.push(t)}),n}(i.originalDeps=gu(n),e);i.entryCount=t.length,0===i.entryCount&&o.push(n),D(t,function(t){x(i.predecessor,t)<0&&i.predecessor.push(t);var e=yu(r,t);x(e.successor,t)<0&&e.successor.push(n)})}),{graph:r,noEntryList:o}}(e),o=r.graph,a=r.noEntryList,s={};for(D(t,function(t){s[t]=!0});a.length;){var l=a.pop(),u=o[l],h=!!s[l];h&&(n.call(i,l,u.originalDeps.slice()),delete s[l]),D(u.successor,h?d:c)}D(s,function(){throw new Error("Circle dependency may exists")})}function c(t){o[t].entryCount--,0===o[t].entryCount&&a.push(t)}function d(t){s[t]=!0,c(t)}},S(vu,{getBoxLayoutParams:function(){return{left:this.get("left"),top:this.get("top"),right:this.get("right"),bottom:this.get("bottom"),width:this.get("width"),height:this.get("height")}}});var _u="";"undefined"!=typeof navigator&&(_u=navigator.platform||"");var xu={color:["#c23531","#2f4554","#61a0a8","#d48265","#91c7ae","#749f83","#ca8622","#bda29a","#6e7074","#546570","#c4ccd3"],gradientColor:["#f6efa6","#d88273","#bf444c"],textStyle:{fontFamily:_u.match(/^Win/)?"Microsoft YaHei":"sans-serif",fontSize:12,fontStyle:"normal",fontWeight:"normal"},blendMode:null,animation:"auto",animationDuration:1e3,animationDurationUpdate:300,animationEasing:"exponentialOut",animationEasingUpdate:"cubicOut",animationThreshold:2e3,progressiveThreshold:3e3,progressive:400,hoverLayerThreshold:3e3,useUTC:!1},wu=Hr();var bu={clearColorPalette:function(){wu(this).colorIdx=0,wu(this).colorNameMap={}},getColorFromPalette:function(t,e,n){var i=wu(e=e||this),r=i.colorIdx||0,o=i.colorNameMap=i.colorNameMap||{};if(o.hasOwnProperty(t))return o[t];var a=Lr(this.get("color",!0)),s=this.get("colorLayer",!0),l=null!=n&&s?function(t,e){for(var n=t.length,i=0;i<n;i++)if(t[i].length>e)return t[i];return t[n-1]}(s,n):a;if((l=l||a)&&l.length){var u=l[r];return t&&(o[t]=u),i.colorIdx=(r+1)%l.length,u}}},Su="original",Mu="arrayRows",Iu="objectRows",Cu="keyedColumns",Tu="unknown",Au="typedArray",Du="column",ku="row";function Pu(t){this.fromDataset=t.fromDataset,this.data=t.data||(t.sourceFormat===Cu?{}:[]),this.sourceFormat=t.sourceFormat||Tu,this.seriesLayoutBy=t.seriesLayoutBy||Du,this.dimensionsDefine=t.dimensionsDefine,this.encodeDefine=t.encodeDefine&&Q(t.encodeDefine),this.startIndex=t.startIndex||0,this.dimensionsDetectCount=t.dimensionsDetectCount}Pu.seriesDataToSource=function(t){return new Pu({data:t,sourceFormat:B(t)?Au:Su,fromDataset:!1})},Qr(Pu);var Lu={Must:1,Might:2,Not:3},Ou=Hr();function Eu(t){var e=t.option,n=e.data,i=B(n)?Au:Su,r=!1,o=e.seriesLayoutBy,a=e.sourceHeader,s=e.dimensions,l=Vu(t);if(l){var u=l.option;n=u.source,i=Ou(l).sourceFormat,r=!0,o=o||u.seriesLayoutBy,null==a&&(a=u.sourceHeader),s=s||u.dimensions}var h=function(t,e,n,i,r){if(!t)return{dimensionsDefine:zu(r)};var o,a;if(e===Mu)"auto"===i||null==i?Nu(function(t){null!=t&&"-"!==t&&(z(t)?null==a&&(a=1):a=0)},n,t,10):a=i?1:0,r||1!==a||(r=[],Nu(function(t,e){r[e]=null!=t?t:""},n,t)),o=r?r.length:n===ku?t.length:t[0]?t[0].length:null;else if(e===Iu)r=r||function(t){var e,n=0;for(;n<t.length&&!(e=t[n++]););if(e){var i=[];return D(e,function(t,e){i.push(e)}),i}}(t);else if(e===Cu)r||(r=[],D(t,function(t,e){r.push(e)}));else if(e===Su){var s=zr(t[0]);o=O(s)&&s.length||1}return{startIndex:a,dimensionsDefine:zu(r),dimensionsDetectCount:o}}(n,i,o,a,s);Ou(t).source=new Pu({data:n,fromDataset:r,seriesLayoutBy:o,sourceFormat:i,dimensionsDefine:h.dimensionsDefine,startIndex:h.startIndex,dimensionsDetectCount:h.dimensionsDetectCount,encodeDefine:e.encode})}function zu(t){if(t){var i=Q();return P(t,function(t,e){if(null==(t=k({},N(t)?t:{name:t})).name)return t;t.name+="",null==t.displayName&&(t.displayName=t.name);var n=i.get(t.name);return n?t.name+="-"+n.count++:i.set(t.name,{count:1}),t})}}function Nu(t,e,n,i){if(null==i&&(i=1/0),e===ku)for(var r=0;r<n.length&&r<i;r++)t(n[r]?n[r][0]:null,r);else{var o=n[0]||[];for(r=0;r<o.length&&r<i;r++)t(o[r],r)}}function Ru(n,t,e){var o={},i=Vu(t);if(!i||!n)return o;var a,r,s=[],l=[],u=t.ecModel,h=Ou(u).datasetMap,c=i.uid+"_"+e.seriesLayoutBy;D(n=n.slice(),function(t,e){N(t)||(n[e]={name:t}),"ordinal"===t.type&&null==a&&(r=p(n[a=e])),o[t.name]=[]});var d=h.get(c)||h.set(c,{categoryWayDim:r,valueWayDim:0});function f(t,e,n){for(var i=0;i<n;i++)t.push(e+i)}function p(t){var e=t.dimsDef;return e?e.length:1}return D(n,function(t,e){var n=t.name,i=p(t);if(null==a){var r=d.valueWayDim;f(o[n],r,i),f(l,r,i),d.valueWayDim+=i}else if(a===e)f(o[n],0,i),f(s,0,i);else{r=d.categoryWayDim;f(o[n],r,i),f(l,r,i),d.categoryWayDim+=i}}),s.length&&(o.itemName=s),l.length&&(o.seriesName=l),o}function Bu(t,l,u){var e={};if(!Vu(t))return e;var h,c=l.sourceFormat,d=l.dimensionsDefine;c!==Iu&&c!==Cu||D(d,function(t,e){"name"===(N(t)?t.name:t)&&(h=e)});var n=function(){for(var t={},e={},n=[],i=0,r=Math.min(5,u);i<r;i++){var o=Fu(l.data,c,l.seriesLayoutBy,d,l.startIndex,i);n.push(o);var a=o===Lu.Not;if(a&&null==t.v&&i!==h&&(t.v=i),null!=t.n&&t.n!==t.v&&(a||n[t.n]!==Lu.Not)||(t.n=i),s(t)&&n[t.n]!==Lu.Not)return t;a||(o===Lu.Might&&null==e.v&&i!==h&&(e.v=i),null!=e.n&&e.n!==e.v||(e.n=i))}function s(t){return null!=t.v&&null!=t.n}return s(t)?t:s(e)?e:null}();if(n){e.value=n.v;var i=null!=h?h:n.n;e.itemName=[i],e.seriesName=[i]}return e}function Vu(t){var e=t.option;if(!e.data)return t.ecModel.getComponent("dataset",e.datasetIndex||0)}function Fu(t,e,n,i,r,o){var a,s,l;if(B(t))return Lu.Not;if(i){var u=i[o];N(u)?(s=u.name,l=u.type):z(u)&&(s=u)}if(null!=l)return"ordinal"===l?Lu.Must:Lu.Not;if(e===Mu)if(n===ku){for(var h=t[o],c=0;c<(h||[]).length&&c<5;c++)if(null!=(a=g(h[r+c])))return a}else for(c=0;c<t.length&&c<5;c++){var d=t[r+c];if(d&&null!=(a=g(d[o])))return a}else if(e===Iu){if(!s)return Lu.Not;for(c=0;c<t.length&&c<5;c++){if((f=t[c])&&null!=(a=g(f[s])))return a}}else if(e===Cu){if(!s)return Lu.Not;if(!(h=t[s])||B(h))return Lu.Not;for(c=0;c<h.length&&c<5;c++)if(null!=(a=g(h[c])))return a}else if(e===Su)for(c=0;c<t.length&&c<5;c++){var f,p=zr(f=t[c]);if(!O(p))return Lu.Not;if(null!=(a=g(p[o])))return a}function g(t){var e=z(t);return null!=t&&isFinite(t)&&""!==t?e?Lu.Might:Lu.Not:e&&"-"!==t?Lu.Must:void 0}return Lu.Not}var Hu="\0_ec_inner",Wu=_l.extend({init:function(t,e,n,i){n=n||{},this.option=null,this._theme=new _l(n),this._optionManager=i},setOption:function(t,e){X(!(Hu in t),"please use chart.getOption()"),this._optionManager.setOption(t,e),this.resetOption(null)},resetOption:function(t){var e=!1,n=this._optionManager;if(!t||"recreate"===t){var i=n.mountOption("recreate"===t);this.option&&"recreate"!==t?(this.restoreData(),this.mergeOption(i)):function(t){t=t,this.option={},this.option[Hu]=1,this._componentsMap=Q({series:[]}),this._seriesIndices,this._seriesIndicesMap,function(n,t){var i=n.color&&!n.colorLayer;D(t,function(t,e){"colorLayer"===e&&i||vu.hasClass(e)||("object"==typeof t?n[e]=n[e]?m(n[e],t,!1):b(t):null==n[e]&&(n[e]=t))})}(t,this._theme.option),m(t,xu,!1),this.mergeOption(t)}.call(this,i),e=!0}if("timeline"!==t&&"media"!==t||this.restoreData(),!t||"recreate"===t||"timeline"===t){var r=n.getTimelineOption(this);r&&(this.mergeOption(r),e=!0)}if(!t||"recreate"===t||"media"===t){var o=n.getMediaOption(this,this._api);o.length&&D(o,function(t){this.mergeOption(t,e=!0)},this)}return e},mergeOption:function(i){var l=this.option,u=this._componentsMap,n=[];!function(t){Ou(t).datasetMap=Q()}(this),D(i,function(t,e){null!=t&&(vu.hasClass(e)?e&&n.push(e):l[e]=null==l[e]?b(t):m(l[e],t,!0))}),vu.topologicalTravel(n,vu.getAllClassMainTypes(),function(a,t){var e=Lr(i[a]),n=Nr(u.get(a),e);Rr(n),D(n,function(t,e){var n=t.option;N(n)&&(t.keyInfo.mainType=a,t.keyInfo.subType=function(t,e,n){return e.type?e.type:n?n.subType:vu.determineSubType(t,e)}(a,n,t.exist))});var s=function(e,t){O(t)||(t=t?[t]:[]);var n={};return D(t,function(t){n[t]=(e.get(t)||[]).slice()}),n}(u,t);l[a]=[],u.set(a,[]),D(n,function(t,e){var n=t.exist,i=t.option;if(X(N(i)||n,"Empty component definition"),i){var r=vu.getClass(a,t.keyInfo.subType,!0);if(n&&n.constructor===r)n.name=t.keyInfo.name,n.mergeOption(i,this),n.optionUpdated(i,!1);else{var o=k({dependentModels:s,componentIndex:e},t.keyInfo);k(n=new r(i,this,this,o),o),n.init(i,this,this,o),n.optionUpdated(null,!0)}}else n.mergeOption({},this),n.optionUpdated({},!1);u.get(a)[e]=n,l[a][e]=n.option},this),"series"===a&&Gu(this,u.get("series"))},this),this._seriesIndicesMap=Q(this._seriesIndices=this._seriesIndices||[])},getOption:function(){var i=b(this.option);return D(i,function(t,e){if(vu.hasClass(e)){for(var n=(t=Lr(t)).length-1;0<=n;n--)Vr(t[n])&&t.splice(n,1);i[e]=t}}),delete i[Hu],i},getTheme:function(){return this._theme},getComponent:function(t,e){var n=this._componentsMap.get(t);if(n)return n[e||0]},queryComponents:function(t){var e=t.mainType;if(!e)return[];var n,i=t.index,r=t.id,o=t.name,a=this._componentsMap.get(e);if(!a||!a.length)return[];if(null!=i)O(i)||(i=[i]),n=I(P(i,function(t){return a[t]}),function(t){return!!t});else if(null!=r){var s=O(r);n=I(a,function(t){return s&&0<=x(r,t.id)||!s&&t.id===r})}else if(null!=o){var l=O(o);n=I(a,function(t){return l&&0<=x(o,t.name)||!l&&t.name===o})}else n=a.slice();return Zu(n,t)},findComponents:function(t){var e,n,i,r,o,a=t.query,s=t.mainType,l=(n=s+"Index",i=s+"Id",r=s+"Name",!(e=a)||null==e[n]&&null==e[i]&&null==e[r]?null:{mainType:s,index:e[n],id:e[i],name:e[r]}),u=l?this.queryComponents(l):this._componentsMap.get(s);return o=Zu(u,t),t.filter?I(o,t.filter):o},eachComponent:function(t,i,r){var e=this._componentsMap;if("function"==typeof t)r=i,i=t,e.each(function(t,n){D(t,function(t,e){i.call(r,n,t,e)})});else if(z(t))D(e.get(t),i,r);else if(N(t)){D(this.findComponents(t),i,r)}},getSeriesByName:function(e){return I(this._componentsMap.get("series"),function(t){return t.name===e})},getSeriesByIndex:function(t){return this._componentsMap.get("series")[t]},getSeriesByType:function(e){return I(this._componentsMap.get("series"),function(t){return t.subType===e})},getSeries:function(){return this._componentsMap.get("series").slice()},getSeriesCount:function(){return this._componentsMap.get("series").length},eachSeries:function(n,i){D(this._seriesIndices,function(t){var e=this._componentsMap.get("series")[t];n.call(i,e,t)},this)},eachRawSeries:function(t,e){D(this._componentsMap.get("series"),t,e)},eachSeriesByType:function(n,i,r){D(this._seriesIndices,function(t){var e=this._componentsMap.get("series")[t];e.subType===n&&i.call(r,e,t)},this)},eachRawSeriesByType:function(t,e,n){return D(this.getSeriesByType(t),e,n)},isSeriesFiltered:function(t){return null==this._seriesIndicesMap.get(t.componentIndex)},getCurrentSeriesIndices:function(){return(this._seriesIndices||[]).slice()},filterSeries:function(t,e){var n=I(this._componentsMap.get("series"),t,e);Gu(this,n)},restoreData:function(n){var i=this._componentsMap;Gu(this,i.get("series"));var r=[];i.each(function(t,e){r.push(e)}),vu.topologicalTravel(r,vu.getAllClassMainTypes(),function(e,t){D(i.get(e),function(t){"series"===e&&function(t,e){if(e){var n=e.seiresIndex,i=e.seriesId,r=e.seriesName;return null!=n&&t.componentIndex!==n||null!=i&&t.id!==i||null!=r&&t.name!==r}}(t,n)||t.restoreData()})})}});function Gu(t,e){t._seriesIndicesMap=Q(t._seriesIndices=P(e,function(t){return t.componentIndex})||[])}function Zu(t,e){return e.hasOwnProperty("subType")?I(t,function(t){return t.subType===e.subType}):t}S(Wu,bu);var Uu=["getDom","getZr","getWidth","getHeight","getDevicePixelRatio","dispatchAction","isDisposed","on","off","getDataURL","getConnectedDataURL","getModel","getOption","getViewOfComponentModel","getViewOfSeriesModel"];function Xu(e){D(Uu,function(t){this[t]=C(e[t],e)},this)}var Yu={};function ju(){this._coordinateSystems=[]}ju.prototype={constructor:ju,create:function(i,r){var o=[];D(Yu,function(t,e){var n=t.create(i,r);o=o.concat(n||[])}),this._coordinateSystems=o},update:function(e,n){D(this._coordinateSystems,function(t){t.update&&t.update(e,n)})},getCoordinateSystems:function(){return this._coordinateSystems.slice()}},ju.register=function(t,e){Yu[t]=e},ju.get=function(t){return Yu[t]};var qu=D,$u=b,Ku=P,Qu=m,Ju=/^(min|max)?(.+)$/;function th(t){this._api=t,this._timelineOptions=[],this._mediaList=[],this._mediaDefault,this._currentMediaIndices=[],this._optionBackup,this._newBaseOption}function eh(t,e,n){var o={width:e,height:n,aspectratio:e/n},a=!0;return D(t,function(t,e){var n=e.match(Ju);if(n&&n[1]&&n[2]){var i=n[1],r=n[2].toLowerCase();!function(t,e,n){return"min"===n?e<=t:"max"===n?t<=e:t===e}(o[r],t,i)&&(a=!1)}}),a}th.prototype={constructor:th,setOption:function(t,e){t&&D(Lr(t.series),function(t){t&&t.data&&B(t.data)&&q(t.data)}),t=$u(t);var n=this._optionBackup,i=function(t,n,i){var e,r,o=[],a=[],s=t.timeline;t.baseOption&&(r=t.baseOption);(s||t.options)&&(r=r||{},o=(t.options||[]).slice());if(t.media){r=r||{};var l=t.media;qu(l,function(t){t&&t.option&&(t.query?a.push(t):e=e||t)})}r=r||t;r.timeline||(r.timeline=s);return qu([r].concat(o).concat(P(a,function(t){return t.option})),function(e){qu(n,function(t){t(e,i)})}),{baseOption:r,timelineOptions:o,mediaDefault:e,mediaList:a}}.call(this,t,e,!n);this._newBaseOption=i.baseOption,n?(function(r,t){qu(t=t||{},function(t,e){if(null!=t){var n=r[e];if(vu.hasClass(e)){t=Lr(t);var i=Nr(n=Lr(n),t);r[e]=Ku(i,function(t){return t.option&&t.exist?Qu(t.exist,t.option,!0):t.exist||t.option})}else r[e]=Qu(n,t,!0)}})}(n.baseOption,i.baseOption),i.timelineOptions.length&&(n.timelineOptions=i.timelineOptions),i.mediaList.length&&(n.mediaList=i.mediaList),i.mediaDefault&&(n.mediaDefault=i.mediaDefault)):this._optionBackup=i},mountOption:function(t){var e=this._optionBackup;return this._timelineOptions=Ku(e.timelineOptions,$u),this._mediaList=Ku(e.mediaList,$u),this._mediaDefault=$u(e.mediaDefault),this._currentMediaIndices=[],$u(t?e.baseOption:this._newBaseOption)},getTimelineOption:function(t){var e,n=this._timelineOptions;if(n.length){var i=t.getComponent("timeline");i&&(e=$u(n[i.getCurrentIndex()],!0))}return e},getMediaOption:function(t){var e=this._api.getWidth(),n=this._api.getHeight(),i=this._mediaList,r=this._mediaDefault,o=[],a=[];if(!i.length&&!r)return a;for(var s=0,l=i.length;s<l;s++)eh(i[s].query,e,n)&&o.push(s);return!o.length&&r&&(o=[-1]),o.length&&!function(t,e){return t.join(",")===e.join(",")}(o,this._currentMediaIndices)&&(a=Ku(o,function(t){return $u(-1===t?r.option:i[t].option)})),this._currentMediaIndices=o,a}};var nh=D,ih=N,rh=["areaStyle","lineStyle","nodeStyle","linkStyle","chordStyle","label","labelLine"];function oh(t){var e=t&&t.itemStyle;if(e)for(var n=0,i=rh.length;n<i;n++){var r=rh[n],o=e.normal,a=e.emphasis;o&&o[r]&&(t[r]=t[r]||{},t[r].normal?m(t[r].normal,o[r]):t[r].normal=o[r],o[r]=null),a&&a[r]&&(t[r]=t[r]||{},t[r].emphasis?m(t[r].emphasis,a[r]):t[r].emphasis=a[r],a[r]=null)}}function ah(t,e,n){if(t&&t[e]&&(t[e].normal||t[e].emphasis)){var i=t[e].normal,r=t[e].emphasis;i&&(n?(t[e].normal=t[e].emphasis=null,A(t[e],i)):t[e]=i),r&&(t.emphasis=t.emphasis||{},t.emphasis[e]=r)}}function sh(t){ah(t,"itemStyle"),ah(t,"lineStyle"),ah(t,"areaStyle"),ah(t,"label"),ah(t,"labelLine"),ah(t,"upperLabel"),ah(t,"edgeLabel")}function lh(t,e){var n=ih(t)&&t[e],i=ih(n)&&n.textStyle;if(i)for(var r=0,o=Er.length;r<o;r++){e=Er[r];i.hasOwnProperty(e)&&(n[e]=i[e])}}function uh(t){t&&(sh(t),lh(t,"label"),t.emphasis&&lh(t.emphasis,"label"))}function hh(t){return O(t)?t:t?[t]:[]}function ch(t){return(O(t)?t[0]:t)||{}}function dh(e,t){nh(hh(e.series),function(t){ih(t)&&function(t){if(ih(t)){oh(t),sh(t),lh(t,"label"),lh(t,"upperLabel"),lh(t,"edgeLabel"),t.emphasis&&(lh(t.emphasis,"label"),lh(t.emphasis,"upperLabel"),lh(t.emphasis,"edgeLabel")),(n=t.markPoint)&&(oh(n),uh(n)),(i=t.markLine)&&(oh(i),uh(i));var e=t.markArea;e&&uh(e);var n,i,r=t.data;if("graph"===t.type){r=r||t.nodes;var o=t.links||t.edges;if(o&&!B(o))for(var a=0;a<o.length;a++)uh(o[a]);D(t.categories,function(t){sh(t)})}if(r&&!B(r))for(a=0;a<r.length;a++)uh(r[a]);if((n=t.markPoint)&&n.data){var s=n.data;for(a=0;a<s.length;a++)uh(s[a])}if((i=t.markLine)&&i.data){var l=i.data;for(a=0;a<l.length;a++)O(l[a])?(uh(l[a][0]),uh(l[a][1])):uh(l[a])}"gauge"===t.type?(lh(t,"axisLabel"),lh(t,"title"),lh(t,"detail")):"treemap"===t.type?(ah(t.breadcrumb,"itemStyle"),D(t.levels,function(t){sh(t)})):"tree"===t.type&&sh(t.leaves)}}(t)});var n=["xAxis","yAxis","radiusAxis","angleAxis","singleAxis","parallelAxis","radar"];t&&n.push("valueAxis","categoryAxis","logAxis","timeAxis"),nh(n,function(t){nh(hh(e[t]),function(t){t&&(lh(t,"axisLabel"),lh(t.axisPointer,"label"))})}),nh(hh(e.parallel),function(t){var e=t&&t.parallelAxisDefault;lh(e,"axisLabel"),lh(e&&e.axisPointer,"label")}),nh(hh(e.calendar),function(t){ah(t,"itemStyle"),lh(t,"dayLabel"),lh(t,"monthLabel"),lh(t,"yearLabel")}),nh(hh(e.radar),function(t){lh(t,"name")}),nh(hh(e.geo),function(t){ih(t)&&(uh(t),nh(hh(t.regions),function(t){uh(t)}))}),nh(hh(e.timeline),function(t){uh(t),ah(t,"label"),ah(t,"itemStyle"),ah(t,"controlStyle",!0);var e=t.data;O(e)&&D(e,function(t){N(t)&&(ah(t,"label"),ah(t,"itemStyle"))})}),nh(hh(e.toolbox),function(t){ah(t,"iconStyle"),nh(t.feature,function(t){ah(t,"iconStyle")})}),lh(ch(e.axisPointer),"label"),lh(ch(e.tooltip).axisPointer,"label")}function fh(e){D(ph,function(t){t[0]in e&&!(t[1]in e)&&(e[t[1]]=e[t[0]])})}var ph=[["x","left"],["y","top"],["x2","right"],["y2","bottom"]],gh=["grid","geo","parallel","legend","toolbox","title","visualMap","dataZoom","timeline"],mh=function(n,t){dh(n,t),n.series=Lr(n.series),D(n.series,function(t){if(N(t)){var e=t.type;if("line"===e)null!=t.clipOverflow&&(t.clip=t.clipOverflow);else if("pie"===e||"gauge"===e)null!=t.clockWise&&(t.clockwise=t.clockWise);else if("gauge"===e){var n=function(t,e){e=e.split(",");for(var n=t,i=0;i<e.length&&null!=(n=n&&n[e[i]]);i++);return n}(t,"pointer.color");null!=n&&function(t,e,n,i){e=e.split(",");for(var r,o=t,a=0;a<e.length-1;a++)null==o[r=e[a]]&&(o[r]={}),o=o[r];!i&&null!=o[e[a]]||(o[e[a]]=n)}(t,"itemStyle.color",n)}fh(t)}}),n.dataRange&&(n.visualMap=n.dataRange),D(gh,function(t){var e=n[t];e&&(O(e)||(e=[e]),D(e,function(t){fh(t)}))})};function vh(m){D(m,function(h,c){var d=[],f=[NaN,NaN],t=[h.stackResultDimension,h.stackedOverDimension],p=h.data,g=h.isStackedByIndex,e=p.map(t,function(t,e,n){var i,r,o=p.get(h.stackedDimension,n);if(isNaN(o))return f;g?r=p.getRawIndex(n):i=p.get(h.stackedByDimension,n);for(var a=NaN,s=c-1;0<=s;s--){var l=m[s];if(g||(r=l.data.rawIndexOf(l.stackedByDimension,i)),0<=r){var u=l.data.getByRawIndex(l.stackResultDimension,r);if(0<=o&&0<u||o<=0&&u<0){o+=u,a=u;break}}}return d[0]=o,d[1]=a,d});p.hostModel.setData(e),h.data=e})}function yh(t,e){Pu.isInstance(t)||(t=Pu.seriesDataToSource(t)),this._source=t;var n=this._data=t.data,i=t.sourceFormat;i===Au&&(this._offset=0,this._dimSize=e,this._data=n),k(this,xh[i===Mu?i+"_"+t.seriesLayoutBy:i])}var _h=yh.prototype;_h.pure=!1;var xh={arrayRows_column:{pure:_h.persistent=!0,count:function(){return Math.max(0,this._data.length-this._source.startIndex)},getItem:function(t){return this._data[t+this._source.startIndex]},appendData:Sh},arrayRows_row:{pure:!0,count:function(){var t=this._data[0];return t?Math.max(0,t.length-this._source.startIndex):0},getItem:function(t){t+=this._source.startIndex;for(var e=[],n=this._data,i=0;i<n.length;i++){var r=n[i];e.push(r?r[t]:null)}return e},appendData:function(){throw new Error('Do not support appendData when set seriesLayoutBy: "row".')}},objectRows:{pure:!0,count:wh,getItem:bh,appendData:Sh},keyedColumns:{pure:!0,count:function(){var t=this._source.dimensionsDefine[0].name,e=this._data[t];return e?e.length:0},getItem:function(t){for(var e=[],n=this._source.dimensionsDefine,i=0;i<n.length;i++){var r=this._data[n[i].name];e.push(r?r[t]:null)}return e},appendData:function(t){var r=this._data;D(t,function(t,e){for(var n=r[e]||(r[e]=[]),i=0;i<(t||[]).length;i++)n.push(t[i])})}},original:{count:wh,getItem:bh,appendData:Sh},typedArray:{persistent:!(_h.getSource=function(){return this._source}),pure:!0,count:function(){return this._data?this._data.length/this._dimSize:0},getItem:function(t,e){t-=this._offset,e=e||[];for(var n=this._dimSize*t,i=0;i<this._dimSize;i++)e[i]=this._data[n+i];return e},appendData:function(t){this._data=t},clean:function(){this._offset+=this.count(),this._data=null}}};function wh(){return this._data.length}function bh(t){return this._data[t]}function Sh(t){for(var e=0;e<t.length;e++)this._data.push(t[e])}var Mh={arrayRows:Ih,objectRows:function(t,e,n,i){return null!=n?t[i]:t},keyedColumns:Ih,original:function(t,e,n,i){var r=zr(t);return null!=n&&r instanceof Array?r[n]:r},typedArray:Ih};function Ih(t,e,n,i){return null!=n?t[n]:t}var Ch={arrayRows:Th,objectRows:function(t,e,n,i){return Ah(t[e],this._dimensionInfos[e])},keyedColumns:Th,original:function(t,e,n,i){var r=t&&(null==t.value?t:t.value);return!this._rawData.pure&&function(t){return Dr(t)&&!(t instanceof Array)}(t)&&(this.hasItemOption=!0),Ah(r instanceof Array?r[i]:r,this._dimensionInfos[e])},typedArray:function(t,e,n,i){return t[i]}};function Th(t,e,n,i){return Ah(t[i],this._dimensionInfos[e])}function Ah(t,e){var n=e&&e.type;if("ordinal"!==n)return"time"===n&&"number"!=typeof t&&null!=t&&"-"!==t&&(t=+Nl(t)),null==t||""===t?NaN:+t;var i=e&&e.ordinalMeta;return i?i.parseAndCollect(t):t}function Dh(t,e,n){if(t){var i=t.getRawDataItem(e);if(null!=i){var r,o,a=t.getProvider().getSource().sourceFormat,s=t.getDimensionInfo(n);return s&&(r=s.name,o=s.index),Mh[a](i,e,o,r)}}}function kh(t,e,n){if(t){var i=t.getProvider().getSource().sourceFormat;if(i===Su||i===Iu){var r=t.getRawDataItem(e);return i!==Su||N(r)||(r=null),r?r[n]:void 0}}}var Ph=/\{@(.+?)\}/g,Lh={getDataParams:function(t,e){var n=this.getData(e),i=this.getRawValue(t,e),r=n.getRawIndex(t),o=n.getName(t),a=n.getRawDataItem(t),s=n.getItemVisual(t,"color"),l=n.getItemVisual(t,"borderColor"),u=this.ecModel.getComponent("tooltip"),h=Xr(u&&u.get("renderMode")),c=this.mainType,d="series"===c,f=n.userOutput;return{componentType:c,componentSubType:this.subType,componentIndex:this.componentIndex,seriesType:d?this.subType:null,seriesIndex:this.seriesIndex,seriesId:d?this.id:null,seriesName:d?this.name:null,name:o,dataIndex:r,data:a,dataType:e,value:i,color:s,borderColor:l,dimensionNames:f?f.dimensionNames:null,encode:f?f.encode:null,marker:$l({color:s,renderMode:h}),$vars:["seriesName","name","value"]}},getFormattedLabel:function(i,t,e,n,r){t=t||"normal";var o=this.getData(e),a=o.getItemModel(i),s=this.getDataParams(i,e);null!=n&&s.value instanceof Array&&(s.value=s.value[n]);var l=a.get("normal"===t?[r||"label","formatter"]:[t,r||"label","formatter"]);return"function"==typeof l?(s.status=t,s.dimensionIndex=n,l(s)):"string"==typeof l?ql(l,s).replace(Ph,function(t,e){var n=e.length;return"["===e.charAt(0)&&"]"===e.charAt(n-1)&&(e=+e.slice(1,n-1)),Dh(o,i,e)}):void 0},getRawValue:function(t,e){return Dh(this.getData(e),t)},formatTooltip:function(){}};function Oh(t){return new Eh(t)}function Eh(t){t=t||{},this._reset=t.reset,this._plan=t.plan,this._count=t.count,this._onDirty=t.onDirty,this._dirty=!0,this.context}var zh=Eh.prototype;zh.perform=function(t){var e,n=this._upstream,i=t&&t.skip;if(this._dirty&&n){var r=this.context;r.data=r.outputData=n.context.outputData}this.__pipeline&&(this.__pipeline.currentTask=this),this._plan&&!i&&(e=this._plan(this.context));var o,a=h(this._modBy),s=this._modDataCount||0,l=h(t&&t.modBy),u=t&&t.modDataCount||0;function h(t){return 1<=t||(t=1),t}a===l&&s===u||(e="reset"),!this._dirty&&"reset"!==e||(this._dirty=!1,o=function(t,e){var n,i;t._dueIndex=t._outputDueEnd=t._dueEnd=0,t._settedOutputEnd=null,!e&&t._reset&&((n=t._reset(t.context))&&n.progress&&(i=n.forceFirstProgress,n=n.progress),O(n)&&!n.length&&(n=null));t._progress=n,t._modBy=t._modDataCount=null;var r=t._downstream;return r&&r.dirty(),i}(this,i)),this._modBy=l,this._modDataCount=u;var c=t&&t.step;if(this._dueEnd=n?n._outputDueEnd:this._count?this._count(this.context):1/0,this._progress){var d=this._dueIndex,f=Math.min(null!=c?this._dueIndex+c:1/0,this._dueEnd);if(!i&&(o||d<f)){var p=this._progress;if(O(p))for(var g=0;g<p.length;g++)Uh(this,p[g],d,f,l,u);else Uh(this,p,d,f,l,u)}this._dueIndex=f;var m=null!=this._settedOutputEnd?this._settedOutputEnd:f;this._outputDueEnd=m}else this._dueIndex=this._outputDueEnd=null!=this._settedOutputEnd?this._settedOutputEnd:this._dueEnd;return this.unfinished()};var Nh,Rh,Bh,Vh,Fh,Hh,Wh=Hh={reset:function(t,e,n,i){Rh=t,Nh=e,Bh=n,Vh=i,Fh=Math.ceil(Vh/Bh),Hh.next=1<Bh&&0<Vh?Zh:Gh}};function Gh(){return Rh<Nh?Rh++:null}function Zh(){var t=Rh%Fh*Bh+Math.ceil(Rh/Fh),e=Nh<=Rh?null:t<Vh?t:Rh;return Rh++,e}function Uh(t,e,n,i,r,o){Wh.reset(n,i,r,o),t._callingProgress=e,t._callingProgress({start:n,end:i,count:i-n,next:Wh.next},t.context)}zh.dirty=function(){this._dirty=!0,this._onDirty&&this._onDirty(this.context)},zh.unfinished=function(){return this._progress&&this._dueIndex<this._dueEnd},zh.pipe=function(t){this._downstream===t&&!this._dirty||((this._downstream=t)._upstream=this,t.dirty())},zh.dispose=function(){this._disposed||(this._upstream&&(this._upstream._downstream=null),this._downstream&&(this._downstream._upstream=null),this._dirty=!1,this._disposed=!0)},zh.getUpstream=function(){return this._upstream},zh.getDownstream=function(){return this._downstream},zh.setOutputEnd=function(t){this._outputDueEnd=this._settedOutputEnd=t};var Xh=Hr(),Yh=vu.extend({type:"series.__base__",seriesIndex:0,coordinateSystem:null,defaultOption:null,legendVisualProvider:null,visualColorAccessPath:"itemStyle.color",visualBorderColorAccessPath:"itemStyle.borderColor",layoutMode:null,init:function(t,e,n,i){this.seriesIndex=this.componentIndex,this.dataTask=Oh({count:qh,reset:$h}),this.dataTask.context={model:this},this.mergeDefaultAndTheme(t,n),Eu(this);var r=this.getInitialData(t,n);Qh(r,this),this.dataTask.context.data=r,Xh(this).dataBeforeProcessed=r,jh(this)},mergeDefaultAndTheme:function(t,e){var n=this.layoutMode,i=n?cu(t):{},r=this.subType;vu.hasClass(r)&&(r+="Series"),m(t,e.getTheme().get(this.subType)),m(t,this.getDefaultOption()),Or(t,"label",["show"]),this.fillDataTextStyle(t.data),n&&hu(t,i,n)},mergeOption:function(t,e){t=m(this.option,t,!0),this.fillDataTextStyle(t.data);var n=this.layoutMode;n&&hu(this.option,t,n),Eu(this);var i=this.getInitialData(t,e);Qh(i,this),this.dataTask.dirty(),this.dataTask.context.data=i,Xh(this).dataBeforeProcessed=i,jh(this)},fillDataTextStyle:function(t){if(t&&!B(t))for(var e=["show"],n=0;n<t.length;n++)t[n]&&t[n].label&&Or(t[n],"label",e)},getInitialData:function(){},appendData:function(t){this.getRawData().appendData(t.data)},getData:function(t){var e=tc(this);if(e){var n=e.context.data;return null==t?n:n.getLinkedData(t)}return Xh(this).data},setData:function(t){var e=tc(this);if(e){var n=e.context;n.data!==t&&e.modifyOutputEnd&&e.setOutputEnd(t.count()),n.outputData=t,e!==this.dataTask&&(n.data=t)}Xh(this).data=t},getSource:function(){return function(t){return Ou(t).source}(this)},getRawData:function(){return Xh(this).dataBeforeProcessed},getBaseAxis:function(){var t=this.coordinateSystem;return t&&t.getBaseAxis&&t.getBaseAxis()},formatTooltip:function(r,h,t,c){var d=this,e="html"===(c=c||"html")?"<br/>":"\n",f="richText"===c,p={},g=0;function n(t){return{renderMode:c,content:Xl(Hl(t)),style:p}}var m=this.getData(),o=m.mapDimension("defaultedTooltip",!0),i=o.length,a=this.getRawValue(r),s=O(a),v=m.getItemVisual(r,"color");N(v)&&v.colorStops&&(v=(v.colorStops[0]||{}).color),v=v||"transparent";var l=(1<i||s&&!i?function(t){var l=M(t,function(t,e,n){var i=m.getDimensionInfo(n);return t|(i&&!1!==i.tooltip&&null!=i.displayName)},0),u=[];function e(t,e){var n=m.getDimensionInfo(e);if(n&&!1!==n.otherDims.tooltip){var i=n.type,r="sub"+d.seriesIndex+"at"+g,o=$l({color:v,type:"subItem",renderMode:c,markerId:r}),a="string"==typeof o?o:o.content,s=(l?a+Xl(n.displayName||"-")+": ":"")+Xl("ordinal"===i?t+"":"time"===i?h?"":Ql("yyyy/MM/dd hh:mm:ss",t):Hl(t));s&&u.push(s),f&&(p[r]=v,++g)}}o.length?D(o,function(t){e(Dh(m,r,t),t)}):D(t,e);var n=l?f?"\n":"<br/>":"",i=n+u.join(n||", ");return{renderMode:c,content:i,style:p}}(a):n(i?Dh(m,r,o[0]):s?a[0]:a)).content,u=d.seriesIndex+"at"+g,y=$l({color:v,type:"item",renderMode:c,markerId:u});p[u]=v,++g;var _=m.getName(r),x=this.name;Br(this)||(x=""),x=x?Xl(x)+(h?": ":e):"";var w="string"==typeof y?y:y.content;return{html:h?w+x+l:x+w+(_?Xl(_)+": "+l:l),markers:p}},isAnimationEnabled:function(){if(v.node)return!1;var t=this.getShallow("animation");return t&&this.getData().count()>this.getShallow("animationThreshold")&&(t=!1),t},restoreData:function(){this.dataTask.dirty()},getColorFromPalette:function(t,e,n){var i=this.ecModel,r=bu.getColorFromPalette.call(this,t,e,n);return r=r||i.getColorFromPalette(t,e,n)},coordDimToDataDim:function(t){return this.getRawData().mapDimension(t,!0)},getProgressive:function(){return this.get("progressive")},getProgressiveThreshold:function(){return this.get("progressiveThreshold")},getAxisTooltipData:null,getTooltipPosition:null,pipeTask:null,preventIncremental:null,pipelineContext:null});function jh(t){var e=t.name;Br(t)||(t.name=function(t){var n=t.getRawData(),e=n.mapDimension("seriesName",!0),i=[];return D(e,function(t){var e=n.getDimensionInfo(t);e.displayName&&i.push(e.displayName)}),i.join(" ")}(t)||e)}function qh(t){return t.model.getRawData().count()}function $h(t){var e=t.model;return e.setData(e.getRawData().cloneShallow()),Kh}function Kh(t,e){e.outputData&&t.end>e.outputData.count()&&e.model.getRawData().cloneShallow(e.outputData)}function Qh(e,n){D(e.CHANGABLE_METHODS,function(t){e.wrapMethod(t,T(Jh,n))})}function Jh(t){var e=tc(t);e&&e.setOutputEnd(this.count())}function tc(t){var e=(t.ecModel||{}).scheduler,n=e&&e.getPipeline(t.uid);if(n){var i=n.currentTask;if(i){var r=i.agentStubMap;r&&(i=r.get(t.uid))}return i}}S(Yh,Lh),S(Yh,bu);var ec=function(){this.group=new Mn,this.uid=Sl("viewComponent")};ec.prototype={constructor:ec,init:function(t,e){},render:function(t,e,n,i){},dispose:function(){},filterForExposedEvent:null};var nc=ec.prototype;nc.updateView=nc.updateLayout=nc.updateVisual=function(t,e,n,i){},$r(ec),eo(ec,{registerWhenExtend:!0});function ic(){var s=Hr();return function(t){var e=s(t),n=t.pipelineContext,i=e.large,r=e.progressiveRender,o=e.large=n&&n.large,a=e.progressiveRender=n&&n.progressiveRender;return!!(i^o||r^a)&&"reset"}}var rc=Hr(),oc=ic();function ac(){this.group=new Mn,this.uid=Sl("viewChart"),this.renderTask=Oh({plan:hc,reset:cc}),this.renderTask.context={view:this}}var sc=ac.prototype={type:"chart",init:function(t,e){},render:function(t,e,n,i){},highlight:function(t,e,n,i){uc(t.getData(),i,"emphasis")},downplay:function(t,e,n,i){uc(t.getData(),i,"normal")},remove:function(t,e){this.group.removeAll()},dispose:function(){},incrementalPrepareRender:null,incrementalRender:null,updateTransform:null,filterForExposedEvent:null};function lc(t,e,n){if(t&&(t.trigger(e,n),t.isGroup&&!Us(t)))for(var i=0,r=t.childCount();i<r;i++)lc(t.childAt(i),e,n)}function uc(e,t,n){var i=Fr(e,t),r=t&&null!=t.highlightKey?Xs(t.highlightKey):null;null!=i?D(Lr(i),function(t){lc(e.getItemGraphicEl(t),n,r)}):e.eachItemGraphicEl(function(t){lc(t,n,r)})}function hc(t){return oc(t.model)}function cc(t){var e=t.model,n=t.ecModel,i=t.api,r=t.payload,o=e.pipelineContext.progressiveRender,a=t.view,s=r&&rc(r).updateMethod,l=o?"incrementalPrepareRender":s&&a[s]?s:"render";return"render"!==l&&a[l](e,n,i,r),dc[l]}sc.updateView=sc.updateLayout=sc.updateVisual=function(t,e,n,i){this.render(t,e,n,i)},$r(ac),eo(ac,{registerWhenExtend:!0}),ac.markUpdateMethod=function(t,e){rc(t).updateMethod=e};var dc={incrementalPrepareRender:{progress:function(t,e){e.view.incrementalRender(t,e.model,e.ecModel,e.api,e.payload)}},render:{forceFirstProgress:!0,progress:function(t,e){e.view.render(e.model,e.ecModel,e.api,e.payload)}}},fc="\0__throttleOriginMethod",pc="\0__throttleRate",gc="\0__throttleType";function mc(t,n,i){var r,o,a,s,l,u=0,h=0,c=null;function d(){h=(new Date).getTime(),c=null,t.apply(a,s||[])}n=n||0;function e(){r=(new Date).getTime(),a=this,s=arguments;var t=l||n,e=l||i;l=null,o=r-(e?u:h)-t,clearTimeout(c),e?c=setTimeout(d,t):0<=o?d():c=setTimeout(d,-o),u=r}return e.clear=function(){c&&(clearTimeout(c),c=null)},e.debounceNextCall=function(t){l=t},e}function vc(t,e,n,i){var r=t[e];if(r){var o=r[fc]||r,a=r[gc];if(r[pc]!==n||a!==i){if(null==n||!i)return t[e]=o;(r=t[e]=mc(o,n,"debounce"===i))[fc]=o,r[gc]=i,r[pc]=n}return r}}function yc(t,e){var n=t[e];n&&n[fc]&&(t[e]=n[fc])}var _c={createOnAllSeries:!0,performRawSeries:!0,reset:function(e,t){var n=e.getData(),o=(e.visualColorAccessPath||"itemStyle.color").split("."),i=e.get(o),r=!E(i)||i instanceof rs?null:i;i&&!r||(i=e.getColorFromPalette(e.name,null,t.getSeriesCount())),n.setVisual("color",i);var a=(e.visualBorderColorAccessPath||"itemStyle.borderColor").split("."),s=e.get(a);if(n.setVisual("borderColor",s),!t.isSeriesFiltered(e)){r&&n.each(function(t){n.setItemVisual(t,"color",r(e.getDataParams(t)))});return{dataEach:n.hasItemOption?function(t,e){var n=t.getItemModel(e),i=n.get(o,!0),r=n.get(a,!0);null!=i&&t.setItemVisual(e,"color",i),null!=r&&t.setItemVisual(e,"borderColor",r)}:null}}}},xc={legend:{selector:{all:"全选",inverse:"反选"}},toolbox:{brush:{title:{rect:"矩形选择",polygon:"圈选",lineX:"横向选择",lineY:"纵向选择",keep:"保持选择",clear:"清除选择"}},dataView:{title:"数据视图",lang:["数据视图","关闭","刷新"]},dataZoom:{title:{zoom:"区域缩放",back:"区域缩放还原"}},magicType:{title:{line:"切换为折线图",bar:"切换为柱状图",stack:"切换为堆叠",tiled:"切换为平铺"}},restore:{title:"还原"},saveAsImage:{title:"保存为图片",lang:["右键另存为图片"]}},series:{typeNames:{pie:"饼图",bar:"柱状图",line:"折线图",scatter:"散点图",effectScatter:"涟漪散点图",radar:"雷达图",tree:"树图",treemap:"矩形树图",boxplot:"箱型图",candlestick:"K线图",k:"K线图",heatmap:"热力图",map:"地图",parallel:"平行坐标图",lines:"线图",graph:"关系图",sankey:"桑基图",funnel:"漏斗图",gauge:"仪表盘图",pictorialBar:"象形柱图",themeRiver:"主题河流图",sunburst:"旭日图"}},aria:{general:{withTitle:"这是一个关于“{title}”的图表。",withoutTitle:"这是一个图表，"},series:{single:{prefix:"",withName:"图表类型是{seriesType}，表示{seriesName}。",withoutName:"图表类型是{seriesType}。"},multiple:{prefix:"它由{seriesCount}个图表系列组成。",withName:"第{seriesId}个系列是一个表示{seriesName}的{seriesType}，",withoutName:"第{seriesId}个系列是一个{seriesType}，",separator:{middle:"；",end:"。"}}},data:{allData:"其数据是——",partialData:"其中，前{displayCnt}项是——",withName:"{name}的数据是{value}",withoutName:"{value}",separator:{middle:"，",end:""}}}},wc=function(t,e){var o=e.getModel("aria");if(o.get("show"))if(o.get("description"))t.setAttribute("aria-label",o.get("description"));else{var h=0;e.eachSeries(function(t,e){++h},this);var n,c=o.get("data.maxCount")||10,i=o.get("series.maxCount")||10,d=Math.min(h,i);if(!(h<1)){var r=function(){var t=e.getModel("title").option;t&&t.length&&(t=t[0]);return t&&t.text}();n=r?p(g("general.withTitle"),{title:r}):g("general.withoutTitle");var f=[];n+=p(g(1<h?"series.multiple.prefix":"series.single.prefix"),{seriesCount:h}),e.eachSeries(function(t,e){if(e<d){var n,i=t.get("name"),r="series."+(1<h?"multiple":"single")+".";n=p(n=g(i?r+"withName":r+"withoutName"),{seriesId:t.seriesIndex,seriesName:t.get("name"),seriesType:function(t){return xc.series.typeNames[t]||"自定义图"}(t.subType)});var o=t.getData();(window.data=o).count()>c?n+=p(g("data.partialData"),{displayCnt:c}):n+=g("data.allData");for(var a=[],s=0;s<o.count();s++)if(s<c){var l=o.getName(s),u=Dh(o,s);a.push(p(g(l?"data.withName":"data.withoutName"),{name:l,value:u}))}n+=a.join(g("data.separator.middle"))+g("data.separator.end"),f.push(n)}}),n+=f.join(g("series.multiple.separator.middle"))+g("series.multiple.separator.end"),t.setAttribute("aria-label",n)}}function p(t,e){if("string"!=typeof t)return t;var n=t;return D(e,function(t,e){n=n.replace(new RegExp("\\{\\s*"+e+"\\s*\\}","g"),t)}),n}function g(t){var e=o.get(t);if(null!=e)return e;for(var n=t.split("."),i=xc.aria,r=0;r<n.length;++r)i=i[n[r]];return i}},bc=Math.PI;function Sc(t,e,n,i){this.ecInstance=t,this.api=e,this.unfinished;n=this._dataProcessorHandlers=n.slice(),i=this._visualHandlers=i.slice();this._allHandlers=n.concat(i),this._stageTaskMap=Q()}var Mc=Sc.prototype;function Ic(l,t,u,h,c){var d;function f(t,e){return t.setDirty&&(!t.dirtyMap||t.dirtyMap.get(e.__pipeline.id))}c=c||{},D(t,function(i,t){if(!c.visualType||c.visualType===i.visualType){var e=l._stageTaskMap.get(i.uid),n=e.seriesTaskMap,r=e.overallTask;if(r){var o,a=r.agentStubMap;a.each(function(t){f(c,t)&&(t.dirty(),o=!0)}),o&&r.dirty(),Cc(r,h);var s=l.getPerformArgs(r,c.block);a.each(function(t){t.perform(s)}),d|=r.perform(s)}else n&&n.each(function(t,e){f(c,t)&&t.dirty();var n=l.getPerformArgs(t,c.block);n.skip=!i.performRawSeries&&u.isSeriesFiltered(t.context.model),Cc(t,h),d|=t.perform(n)})}}),l.unfinished|=d}Mc.restoreData=function(t,e){t.restoreData(e),this._stageTaskMap.each(function(t){var e=t.overallTask;e&&e.dirty()})},Mc.getPerformArgs=function(t,e){if(t.__pipeline){var n=this._pipelineMap.get(t.__pipeline.id),i=n.context,r=!e&&n.progressiveEnabled&&(!i||i.progressiveRender)&&t.__idxInPipeline>n.blockIndex?n.step:null,o=i&&i.modDataCount;return{step:r,modBy:null!=o?Math.ceil(o/r):null,modDataCount:o}}},Mc.getPipeline=function(t){return this._pipelineMap.get(t)},Mc.updateStreamModes=function(t,e){var n=this._pipelineMap.get(t.uid),i=t.getData().count(),r=n.progressiveEnabled&&e.incrementalPrepareRender&&i>=n.threshold,o=t.get("large")&&i>=t.get("largeThreshold"),a="mod"===t.get("progressiveChunkMode")?i:null;t.pipelineContext=n.context={progressiveRender:r,modDataCount:a,large:o}},Mc.restorePipelines=function(t){var i=this,r=i._pipelineMap=Q();t.eachSeries(function(t){var e=t.getProgressive(),n=t.uid;r.set(n,{id:n,head:null,tail:null,threshold:t.getProgressiveThreshold(),progressiveEnabled:e&&!(t.preventIncremental&&t.preventIncremental()),blockIndex:-1,step:Math.round(e||700),count:0}),Nc(i,t,t.dataTask)})},Mc.prepareStageTasks=function(){var n=this._stageTaskMap,i=this.ecInstance.getModel(),r=this.api;D(this._allHandlers,function(t){var e=n.get(t.uid)||n.set(t.uid,[]);t.reset&&function(i,r,t,o,a){var s=t.seriesTaskMap||(t.seriesTaskMap=Q()),e=r.seriesType,n=r.getTargetSeries;r.createOnAllSeries?o.eachRawSeries(l):e?o.eachRawSeriesByType(e,l):n&&n(o,a).each(l);function l(t){var e=t.uid,n=s.get(e)||s.set(e,Oh({plan:Pc,reset:Lc,count:zc}));n.context={model:t,ecModel:o,api:a,useClearVisual:r.isVisual&&!r.isLayout,plan:r.plan,reset:r.reset,scheduler:i},Nc(i,t,n)}var u=i._pipelineMap;s.each(function(t,e){u.get(e)||(t.dispose(),s.removeKey(e))})}(this,t,e,i,r),t.overallReset&&function(i,t,e,n,r){var o=e.overallTask=e.overallTask||Oh({reset:Tc});o.context={ecModel:n,api:r,overallReset:t.overallReset,scheduler:i};var a=o.agentStubMap=o.agentStubMap||Q(),s=t.seriesType,l=t.getTargetSeries,u=!0,h=t.modifyOutputEnd;s?n.eachRawSeriesByType(s,c):l?l(n,r).each(c):(u=!1,D(n.getSeries(),c));function c(t){var e=t.uid,n=a.get(e);n||(n=a.set(e,Oh({reset:Ac,onDirty:kc})),o.dirty()),n.context={model:t,overallProgress:u,modifyOutputEnd:h},n.agent=o,n.__block=u,Nc(i,t,n)}var d=i._pipelineMap;a.each(function(t,e){d.get(e)||(t.dispose(),o.dirty(),a.removeKey(e))})}(this,t,e,i,r)},this)},Mc.prepareView=function(t,e,n,i){var r=t.renderTask,o=r.context;o.model=e,o.ecModel=n,o.api=i,r.__block=!t.incrementalPrepareRender,Nc(this,e,r)},Mc.performDataProcessorTasks=function(t,e){Ic(this,this._dataProcessorHandlers,t,e,{block:!0})},Mc.performVisualTasks=function(t,e,n){Ic(this,this._visualHandlers,t,e,n)},Mc.performSeriesTasks=function(t){var e;t.eachSeries(function(t){e|=t.dataTask.perform()}),this.unfinished|=e},Mc.plan=function(){this._pipelineMap.each(function(t){var e=t.tail;do{if(e.__block){t.blockIndex=e.__idxInPipeline;break}e=e.getUpstream()}while(e)})};var Cc=Mc.updatePayload=function(t,e){"remain"!==e&&(t.context.payload=e)};function Tc(t){t.overallReset(t.ecModel,t.api,t.payload)}function Ac(t,e){return t.overallProgress&&Dc}function Dc(){this.agent.dirty(),this.getDownstream().dirty()}function kc(){this.agent&&this.agent.dirty()}function Pc(t){return t.plan&&t.plan(t.model,t.ecModel,t.api,t.payload)}function Lc(t){t.useClearVisual&&t.data.clearAllVisual();var e=t.resetDefines=Lr(t.reset(t.model,t.ecModel,t.api,t.payload));return 1<e.length?P(e,function(t,e){return Ec(e)}):Oc}var Oc=Ec(0);function Ec(o){return function(t,e){var n=e.data,i=e.resetDefines[o];if(i&&i.dataEach)for(var r=t.start;r<t.end;r++)i.dataEach(n,r);else i&&i.progress&&i.progress(t,n)}}function zc(t){return t.data.count()}function Nc(t,e,n){var i=e.uid,r=t._pipelineMap.get(i);r.head||(r.head=n),r.tail&&r.tail.pipe(n),(r.tail=n).__idxInPipeline=r.count++,n.__pipeline=r}Sc.wrapStageHandler=function(t,e){return E(t)&&(t={overallReset:t,seriesType:function(t){Rc=null;try{t(Bc,Vc)}catch(t){}return Rc}(t)}),t.uid=Sl("stageHandler"),e&&(t.visualType=e),t};var Rc,Bc={},Vc={};function Fc(t,e){for(var n in e.prototype)t[n]=J}Fc(Bc,Wu),Fc(Vc,Xu),Bc.eachSeriesByType=Bc.eachRawSeriesByType=function(t){Rc=t},Bc.eachComponent=function(t){"series"===t.mainType&&t.subType&&(Rc=t.subType)};function Hc(){return{axisLine:{lineStyle:{color:Zc}},axisTick:{lineStyle:{color:Zc}},axisLabel:{textStyle:{color:Zc}},splitLine:{lineStyle:{type:"dashed",color:"#aaa"}},splitArea:{areaStyle:{color:Zc}}}}var Wc=["#37A2DA","#32C5E9","#67E0E3","#9FE6B8","#FFDB5C","#ff9f7f","#fb7293","#E062AE","#E690D1","#e7bcf3","#9d96f5","#8378EA","#96BFFF"],Gc={color:Wc,colorLayer:[["#37A2DA","#ffd85c","#fd7b5f"],["#37A2DA","#67E0E3","#FFDB5C","#ff9f7f","#E062AE","#9d96f5"],["#37A2DA","#32C5E9","#9FE6B8","#FFDB5C","#ff9f7f","#fb7293","#e7bcf3","#8378EA","#96BFFF"],Wc]},Zc="#eee",Uc=["#dd6b66","#759aa0","#e69d87","#8dc1a9","#ea7e53","#eedd78","#73a373","#73b9bc","#7289ab","#91ca8c","#f49f42"],Xc={color:Uc,backgroundColor:"#333",tooltip:{axisPointer:{lineStyle:{color:Zc},crossStyle:{color:Zc},label:{color:"#000"}}},legend:{textStyle:{color:Zc}},textStyle:{color:Zc},title:{textStyle:{color:Zc}},toolbox:{iconStyle:{normal:{borderColor:Zc}}},dataZoom:{textStyle:{color:Zc}},visualMap:{textStyle:{color:Zc}},timeline:{lineStyle:{color:Zc},itemStyle:{normal:{color:Uc[1]}},label:{normal:{textStyle:{color:Zc}}},controlStyle:{normal:{color:Zc,borderColor:Zc}}},timeAxis:Hc(),logAxis:Hc(),valueAxis:Hc(),categoryAxis:Hc(),line:{symbol:"circle"},graph:{color:Uc},gauge:{title:{textStyle:{color:Zc}}},candlestick:{itemStyle:{normal:{color:"#FD1050",color0:"#0CF49B",borderColor:"#FD1050",borderColor0:"#0CF49B"}}}};Xc.categoryAxis.splitLine.show=!1,vu.extend({type:"dataset",defaultOption:{seriesLayoutBy:Du,sourceHeader:null,dimensions:null,source:null},optionUpdated:function(){!function(t){var e=t.option.source,n=Tu;if(B(e))n=Au;else if(O(e)){0===e.length&&(n=Mu);for(var i=0,r=e.length;i<r;i++){var o=e[i];if(null!=o){if(O(o)){n=Mu;break}if(N(o)){n=Iu;break}}}}else if(N(e)){for(var a in e)if(e.hasOwnProperty(a)&&L(e[a])){n=Cu;break}}else if(null!=e)throw new Error("Invalid data");Ou(t).sourceFormat=n}(this)}}),ec.extend({type:"dataset"});var Yc=xa.extend({type:"ellipse",shape:{cx:0,cy:0,rx:0,ry:0},buildPath:function(t,e){var n=e.cx,i=e.cy,r=e.rx,o=e.ry,a=.5522848*r,s=.5522848*o;t.moveTo(n-r,i),t.bezierCurveTo(n-r,i-s,n-a,i-o,n,i-o),t.bezierCurveTo(n+a,i-o,n+r,i-s,n+r,i),t.bezierCurveTo(n+r,i+s,n+a,i+o,n,i+o),t.bezierCurveTo(n-a,i+o,n-r,i+s,n-r,i),t.closePath()}}),jc=/[\s,]+/;function qc(t){z(t)&&(t=(new DOMParser).parseFromString(t,"text/xml"));for(9===t.nodeType&&(t=t.firstChild);"svg"!==t.nodeName.toLowerCase()||1!==t.nodeType;)t=t.nextSibling;return t}var $c={g:function(t,e){var n=new Mn;return Qc(e,n),ed(t,n,this._defs),n},rect:function(t,e){var n=new Ja;return Qc(e,n),ed(t,n,this._defs),n.setShape({x:parseFloat(t.getAttribute("x")||0),y:parseFloat(t.getAttribute("y")||0),width:parseFloat(t.getAttribute("width")||0),height:parseFloat(t.getAttribute("height")||0)}),n},circle:function(t,e){var n=new Fa;return Qc(e,n),ed(t,n,this._defs),n.setShape({cx:parseFloat(t.getAttribute("cx")||0),cy:parseFloat(t.getAttribute("cy")||0),r:parseFloat(t.getAttribute("r")||0)}),n},line:function(t,e){var n=new es;return Qc(e,n),ed(t,n,this._defs),n.setShape({x1:parseFloat(t.getAttribute("x1")||0),y1:parseFloat(t.getAttribute("y1")||0),x2:parseFloat(t.getAttribute("x2")||0),y2:parseFloat(t.getAttribute("y2")||0)}),n},ellipse:function(t,e){var n=new Yc;return Qc(e,n),ed(t,n,this._defs),n.setShape({cx:parseFloat(t.getAttribute("cx")||0),cy:parseFloat(t.getAttribute("cy")||0),rx:parseFloat(t.getAttribute("rx")||0),ry:parseFloat(t.getAttribute("ry")||0)}),n},polygon:function(t,e){var n=t.getAttribute("points");n=n&&Jc(n);var i=new Xa({shape:{points:n||[]}});return Qc(e,i),ed(t,i,this._defs),i},polyline:function(t,e){var n=new xa;Qc(e,n),ed(t,n,this._defs);var i=t.getAttribute("points");return i=i&&Jc(i),new Ya({shape:{points:i||[]}})},image:function(t,e){var n=new Yi;return Qc(e,n),ed(t,n,this._defs),n.setStyle({image:t.getAttribute("xlink:href"),x:t.getAttribute("x"),y:t.getAttribute("y"),width:t.getAttribute("width"),height:t.getAttribute("height")}),n},text:function(t,e){var n=t.getAttribute("x")||0,i=t.getAttribute("y")||0,r=t.getAttribute("dx")||0,o=t.getAttribute("dy")||0;this._textX=parseFloat(n)+parseFloat(r),this._textY=parseFloat(i)+parseFloat(o);var a=new Mn;return Qc(e,a),ed(t,a,this._defs),a},tspan:function(t,e){var n=t.getAttribute("x"),i=t.getAttribute("y");null!=n&&(this._textX=parseFloat(n)),null!=i&&(this._textY=parseFloat(i));var r=t.getAttribute("dx")||0,o=t.getAttribute("dy")||0,a=new Mn;return Qc(e,a),ed(t,a,this._defs),this._textX+=r,this._textY+=o,a},path:function(t,e){var n=Ra(t.getAttribute("d")||"");return Qc(e,n),ed(t,n,this._defs),n}},Kc={lineargradient:function(t){var e=parseInt(t.getAttribute("x1")||0,10),n=parseInt(t.getAttribute("y1")||0,10),i=parseInt(t.getAttribute("x2")||10,10),r=parseInt(t.getAttribute("y2")||0,10),o=new ls(e,n,i,r);return function(t,e){var n=t.firstChild;for(;n;){if(1===n.nodeType){var i=n.getAttribute("offset");i=0<i.indexOf("%")?parseInt(i,10)/100:i?parseFloat(i):0;var r=n.getAttribute("stop-color")||"#000000";e.addColorStop(i,r)}n=n.nextSibling}}(t,o),o},radialgradient:function(t){}};function Qc(t,e){t&&t.__inheritedStyle&&(e.__inheritedStyle||(e.__inheritedStyle={}),A(e.__inheritedStyle,t.__inheritedStyle))}function Jc(t){for(var e=Y(t).split(jc),n=[],i=0;i<e.length;i+=2){var r=parseFloat(e[i]),o=parseFloat(e[i+1]);n.push([r,o])}return n}var td={fill:"fill",stroke:"stroke","stroke-width":"lineWidth",opacity:"opacity","fill-opacity":"fillOpacity","stroke-opacity":"strokeOpacity","stroke-dasharray":"lineDash","stroke-dashoffset":"lineDashOffset","stroke-linecap":"lineCap","stroke-linejoin":"lineJoin","stroke-miterlimit":"miterLimit","font-family":"fontFamily","font-size":"fontSize","font-style":"fontStyle","font-weight":"fontWeight","text-align":"textAlign","alignment-baseline":"textBaseline"};function ed(t,e,n,i){var r=e.__inheritedStyle||{},o="text"===e.type;if(1===t.nodeType&&(function(t,e){var n=t.getAttribute("transform");if(n){n=n.replace(/,/g," ");var i=null,r=[];n.replace(rd,function(t,e,n){r.push(e,n)});for(var o=r.length-1;0<o;o-=2){var a=r[o],s=r[o-1];switch(i=i||ee(),s){case"translate":a=Y(a).split(jc),oe(i,i,[parseFloat(a[0]),parseFloat(a[1]||0)]);break;case"scale":a=Y(a).split(jc),se(i,i,[parseFloat(a[0]),parseFloat(a[1]||a[0])]);break;case"rotate":a=Y(a).split(jc),ae(i,i,parseFloat(a[0]));break;case"skew":a=Y(a).split(jc),console.warn("Skew transform is not supported yet");break;case"matrix":a=Y(a).split(jc);i[0]=parseFloat(a[0]),i[1]=parseFloat(a[1]),i[2]=parseFloat(a[2]),i[3]=parseFloat(a[3]),i[4]=parseFloat(a[4]),i[5]=parseFloat(a[5])}}e.setLocalTransform(i)}}(t,e),k(r,function(t){var e=t.getAttribute("style"),n={};if(!e)return n;var i,r={};od.lastIndex=0;for(;null!=(i=od.exec(e));)r[i[1]]=i[2];for(var o in td)td.hasOwnProperty(o)&&null!=r[o]&&(n[td[o]]=r[o]);return n}(t)),!i))for(var a in td)if(td.hasOwnProperty(a)){var s=t.getAttribute(a);null!=s&&(r[td[a]]=s)}var l=o?"textFill":"fill",u=o?"textStroke":"stroke";e.style=e.style||new Fn;var h=e.style;null!=r.fill&&h.set(l,id(r.fill,n)),null!=r.stroke&&h.set(u,id(r.stroke,n)),D(["lineWidth","opacity","fillOpacity","strokeOpacity","miterLimit","fontSize"],function(t){var e="lineWidth"===t&&o?"textStrokeWidth":t;null!=r[t]&&h.set(e,parseFloat(r[t]))}),r.textBaseline&&"auto"!==r.textBaseline||(r.textBaseline="alphabetic"),"alphabetic"===r.textBaseline&&(r.textBaseline="bottom"),"start"===r.textAlign&&(r.textAlign="left"),"end"===r.textAlign&&(r.textAlign="right"),D(["lineDashOffset","lineCap","lineJoin","fontWeight","fontFamily","fontStyle","textAlign","textBaseline"],function(t){null!=r[t]&&h.set(t,r[t])}),r.lineDash&&(e.style.lineDash=Y(r.lineDash).split(jc)),h[u]&&"none"!==h[u]&&(e[u]=!0),e.__inheritedStyle=r}var nd=/url\(\s*#(.*?)\)/;function id(t,e){var n=e&&t&&t.match(nd);return n?e[Y(n[1])]:t}var rd=/(translate|scale|rotate|skewX|skewY|matrix)\(([\-\s0-9\.e,]*)\)/g;var od=/([^\s:;]+)\s*:\s*([^:;]+)/g;var ad=Q(),sd=function(t,e,n){var i;return D(i=O(e)?e:e.svg?[{type:"svg",source:e.svg,specialAreas:e.specialAreas}]:(e.geoJson&&!e.features&&(n=e.specialAreas,e=e.geoJson),[{type:"geoJSON",source:e,specialAreas:n}]),function(t){var e=t.type;"geoJson"===e&&(e=t.type="geoJSON"),(0,ud[e])(t)}),ad.set(t,i)},ld=function(t){return ad.get(t)},ud={geoJSON:function(t){var e=t.source;t.geoJSON=z(e)?"undefined"!=typeof JSON&&JSON.parse?JSON.parse(e):new Function("return ("+e+");")():e},svg:function(t){t.svgXML=qc(t.source)}},hd=X,cd=D,dd=E,fd=N,pd=vu.parseClassType,gd={PROCESSOR:{FILTER:1e3,SERIES_FILTER:800,STATISTIC:5e3},VISUAL:{LAYOUT:1e3,PROGRESSIVE_LAYOUT:1100,GLOBAL:2e3,CHART:3e3,POST_CHART_LAYOUT:3500,COMPONENT:4e3,BRUSH:5e3}},md="__flagInMainProcess",vd="__optionUpdated",yd=/^[a-zA-Z0-9_]+$/;function _d(i,r){return function(t,e,n){!r&&this._disposed||(t=t&&t.toLowerCase(),It.prototype[i].call(this,t,e,n))}}function xd(){It.call(this)}function wd(t,e,n){n=n||{},"string"==typeof e&&(e=Ud[e]),this.id,this.group,this._dom=t;var i=this._zr=Mr(t,{renderer:n.renderer||"canvas",devicePixelRatio:n.devicePixelRatio,width:n.width,height:n.height});this._throttledZrFlush=mc(C(i.flush,i),17),(e=b(e))&&mh(e,!0),this._theme=e,this._chartsViews=[],this._chartsMap={},this._componentsViews=[],this._componentsMap={},this._coordSysMgr=new ju;var r=this._api=function(n){var t=n._coordSysMgr;return k(new Xu(n),{getCoordinateSystems:C(t.getCoordinateSystems,t),getComponentByElement:function(t){for(;t;){var e=t.__ecComponentInfo;if(null!=e)return n._model.getComponent(e.mainType,e.index);t=t.parent}}})}(this);function o(t,e){return t.__prio-e.__prio}Ln(Zd,o),Ln(Hd,o),this._scheduler=new Sc(this,r,Hd,Zd),It.call(this,this._ecEventProcessor=new Bd),this._messageCenter=new xd,this._initEvents(),this.resize=C(this.resize,this),this._pendingActions=[],i.animation.on("frame",this._onframe,this),function(t,e){t.on("rendered",function(){e.trigger("rendered"),!t.animation.isFinished()||e[vd]||e._scheduler.unfinished||e._pendingActions.length||e.trigger("finished")})}(i,this),q(this)}xd.prototype.on=_d("on",!0),xd.prototype.off=_d("off",!0),xd.prototype.one=_d("one",!0),S(xd,It);var bd=wd.prototype;function Sd(t,e,n){if(!this._disposed){var i,r=this._model,o=this._coordSysMgr.getCoordinateSystems();e=Gr(r,e);for(var a=0;a<o.length;a++){var s=o[a];if(s[t]&&null!=(i=s[t](r,e,n)))return i}}}bd._onframe=function(){if(!this._disposed){var t=this._scheduler;if(this[vd]){var e=this[vd].silent;this[md]=!0,Id(this),Md.update.call(this),this[md]=!1,this[vd]=!1,Dd.call(this,e),kd.call(this,e)}else if(t.unfinished){var n=1,i=this._model,r=this._api;t.unfinished=!1;do{var o=+new Date;t.performSeriesTasks(i),t.performDataProcessorTasks(i),Td(this,i),t.performVisualTasks(i),Ed(this,this._model,r,"remain"),n-=+new Date-o}while(0<n&&t.unfinished);t.unfinished||this._zr.flush()}}},bd.getDom=function(){return this._dom},bd.getZr=function(){return this._zr},bd.setOption=function(t,e,n){if(!this._disposed){var i;if(fd(e)&&(n=e.lazyUpdate,i=e.silent,e=e.notMerge),this[md]=!0,!this._model||e){var r=new th(this._api),o=this._theme,a=this._model=new Wu;a.scheduler=this._scheduler,a.init(null,null,o,r)}this._model.setOption(t,Wd),n?(this[vd]={silent:i},this[md]=!1):(Id(this),Md.update.call(this),this._zr.flush(),this[vd]=!1,this[md]=!1,Dd.call(this,i),kd.call(this,i))}},bd.setTheme=function(){console.error("ECharts#setTheme() is DEPRECATED in ECharts 3.0")},bd.getModel=function(){return this._model},bd.getOption=function(){return this._model&&this._model.getOption()},bd.getWidth=function(){return this._zr.getWidth()},bd.getHeight=function(){return this._zr.getHeight()},bd.getDevicePixelRatio=function(){return this._zr.painter.dpr||window.devicePixelRatio||1},bd.getRenderedCanvas=function(t){if(v.canvasSupported)return(t=t||{}).pixelRatio=t.pixelRatio||1,t.backgroundColor=t.backgroundColor||this._model.get("backgroundColor"),this._zr.painter.getRenderedCanvas(t)},bd.getSvgDataURL=function(){if(v.svgSupported){var t=this._zr;return D(t.storage.getDisplayList(),function(t){t.stopAnimation(!0)}),t.painter.toDataURL()}},bd.getDataURL=function(t){if(!this._disposed){var e=(t=t||{}).excludeComponents,n=this._model,i=[],r=this;cd(e,function(t){n.eachComponent({mainType:t},function(t){var e=r._componentsMap[t.__viewId];e.group.ignore||(i.push(e),e.group.ignore=!0)})});var o="svg"===this._zr.painter.getType()?this.getSvgDataURL():this.getRenderedCanvas(t).toDataURL("image/"+(t&&t.type||"png"));return cd(i,function(t){t.group.ignore=!1}),o}},bd.getConnectedDataURL=function(r){if(!this._disposed&&v.canvasSupported){var o="svg"===r.type,a=this.group,s=Math.min,l=Math.max;if(jd[a]){var u=1/0,h=1/0,c=-1/0,d=-1/0,f=[],n=r&&r.pixelRatio||1;D(Yd,function(t,e){if(t.group===a){var n=o?t.getZr().painter.getSvgDom().innerHTML:t.getRenderedCanvas(b(r)),i=t.getDom().getBoundingClientRect();u=s(i.left,u),h=s(i.top,h),c=l(i.right,c),d=l(i.bottom,d),f.push({dom:n,left:i.left,top:i.top})}});var t=(c*=n)-(u*=n),e=(d*=n)-(h*=n),i=y(),p=Mr(i,{renderer:o?"svg":"canvas"});if(p.resize({width:t,height:e}),o){var g="";return cd(f,function(t){var e=t.left-u,n=t.top-h;g+='<g transform="translate('+e+","+n+')">'+t.dom+"</g>"}),p.painter.getSvgRoot().innerHTML=g,r.connectedBackgroundColor&&p.painter.setBackgroundColor(r.connectedBackgroundColor),p.refreshImmediately(),p.painter.toDataURL()}return r.connectedBackgroundColor&&p.add(new Ja({shape:{x:0,y:0,width:t,height:e},style:{fill:r.connectedBackgroundColor}})),cd(f,function(t){var e=new Yi({style:{x:t.left*n-u,y:t.top*n-h,image:t.dom}});p.add(e)}),p.refreshImmediately(),i.toDataURL("image/"+(r&&r.type||"png"))}return this.getDataURL(r)}},bd.convertToPixel=T(Sd,"convertToPixel"),bd.convertFromPixel=T(Sd,"convertFromPixel"),bd.containPixel=function(t,r){var o;if(!this._disposed)return D(t=Gr(this._model,t),function(t,i){0<=i.indexOf("Models")&&D(t,function(t){var e=t.coordinateSystem;if(e&&e.containPoint)o|=!!e.containPoint(r);else if("seriesModels"===i){var n=this._chartsMap[t.__viewId];n&&n.containPoint&&(o|=n.containPoint(r,t))}},this)},this),!!o},bd.getVisual=function(t,e){var n=(t=Gr(this._model,t,{defaultMainType:"series"})).seriesModel.getData(),i=t.hasOwnProperty("dataIndexInside")?t.dataIndexInside:t.hasOwnProperty("dataIndex")?n.indexOfRawIndex(t.dataIndex):null;return null!=i?n.getItemVisual(i,e):n.getVisual(e)},bd.getViewOfComponentModel=function(t){return this._componentsMap[t.__viewId]},bd.getViewOfSeriesModel=function(t){return this._chartsMap[t.__viewId]};var Md={prepareAndUpdate:function(t){Id(this),Md.update.call(this,t)},update:function(t){var e=this._model,n=this._api,i=this._zr,r=this._coordSysMgr,o=this._scheduler;if(e){o.restoreData(e,t),o.performSeriesTasks(e),r.create(e,n),o.performDataProcessorTasks(e,t),Td(this,e),r.update(e,n),Ld(e),o.performVisualTasks(e,t),Od(this,e,n,t);var a=e.get("backgroundColor")||"transparent";if(v.canvasSupported)i.setBackgroundColor(a);else{var s=Ne(a);a=Ue(s,"rgb"),0===s[3]&&(a="transparent")}zd(e,n)}},updateTransform:function(r){var o=this._model,a=this,s=this._api;if(o){var l=[];o.eachComponent(function(t,e){var n=a.getViewOfComponentModel(e);if(n&&n.__alive)if(n.updateTransform){var i=n.updateTransform(e,o,s,r);i&&i.update&&l.push(n)}else l.push(n)});var i=Q();o.eachSeries(function(t){var e=a._chartsMap[t.__viewId];if(e.updateTransform){var n=e.updateTransform(t,o,s,r);n&&n.update&&i.set(t.uid,1)}else i.set(t.uid,1)}),Ld(o),this._scheduler.performVisualTasks(o,r,{setDirty:!0,dirtyMap:i}),Ed(a,o,s,r,i),zd(o,this._api)}},updateView:function(t){var e=this._model;e&&(ac.markUpdateMethod(t,"updateView"),Ld(e),this._scheduler.performVisualTasks(e,t,{setDirty:!0}),Od(this,this._model,this._api,t),zd(e,this._api))},updateVisual:function(t){Md.update.call(this,t)},updateLayout:function(t){Md.update.call(this,t)}};function Id(t){var e=t._model,n=t._scheduler;n.restorePipelines(e),n.prepareStageTasks(),Pd(t,"component",e,n),Pd(t,"chart",e,n),n.plan()}function Cd(e,n,i,r,t){var o=e._model;if(r){var a={};a[r+"Id"]=i[r+"Id"],a[r+"Index"]=i[r+"Index"],a[r+"Name"]=i[r+"Name"];var s={mainType:r,query:a};t&&(s.subType=t);var l=i.excludeSeriesId;null!=l&&(l=Q(Lr(l))),o&&o.eachComponent(s,function(t){l&&null!=l.get(t.id)||u(e["series"===r?"_chartsMap":"_componentsMap"][t.__viewId])},e)}else cd(e._componentsViews.concat(e._chartsViews),u);function u(t){t&&t.__alive&&t[n]&&t[n](t.__model,o,e._api,i)}}function Td(t,e){var n=t._chartsMap,i=t._scheduler;e.eachSeries(function(t){i.updateStreamModes(t,n[t.__viewId])})}function Ad(e,t){var n=e.type,i=e.escapeConnect,r=Vd[n],o=r.actionInfo,a=(o.update||"update").split(":"),s=a.pop();a=null!=a[0]&&pd(a[0]),this[md]=!0;var l=[e],u=!1;e.batch&&(u=!0,l=P(e.batch,function(t){return(t=A(k({},t),e)).batch=null,t}));var h,c=[],d="highlight"===n||"downplay"===n;cd(l,function(t){(h=(h=r.action(t,this._model,this._api))||k({},t)).type=o.event||h.type,c.push(h),d?Cd(this,s,t,"series"):a&&Cd(this,s,t,a.main,a.sub)},this),"none"===s||d||a||(this[vd]?(Id(this),Md.update.call(this,e),this[vd]=!1):Md[s].call(this,e)),h=u?{type:o.event||n,escapeConnect:i,batch:c}:c[0],this[md]=!1,t||this._messageCenter.trigger(h.type,h)}function Dd(t){for(var e=this._pendingActions;e.length;){var n=e.shift();Ad.call(this,n,t)}}function kd(t){t||this.trigger("updated")}function Pd(t,e,r,o){for(var a="component"===e,s=a?t._componentsViews:t._chartsViews,l=a?t._componentsMap:t._chartsMap,u=t._zr,h=t._api,n=0;n<s.length;n++)s[n].__alive=!1;function i(t){var e="_ec_"+t.id+"_"+t.type,n=l[e];if(!n){var i=pd(t.type);(n=new(a?ec.getClass(i.main,i.sub):ac.getClass(i.sub))).init(r,h),l[e]=n,s.push(n),u.add(n.group)}t.__viewId=n.__id=e,n.__alive=!0,n.__model=t,n.group.__ecComponentInfo={mainType:t.mainType,index:t.componentIndex},a||o.prepareView(n,t,r,h)}a?r.eachComponent(function(t,e){"series"!==t&&i(e)}):r.eachSeries(i);for(n=0;n<s.length;){var c=s[n];c.__alive?n++:(a||c.renderTask.dispose(),u.remove(c.group),c.dispose(r,h),s.splice(n,1),delete l[c.__id],c.__id=c.group.__ecComponentInfo=null)}}function Ld(t){t.clearColorPalette(),t.eachSeries(function(t){t.clearColorPalette()})}function Od(t,e,n,i){!function(t,n,i,r,e){cd(e||t._componentsViews,function(t){var e=t.__model;t.render(e,n,i,r),Rd(e,t)})}(t,e,n,i),cd(t._chartsViews,function(t){t.__alive=!1}),Ed(t,e,n,i),cd(t._chartsViews,function(t){t.__alive||t.remove(e,n)})}function Ed(i,t,e,r,o){var a,s=i._scheduler;t.eachSeries(function(t){var e=i._chartsMap[t.__viewId];e.__alive=!0;var n=e.renderTask;s.updatePayload(n,r),o&&o.get(t.uid)&&n.dirty(),a|=n.perform(s.getPerformArgs(n)),e.group.silent=!!t.get("silent"),Rd(t,e),function(t,e){var n=t.get("blendMode")||null;e.group.traverse(function(t){t.isGroup||t.style.blend!==n&&t.setStyle("blend",n),t.eachPendingDisplayable&&t.eachPendingDisplayable(function(t){t.setStyle("blend",n)})})}(t,e)}),s.unfinished|=a,function(n,t){var e=n._zr.storage,i=0;e.traverse(function(t){i++}),i>t.get("hoverLayerThreshold")&&!v.node&&t.eachSeries(function(t){if(!t.preventUsingHoverLayer){var e=n._chartsMap[t.__viewId];e.__alive&&e.group.traverse(function(t){t.useHoverLayer=!0})}})}(i,t),wc(i._zr.dom,t)}function zd(e,n){cd(Gd,function(t){t(e,n)})}bd.resize=function(t){if(!this._disposed){this._zr.resize(t);var e=this._model;if(this._loadingFX&&this._loadingFX.resize(),e){var n=e.resetOption("media"),i=t&&t.silent;this[md]=!0,n&&Id(this),Md.update.call(this),this[md]=!1,Dd.call(this,i),kd.call(this,i)}}},bd.showLoading=function(t,e){if(!this._disposed&&(fd(t)&&(e=t,t=""),t=t||"default",this.hideLoading(),Xd[t])){var n=Xd[t](this._api,e),i=this._zr;this._loadingFX=n,i.add(n)}},bd.hideLoading=function(){this._disposed||(this._loadingFX&&this._zr.remove(this._loadingFX),this._loadingFX=null)},bd.makeActionFromEvent=function(t){var e=k({},t);return e.type=Fd[t.type],e},bd.dispatchAction=function(t,e){this._disposed||(fd(e)||(e={silent:!!e}),Vd[t.type]&&this._model&&(this[md]?this._pendingActions.push(t):(Ad.call(this,t,e.silent),e.flush?this._zr.flush(!0):!1!==e.flush&&v.browser.weChat&&this._throttledZrFlush(),Dd.call(this,e.silent),kd.call(this,e.silent))))},bd.appendData=function(t){if(!this._disposed){var e=t.seriesIndex;this.getModel().getSeriesByIndex(e).appendData(t),this._scheduler.unfinished=!0}},bd.on=_d("on",!1),bd.off=_d("off",!1),bd.one=_d("one",!1);var Nd=["click","dblclick","mouseover","mouseout","mousemove","mousedown","mouseup","globalout","contextmenu"];function Rd(t,e){var n=t.get("z"),i=t.get("zlevel");e.group.traverse(function(t){"group"!==t.type&&(null!=n&&(t.z=n),null!=i&&(t.zlevel=i))})}function Bd(){this.eventInfo}bd._initEvents=function(){cd(Nd,function(u){function t(t){var e,n=this.getModel(),i=t.target;if("globalout"===u)e={};else if(i&&null!=i.dataIndex){var r=i.dataModel||n.getSeriesByIndex(i.seriesIndex);e=r&&r.getDataParams(i.dataIndex,i.dataType,i)||{}}else i&&i.eventData&&(e=k({},i.eventData));if(e){var o=e.componentType,a=e.componentIndex;"markLine"!==o&&"markPoint"!==o&&"markArea"!==o||(o="series",a=e.seriesIndex);var s=o&&null!=a&&n.getComponent(o,a),l=s&&this["series"===s.mainType?"_chartsMap":"_componentsMap"][s.__viewId];e.event=t,e.type=u,this._ecEventProcessor.eventInfo={targetEl:i,packedEvent:e,model:s,view:l},this.trigger(u,e)}}t.zrEventfulCallAtLast=!0,this._zr.on(u,t,this)},this),cd(Fd,function(t,e){this._messageCenter.on(e,function(t){this.trigger(e,t)},this)},this)},bd.isDisposed=function(){return this._disposed},bd.clear=function(){this._disposed||this.setOption({series:[]},!0)},bd.dispose=function(){if(!this._disposed){this._disposed=!0,Ur(this.getDom(),Kd,"");var e=this._api,n=this._model;cd(this._componentsViews,function(t){t.dispose(n,e)}),cd(this._chartsViews,function(t){t.dispose(n,e)}),this._zr.dispose(),delete Yd[this.id]}},S(wd,It),Bd.prototype={constructor:Bd,normalizeQuery:function(t){var s={},l={},u={};if(z(t)){var e=pd(t);s.mainType=e.main||null,s.subType=e.sub||null}else{var h=["Index","Name","Id"],c={name:1,dataIndex:1,dataType:1};D(t,function(t,e){for(var n=!1,i=0;i<h.length;i++){var r=h[i],o=e.lastIndexOf(r);if(0<o&&o===e.length-r.length){var a=e.slice(0,o);"data"!==a&&(s.mainType=a,s[r.toLowerCase()]=t,n=!0)}}c.hasOwnProperty(e)&&(l[e]=t,n=!0),n||(u[e]=t)})}return{cptQuery:s,dataQuery:l,otherQuery:u}},filter:function(t,e,n){var i=this.eventInfo;if(!i)return!0;var r=i.targetEl,o=i.packedEvent,a=i.model,s=i.view;if(!a||!s)return!0;var l=e.cptQuery,u=e.dataQuery;return h(l,a,"mainType")&&h(l,a,"subType")&&h(l,a,"index","componentIndex")&&h(l,a,"name")&&h(l,a,"id")&&h(u,o,"name")&&h(u,o,"dataIndex")&&h(u,o,"dataType")&&(!s.filterForExposedEvent||s.filterForExposedEvent(t,e.otherQuery,r,o));function h(t,e,n,i){return null==t[n]||e[i||n]===t[n]}},afterTrigger:function(){this.eventInfo=null}};var Vd={},Fd={},Hd=[],Wd=[],Gd=[],Zd=[],Ud={},Xd={},Yd={},jd={},qd=new Date-0,$d=new Date-0,Kd="_echarts_instance_";function Qd(t){jd[t]=!1}var Jd=Qd;function tf(t){return Yd[function(t,e){return t.getAttribute?t.getAttribute(e):t[e]}(t,Kd)]}function ef(t,e){Ud[t]=e}function nf(t){Wd.push(t)}function rf(t,e){lf(Hd,t,e,1e3)}function of(t,e,n){"function"==typeof e&&(n=e,e="");var i=fd(t)?t.type:[t,t={event:e}][0];t.event=(t.event||i).toLowerCase(),e=t.event,hd(yd.test(i)&&yd.test(e)),Vd[i]||(Vd[i]={action:n,actionInfo:t}),Fd[e]=i}function af(t,e){lf(Zd,t,e,1e3,"layout")}function sf(t,e){lf(Zd,t,e,3e3,"visual")}function lf(t,e,n,i,r){(dd(e)||fd(e))&&(n=e,e=i);var o=Sc.wrapStageHandler(n,r);return o.__prio=e,o.__raw=n,t.push(o),o}function uf(t,e){Xd[t]=e}function hf(t){return vu.extend(t)}function cf(t){return ec.extend(t)}function df(t){return Yh.extend(t)}function ff(t){return ac.extend(t)}sf(2e3,_c),nf(mh),rf(900,function(t){var o=Q();t.eachSeries(function(t){var e=t.get("stack");if(e){var n=o.get(e)||o.set(e,[]),i=t.getData(),r={stackResultDimension:i.getCalculationInfo("stackResultDimension"),stackedOverDimension:i.getCalculationInfo("stackedOverDimension"),stackedDimension:i.getCalculationInfo("stackedDimension"),stackedByDimension:i.getCalculationInfo("stackedByDimension"),isStackedByIndex:i.getCalculationInfo("isStackedByIndex"),data:i,seriesModel:t};if(!r.stackedDimension||!r.isStackedByIndex&&!r.stackedByDimension)return;n.length&&i.setCalculationInfo("stackedOnSeries",n[n.length-1].seriesModel),n.push(r)}}),o.each(vh)}),uf("default",function(r,o){A(o=o||{},{text:"loading",textColor:"#000",fontSize:"12px",maskColor:"rgba(255, 255, 255, 0.8)",showSpinner:!0,color:"#c23531",spinnerRadius:10,lineWidth:5,zlevel:0});var t=new Mn,a=new Ja({style:{fill:o.maskColor},zlevel:o.zlevel,z:1e4});t.add(a);var s=o.fontSize+" sans-serif",l=new Ja({style:{fill:"none",text:o.text,font:s,textPosition:"right",textDistance:10,textFill:o.textColor},zlevel:o.zlevel,z:10001});if(t.add(l),o.showSpinner){var u=new as({shape:{startAngle:-bc/2,endAngle:-bc/2+.1,r:o.spinnerRadius},style:{stroke:o.color,lineCap:"round",lineWidth:o.lineWidth},zlevel:o.zlevel,z:10001});u.animateShape(!0).when(1e3,{endAngle:3*bc/2}).start("circularInOut"),u.animateShape(!0).when(1e3,{startAngle:3*bc/2}).delay(300).start("circularInOut"),t.add(u)}return t.resize=function(){var t=li(o.text,s),e=o.showSpinner?o.spinnerRadius:0,n=(r.getWidth()-2*e-(o.showSpinner&&t?10:0)-t)/2-(o.showSpinner?0:t/2),i=r.getHeight()/2;o.showSpinner&&u.setShape({cx:n,cy:i}),l.setShape({x:n-e,y:i-e,width:2*e,height:2*e}),a.setShape({x:0,y:0,width:r.getWidth(),height:r.getHeight()})},t.resize(),t}),of({type:"highlight",event:"highlight",update:"highlight"},J),of({type:"downplay",event:"downplay",update:"downplay"},J),ef("light",Gc),ef("dark",Xc);function pf(t){return t}function gf(t,e,n,i,r){this._old=t,this._new=e,this._oldKeyGetter=n||pf,this._newKeyGetter=i||pf,this.context=r}function mf(t,e,n,i,r){for(var o=0;o<t.length;o++){var a="_ec_"+r[i](t[o],o),s=e[a];null==s?(n.push(a),e[a]=o):(s.length||(e[a]=s=[s]),s.push(o))}}gf.prototype={constructor:gf,add:function(t){return this._add=t,this},update:function(t){return this._update=t,this},remove:function(t){return this._remove=t,this},execute:function(){var t=this._old,e=this._new,n={},i=[],r=[];for(mf(t,{},i,"_oldKeyGetter",this),mf(e,n,r,"_newKeyGetter",this),o=0;o<t.length;o++){if(null!=(s=n[a=i[o]]))(u=s.length)?(1===u&&(n[a]=null),s=s.shift()):n[a]=null,this._update&&this._update(s,o);else this._remove&&this._remove(o)}for(var o=0;o<r.length;o++){var a=r[o];if(n.hasOwnProperty(a)){var s;if(null==(s=n[a]))continue;if(s.length)for(var l=0,u=s.length;l<u;l++)this._add&&this._add(s[l]);else this._add&&this._add(s)}}}};var vf=Q(["tooltip","label","itemName","itemId","seriesName"]);function yf(t,e){return t.hasOwnProperty(e)||(t[e]=[]),t[e]}function _f(t){null!=t&&k(this,t),this.otherDims={}}var xf=N,wf="undefined",bf={float:typeof Float64Array==wf?Array:Float64Array,int:typeof Int32Array==wf?Array:Int32Array,ordinal:Array,number:Array,time:Array},Sf=typeof Uint32Array==wf?Array:Uint32Array,Mf=typeof Int32Array==wf?Array:Int32Array,If=typeof Uint16Array==wf?Array:Uint16Array;function Cf(t){return 65535<t._rawCount?Sf:If}var Tf=["hasItemOption","_nameList","_idList","_invertedIndicesMap","_rawData","_chunkSize","_chunkCount","_dimValueGetter","_count","_rawCount","_nameDimIdx","_idDimIdx"],Af=["_extent","_approximateExtent","_rawExtent"];function Df(e,n){D(Tf.concat(n.__wrappedMethods||[]),function(t){n.hasOwnProperty(t)&&(e[t]=n[t])}),e.__wrappedMethods=n.__wrappedMethods,D(Af,function(t){e[t]=b(n[t])}),e._calculationInfo=k(n._calculationInfo)}var kf=function(t,e){t=t||["x","y"];for(var n={},i=[],r={},o=0;o<t.length;o++){var a=t[o];z(a)?a=new _f({name:a}):a instanceof _f||(a=new _f(a));var s=a.name;a.type=a.type||"float",a.coordDim||(a.coordDim=s,a.coordDimIndex=0),a.otherDims=a.otherDims||{},i.push(s),(n[s]=a).index=o,a.createInvertedIndices&&(r[s]=[])}this.dimensions=i,this._dimensionInfos=n,this.hostModel=e,this.dataType,this._indices=null,this._count=0,this._rawCount=0,this._storage={},this._nameList=[],this._idList=[],this._optionModels=[],this._visual={},this._layout={},this._itemVisuals=[],this.hasItemVisual={},this._itemLayouts=[],this._graphicEls=[],this._chunkSize=1e5,this._chunkCount=0,this._rawData,this._rawExtent={},this._extent={},this._approximateExtent={},this._dimensionsSummary=function(i){var t={},o=t.encode={},a=Q(),s=[],l=[],u=t.userOutput={dimensionNames:i.dimensions.slice(),encode:{}};D(i.dimensions,function(t){var r=i.getDimensionInfo(t),e=r.coordDim;if(e){var n=r.coordDimIndex;yf(o,e)[n]=t,r.isExtraCoord||(a.set(e,1),function(t){return!("ordinal"===t||"time"===t)}(r.type)&&(s[0]=t),yf(u.encode,e)[n]=r.index),r.defaultTooltip&&l.push(t)}vf.each(function(t,e){var n=yf(o,e),i=r.otherDims[e];null!=i&&!1!==i&&(n[i]=r.name)})});var r=[],h={};a.each(function(t,e){var n=o[e];h[e]=n[0],r=r.concat(n)}),t.dataDimsOnCoord=r,t.encodeFirstDimNotExtra=h;var e=o.label;e&&e.length&&(s=e.slice());var n=o.tooltip;return n&&n.length?l=n.slice():l.length||(l=s.slice()),o.defaultedLabel=s,o.defaultedTooltip=l,t}(this),this._invertedIndicesMap=r,this._calculationInfo={},this.userOutput=this._dimensionsSummary.userOutput},Pf=kf.prototype;function Lf(t,e,n,i,r){var o=bf[e.type],a=i-1,s=e.name,l=t[s][a];if(l&&l.length<n){for(var u=new o(Math.min(r-a*n,n)),h=0;h<l.length;h++)u[h]=l[h];t[s][a]=u}for(var c=i*n;c<r;c+=n)t[s].push(new o(Math.min(r-c,n)))}function Of(r){var o=r._invertedIndicesMap;D(o,function(t,e){var n=r._dimensionInfos[e].ordinalMeta;if(n){t=o[e]=new Mf(n.categories.length);for(var i=0;i<t.length;i++)t[i]=-1;for(i=0;i<r._count;i++)t[r.get(e,i)]=i}})}function Ef(t,e,n){var i;if(null!=e){var r=t._chunkSize,o=Math.floor(n/r),a=n%r,s=t.dimensions[e],l=t._storage[s][o];if(l){i=l[a];var u=t._dimensionInfos[s].ordinalMeta;u&&u.categories.length&&(i=u.categories[i])}}return i}function zf(t){return t}function Nf(t){return t<this._count&&0<=t?this._indices[t]:-1}function Rf(t,e){var n=t._idList[e];return null==n&&(n=Ef(t,t._idDimIdx,e)),null==n&&(n="e\0\0"+e),n}function Bf(t){return O(t)||(t=[t]),t}function Vf(t,e){var n=t.dimensions,i=new kf(P(n,t.getDimensionInfo,t),t.hostModel);Df(i,t);for(var r=i._storage={},o=t._storage,a=0;a<n.length;a++){var s=n[a];o[s]&&(0<=x(e,s)?(r[s]=Ff(o[s]),i._rawExtent[s]=Hf(),i._extent[s]=null):r[s]=o[s])}return i}function Ff(t){for(var e,n,i=new Array(t.length),r=0;r<t.length;r++)i[r]=(e=t[r],n=void 0,(n=e.constructor)===Array?e.slice():new n(e));return i}function Hf(){return[1/0,-1/0]}Pf.type="list",Pf.hasItemOption=!0,Pf.getDimension=function(t){return"number"!=typeof t&&(isNaN(t)||this._dimensionInfos.hasOwnProperty(t))||(t=this.dimensions[t]),t},Pf.getDimensionInfo=function(t){return this._dimensionInfos[this.getDimension(t)]},Pf.getDimensionsOnCoord=function(){return this._dimensionsSummary.dataDimsOnCoord.slice()},Pf.mapDimension=function(t,e){var n=this._dimensionsSummary;if(null==e)return n.encodeFirstDimNotExtra[t];var i=n.encode[t];return!0===e?(i||[]).slice():i&&i[e]},Pf.initData=function(t,e,n){(Pu.isInstance(t)||L(t))&&(t=new yh(t,this.dimensions.length)),this._rawData=t,this._storage={},this._indices=null,this._nameList=e||[],this._idList=[],this._nameRepeatCount={},n||(this.hasItemOption=!1),this.defaultDimValueGetter=Ch[this._rawData.getSource().sourceFormat],this._dimValueGetter=n=n||this.defaultDimValueGetter,this._dimValueGetterArrayRows=Ch.arrayRows,this._rawExtent={},this._initDataFromProvider(0,t.count()),t.pure&&(this.hasItemOption=!1)},Pf.getProvider=function(){return this._rawData},Pf.appendData=function(t){var e=this._rawData,n=this.count();e.appendData(t);var i=e.count();e.persistent||(i+=n),this._initDataFromProvider(n,i)},Pf.appendValues=function(t,e){for(var n=this._chunkSize,i=this._storage,r=this.dimensions,o=r.length,a=this._rawExtent,s=this.count(),l=s+Math.max(t.length,e?e.length:0),u=this._chunkCount,h=0;h<o;h++){a[v=r[h]]||(a[v]=Hf()),i[v]||(i[v]=[]),Lf(i,this._dimensionInfos[v],n,u,l),this._chunkCount=i[v].length}for(var c=new Array(o),d=s;d<l;d++){for(var f=d-s,p=Math.floor(d/n),g=d%n,m=0;m<o;m++){var v=r[m],y=this._dimValueGetterArrayRows(t[f]||c,v,f,m);i[v][p][g]=y;var _=a[v];y<_[0]&&(_[0]=y),y>_[1]&&(_[1]=y)}e&&(this._nameList[d]=e[f])}this._rawCount=this._count=l,this._extent={},Of(this)},Pf._initDataFromProvider=function(t,e){if(!(e<=t)){for(var n,i=this._chunkSize,r=this._rawData,o=this._storage,a=this.dimensions,s=a.length,l=this._dimensionInfos,u=this._nameList,h=this._idList,c=this._rawExtent,d=this._nameRepeatCount={},f=this._chunkCount,p=0;p<s;p++){c[w=a[p]]||(c[w]=Hf());var g=l[w];0===g.otherDims.itemName&&(n=this._nameDimIdx=p),0===g.otherDims.itemId&&(this._idDimIdx=p),o[w]||(o[w]=[]),Lf(o,g,i,f,e),this._chunkCount=o[w].length}for(var m=new Array(s),v=t;v<e;v++){m=r.getItem(v,m);for(var y=Math.floor(v/i),_=v%i,x=0;x<s;x++){var w,b=o[w=a[x]][y],S=this._dimValueGetter(m,w,v,x);b[_]=S;var M=c[w];S<M[0]&&(M[0]=S),S>M[1]&&(M[1]=S)}if(!r.pure){var I=u[v];if(m&&null==I)if(null!=m.name)u[v]=I=m.name;else if(null!=n){var C=a[n],T=o[C][y];if(T){I=T[_];var A=l[C].ordinalMeta;A&&A.categories.length&&(I=A.categories[I])}}var D=null==m?null:m.id;null==D&&null!=I&&(d[I]=d[I]||0,0<d[D=I]&&(D+="__ec__"+d[I]),d[I]++),null!=D&&(h[v]=D)}}!r.persistent&&r.clean&&r.clean(),this._rawCount=this._count=e,this._extent={},Of(this)}},Pf.count=function(){return this._count},Pf.getIndices=function(){var t=this._indices;if(t){var e=t.constructor,n=this._count;if(e===Array){r=new e(n);for(var i=0;i<n;i++)r[i]=t[i]}else r=new e(t.buffer,0,n)}else{var r=new(e=Cf(this))(this.count());for(i=0;i<r.length;i++)r[i]=i}return r},Pf.get=function(t,e){if(!(0<=e&&e<this._count))return NaN;var n=this._storage;if(!n[t])return NaN;e=this.getRawIndex(e);var i=Math.floor(e/this._chunkSize),r=e%this._chunkSize;return n[t][i][r]},Pf.getByRawIndex=function(t,e){if(!(0<=e&&e<this._rawCount))return NaN;var n=this._storage[t];if(!n)return NaN;var i=Math.floor(e/this._chunkSize),r=e%this._chunkSize;return n[i][r]},Pf._getFast=function(t,e){var n=Math.floor(e/this._chunkSize),i=e%this._chunkSize;return this._storage[t][n][i]},Pf.getValues=function(t,e){var n=[];O(t)||(e=t,t=this.dimensions);for(var i=0,r=t.length;i<r;i++)n.push(this.get(t[i],e));return n},Pf.hasValue=function(t){for(var e=this._dimensionsSummary.dataDimsOnCoord,n=0,i=e.length;n<i;n++)if(isNaN(this.get(e[n],t)))return!1;return!0},Pf.getDataExtent=function(t){t=this.getDimension(t);var e=this._storage[t],n=Hf();if(!e)return n;var i,r=this.count();if(!this._indices)return this._rawExtent[t].slice();if(i=this._extent[t])return i.slice();for(var o=(i=n)[0],a=i[1],s=0;s<r;s++){var l=this._getFast(t,this.getRawIndex(s));l<o&&(o=l),a<l&&(a=l)}return i=[o,a],this._extent[t]=i},Pf.getApproximateExtent=function(t){return t=this.getDimension(t),this._approximateExtent[t]||this.getDataExtent(t)},Pf.setApproximateExtent=function(t,e){e=this.getDimension(e),this._approximateExtent[e]=t.slice()},Pf.getCalculationInfo=function(t){return this._calculationInfo[t]},Pf.setCalculationInfo=function(t,e){xf(t)?k(this._calculationInfo,t):this._calculationInfo[t]=e},Pf.getSum=function(t){var e=0;if(this._storage[t])for(var n=0,i=this.count();n<i;n++){var r=this.get(t,n);isNaN(r)||(e+=r)}return e},Pf.getMedian=function(t){var n=[];this.each(t,function(t,e){isNaN(t)||n.push(t)});var e=[].concat(n).sort(function(t,e){return t-e}),i=this.count();return 0===i?0:i%2==1?e[(i-1)/2]:(e[i/2]+e[i/2-1])/2},Pf.rawIndexOf=function(t,e){var n=(t&&this._invertedIndicesMap[t])[e];return null==n||isNaN(n)?-1:n},Pf.indexOfName=function(t){for(var e=0,n=this.count();e<n;e++)if(this.getName(e)===t)return e;return-1},Pf.indexOfRawIndex=function(t){if(t>=this._rawCount||t<0)return-1;if(!this._indices)return t;var e=this._indices,n=e[t];if(null!=n&&n<this._count&&n===t)return t;for(var i=0,r=this._count-1;i<=r;){var o=(i+r)/2|0;if(e[o]<t)i=1+o;else{if(!(e[o]>t))return o;r=o-1}}return-1},Pf.indicesOfNearest=function(t,e,n){var i=[];if(!this._storage[t])return i;null==n&&(n=1/0);for(var r=1/0,o=-1,a=0,s=0,l=this.count();s<l;s++){var u=e-this.get(t,s),h=Math.abs(u);h<=n&&((h<r||h===r&&0<=u&&o<0)&&(r=h,o=u,a=0),u===o&&(i[a++]=s))}return i.length=a,i},Pf.getRawIndex=zf,Pf.getRawDataItem=function(t){if(this._rawData.persistent)return this._rawData.getItem(this.getRawIndex(t));for(var e=[],n=0;n<this.dimensions.length;n++){var i=this.dimensions[n];e.push(this.get(i,t))}return e},Pf.getName=function(t){var e=this.getRawIndex(t);return this._nameList[e]||Ef(this,this._nameDimIdx,e)||""},Pf.getId=function(t){return Rf(this,this.getRawIndex(t))},Pf.each=function(t,e,n,i){if(this._count){"function"==typeof t&&(i=n,n=e,e=t,t=[]),n=n||i||this;for(var r=(t=P(Bf(t),this.getDimension,this)).length,o=0;o<this.count();o++)switch(r){case 0:e.call(n,o);break;case 1:e.call(n,this.get(t[0],o),o);break;case 2:e.call(n,this.get(t[0],o),this.get(t[1],o),o);break;default:for(var a=0,s=[];a<r;a++)s[a]=this.get(t[a],o);s[a]=o,e.apply(n,s)}}},Pf.filterSelf=function(t,e,n,i){if(this._count){"function"==typeof t&&(i=n,n=e,e=t,t=[]),n=n||i||this,t=P(Bf(t),this.getDimension,this);for(var r=this.count(),o=new(Cf(this))(r),a=[],s=t.length,l=0,u=t[0],h=0;h<r;h++){var c,d=this.getRawIndex(h);if(0===s)c=e.call(n,h);else if(1===s){var f=this._getFast(u,d);c=e.call(n,f,h)}else{for(var p=0;p<s;p++)a[p]=this._getFast(u,d);a[p]=h,c=e.apply(n,a)}c&&(o[l++]=d)}return l<r&&(this._indices=o),this._count=l,this._extent={},this.getRawIndex=this._indices?Nf:zf,this}},Pf.selectRange=function(t){if(this._count){var e=[];for(var n in t)t.hasOwnProperty(n)&&e.push(n);var i=e.length;if(i){var r=this.count(),o=new(Cf(this))(r),a=0,s=e[0],l=t[s][0],u=t[s][1],h=!1;if(!this._indices){var c=0;if(1===i){for(var d=this._storage[e[0]],f=0;f<this._chunkCount;f++)for(var p=d[f],g=Math.min(this._count-f*this._chunkSize,this._chunkSize),m=0;m<g;m++){(l<=(w=p[m])&&w<=u||isNaN(w))&&(o[a++]=c),c++}h=!0}else if(2===i){d=this._storage[s];var v=this._storage[e[1]],y=t[e[1]][0],_=t[e[1]][1];for(f=0;f<this._chunkCount;f++){p=d[f];var x=v[f];for(g=Math.min(this._count-f*this._chunkSize,this._chunkSize),m=0;m<g;m++){var w=p[m],b=x[m];(l<=w&&w<=u||isNaN(w))&&(y<=b&&b<=_||isNaN(b))&&(o[a++]=c),c++}}h=!0}}if(!h)if(1===i)for(m=0;m<r;m++){var S=this.getRawIndex(m);(l<=(w=this._getFast(s,S))&&w<=u||isNaN(w))&&(o[a++]=S)}else for(m=0;m<r;m++){var M=!0;for(S=this.getRawIndex(m),f=0;f<i;f++){var I=e[f];((w=this._getFast(n,S))<t[I][0]||w>t[I][1])&&(M=!1)}M&&(o[a++]=this.getRawIndex(m))}return a<r&&(this._indices=o),this._count=a,this._extent={},this.getRawIndex=this._indices?Nf:zf,this}}},Pf.mapArray=function(t,e,n,i){"function"==typeof t&&(i=n,n=e,e=t,t=[]),n=n||i||this;var r=[];return this.each(t,function(){r.push(e&&e.apply(this,arguments))},n),r},Pf.map=function(t,e,n,i){n=n||i||this;var r=Vf(this,t=P(Bf(t),this.getDimension,this));r._indices=this._indices,r.getRawIndex=r._indices?Nf:zf;for(var o=r._storage,a=[],s=this._chunkSize,l=t.length,u=this.count(),h=[],c=r._rawExtent,d=0;d<u;d++){for(var f=0;f<l;f++)h[f]=this.get(t[f],d);h[l]=d;var p=e&&e.apply(n,h);if(null!=p){"object"!=typeof p&&(a[0]=p,p=a);for(var g=this.getRawIndex(d),m=Math.floor(g/s),v=g%s,y=0;y<p.length;y++){var _=t[y],x=p[y],w=c[_],b=o[_];b&&(b[m][v]=x),x<w[0]&&(w[0]=x),x>w[1]&&(w[1]=x)}}}return r},Pf.downSample=function(t,e,n,i){for(var r=Vf(this,[t]),o=r._storage,a=[],s=Math.floor(1/e),l=o[t],u=this.count(),h=this._chunkSize,c=r._rawExtent[t],d=new(Cf(this))(u),f=0,p=0;p<u;p+=s){u-p<s&&(s=u-p,a.length=s);for(var g=0;g<s;g++){var m=this.getRawIndex(p+g),v=Math.floor(m/h),y=m%h;a[g]=l[v][y]}var _=n(a),x=this.getRawIndex(Math.min(p+i(a,_)||0,u-1)),w=x%h;(l[Math.floor(x/h)][w]=_)<c[0]&&(c[0]=_),_>c[1]&&(c[1]=_),d[f++]=x}return r._count=f,r._indices=d,r.getRawIndex=Nf,r},Pf.getItemModel=function(t){var e=this.hostModel;return new _l(this.getRawDataItem(t),e,e&&e.ecModel)},Pf.diff=function(e){var n=this;return new gf(e?e.getIndices():[],this.getIndices(),function(t){return Rf(e,t)},function(t){return Rf(n,t)})},Pf.getVisual=function(t){var e=this._visual;return e&&e[t]},Pf.setVisual=function(t,e){if(xf(t))for(var n in t)t.hasOwnProperty(n)&&this.setVisual(n,t[n]);else this._visual=this._visual||{},this._visual[t]=e},Pf.setLayout=function(t,e){if(xf(t))for(var n in t)t.hasOwnProperty(n)&&this.setLayout(n,t[n]);else this._layout[t]=e},Pf.getLayout=function(t){return this._layout[t]},Pf.getItemLayout=function(t){return this._itemLayouts[t]},Pf.setItemLayout=function(t,e,n){this._itemLayouts[t]=n?k(this._itemLayouts[t]||{},e):e},Pf.clearItemLayouts=function(){this._itemLayouts.length=0},Pf.getItemVisual=function(t,e,n){var i=this._itemVisuals[t],r=i&&i[e];return null!=r||n?r:this.getVisual(e)},Pf.setItemVisual=function(t,e,n){var i=this._itemVisuals[t]||{},r=this.hasItemVisual;if(this._itemVisuals[t]=i,xf(e))for(var o in e)e.hasOwnProperty(o)&&(i[o]=e[o],r[o]=!0);else i[e]=n,r[e]=!0},Pf.clearAllVisual=function(){this._visual={},this._itemVisuals=[],this.hasItemVisual={}};function Wf(t){t.seriesIndex=this.seriesIndex,t.dataIndex=this.dataIndex,t.dataType=this.dataType}function Gf(t,e,n){Pu.isInstance(e)||(e=Pu.seriesDataToSource(e)),n=n||{},t=(t||[]).slice();for(var i=(n.dimsDef||[]).slice(),r=Q(),o=Q(),l=[],a=function(t,e,n,i){var r=Math.max(t.dimensionsDetectCount||1,e.length,n.length,i||0);return D(e,function(t){var e=t.dimsDef;e&&(r=Math.max(r,e.length))}),r}(e,t,i,n.dimCount),s=0;s<a;s++){var u=i[s]=k({},N(i[s])?i[s]:{name:i[s]}),h=u.name,c=l[s]=new _f;null!=h&&null==r.get(h)&&(c.name=c.displayName=h,r.set(h,s)),null!=u.type&&(c.type=u.type),null!=u.displayName&&(c.displayName=u.displayName)}var d=n.encodeDef;!d&&n.encodeDefaulter&&(d=n.encodeDefaulter(e,a)),(d=Q(d)).each(function(t,n){if(1===(t=Lr(t).slice()).length&&!z(t[0])&&t[0]<0)d.set(n,!1);else{var i=d.set(n,[]);D(t,function(t,e){z(t)&&(t=r.get(t)),null!=t&&t<a&&(i[e]=t,p(l[t],n,e))})}});var f=0;function p(t,e,n){null!=vf.get(e)?t.otherDims[e]=n:(t.coordDim=e,t.coordDimIndex=n,o.set(e,!0))}D(t,function(r,t){var o,a,s;if(z(r))o=r,r={};else{o=r.name;var e=r.ordinalMeta;r.ordinalMeta=null,(r=b(r)).ordinalMeta=e,a=r.dimsDef,s=r.otherDims,r.name=r.coordDim=r.coordDimIndex=r.dimsDef=r.otherDims=null}if(!1!==(n=d.get(o))){var n;if(!(n=Lr(n)).length)for(var i=0;i<(a&&a.length||1);i++){for(;f<l.length&&null!=l[f].coordDim;)f++;f<l.length&&n.push(f++)}D(n,function(t,e){var n=l[t];if(p(A(n,r),o,e),null==n.name&&a){var i=a[e];N(i)||(i={name:i}),n.name=n.displayName=i.name,n.defaultTooltip=i.defaultTooltip}s&&A(n.otherDims,s)})}});var g=n.generateCoord,m=n.generateCoordCount,v=null!=m;m=g?m||1:0;for(var y,_,x=g||"value",w=0;w<a;w++){null==(c=l[w]=l[w]||new _f).coordDim&&(c.coordDim=Zf(x,o,v),c.coordDimIndex=0,(!g||m<=0)&&(c.isExtraCoord=!0),m--),null==c.name&&(c.name=Zf(c.coordDim,r)),null==c.type&&(y=e,_=w,c.name,Fu(y.data,y.sourceFormat,y.seriesLayoutBy,y.dimensionsDefine,y.startIndex,_)===Lu.Must||c.isExtraCoord&&(null!=c.otherDims.itemName||null!=c.otherDims.seriesName))&&(c.type="ordinal")}return l}function Zf(t,e,n){if(n||null!=e.get(t)){for(var i=0;null!=e.get(t+i);)i++;t+=i}return e.set(t,!0),t}Pf.setItemGraphicEl=function(t,e){var n=this.hostModel;e&&(e.dataIndex=t,e.dataType=this.dataType,e.seriesIndex=n&&n.seriesIndex,"group"===e.type&&e.traverse(Wf,e)),this._graphicEls[t]=e},Pf.getItemGraphicEl=function(t){return this._graphicEls[t]},Pf.eachItemGraphicEl=function(n,i){D(this._graphicEls,function(t,e){t&&n&&n.call(i,t,e)})},Pf.cloneShallow=function(t){if(!t){var e=P(this.dimensions,this.getDimensionInfo,this);t=new kf(e,this.hostModel)}if(t._storage=this._storage,Df(t,this),this._indices){var n=this._indices.constructor;t._indices=new n(this._indices)}else t._indices=null;return t.getRawIndex=t._indices?Nf:zf,t},Pf.wrapMethod=function(t,e){var n=this[t];"function"==typeof n&&(this.__wrappedMethods=this.__wrappedMethods||[],this.__wrappedMethods.push(t),this[t]=function(){var t=n.apply(this,arguments);return e.apply(this,[t].concat(Z(arguments)))})},Pf.TRANSFERABLE_METHODS=["cloneShallow","downSample","map"],Pf.CHANGABLE_METHODS=["filterSelf","selectRange"];var Uf=function(t,e){return Gf((e=e||{}).coordDimensions||[],t,{dimsDef:e.dimensionsDefine||t.dimensionsDefine,encodeDef:e.encodeDefine||t.encodeDefine,dimCount:e.dimensionsCount,encodeDefaulter:e.encodeDefaulter,generateCoord:e.generateCoord,generateCoordCount:e.generateCoordCount})};function Xf(t){this.coordSysName=t,this.coordSysDims=[],this.axisMap=Q(),this.categoryAxisMap=Q(),this.firstCategoryDimIndex=null}var Yf={cartesian2d:function(t,e,n,i){var r=t.getReferringComponents("xAxis")[0],o=t.getReferringComponents("yAxis")[0];e.coordSysDims=["x","y"],n.set("x",r),n.set("y",o),jf(r)&&(i.set("x",r),e.firstCategoryDimIndex=0),jf(o)&&(i.set("y",o),e.firstCategoryDimIndex,e.firstCategoryDimIndex=1)},singleAxis:function(t,e,n,i){var r=t.getReferringComponents("singleAxis")[0];e.coordSysDims=["single"],n.set("single",r),jf(r)&&(i.set("single",r),e.firstCategoryDimIndex=0)},polar:function(t,e,n,i){var r=t.getReferringComponents("polar")[0],o=r.findAxisModel("radiusAxis"),a=r.findAxisModel("angleAxis");e.coordSysDims=["radius","angle"],n.set("radius",o),n.set("angle",a),jf(o)&&(i.set("radius",o),e.firstCategoryDimIndex=0),jf(a)&&(i.set("angle",a),null==e.firstCategoryDimIndex&&(e.firstCategoryDimIndex=1))},geo:function(t,e,n,i){e.coordSysDims=["lng","lat"]},parallel:function(t,r,o,a){var s=t.ecModel,e=s.getComponent("parallel",t.get("parallelIndex")),l=r.coordSysDims=e.dimensions.slice();D(e.parallelAxisIndex,function(t,e){var n=s.getComponent("parallelAxis",t),i=l[e];o.set(i,n),jf(n)&&null==r.firstCategoryDimIndex&&(a.set(i,n),r.firstCategoryDimIndex=e)})}};function jf(t){return"category"===t.get("type")}function qf(t,n,e){var i,r,o,a,s=(e=e||{}).byIndex,l=e.stackedCoordDimension,u=!(!t||!t.get("stack"));if(D(n,function(t,e){z(t)&&(n[e]=t={name:t}),u&&!t.isExtraCoord&&(s||i||!t.ordinalMeta||(i=t),r||"ordinal"===t.type||"time"===t.type||l&&l!==t.coordDim||(r=t))}),!r||s||i||(s=!0),r){o="__\0ecstackresult",a="__\0ecstackedover",i&&(i.createInvertedIndices=!0);var h=r.coordDim,c=r.type,d=0;D(n,function(t){t.coordDim===h&&d++}),n.push({name:o,coordDim:h,coordDimIndex:d,type:c,isExtraCoord:!0,isCalculationCoord:!0}),d++,n.push({name:a,coordDim:a,coordDimIndex:d,type:c,isExtraCoord:!0,isCalculationCoord:!0})}return{stackedDimension:r&&r.name,stackedByDimension:i&&i.name,isStackedByIndex:s,stackedOverDimension:a,stackResultDimension:o}}function $f(t,e){return!!e&&e===t.getCalculationInfo("stackedDimension")}function Kf(t,e){return $f(t,e)?t.getCalculationInfo("stackResultDimension"):e}function Qf(t,e,n){n=n||{},Pu.isInstance(t)||(t=Pu.seriesDataToSource(t));var i,r=e.get("coordinateSystem"),o=ju.get(r),a=function(t){var e=t.get("coordinateSystem"),n=new Xf(e),i=Yf[e];if(i)return i(t,n,n.axisMap,n.categoryAxisMap),n}(e);a&&(i=P(a.coordSysDims,function(t){var e={name:t},n=a.axisMap.get(t);if(n){var i=n.get("type");e.type=function(t){return"category"===t?"ordinal":"time"===t?"time":"float"}(i)}return e})),i=i||(o&&(o.getDimensionsInfo?o.getDimensionsInfo():o.dimensions.slice())||["x","y"]);var s,l,u=Uf(t,{coordDimensions:i,generateCoord:n.generateCoord,encodeDefaulter:n.useEncodeDefaulter?T(Ru,i,e):null});a&&D(u,function(t,e){var n=t.coordDim,i=a.categoryAxisMap.get(n);i&&(null==s&&(s=e),t.ordinalMeta=i.getOrdinalMeta()),null!=t.otherDims.itemName&&(l=!0)}),l||null==s||(u[s].otherDims.itemName=0);var h=qf(e,u),c=new kf(u,e);c.setCalculationInfo(h);var d=null!=s&&function(t){if(t.sourceFormat===Su){var e=function(t){var e=0;for(;e<t.length&&null==t[e];)e++;return t[e]}(t.data||[]);return null!=e&&!O(zr(e))}}(t)?function(t,e,n,i){return i===s?n:this.defaultDimValueGetter(t,e,n,i)}:null;return c.hasItemOption=!1,c.initData(t,null,d),c}function Jf(t){this._setting=t||{},this._extent=[1/0,-1/0],this._interval=0,this.init&&this.init.apply(this,arguments)}function tp(t){this.categories=t.categories||[],this._needCollect=t.needCollect,this._deduplication=t.deduplication,this._map}Jf.prototype.parse=function(t){return t},Jf.prototype.getSetting=function(t){return this._setting[t]},Jf.prototype.contain=function(t){var e=this._extent;return t>=e[0]&&t<=e[1]},Jf.prototype.normalize=function(t){var e=this._extent;return e[1]===e[0]?.5:(t-e[0])/(e[1]-e[0])},Jf.prototype.scale=function(t){var e=this._extent;return t*(e[1]-e[0])+e[0]},Jf.prototype.unionExtent=function(t){var e=this._extent;t[0]<e[0]&&(e[0]=t[0]),t[1]>e[1]&&(e[1]=t[1])},Jf.prototype.unionExtentFromData=function(t,e){this.unionExtent(t.getApproximateExtent(e))},Jf.prototype.getExtent=function(){return this._extent.slice()},Jf.prototype.setExtent=function(t,e){var n=this._extent;isNaN(t)||(n[0]=t),isNaN(e)||(n[1]=e)},Jf.prototype.isBlank=function(){return this._isBlank},Jf.prototype.setBlank=function(t){this._isBlank=t},Jf.prototype.getLabel=null,$r(Jf),eo(Jf,{registerWhenExtend:!0}),tp.createByAxisModel=function(t){var e=t.option,n=e.data,i=n&&P(n,ip);return new tp({categories:i,needCollect:!i,deduplication:!1!==e.dedplication})};var ep=tp.prototype;function np(t){return t._map||(t._map=Q(t.categories))}function ip(t){return N(t)&&null!=t.value?t.value:t+""}ep.getOrdinal=function(t){return np(this).get(t)},ep.parseAndCollect=function(t){var e,n=this._needCollect;if("string"!=typeof t&&!n)return t;if(n&&!this._deduplication)return e=this.categories.length,this.categories[e]=t,e;var i=np(this);return null==(e=i.get(t))&&(n?(e=this.categories.length,this.categories[e]=t,i.set(t,e)):e=NaN),e};var rp=Jf.prototype,op=Jf.extend({type:"ordinal",init:function(t,e){t&&!O(t)||(t=new tp({categories:t})),this._ordinalMeta=t,this._extent=e||[0,t.categories.length-1]},parse:function(t){return"string"==typeof t?this._ordinalMeta.getOrdinal(t):Math.round(t)},contain:function(t){return t=this.parse(t),rp.contain.call(this,t)&&null!=this._ordinalMeta.categories[t]},normalize:function(t){return rp.normalize.call(this,this.parse(t))},scale:function(t){return Math.round(rp.scale.call(this,t))},getTicks:function(){for(var t=[],e=this._extent,n=e[0];n<=e[1];)t.push(n),n++;return t},getLabel:function(t){if(!this.isBlank())return this._ordinalMeta.categories[t]},count:function(){return this._extent[1]-this._extent[0]+1},unionExtentFromData:function(t,e){this.unionExtent(t.getApproximateExtent(e))},getOrdinalMeta:function(){return this._ordinalMeta},niceTicks:J,niceExtent:J});op.create=function(){return new op};var ap=Tl;function sp(t){return kl(t)+2}function lp(t,e,n){t[e]=Math.max(Math.min(t[e],n[1]),n[0])}function up(t,e){isFinite(t[0])||(t[0]=e[0]),isFinite(t[1])||(t[1]=e[1]),lp(t,0,e),lp(t,1,e),t[0]>t[1]&&(t[0]=t[1])}var hp=Tl,cp=Jf.extend({type:"interval",_interval:0,_intervalPrecision:2,setExtent:function(t,e){var n=this._extent;isNaN(t)||(n[0]=parseFloat(t)),isNaN(e)||(n[1]=parseFloat(e))},unionExtent:function(t){var e=this._extent;t[0]<e[0]&&(e[0]=t[0]),t[1]>e[1]&&(e[1]=t[1]),cp.prototype.setExtent.call(this,e[0],e[1])},getInterval:function(){return this._interval},setInterval:function(t){this._interval=t,this._niceExtent=this._extent.slice(),this._intervalPrecision=sp(t)},getTicks:function(t){var e=this._interval,n=this._extent,i=this._niceExtent,r=this._intervalPrecision,o=[];if(!e)return o;n[0]<i[0]&&(t?o.push(hp(i[0]-e,r)):o.push(n[0]));for(var a=i[0];a<=i[1]&&(o.push(a),(a=hp(a+e,r))!==o[o.length-1]);)if(1e4<o.length)return[];var s=o.length?o[o.length-1]:i[1];return n[1]>s&&(t?o.push(hp(s+e,r)):o.push(n[1])),o},getMinorTicks:function(t){for(var e=this.getTicks(!0),n=[],i=this.getExtent(),r=1;r<e.length;r++){for(var o=e[r],a=e[r-1],s=0,l=[],u=(o-a)/t;s<t-1;){var h=Tl(a+(s+1)*u);h>i[0]&&h<i[1]&&l.push(h),s++}n.push(l)}return n},getLabel:function(t,e){if(null==t)return"";var n=e&&e.precision;return null==n?n=kl(t)||0:"auto"===n&&(n=this._intervalPrecision),Hl(t=hp(t,n,!0))},niceTicks:function(t,e,n){t=t||5;var i=this._extent,r=i[1]-i[0];if(isFinite(r)){r<0&&(r=-r,i.reverse());var o=function(t,e,n,i){var r={},o=t[1]-t[0],a=r.interval=Vl(o/e,!0);null!=n&&a<n&&(a=r.interval=n),null!=i&&i<a&&(a=r.interval=i);var s=r.intervalPrecision=sp(a);return up(r.niceTickExtent=[ap(Math.ceil(t[0]/a)*a,s),ap(Math.floor(t[1]/a)*a,s)],t),r}(i,t,e,n);this._intervalPrecision=o.intervalPrecision,this._interval=o.interval,this._niceExtent=o.niceTickExtent}},niceExtent:function(t){var e=this._extent;if(e[0]===e[1])if(0!==e[0]){var n=e[0];t.fixMax||(e[1]+=n/2),e[0]-=n/2}else e[1]=1;var i=e[1]-e[0];isFinite(i)||(e[0]=0,e[1]=1),this.niceTicks(t.splitNumber,t.minInterval,t.maxInterval);var r=this._interval;t.fixMin||(e[0]=hp(Math.floor(e[0]/r)*r)),t.fixMax||(e[1]=hp(Math.ceil(e[1]/r)*r))}});cp.create=function(){return new cp};var dp="__ec_stack_",fp="undefined"!=typeof Float32Array?Float32Array:Array;function pp(t){return t.get("stack")||dp+t.seriesIndex}function gp(t){return t.dim+t.index}function mp(t,e){var n=[];return e.eachSeriesByType(t,function(t){xp(t)&&!wp(t)&&n.push(t)}),n}function vp(t){var g=function(t){var l={};D(t,function(t){var e=t.coordinateSystem.getBaseAxis();if("time"===e.type||"value"===e.type)for(var n=t.getData(),i=e.dim+"_"+e.index,r=n.mapDimension(e.dim),o=0,a=n.count();o<a;++o){var s=n.get(r,o);l[i]?l[i].push(s):l[i]=[s]}});var e=[];for(var n in l)if(l.hasOwnProperty(n)){var i=l[n];if(i){i.sort(function(t,e){return t-e});for(var r=null,o=1;o<i.length;++o){var a=i[o]-i[o-1];0<a&&(r=null===r?a:Math.min(r,a))}e[n]=r}}return e}(t),m=[];return D(t,function(t){var e,n=t.coordinateSystem.getBaseAxis(),i=n.getExtent();if("category"===n.type)e=n.getBandWidth();else if("value"===n.type||"time"===n.type){var r=n.dim+"_"+n.index,o=g[r],a=Math.abs(i[1]-i[0]),s=n.scale.getExtent(),l=Math.abs(s[1]-s[0]);e=o?a/l*o:a}else{var u=t.getData();e=Math.abs(i[1]-i[0])/u.count()}var h=Cl(t.get("barWidth"),e),c=Cl(t.get("barMaxWidth"),e),d=Cl(t.get("barMinWidth")||1,e),f=t.get("barGap"),p=t.get("barCategoryGap");m.push({bandWidth:e,barWidth:h,barMaxWidth:c,barMinWidth:d,barGap:f,barCategoryGap:p,axisKey:gp(n),stackId:pp(t)})}),function(t){var d={};D(t,function(t,e){var n=t.axisKey,i=t.bandWidth,r=d[n]||{bandWidth:i,remainedWidth:i,autoWidthCount:0,categoryGap:"20%",gap:"30%",stacks:{}},o=r.stacks;d[n]=r;var a=t.stackId;o[a]||r.autoWidthCount++,o[a]=o[a]||{width:0,maxWidth:0};var s=t.barWidth;s&&!o[a].width&&(o[a].width=s,s=Math.min(r.remainedWidth,s),r.remainedWidth-=s);var l=t.barMaxWidth;l&&(o[a].maxWidth=l);var u=t.barMinWidth;u&&(o[a].minWidth=u);var h=t.barGap;null!=h&&(r.gap=h);var c=t.barCategoryGap;null!=c&&(r.categoryGap=c)});var f={};return D(d,function(t,n){f[n]={};var e=t.stacks,i=t.bandWidth,r=Cl(t.categoryGap,i),o=Cl(t.gap,1),a=t.remainedWidth,s=t.autoWidthCount,l=(a-r)/(s+(s-1)*o);l=Math.max(l,0),D(e,function(t){var e=t.maxWidth,n=t.minWidth;if(t.width){i=t.width;e&&(i=Math.min(i,e)),n&&(i=Math.max(i,n)),t.width=i,a-=i+o*i,s--}else{var i=l;e&&e<i&&(i=Math.min(e,a)),n&&i<n&&(i=n),i!==l&&(t.width=i,a-=i+o*i,s--)}}),l=(a-r)/(s+(s-1)*o),l=Math.max(l,0);var u,h=0;D(e,function(t,e){t.width||(t.width=l),h+=(u=t).width*(1+o)}),u&&(h-=u.width*o);var c=-h/2;D(e,function(t,e){f[n][e]=f[n][e]||{bandWidth:i,offset:c,width:t.width},c+=t.width*(1+o)})}),f}(m)}function yp(t,e,n){if(t&&e){var i=t[gp(e)];return null!=i&&null!=n&&(i=i[pp(n)]),i}}var _p={seriesType:"bar",plan:ic(),reset:function(t){if(xp(t)&&wp(t)){var e=t.getData(),c=t.coordinateSystem,d=c.grid.getRect(),f=c.getBaseAxis(),p=c.getOtherAxis(f),g=e.mapDimension(p.dim),m=e.mapDimension(f.dim),v=p.isHorizontal(),y=v?0:1,_=yp(vp([t]),f,t).width;return.5<_||(_=.5),{progress:function(t,e){var n,i=t.count,r=new fp(2*i),o=new fp(2*i),a=new fp(i),s=[],l=[],u=0,h=0;for(;null!=(n=t.next());)l[y]=e.get(g,n),l[1-y]=e.get(m,n),s=c.dataToPoint(l,null,s),o[u]=v?d.x+d.width:s[0],r[u++]=s[0],o[u]=v?s[1]:d.y+d.height,r[u++]=s[1],a[h++]=n;e.setLayout({largePoints:r,largeDataIndices:a,largeBackgroundPoints:o,barWidth:_,valueAxisStart:bp(f,p,!1),backgroundStart:v?d.x:d.y,valueAxisHorizontal:v})}}}}};function xp(t){return t.coordinateSystem&&"cartesian2d"===t.coordinateSystem.type}function wp(t){return t.pipelineContext&&t.pipelineContext.large}function bp(t,e){return e.toGlobalCoord(e.dataToCoord("log"===e.type?1:0))}var Sp=cp.prototype,Mp=Math.ceil,Ip=Math.floor,Cp=36e5,Tp=864e5,Ap=cp.extend({type:"time",getLabel:function(t){var e=this._stepLvl,n=new Date(t);return Ql(e[0],n,this.getSetting("useUTC"))},niceExtent:function(t){var e=this._extent;if(e[0]===e[1]&&(e[0]-=Tp,e[1]+=Tp),e[1]===-1/0&&e[0]===1/0){var n=new Date;e[1]=+new Date(n.getFullYear(),n.getMonth(),n.getDate()),e[0]=e[1]-Tp}this.niceTicks(t.splitNumber,t.minInterval,t.maxInterval);var i=this._interval;t.fixMin||(e[0]=Tl(Ip(e[0]/i)*i)),t.fixMax||(e[1]=Tl(Mp(e[1]/i)*i))},niceTicks:function(t,e,n){t=t||10;var i=this._extent,r=i[1]-i[0],o=r/t;null!=e&&o<e&&(o=e),null!=n&&n<o&&(o=n);var a=Dp.length,s=function(t,e,n,i){for(;n<i;){var r=n+i>>>1;t[r][1]<e?n=1+r:i=r}return n}(Dp,o,0,a),l=Dp[Math.min(s,a-1)],u=l[1];"year"===l[0]&&(u*=Vl(r/u/t,!0));var h=this.getSetting("useUTC")?0:60*new Date(+i[0]||+i[1]).getTimezoneOffset()*1e3,c=[Math.round(Mp((i[0]-h)/u)*u+h),Math.round(Ip((i[1]-h)/u)*u+h)];up(c,i),this._stepLvl=l,this._interval=u,this._niceExtent=c},parse:function(t){return+Nl(t)}});D(["contain","normalize"],function(e){Ap.prototype[e]=function(t){return Sp[e].call(this,this.parse(t))}});var Dp=[["hh:mm:ss",1e3],["hh:mm:ss",5e3],["hh:mm:ss",1e4],["hh:mm:ss",15e3],["hh:mm:ss",3e4],["hh:mm\nMM-dd",6e4],["hh:mm\nMM-dd",3e5],["hh:mm\nMM-dd",6e5],["hh:mm\nMM-dd",9e5],["hh:mm\nMM-dd",18e5],["hh:mm\nMM-dd",Cp],["hh:mm\nMM-dd",72e5],["hh:mm\nMM-dd",6*Cp],["hh:mm\nMM-dd",432e5],["MM-dd\nyyyy",Tp],["MM-dd\nyyyy",2*Tp],["MM-dd\nyyyy",3*Tp],["MM-dd\nyyyy",4*Tp],["MM-dd\nyyyy",5*Tp],["MM-dd\nyyyy",6*Tp],["week",7*Tp],["MM-dd\nyyyy",864e6],["week",14*Tp],["week",21*Tp],["month",31*Tp],["week",42*Tp],["month",62*Tp],["week",70*Tp],["quarter",95*Tp],["month",31*Tp*4],["month",13392e6],["half-year",16416e6],["month",31*Tp*8],["month",26784e6],["year",380*Tp]];Ap.create=function(t){return new Ap({useUTC:t.ecModel.get("useUTC")})};var kp=Jf.prototype,Pp=cp.prototype,Lp=kl,Op=Tl,Ep=Math.floor,zp=Math.ceil,Np=Math.pow,Rp=Math.log,Bp=Jf.extend({type:"log",base:10,$constructor:function(){Jf.apply(this,arguments),this._originalScale=new cp},getTicks:function(t){var n=this._originalScale,i=this._extent,r=n.getExtent();return P(Pp.getTicks.call(this,t),function(t){var e=Tl(Np(this.base,t));return e=t===i[0]&&n.__fixMin?Vp(e,r[0]):e,e=t===i[1]&&n.__fixMax?Vp(e,r[1]):e},this)},getMinorTicks:Pp.getMinorTicks,getLabel:Pp.getLabel,scale:function(t){return t=kp.scale.call(this,t),Np(this.base,t)},setExtent:function(t,e){var n=this.base;t=Rp(t)/Rp(n),e=Rp(e)/Rp(n),Pp.setExtent.call(this,t,e)},getExtent:function(){var t=this.base,e=kp.getExtent.call(this);e[0]=Np(t,e[0]),e[1]=Np(t,e[1]);var n=this._originalScale,i=n.getExtent();return n.__fixMin&&(e[0]=Vp(e[0],i[0])),n.__fixMax&&(e[1]=Vp(e[1],i[1])),e},unionExtent:function(t){this._originalScale.unionExtent(t);var e=this.base;t[0]=Rp(t[0])/Rp(e),t[1]=Rp(t[1])/Rp(e),kp.unionExtent.call(this,t)},unionExtentFromData:function(t,e){this.unionExtent(t.getApproximateExtent(e))},niceTicks:function(t){t=t||10;var e=this._extent,n=e[1]-e[0];if(!(n==1/0||n<=0)){var i=Rl(n);for(t/n*i<=.5&&(i*=10);!isNaN(i)&&Math.abs(i)<1&&0<Math.abs(i);)i*=10;var r=[Tl(zp(e[0]/i)*i),Tl(Ep(e[1]/i)*i)];this._interval=i,this._niceExtent=r}},niceExtent:function(t){Pp.niceExtent.call(this,t);var e=this._originalScale;e.__fixMin=t.fixMin,e.__fixMax=t.fixMax}});function Vp(t,e){return Op(t,Lp(e))}function Fp(t,e){var n,i,r,o=t.type,a=e.getMin(),s=e.getMax(),l=t.getExtent();"ordinal"===o?n=e.getCategories().length:(O(i=e.get("boundaryGap"))||(i=[i||0,i||0]),"boolean"==typeof i[0]&&(i=[0,0]),i[0]=Cl(i[0],1),i[1]=Cl(i[1],1),r=l[1]-l[0]||Math.abs(l[0])),"dataMin"===a?a=l[0]:"function"==typeof a&&(a=a({min:l[0],max:l[1]})),"dataMax"===s?s=l[1]:"function"==typeof s&&(s=s({min:l[0],max:l[1]}));var u=null!=a,h=null!=s;null==a&&(a="ordinal"===o?n?0:NaN:l[0]-i[0]*r),null==s&&(s="ordinal"===o?n?n-1:NaN:l[1]+i[1]*r),null!=a&&isFinite(a)||(a=NaN),null!=s&&isFinite(s)||(s=NaN),t.setBlank(F(a)||F(s)||"ordinal"===o&&!t.getOrdinalMeta().categories.length),e.getNeedCrossZero()&&(0<a&&0<s&&!u&&(a=0),a<0&&s<0&&!h&&(s=0));var c=e.ecModel;if(c&&"time"===o){var d,f=mp("bar",c);if(D(f,function(t){d|=t.getBaseAxis()===e.axis}),d){var p=vp(f),g=function(t,e,n,i){var r=n.axis.getExtent(),o=r[1]-r[0],a=yp(i,n.axis);if(void 0===a)return{min:t,max:e};var s=1/0;D(a,function(t){s=Math.min(t.offset,s)});var l=-1/0;D(a,function(t){l=Math.max(t.offset+t.width,l)}),s=Math.abs(s),l=Math.abs(l);var u=s+l,h=e-t,c=h/(1-(s+l)/o)-h;return{min:t-=s/u*c,max:e+=l/u*c}}(a,s,e,p);a=g.min,s=g.max}}return{extent:[a,s],fixMin:u,fixMax:h}}function Hp(t,e){var n=Fp(t,e),i=n.extent,r=e.get("splitNumber");"log"===t.type&&(t.base=e.get("logBase"));var o=t.type;t.setExtent(i[0],i[1]),t.niceExtent({splitNumber:r,fixMin:n.fixMin,fixMax:n.fixMax,minInterval:"interval"===o||"time"===o?e.get("minInterval"):null,maxInterval:"interval"===o||"time"===o?e.get("maxInterval"):null});var a=e.get("interval");null!=a&&t.setInterval&&t.setInterval(a)}function Wp(t,e){if(e=e||t.get("type"))switch(e){case"category":return new op(t.getOrdinalMeta?t.getOrdinalMeta():t.getCategories(),[1/0,-1/0]);case"value":return new cp;default:return(Jf.getClass(e)||cp).create(t)}}function Gp(n){var e,i=n.getLabelModel().get("formatter"),r="category"===n.type?n.scale.getExtent()[0]:null;return"string"==typeof i?(e=i,i=function(t){return t=n.scale.getLabel(t),e.replace("{value}",null!=t?t:"")}):"function"==typeof i?function(t,e){return null!=r&&(e=t-r),i(Zp(n,t),e)}:function(t){return n.scale.getLabel(t)}}function Zp(t,e){return"category"===t.type?t.scale.getLabel(e):e}function Up(t){var e=t.get("interval");return null==e?"auto":e}function Xp(t){return"category"===t.type&&0===Up(t.getLabelModel())}D(["contain","normalize"],function(e){Bp.prototype[e]=function(t){return t=Rp(t)/Rp(this.base),kp[e].call(this,t)}}),Bp.create=function(){return new Bp};var Yp={getMin:function(t){var e=this.option,n=t||null==e.rangeStart?e.min:e.rangeStart;return this.axis&&null!=n&&"dataMin"!==n&&"function"!=typeof n&&!F(n)&&(n=this.axis.scale.parse(n)),n},getMax:function(t){var e=this.option,n=t||null==e.rangeEnd?e.max:e.rangeEnd;return this.axis&&null!=n&&"dataMax"!==n&&"function"!=typeof n&&!F(n)&&(n=this.axis.scale.parse(n)),n},getNeedCrossZero:function(){var t=this.option;return null==t.rangeStart&&null==t.rangeEnd&&!t.scale},getCoordSysModel:J,setRange:function(t,e){this.option.rangeStart=t,this.option.rangeEnd=e},resetRange:function(){this.option.rangeStart=this.option.rangeEnd=null}},jp=ws({type:"triangle",shape:{cx:0,cy:0,width:0,height:0},buildPath:function(t,e){var n=e.cx,i=e.cy,r=e.width/2,o=e.height/2;t.moveTo(n,i-o),t.lineTo(n+r,i+o),t.lineTo(n-r,i+o),t.closePath()}}),qp=ws({type:"diamond",shape:{cx:0,cy:0,width:0,height:0},buildPath:function(t,e){var n=e.cx,i=e.cy,r=e.width/2,o=e.height/2;t.moveTo(n,i-o),t.lineTo(n+r,i),t.lineTo(n,i+o),t.lineTo(n-r,i),t.closePath()}}),$p=ws({type:"pin",shape:{x:0,y:0,width:0,height:0},buildPath:function(t,e){var n=e.x,i=e.y,r=e.width/5*3,o=Math.max(r,e.height),a=r/2,s=a*a/(o-a),l=i-o+a+s,u=Math.asin(s/a),h=Math.cos(u)*a,c=Math.sin(u),d=Math.cos(u),f=.6*a,p=.7*a;t.moveTo(n-h,l+s),t.arc(n,l,a,Math.PI-u,2*Math.PI+u),t.bezierCurveTo(n+h-c*f,l+s+d*f,n,i-p,n,i),t.bezierCurveTo(n,i-p,n-h+c*f,l+s+d*f,n-h,l+s),t.closePath()}}),Kp=ws({type:"arrow",shape:{x:0,y:0,width:0,height:0},buildPath:function(t,e){var n=e.height,i=e.width,r=e.x,o=e.y,a=i/3*2;t.moveTo(r,o),t.lineTo(r+a,o+n),t.lineTo(r,o+n/4*3),t.lineTo(r-a,o+n),t.lineTo(r,o),t.closePath()}}),Qp={line:function(t,e,n,i,r){r.x1=t,r.y1=e+i/2,r.x2=t+n,r.y2=e+i/2},rect:function(t,e,n,i,r){r.x=t,r.y=e,r.width=n,r.height=i},roundRect:function(t,e,n,i,r){r.x=t,r.y=e,r.width=n,r.height=i,r.r=Math.min(n,i)/4},square:function(t,e,n,i,r){var o=Math.min(n,i);r.x=t,r.y=e,r.width=o,r.height=o},circle:function(t,e,n,i,r){r.cx=t+n/2,r.cy=e+i/2,r.r=Math.min(n,i)/2},diamond:function(t,e,n,i,r){r.cx=t+n/2,r.cy=e+i/2,r.width=n,r.height=i},pin:function(t,e,n,i,r){r.x=t+n/2,r.y=e+i/2,r.width=n,r.height=i},arrow:function(t,e,n,i,r){r.x=t+n/2,r.y=e+i/2,r.width=n,r.height=i},triangle:function(t,e,n,i,r){r.cx=t+n/2,r.cy=e+i/2,r.width=n,r.height=i}},Jp={};D({line:es,rect:Ja,roundRect:Ja,square:Ja,circle:Fa,diamond:qp,pin:$p,arrow:Kp,triangle:jp},function(t,e){Jp[e]=new t});var tg=ws({type:"symbol",shape:{symbolType:"",x:0,y:0,width:0,height:0},calculateTextPosition:function(t,e,n){var i=di(t,e,n),r=this.shape;return r&&"pin"===r.symbolType&&"inside"===e.textPosition&&(i.y=n.y+.4*n.height),i},buildPath:function(t,e,n){var i=e.symbolType;if("none"!==i){var r=Jp[i];r=r||Jp[i="rect"],Qp[i](e.x,e.y,e.width,e.height,r.shape),r.buildPath(t,r.shape,n)}}});function eg(t,e){if("image"!==this.type){var n=this.style,i=this.shape;i&&"line"===i.symbolType?n.stroke=t:this.__isEmptyBrush?(n.stroke=t,n.fill=e||"#fff"):(n.fill&&(n.fill=t),n.stroke&&(n.stroke=t)),this.dirty(!1)}}function ng(t,e,n,i,r,o,a){var s,l=0===t.indexOf("empty");return l&&(t=t.substr(5,1).toLowerCase()+t.substr(6)),(s=0===t.indexOf("image://")?Is(t.slice(8),new Sn(e,n,i,r),a?"center":"cover"):0===t.indexOf("path://")?Ms(t.slice(7),{},new Sn(e,n,i,r),a?"center":"cover"):new tg({shape:{symbolType:t,x:e,y:n,width:i,height:r}})).__isEmptyBrush=l,s.setColor=eg,s.setColor(o),s}var ig={isDimensionStacked:$f,enableDataStack:qf,getStackedDimension:Kf};var rg=(Object.freeze||Object)({createList:function(t){return Qf(t.getSource(),t)},getLayoutRect:lu,dataStack:ig,createScale:function(t,e){var n=e;_l.isInstance(e)||S(n=new _l(e),Yp);var i=Wp(n);return i.setExtent(t[0],t[1]),Hp(i,n),i},mixinAxisModelCommonMethods:function(t){S(t,Yp)},completeDimensions:Gf,createDimensions:Uf,createSymbol:ng}),og=1e-8;function ag(t,e){return Math.abs(t-e)<og}function sg(t,e,n){var i=0,r=t[0];if(!r)return!1;for(var o=1;o<t.length;o++){var a=t[o];i+=sa(r[0],r[1],a[0],a[1],e,n),r=a}var s=t[0];return ag(r[0],s[0])&&ag(r[1],s[1])||(i+=sa(r[0],r[1],s[0],s[1],e,n)),0!==i}function lg(t,e,n){if(this.name=t,this.geometries=e,n)n=[n[0],n[1]];else{var i=this.getBoundingRect();n=[i.x+i.width/2,i.y+i.height/2]}this.center=n}function ug(t,e,n){for(var i=[],r=e[0],o=e[1],a=0;a<t.length;a+=2){var s=t.charCodeAt(a)-64,l=t.charCodeAt(a+1)-64;s=s>>1^-(1&s),l=l>>1^-(1&l),r=s+=r,o=l+=o,i.push([s/n,l/n])}return i}lg.prototype={constructor:lg,properties:null,getBoundingRect:function(){var t=this._rect;if(t)return t;for(var e=Number.MAX_VALUE,n=[e,e],i=[-e,-e],r=[],o=[],a=this.geometries,s=0;s<a.length;s++){if("polygon"===a[s].type)zo(a[s].exterior,r,o),_t(n,n,r),xt(i,i,o)}return 0===s&&(n[0]=n[1]=i[0]=i[1]=0),this._rect=new Sn(n[0],n[1],i[0]-n[0],i[1]-n[1])},contain:function(t){var e=this.getBoundingRect(),n=this.geometries;if(!e.contain(t[0],t[1]))return!1;t:for(var i=0,r=n.length;i<r;i++)if("polygon"===n[i].type){var o=n[i].exterior,a=n[i].interiors;if(sg(o,t[0],t[1])){for(var s=0;s<(a?a.length:0);s++)if(sg(a[s]))continue t;return!0}}return!1},transformTo:function(t,e,n,i){var r=this.getBoundingRect(),o=r.width/r.height;n?i=i||n/o:n=o*i;for(var a=new Sn(t,e,n,i),s=r.calculateTransform(a),l=this.geometries,u=0;u<l.length;u++)if("polygon"===l[u].type){for(var h=l[u].exterior,c=l[u].interiors,d=0;d<h.length;d++)yt(h[d],h[d],s);for(var f=0;f<(c?c.length:0);f++)for(d=0;d<c[f].length;d++)yt(c[f][d],c[f][d],s)}(r=this._rect).copy(a),this.center=[r.x+r.width/2,r.y+r.height/2]},cloneShallow:function(t){null==t&&(t=this.name);var e=new lg(t,this.geometries,this.center);return e._rect=this._rect,e.transformTo=null,e}};function hg(t,a){return function(t){if(!t.UTF8Encoding)return;var e=t.UTF8Scale;null==e&&(e=1024);for(var n=t.features,i=0;i<n.length;i++)for(var r=n[i].geometry,o=r.coordinates,a=r.encodeOffsets,s=0;s<o.length;s++){var l=o[s];if("Polygon"===r.type)o[s]=ug(l,a[s],e);else if("MultiPolygon"===r.type)for(var u=0;u<l.length;u++){var h=l[u];l[u]=ug(h,a[s][u],e)}}t.UTF8Encoding=!1}(t),P(I(t.features,function(t){return t.geometry&&t.properties&&0<t.geometry.coordinates.length}),function(t){var e=t.properties,n=t.geometry,i=n.coordinates,r=[];"Polygon"===n.type&&r.push({type:"polygon",exterior:i[0],interiors:i.slice(1)}),"MultiPolygon"===n.type&&D(i,function(t){t[0]&&r.push({type:"polygon",exterior:t[0],interiors:t.slice(1)})});var o=new lg(e[a||"name"],r,e.cp);return o.properties=e,o})}var cg=Hr();function dg(t){return"category"===t.type?function(t){var e=t.getLabelModel(),n=pg(t,e);return!e.get("show")||t.scale.isBlank()?{labels:[],labelCategoryInterval:n.labelCategoryInterval}:n}(t):function(n){var t=n.scale.getTicks(),i=Gp(n);return{labels:P(t,function(t,e){return{formattedLabel:i(t,e),rawLabel:n.scale.getLabel(t),tickValue:t}})}}(t)}function fg(t,e){return"category"===t.type?function(t,e){var n,i,r=gg(t,"ticks"),o=Up(e),a=mg(r,o);if(a)return a;e.get("show")&&!t.scale.isBlank()||(n=[]);if(E(o))n=_g(t,o,!0);else if("auto"===o){var s=pg(t,t.getLabelModel());i=s.labelCategoryInterval,n=P(s.labels,function(t){return t.tickValue})}else n=yg(t,i=o,!0);return vg(r,o,{ticks:n,tickCategoryInterval:i})}(t,e):{ticks:t.scale.getTicks()}}function pg(t,e){var n,i=gg(t,"labels"),r=Up(e),o=mg(i,r);return o||vg(i,r,{labels:E(r)?_g(t,r):yg(t,n="auto"===r?function(t){var e=cg(t).autoInterval;return null!=e?e:cg(t).autoInterval=t.calculateCategoryInterval()}(t):r),labelCategoryInterval:n})}function gg(t,e){return cg(t)[e]||(cg(t)[e]=[])}function mg(t,e){for(var n=0;n<t.length;n++)if(t[n].key===e)return t[n].value}function vg(t,e,n){return t.push({key:e,value:n}),n}function yg(t,e,n){var i=Gp(t),r=t.scale,o=r.getExtent(),a=t.getLabelModel(),s=[],l=Math.max((e||0)+1,1),u=o[0],h=r.count();0!==u&&1<l&&2<h/l&&(u=Math.round(Math.ceil(u/l)*l));var c=Xp(t),d=a.get("showMinLabel")||c,f=a.get("showMaxLabel")||c;d&&u!==o[0]&&g(o[0]);for(var p=u;p<=o[1];p+=l)g(p);function g(t){s.push(n?t:{formattedLabel:i(t),rawLabel:r.getLabel(t),tickValue:t})}return f&&p-l!==o[1]&&g(o[1]),s}function _g(t,n,i){var r=t.scale,o=Gp(t),a=[];return D(r.getTicks(),function(t){var e=r.getLabel(t);n(t,e)&&a.push(i?t:{formattedLabel:o(t),rawLabel:e,tickValue:t})}),a}function xg(t,e,n){this.dim=t,this.scale=e,this._extent=n||[0,0],this.inverse=!1,this.onBand=!1}var wg=[0,1];function bg(t,e){var n=(t[1]-t[0])/e/2;t[0]+=n,t[1]-=n}xg.prototype={constructor:xg,contain:function(t){var e=this._extent,n=Math.min(e[0],e[1]),i=Math.max(e[0],e[1]);return n<=t&&t<=i},containData:function(t){return this.scale.contain(t)},getExtent:function(){return this._extent.slice()},getPixelPrecision:function(t){return Pl(t||this.scale.getExtent(),this._extent)},setExtent:function(t,e){var n=this._extent;n[0]=t,n[1]=e},dataToCoord:function(t,e){var n=this._extent,i=this.scale;return t=i.normalize(t),this.onBand&&"ordinal"===i.type&&bg(n=n.slice(),i.count()),Il(t,wg,n,e)},coordToData:function(t,e){var n=this._extent,i=this.scale;this.onBand&&"ordinal"===i.type&&bg(n=n.slice(),i.count());var r=Il(t,n,wg,e);return this.scale.scale(r)},pointToData:function(t,e){},getTicksCoords:function(t){var e=(t=t||{}).tickModel||this.getTickModel(),n=P(fg(this,e).ticks,function(t){return{coord:this.dataToCoord(t),tickValue:t}},this);return function(t,e,n,i){var r=e.length;if(!t.onBand||n||!r)return;var o,a,s=t.getExtent();if(1===r)e[0].coord=s[0],o=e[1]={coord:s[0]};else{var l=e[r-1].tickValue-e[0].tickValue,u=(e[r-1].coord-e[0].coord)/l;D(e,function(t){t.coord-=u/2});var h=t.scale.getExtent();a=1+h[1]-e[r-1].tickValue,o={coord:e[r-1].coord+u*a},e.push(o)}var c=s[0]>s[1];d(e[0].coord,s[0])&&(i?e[0].coord=s[0]:e.shift());i&&d(s[0],e[0].coord)&&e.unshift({coord:s[0]});d(s[1],o.coord)&&(i?o.coord=s[1]:e.pop());i&&d(o.coord,s[1])&&e.push({coord:s[1]});function d(t,e){return t=Tl(t),e=Tl(e),c?e<t:t<e}}(this,n,e.get("alignWithLabel"),t.clamp),n},getMinorTicksCoords:function(){if("ordinal"===this.scale.type)return[];var t=this.model.getModel("minorTick").get("splitNumber");return 0<t&&t<100||(t=5),P(this.scale.getMinorTicks(t),function(t){return P(t,function(t){return{coord:this.dataToCoord(t),tickValue:t}},this)},this)},getViewLabels:function(){return dg(this).labels},getLabelModel:function(){return this.model.getModel("axisLabel")},getTickModel:function(){return this.model.getModel("axisTick")},getBandWidth:function(){var t=this._extent,e=this.scale.getExtent(),n=e[1]-e[0]+(this.onBand?1:0);0===n&&(n=1);var i=Math.abs(t[1]-t[0]);return Math.abs(i)/n},isHorizontal:null,getRotate:null,calculateCategoryInterval:function(){return function(t){var e=function(t){var e=t.getLabelModel();return{axisRotate:t.getRotate?t.getRotate():t.isHorizontal&&!t.isHorizontal()?90:0,labelRotate:e.get("rotate")||0,font:e.getFont()}}(t),n=Gp(t),i=(e.axisRotate-e.labelRotate)/180*Math.PI,r=t.scale,o=r.getExtent(),a=r.count();if(o[1]-o[0]<1)return 0;var s=1;40<a&&(s=Math.max(1,Math.floor(a/40)));for(var l=o[0],u=t.dataToCoord(l+1)-t.dataToCoord(l),h=Math.abs(u*Math.cos(i)),c=Math.abs(u*Math.sin(i)),d=0,f=0;l<=o[1];l+=s){var p,g,m=ui(n(l),e.font,"center","top");p=1.3*m.width,g=1.3*m.height,d=Math.max(d,p,7),f=Math.max(f,g,7)}var v=d/h,y=f/c;isNaN(v)&&(v=1/0),isNaN(y)&&(y=1/0);var _=Math.max(0,Math.floor(Math.min(v,y))),x=cg(t.model),w=t.getExtent(),b=x.lastAutoInterval,S=x.lastTickCount;return null!=b&&null!=S&&Math.abs(b-_)<=1&&Math.abs(S-a)<=1&&_<b&&x.axisExtend0===w[0]&&x.axisExtend1===w[1]?_=b:(x.lastTickCount=a,x.lastAutoInterval=_,x.axisExtend0=w[0],x.axisExtend1=w[1]),_}(this)}};var Sg=hg,Mg={};D(["map","each","filter","indexOf","inherits","reduce","filter","bind","curry","isArray","isString","isObject","isFunction","extend","defaults","clone","merge"],function(t){Mg[t]=tt[t]});var Ig={};function Cg(t,e){var n=t.mapDimension("defaultedLabel",!0),i=n.length;if(1===i)return Dh(t,e,n[0]);if(i){for(var r=[],o=0;o<n.length;o++){var a=Dh(t,e,n[o]);r.push(a)}return r.join(" ")}}function Tg(t,e,n){Mn.call(this),this.updateData(t,e,n)}D(["extendShape","extendPath","makePath","makeImage","mergePath","resizePath","createIcon","setHoverStyle","setLabelStyle","setTextStyle","setText","getFont","updateProps","initProps","getTransform","clipPointsByRect","clipRectByRect","registerShape","getShapeClass","Group","Image","Text","Circle","Sector","Ring","Polygon","Polyline","Rect","Line","BezierCurve","Arc","IncrementalDisplayable","CompoundPath","LinearGradient","RadialGradient","BoundingRect"],function(t){Ig[t]=dl[t]}),Yh.extend({type:"series.line",dependencies:["grid","polar"],getInitialData:function(t,e){return Qf(this.getSource(),this,{useEncodeDefaulter:!0})},defaultOption:{zlevel:0,z:2,coordinateSystem:"cartesian2d",legendHoverLink:!0,hoverAnimation:!0,clip:!0,label:{position:"top"},lineStyle:{width:2,type:"solid"},step:!1,smooth:!1,smoothMonotone:null,symbol:"emptyCircle",symbolSize:4,symbolRotate:null,showSymbol:!0,showAllSymbol:"auto",connectNulls:!1,sampling:"none",animationEasing:"linear",progressive:0,hoverLayerThreshold:1/0}});var Ag=Tg.prototype,Dg=Tg.getSymbolSize=function(t,e){var n=t.getItemVisual(e,"symbolSize");return n instanceof Array?n.slice():[+n,+n]};function kg(t){return[t[0]/2,t[1]/2]}function Pg(t,e){this.parent.drift(t,e)}Ag._createSymbol=function(t,e,n,i,r){this.removeAll();var o=ng(t,-1,-1,2,2,e.getItemVisual(n,"color"),r);o.attr({z2:100,culling:!0,scale:kg(i)}),o.drift=Pg,this._symbolType=t,this.add(o)},Ag.stopSymbolAnimation=function(t){this.childAt(0).stopAnimation(t)},Ag.getSymbolPath=function(){return this.childAt(0)},Ag.getScale=function(){return this.childAt(0).scale},Ag.highlight=function(){this.childAt(0).trigger("emphasis")},Ag.downplay=function(){this.childAt(0).trigger("normal")},Ag.setZ=function(t,e){var n=this.childAt(0);n.zlevel=t,n.z=e},Ag.setDraggable=function(t){var e=this.childAt(0);e.draggable=t,e.cursor=t?"move":e.cursor},Ag.updateData=function(t,e,n){this.silent=!1;var i=t.getItemVisual(e,"symbol")||"circle",r=t.hostModel,o=Dg(t,e),a=i!==this._symbolType;if(a){var s=t.getItemVisual(e,"symbolKeepAspect");this._createSymbol(i,t,e,o,s)}else{(l=this.childAt(0)).silent=!1,nl(l,{scale:kg(o)},r,e)}if(this._updateCommon(t,e,o,n),a){var l=this.childAt(0),u=n&&n.fadeIn,h={scale:l.scale.slice()};u&&(h.style={opacity:l.style.opacity}),l.scale=[0,0],u&&(l.style.opacity=0),il(l,h,r,e)}this._seriesModel=r};var Lg=["itemStyle"],Og=["emphasis","itemStyle"],Eg=["label"],zg=["emphasis","label"];function Ng(t,e){if(!this.incremental&&!this.useHoverLayer)if("emphasis"===e){var n=this.__symbolOriginalScale,i=n[1]/n[0],r={scale:[Math.max(1.1*n[0],n[0]+3),Math.max(1.1*n[1],n[1]+3*i)]};this.animateTo(r,400,"elasticOut")}else"normal"===e&&this.animateTo({scale:this.__symbolOriginalScale},400,"elasticOut")}function Rg(t){this.group=new Mn,this._symbolCtor=t||Tg}Ag._updateCommon=function(n,t,e,i){var r=this.childAt(0),o=n.hostModel,a=n.getItemVisual(t,"color");"image"!==r.type?r.useStyle({strokeNoScale:!0}):r.setStyle({opacity:null,shadowBlur:null,shadowOffsetX:null,shadowOffsetY:null,shadowColor:null});var s=i&&i.itemStyle,l=i&&i.hoverItemStyle,u=i&&i.symbolOffset,h=i&&i.labelModel,c=i&&i.hoverLabelModel,d=i&&i.hoverAnimation,f=i&&i.cursorStyle;if(!i||n.hasItemOption){var p=i&&i.itemModel?i.itemModel:n.getItemModel(t);s=p.getModel(Lg).getItemStyle(["color"]),l=p.getModel(Og).getItemStyle(),u=p.getShallow("symbolOffset"),h=p.getModel(Eg),c=p.getModel(zg),d=p.getShallow("hoverAnimation"),f=p.getShallow("cursor")}else l=k({},l);var g=r.style,m=n.getItemVisual(t,"symbolRotate");r.attr("rotation",(m||0)*Math.PI/180||0),u&&r.attr("position",[Cl(u[0],e[0]),Cl(u[1],e[1])]),f&&r.attr("cursor",f),r.setColor(a,i&&i.symbolInnerColor),r.setStyle(s);var v=n.getItemVisual(t,"opacity");null!=v&&(g.opacity=v);var y=n.getItemVisual(t,"liftZ"),_=r.__z2Origin;null!=y?null==_&&(r.__z2Origin=r.z2,r.z2+=y):null!=_&&(r.z2=_,r.__z2Origin=null);var x=i&&i.useNameLabel;Ys(g,l,h,c,{labelFetcher:o,labelDataIndex:t,defaultText:function(t,e){return x?n.getName(t):Cg(n,t)},isRectText:!0,autoColor:a}),r.__symbolOriginalScale=kg(e),r.hoverStyle=l,r.highDownOnUpdate=d&&o.isAnimationEnabled()?Ng:null,Gs(r)},Ag.fadeOut=function(t,e){var n=this.childAt(0);this.silent=n.silent=!0,e&&e.keepLabel||(n.style.text=null),nl(n,{style:{opacity:0},scale:[0,0]},this._seriesModel,this.dataIndex,t)},w(Tg,Mn);var Bg=Rg.prototype;function Vg(t,e,n,i){return e&&!isNaN(e[0])&&!isNaN(e[1])&&!(i.isIgnore&&i.isIgnore(n))&&!(i.clipShape&&!i.clipShape.contain(e[0],e[1]))&&"none"!==t.getItemVisual(n,"symbol")}function Fg(t){return null==t||N(t)||(t={isIgnore:t}),t||{}}function Hg(t){var e=t.hostModel;return{itemStyle:e.getModel("itemStyle").getItemStyle(["color"]),hoverItemStyle:e.getModel("emphasis.itemStyle").getItemStyle(),symbolRotate:e.get("symbolRotate"),symbolOffset:e.get("symbolOffset"),hoverAnimation:e.get("hoverAnimation"),labelModel:e.getModel("label"),hoverLabelModel:e.getModel("emphasis.label"),cursorStyle:e.get("cursor")}}function Wg(t,e,n){var i,r=t.getBaseAxis(),o=t.getOtherAxis(r),a=function(t,e){var n=0,i=t.scale.getExtent();"start"===e?n=i[0]:"end"===e?n=i[1]:0<i[0]?n=i[0]:i[1]<0&&(n=i[1]);return n}(o,n),s=r.dim,l=o.dim,u=e.mapDimension(l),h=e.mapDimension(s),c="x"===l||"radius"===l?1:0,d=P(t.dimensions,function(t){return e.mapDimension(t)}),f=e.getCalculationInfo("stackResultDimension");return(i|=$f(e,d[0]))&&(d[0]=f),(i|=$f(e,d[1]))&&(d[1]=f),{dataDimsForPoint:d,valueStart:a,valueAxisDim:l,baseAxisDim:s,stacked:!!i,valueDim:u,baseDim:h,baseDataOffset:c,stackedOverDimension:e.getCalculationInfo("stackedOverDimension")}}function Gg(t,e,n,i){var r=NaN;t.stacked&&(r=n.get(n.getCalculationInfo("stackedOverDimension"),i)),isNaN(r)&&(r=t.valueStart);var o=t.baseDataOffset,a=[];return a[o]=n.get(t.baseDim,i),a[1-o]=r,e.dataToPoint(a)}Bg.updateData=function(r,o){o=Fg(o);var a=this.group,s=r.hostModel,l=this._data,u=this._symbolCtor,h=Hg(r);l||a.removeAll(),r.diff(l).add(function(t){var e=r.getItemLayout(t);if(Vg(r,e,t,o)){var n=new u(r,t,h);n.attr("position",e),r.setItemGraphicEl(t,n),a.add(n)}}).update(function(t,e){var n=l.getItemGraphicEl(e),i=r.getItemLayout(t);Vg(r,i,t,o)?(n?(n.updateData(r,t,h),nl(n,{position:i},s)):(n=new u(r,t)).attr("position",i),a.add(n),r.setItemGraphicEl(t,n)):a.remove(n)}).remove(function(t){var e=l.getItemGraphicEl(t);e&&e.fadeOut(function(){a.remove(e)})}).execute(),this._data=r},Bg.isPersistent=function(){return!0},Bg.updateLayout=function(){var i=this._data;i&&i.eachItemGraphicEl(function(t,e){var n=i.getItemLayout(e);t.attr("position",n)})},Bg.incrementalPrepareUpdate=function(t){this._seriesScope=Hg(t),this._data=null,this.group.removeAll()},Bg.incrementalUpdate=function(t,e,n){function i(t){t.isGroup||(t.incremental=t.useHoverLayer=!0)}n=Fg(n);for(var r=t.start;r<t.end;r++){var o=e.getItemLayout(r);if(Vg(e,o,r,n)){var a=new this._symbolCtor(e,r,this._seriesScope);a.traverse(i),a.attr("position",o),this.group.add(a),e.setItemGraphicEl(r,a)}}},Bg.remove=function(t){var e=this.group,n=this._data;n&&t?n.eachItemGraphicEl(function(t){t.fadeOut(function(){e.remove(t)})}):e.removeAll()};var Zg=_t,Ug=xt,Xg=at,Yg=it,jg=[],qg=[],$g=[];function Kg(t){return isNaN(t[0])||isNaN(t[1])}function Qg(t,e,n,i,r,o,a,s,l,u){return"none"!==u&&u?function(t,e,n,i,r,o,a,s,l,u,h){for(var c=0,d=n,f=0;f<i;f++){var p=e[d];if(r<=d||d<0)break;if(Kg(p)){if(h){d+=o;continue}break}if(d===n)t[0<o?"moveTo":"lineTo"](p[0],p[1]);else if(0<l){var g=e[c],m="y"===u?1:0,v=(p[m]-g[m])*l;Yg(qg,g),qg[m]=g[m]+v,Yg($g,p),$g[m]=p[m]-v,t.bezierCurveTo(qg[0],qg[1],$g[0],$g[1],p[0],p[1])}else t.lineTo(p[0],p[1]);c=d,d+=o}return f}.apply(this,arguments):function(t,e,n,i,r,o,a,s,l,u,h){for(var c=0,d=n,f=0;f<i;f++){var p=e[d];if(r<=d||d<0)break;if(Kg(p)){if(h){d+=o;continue}break}if(d===n)t[0<o?"moveTo":"lineTo"](p[0],p[1]),Yg(qg,p);else if(0<l){var g=d+o,m=e[g];if(h)for(;m&&Kg(e[g]);)m=e[g+=o];var v=.5,y=e[c];if(!(m=e[g])||Kg(m))Yg($g,p);else{var _,x;if(Kg(m)&&!h&&(m=p),st(jg,m,y),"x"===u||"y"===u){var w="x"===u?0:1;_=Math.abs(p[w]-y[w]),x=Math.abs(p[w]-m[w])}else _=gt(p,y),x=gt(p,m);Xg($g,p,jg,-l*(1-(v=x/(x+_))))}Zg(qg,qg,s),Ug(qg,qg,a),Zg($g,$g,s),Ug($g,$g,a),t.bezierCurveTo(qg[0],qg[1],$g[0],$g[1],p[0],p[1]),Xg(qg,p,jg,l*v)}else t.lineTo(p[0],p[1]);c=d,d+=o}return f}.apply(this,arguments)}function Jg(t,e){var n=[1/0,1/0],i=[-1/0,-1/0];if(e)for(var r=0;r<t.length;r++){var o=t[r];o[0]<n[0]&&(n[0]=o[0]),o[1]<n[1]&&(n[1]=o[1]),o[0]>i[0]&&(i[0]=o[0]),o[1]>i[1]&&(i[1]=o[1])}return{min:e?n:i,max:e?i:n}}var tm=xa.extend({type:"ec-polyline",shape:{points:[],smooth:0,smoothConstraint:!0,smoothMonotone:null,connectNulls:!1},style:{fill:null,stroke:"#000"},brush:Va(xa.prototype.brush),buildPath:function(t,e){var n=e.points,i=0,r=n.length,o=Jg(n,e.smoothConstraint);if(e.connectNulls){for(;0<r&&Kg(n[r-1]);r--);for(;i<r&&Kg(n[i]);i++);}for(;i<r;)i+=Qg(t,n,i,r,r,1,o.min,o.max,e.smooth,e.smoothMonotone,e.connectNulls)+1}}),em=xa.extend({type:"ec-polygon",shape:{points:[],stackedOnPoints:[],smooth:0,stackedOnSmooth:0,smoothConstraint:!0,smoothMonotone:null,connectNulls:!1},brush:Va(xa.prototype.brush),buildPath:function(t,e){var n=e.points,i=e.stackedOnPoints,r=0,o=n.length,a=e.smoothMonotone,s=Jg(n,e.smoothConstraint),l=Jg(i,e.smoothConstraint);if(e.connectNulls){for(;0<o&&Kg(n[o-1]);o--);for(;r<o&&Kg(n[r]);r++);}for(;r<o;){var u=Qg(t,n,r,o,o,1,s.min,s.max,e.smooth,a,e.connectNulls);Qg(t,i,r+u-1,u,o,-1,l.min,l.max,e.stackedOnSmooth,a,e.connectNulls),r+=u+1,t.closePath()}}});function nm(t,e,n){var i=t.getArea(),r=t.getBaseAxis().isHorizontal(),o=i.x,a=i.y,s=i.width,l=i.height,u=n.get("lineStyle.width")||2;o-=u/2,a-=u/2,s+=u,l+=u,o=Math.floor(o),s=Math.round(s);var h=new Ja({shape:{x:o,y:a,width:s,height:l}});return e&&(h.shape[r?"width":"height"]=0,il(h,{shape:{width:s,height:l}},n)),h}function im(t,e,n){var i=t.getArea(),r=new Wa({shape:{cx:Tl(t.cx,1),cy:Tl(t.cy,1),r0:Tl(i.r0,1),r:Tl(i.r,1),startAngle:i.startAngle,endAngle:i.endAngle,clockwise:i.clockwise}});return e&&(r.shape.endAngle=i.startAngle,il(r,{shape:{endAngle:i.endAngle}},n)),r}function rm(t,e){if(t.length===e.length){for(var n=0;n<t.length;n++){var i=t[n],r=e[n];if(i[0]!==r[0]||i[1]!==r[1])return}return!0}}function om(t,e){var n=[],i=[],r=[],o=[];return zo(t,n,i),zo(e,r,o),Math.max(Math.abs(n[0]-r[0]),Math.abs(n[1]-r[1]),Math.abs(i[0]-o[0]),Math.abs(i[1]-o[1]))}function am(t){return"number"==typeof t?t:t?.5:0}function sm(t,e,n){for(var i=e.getBaseAxis(),r="x"===i.dim||"radius"===i.dim?0:1,o=[],a=0;a<t.length-1;a++){var s=t[a+1],l=t[a];o.push(l);var u=[];switch(n){case"end":u[r]=s[r],u[1-r]=l[1-r],o.push(u);break;case"middle":var h=(l[r]+s[r])/2,c=[];u[r]=c[r]=h,u[1-r]=l[1-r],c[1-r]=s[1-r],o.push(u),o.push(c);break;default:u[r]=l[r],u[1-r]=s[1-r],o.push(u)}}return t[a]&&o.push(t[a]),o}function lm(t,e,n){var i=t.get("showAllSymbol"),r="auto"===i;if(!i||r){var o=n.getAxesByScale("ordinal")[0];if(o&&(!r||!function(t,e){var n=t.getExtent(),i=Math.abs(n[1]-n[0])/t.scale.count();isNaN(i)&&(i=0);for(var r=e.count(),o=Math.max(1,Math.round(r/5)),a=0;a<r;a+=o)if(1.5*Tg.getSymbolSize(e,a)[t.isHorizontal()?1:0]>i)return!1;return!0}(o,e))){var a=e.mapDimension(o.dim),s={};return D(o.getViewLabels(),function(t){s[t.tickValue]=1}),function(t){return!s.hasOwnProperty(e.get(a,t))}}}}function um(t,e,n){if("cartesian2d"!==t.type)return im(t,e,n);var i=t.getBaseAxis().isHorizontal(),r=nm(t,e,n);if(!n.get("clip",!0)){var o=r.shape,a=Math.max(o.width,o.height);i?(o.y-=a,o.height+=2*a):(o.x-=a,o.width+=2*a)}return r}ac.extend({type:"line",init:function(){var t=new Mn,e=new Rg;this.group.add(e.group),this._symbolDraw=e,this._lineGroup=t},render:function(t,e,n){var i=t.coordinateSystem,r=this.group,o=t.getData(),a=t.getModel("lineStyle"),s=t.getModel("areaStyle"),l=o.mapArray(o.getItemLayout),u="polar"===i.type,h=this._coordSys,c=this._symbolDraw,d=this._polyline,f=this._polygon,p=this._lineGroup,g=t.get("animation"),m=!s.isEmpty(),v=s.get("origin"),y=function(t,e,n){if(!n.valueDim)return[];for(var i=[],r=0,o=e.count();r<o;r++)i.push(Gg(n,t,e,r));return i}(i,o,Wg(i,o,v)),_=t.get("showSymbol"),x=_&&!u&&lm(t,o,i),w=this._data;w&&w.eachItemGraphicEl(function(t,e){t.__temp&&(r.remove(t),w.setItemGraphicEl(e,null))}),_||c.remove(),r.add(p);var b,S=!u&&t.get("step");i&&i.getArea&&t.get("clip",!0)&&(null!=(b=i.getArea()).width?(b.x-=.1,b.y-=.1,b.width+=.2,b.height+=.2):b.r0&&(b.r0-=.5,b.r1+=.5)),this._clipShapeForSymbol=b,d&&h.type===i.type&&S===this._step?(m&&!f?f=this._newPolygon(l,y,i,g):f&&!m&&(p.remove(f),f=this._polygon=null),p.setClipPath(um(i,!1,t)),_&&c.updateData(o,{isIgnore:x,clipShape:b}),o.eachItemGraphicEl(function(t){t.stopAnimation(!0)}),rm(this._stackedOnPoints,y)&&rm(this._points,l)||(g?this._updateAnimation(o,y,i,n,S,v):(S&&(l=sm(l,i,S),y=sm(y,i,S)),d.setShape({points:l}),f&&f.setShape({points:l,stackedOnPoints:y})))):(_&&c.updateData(o,{isIgnore:x,clipShape:b}),S&&(l=sm(l,i,S),y=sm(y,i,S)),d=this._newPolyline(l,i,g),m&&(f=this._newPolygon(l,y,i,g)),p.setClipPath(um(i,!0,t)));var M=function(t,e){var n=t.getVisual("visualMeta");if(n&&n.length&&t.count()&&"cartesian2d"===e.type){for(var i,r,o=n.length-1;0<=o;o--){var a=n[o].dimension,s=t.dimensions[a],l=t.getDimensionInfo(s);if("x"===(i=l&&l.coordDim)||"y"===i){r=n[o];break}}if(r){var u=e.getAxis(i),h=P(r.stops,function(t){return{coord:u.toGlobalCoord(u.dataToCoord(t.value)),color:t.color}}),c=h.length,d=r.outerColors.slice();c&&h[0].coord>h[c-1].coord&&(h.reverse(),d.reverse());var f=h[0].coord-10,p=h[c-1].coord+10,g=p-f;if(g<.001)return"transparent";D(h,function(t){t.offset=(t.coord-f)/g}),h.push({offset:c?h[c-1].offset:.5,color:d[1]||"transparent"}),h.unshift({offset:c?h[0].offset:.5,color:d[0]||"transparent"});var m=new ls(0,0,0,0,h,!0);return m[i]=f,m[i+"2"]=p,m}}}(o,i)||o.getVisual("color");d.useStyle(A(a.getLineStyle(),{fill:"none",stroke:M,lineJoin:"bevel"}));var I=t.get("smooth");if(I=am(t.get("smooth")),d.setShape({smooth:I,smoothMonotone:t.get("smoothMonotone"),connectNulls:t.get("connectNulls")}),f){var C=o.getCalculationInfo("stackedOnSeries"),T=0;f.useStyle(A(s.getAreaStyle(),{fill:M,opacity:.7,lineJoin:"bevel"})),C&&(T=am(C.get("smooth"))),f.setShape({smooth:I,stackedOnSmooth:T,smoothMonotone:t.get("smoothMonotone"),connectNulls:t.get("connectNulls")})}this._data=o,this._coordSys=i,this._stackedOnPoints=y,this._points=l,this._step=S,this._valueOrigin=v},dispose:function(){},highlight:function(t,e,n,i){var r=t.getData(),o=Fr(r,i);if(!(o instanceof Array)&&null!=o&&0<=o){var a=r.getItemGraphicEl(o);if(!a){var s=r.getItemLayout(o);if(!s)return;if(this._clipShapeForSymbol&&!this._clipShapeForSymbol.contain(s[0],s[1]))return;(a=new Tg(r,o)).position=s,a.setZ(t.get("zlevel"),t.get("z")),a.ignore=isNaN(s[0])||isNaN(s[1]),a.__temp=!0,r.setItemGraphicEl(o,a),a.stopSymbolAnimation(!0),this.group.add(a)}a.highlight()}else ac.prototype.highlight.call(this,t,e,n,i)},downplay:function(t,e,n,i){var r=t.getData(),o=Fr(r,i);if(null!=o&&0<=o){var a=r.getItemGraphicEl(o);a&&(a.__temp?(r.setItemGraphicEl(o,null),this.group.remove(a)):a.downplay())}else ac.prototype.downplay.call(this,t,e,n,i)},_newPolyline:function(t){var e=this._polyline;return e&&this._lineGroup.remove(e),e=new tm({shape:{points:t},silent:!0,z2:10}),this._lineGroup.add(e),this._polyline=e},_newPolygon:function(t,e){var n=this._polygon;return n&&this._lineGroup.remove(n),n=new em({shape:{points:t,stackedOnPoints:e},silent:!0}),this._lineGroup.add(n),this._polygon=n},_updateAnimation:function(t,e,n,i,r,o){var a=this._polyline,s=this._polygon,l=t.hostModel,u=function(t,e,n,i,r,o,a,s){for(var l=function(t,e){var n=[];return e.diff(t).add(function(t){n.push({cmd:"+",idx:t})}).update(function(t,e){n.push({cmd:"=",idx:e,idx1:t})}).remove(function(t){n.push({cmd:"-",idx:t})}).execute(),n}(t,e),u=[],h=[],c=[],d=[],f=[],p=[],g=[],m=Wg(r,e,a),v=Wg(o,t,s),y=0;y<l.length;y++){var _=l[y],x=!0;switch(_.cmd){case"=":var w=t.getItemLayout(_.idx),b=e.getItemLayout(_.idx1);(isNaN(w[0])||isNaN(w[1]))&&(w=b.slice()),u.push(w),h.push(b),c.push(n[_.idx]),d.push(i[_.idx1]),g.push(e.getRawIndex(_.idx1));break;case"+":var S=_.idx;u.push(r.dataToPoint([e.get(m.dataDimsForPoint[0],S),e.get(m.dataDimsForPoint[1],S)])),h.push(e.getItemLayout(S).slice()),c.push(Gg(m,r,e,S)),d.push(i[S]),g.push(e.getRawIndex(S));break;case"-":S=_.idx;var M=t.getRawIndex(S);M!==S?(u.push(t.getItemLayout(S)),h.push(o.dataToPoint([t.get(v.dataDimsForPoint[0],S),t.get(v.dataDimsForPoint[1],S)])),c.push(n[S]),d.push(Gg(v,o,t,S)),g.push(M)):x=!1}x&&(f.push(_),p.push(p.length))}p.sort(function(t,e){return g[t]-g[e]});var I=[],C=[],T=[],A=[],D=[];for(y=0;y<p.length;y++){S=p[y];I[y]=u[S],C[y]=h[S],T[y]=c[S],A[y]=d[S],D[y]=f[S]}return{current:I,next:C,stackedOnCurrent:T,stackedOnNext:A,status:D}}(this._data,t,this._stackedOnPoints,e,this._coordSys,n,this._valueOrigin,o),h=u.current,c=u.stackedOnCurrent,d=u.next,f=u.stackedOnNext;if(r&&(h=sm(u.current,n,r),c=sm(u.stackedOnCurrent,n,r),d=sm(u.next,n,r),f=sm(u.stackedOnNext,n,r)),3e3<om(h,d)||s&&3e3<om(c,f))return a.setShape({points:d}),void(s&&s.setShape({points:d,stackedOnPoints:f}));a.shape.__points=u.current,a.shape.points=h,nl(a,{shape:{points:d}},l),s&&(s.setShape({points:h,stackedOnPoints:c}),nl(s,{shape:{points:d,stackedOnPoints:f}},l));for(var p=[],g=u.status,m=0;m<g.length;m++){if("="===g[m].cmd){var v=t.getItemGraphicEl(g[m].idx1);v&&p.push({el:v,ptIdx:m})}}a.animators&&a.animators.length&&a.animators[0].during(function(){for(var t=0;t<p.length;t++){p[t].el.attr("position",a.shape.__points[p[t].ptIdx])}})},remove:function(t){var n=this.group,i=this._data;this._lineGroup.removeAll(),this._symbolDraw.remove(!0),i&&i.eachItemGraphicEl(function(t,e){t.__temp&&(n.remove(t),i.setItemGraphicEl(e,null))}),this._polyline=this._polygon=this._coordSys=this._points=this._stackedOnPoints=this._data=null}});function hm(t,a,s){return{seriesType:t,performRawSeries:!0,reset:function(u,t,e){var n=u.getData(),h=u.get("symbol"),c=u.get("symbolSize"),i=u.get("symbolKeepAspect"),d=u.get("symbolRotate"),f=E(h),p=E(c),g=E(d),m=f||p||g,r=!f&&h?h:a,o=p?null:c;if(n.setVisual({legendSymbol:s||r,symbol:r,symbolSize:o,symbolKeepAspect:i,symbolRotate:d}),!t.isSeriesFiltered(u))return{dataEach:n.hasItemOption||m?function(t,e){if(m){var n=u.getRawValue(e),i=u.getDataParams(e);f&&t.setItemVisual(e,"symbol",h(n,i)),p&&t.setItemVisual(e,"symbolSize",c(n,i)),g&&t.setItemVisual(e,"symbolRotate",d(n,i))}if(t.hasItemOption){var r=t.getItemModel(e),o=r.getShallow("symbol",!0),a=r.getShallow("symbolSize",!0),s=r.getShallow("symbolRotate",!0),l=r.getShallow("symbolKeepAspect",!0);null!=o&&t.setItemVisual(e,"symbol",o),null!=a&&t.setItemVisual(e,"symbolSize",a),null!=s&&t.setItemVisual(e,"symbolRotate",s),null!=l&&t.setItemVisual(e,"symbolKeepAspect",l)}}:null}}}}function cm(t){return{seriesType:t,plan:ic(),reset:function(t){var e=t.getData(),c=t.coordinateSystem,d=t.pipelineContext.large;if(c){var f=P(c.dimensions,function(t){return e.mapDimension(t)}).slice(0,2),p=f.length,n=e.getCalculationInfo("stackResultDimension");return $f(e,f[0])&&(f[0]=n),$f(e,f[1])&&(f[1]=n),p&&{progress:function(t,e){for(var n=t.end-t.start,i=d&&new Float32Array(n*p),r=t.start,o=0,a=[],s=[];r<t.end;r++){var l;if(1===p){var u=e.get(f[0],r);l=!isNaN(u)&&c.dataToPoint(u,null,s)}else{u=a[0]=e.get(f[0],r);var h=a[1]=e.get(f[1],r);l=!isNaN(u)&&!isNaN(h)&&c.dataToPoint(a,null,s)}d?(i[o++]=l?l[0]:NaN,i[o++]=l?l[1]:NaN):e.setItemLayout(r,l&&l.slice()||[NaN,NaN])}d&&e.setLayout("symbolPoints",i)}}}}}}function dm(t,e){return Math.round(t.length/2)}var fm={average:function(t){for(var e=0,n=0,i=0;i<t.length;i++)isNaN(t[i])||(e+=t[i],n++);return 0===n?NaN:e/n},sum:function(t){for(var e=0,n=0;n<t.length;n++)e+=t[n]||0;return e},max:function(t){for(var e=-1/0,n=0;n<t.length;n++)t[n]>e&&(e=t[n]);return isFinite(e)?e:NaN},min:function(t){for(var e=1/0,n=0;n<t.length;n++)t[n]<e&&(e=t[n]);return isFinite(e)?e:NaN},nearest:function(t){return t[0]}};function pm(t){return this._axes[t]}function gm(t){this._axes={},this._dimList=[],this.name=t||""}function mm(t){gm.call(this,t)}gm.prototype={constructor:gm,type:"cartesian",getAxis:function(t){return this._axes[t]},getAxes:function(){return P(this._dimList,pm,this)},getAxesByScale:function(e){return e=e.toLowerCase(),I(this.getAxes(),function(t){return t.scale.type===e})},addAxis:function(t){var e=t.dim;this._axes[e]=t,this._dimList.push(e)},dataToCoord:function(t){return this._dataCoordConvert(t,"dataToCoord")},coordToData:function(t){return this._dataCoordConvert(t,"coordToData")},_dataCoordConvert:function(t,e){for(var n=this._dimList,i=t instanceof Array?[]:{},r=0;r<n.length;r++){var o=n[r],a=this._axes[o];i[o]=a[e](t[o])}return i}},mm.prototype={constructor:mm,type:"cartesian2d",dimensions:["x","y"],getBaseAxis:function(){return this.getAxesByScale("ordinal")[0]||this.getAxesByScale("time")[0]||this.getAxis("x")},containPoint:function(t){var e=this.getAxis("x"),n=this.getAxis("y");return e.contain(e.toLocalCoord(t[0]))&&n.contain(n.toLocalCoord(t[1]))},containData:function(t){return this.getAxis("x").containData(t[0])&&this.getAxis("y").containData(t[1])},dataToPoint:function(t,e,n){var i=this.getAxis("x"),r=this.getAxis("y");return(n=n||[])[0]=i.toGlobalCoord(i.dataToCoord(t[0])),n[1]=r.toGlobalCoord(r.dataToCoord(t[1])),n},clampData:function(t,e){var n=this.getAxis("x").scale,i=this.getAxis("y").scale,r=n.getExtent(),o=i.getExtent(),a=n.parse(t[0]),s=i.parse(t[1]);return(e=e||[])[0]=Math.min(Math.max(Math.min(r[0],r[1]),a),Math.max(r[0],r[1])),e[1]=Math.min(Math.max(Math.min(o[0],o[1]),s),Math.max(o[0],o[1])),e},pointToData:function(t,e){var n=this.getAxis("x"),i=this.getAxis("y");return(e=e||[])[0]=n.coordToData(n.toLocalCoord(t[0])),e[1]=i.coordToData(i.toLocalCoord(t[1])),e},getOtherAxis:function(t){return this.getAxis("x"===t.dim?"y":"x")},getArea:function(){var t=this.getAxis("x").getGlobalExtent(),e=this.getAxis("y").getGlobalExtent(),n=Math.min(t[0],t[1]),i=Math.min(e[0],e[1]);return new Sn(n,i,Math.max(t[0],t[1])-n,Math.max(e[0],e[1])-i)}},w(mm,gm);function vm(t,e,n,i,r){xg.call(this,t,e,n),this.type=i||"value",this.position=r||"bottom"}vm.prototype={constructor:vm,index:0,getAxesOnZeroOf:null,model:null,isHorizontal:function(){var t=this.position;return"top"===t||"bottom"===t},getGlobalExtent:function(t){var e=this.getExtent();return e[0]=this.toGlobalCoord(e[0]),e[1]=this.toGlobalCoord(e[1]),t&&e[0]>e[1]&&e.reverse(),e},getOtherAxis:function(){this.grid.getOtherAxis()},pointToData:function(t,e){return this.coordToData(this.toLocalCoord(t["x"===this.dim?0:1]),e)},toLocalCoord:null,toGlobalCoord:null},w(vm,xg);var ym={show:!0,zlevel:0,z:0,inverse:!1,name:"",nameLocation:"end",nameRotate:null,nameTruncate:{maxWidth:null,ellipsis:"...",placeholder:"."},nameTextStyle:{},nameGap:15,silent:!1,triggerEvent:!1,tooltip:{show:!1},axisPointer:{},axisLine:{show:!0,onZero:!0,onZeroAxisIndex:null,lineStyle:{color:"#333",width:1,type:"solid"},symbol:["none","none"],symbolSize:[10,15]},axisTick:{show:!0,inside:!1,length:5,lineStyle:{width:1}},axisLabel:{show:!0,inside:!1,rotate:0,showMinLabel:null,showMaxLabel:null,margin:8,fontSize:12},splitLine:{show:!0,lineStyle:{color:["#ccc"],width:1,type:"solid"}},splitArea:{show:!1,areaStyle:{color:["rgba(250,250,250,0.3)","rgba(200,200,200,0.3)"]}}},_m={};_m.categoryAxis=m({boundaryGap:!0,deduplication:null,splitLine:{show:!1},axisTick:{alignWithLabel:!1,interval:"auto"},axisLabel:{interval:"auto"}},ym),_m.valueAxis=m({boundaryGap:[0,0],splitNumber:5,minorTick:{show:!1,splitNumber:5,length:3,lineStyle:{}},minorSplitLine:{show:!1,lineStyle:{color:"#eee",width:1}}},ym),_m.timeAxis=A({scale:!0,min:"dataMin",max:"dataMax"},_m.valueAxis),_m.logAxis=A({scale:!0,logBase:10},_m.valueAxis);function xm(o,t,a,e){D(wm,function(r){t.extend({type:o+"Axis."+r,mergeDefaultAndTheme:function(t,e){var n=this.layoutMode,i=n?cu(t):{};m(t,e.getTheme().get(r+"Axis")),m(t,this.getDefaultOption()),t.type=a(o,t),n&&hu(t,i,n)},optionUpdated:function(){"category"===this.option.type&&(this.__ordinalMeta=tp.createByAxisModel(this))},getCategories:function(t){var e=this.option;if("category"===e.type)return t?e.data:this.__ordinalMeta.categories},getOrdinalMeta:function(){return this.__ordinalMeta},defaultOption:p([{},_m[r+"Axis"],e],!0)})}),vu.registerSubTypeDefaulter(o+"Axis",T(a,o))}var wm=["value","category","time","log"],bm=vu.extend({type:"cartesian2dAxis",axis:null,init:function(){bm.superApply(this,"init",arguments),this.resetRange()},mergeOption:function(){bm.superApply(this,"mergeOption",arguments),this.resetRange()},restoreData:function(){bm.superApply(this,"restoreData",arguments),this.resetRange()},getCoordSysModel:function(){return this.ecModel.queryComponents({mainType:"grid",index:this.option.gridIndex,id:this.option.gridId})[0]}});function Sm(t,e){return e.type||(e.data?"category":"value")}m(bm.prototype,Yp);var Mm={offset:0};function Im(t,e){return t.getCoordSysModel()===e}function Cm(t,e,n){this._coordsMap={},this._coordsList=[],this._axesMap={},this._axesList=[],this._initCartesian(t,e,n),this.model=t}xm("x",bm,Sm,Mm),xm("y",bm,Sm,Mm),vu.extend({type:"grid",dependencies:["xAxis","yAxis"],layoutMode:"box",coordinateSystem:null,defaultOption:{show:!1,zlevel:0,z:0,left:"10%",top:60,right:"10%",bottom:60,containLabel:!1,backgroundColor:"rgba(0,0,0,0)",borderWidth:1,borderColor:"#ccc"}});var Tm=Cm.prototype;function Am(t,e,n,i){n.getAxesOnZeroOf=function(){return r?[r]:[]};var r,o=t[e],a=n.model,s=a.get("axisLine.onZero"),l=a.get("axisLine.onZeroAxisIndex");if(s){if(null!=l)Dm(o[l])&&(r=o[l]);else for(var u in o)if(o.hasOwnProperty(u)&&Dm(o[u])&&!i[h(o[u])]){r=o[u];break}r&&(i[h(r)]=!0)}function h(t){return t.dim+"_"+t.index}}function Dm(t){return t&&"category"!==t.type&&"time"!==t.type&&function(t){var e=t.scale.getExtent(),n=e[0],i=e[1];return!(0<n&&0<i||n<0&&i<0)}(t)}Tm.type="grid",Tm.axisPointerEnabled=!0,Tm.getRect=function(){return this._rect},Tm.update=function(t,e){var n=this._axesMap;this._updateScale(t,this.model),D(n.x,function(t){Hp(t.scale,t.model)}),D(n.y,function(t){Hp(t.scale,t.model)});var i={};D(n.x,function(t){Am(n,"y",t,i)}),D(n.y,function(t){Am(n,"x",t,i)}),this.resize(this.model,e)},Tm.resize=function(t,e,n){var r=lu(t.getBoxLayoutParams(),{width:e.getWidth(),height:e.getHeight()});this._rect=r;var i=this._axesList;function o(){D(i,function(t){var e=t.isHorizontal(),n=e?[0,r.width]:[0,r.height],i=t.inverse?1:0;t.setExtent(n[i],n[1-i]),function(t,e){var n=t.getExtent(),i=n[0]+n[1];t.toGlobalCoord="x"===t.dim?function(t){return t+e}:function(t){return i-t+e},t.toLocalCoord="x"===t.dim?function(t){return t-e}:function(t){return i-t+e}}(t,e?r.x:r.y)})}o(),!n&&t.get("containLabel")&&(D(i,function(t){if(!t.model.get("axisLabel.inside")){var e=function(t){var e=t.model,n=t.scale;if(e.get("axisLabel.show")&&!n.isBlank()){var i,r,o="category"===t.type,a=n.getExtent();r=o?n.count():(i=n.getTicks()).length;var s,l,u,h,c,d,f,p,g,m=t.getLabelModel(),v=Gp(t),y=1;40<r&&(y=Math.ceil(r/40));for(var _=0;_<r;_+=y){var x=v(i?i[_]:a[0]+_),w=m.getTextRect(x),b=(l=w,u=m.get("rotate")||0,void 0,h=u*Math.PI/180,c=l.plain(),d=c.width,f=c.height,p=d*Math.cos(h)+f*Math.sin(h),g=d*Math.sin(h)+f*Math.cos(h),new Sn(c.x,c.y,p,g));s?s.union(b):s=b}return s}}(t);if(e){var n=t.isHorizontal()?"height":"width",i=t.model.get("axisLabel.margin");r[n]-=e[n]+i,"top"===t.position?r.y+=e.height+i:"left"===t.position&&(r.x+=e.width+i)}}}),o())},Tm.getAxis=function(t,e){var n=this._axesMap[t];if(null!=n){if(null==e)for(var i in n)if(n.hasOwnProperty(i))return n[i];return n[e]}},Tm.getAxes=function(){return this._axesList.slice()},Tm.getCartesian=function(t,e){if(null!=t&&null!=e){var n="x"+t+"y"+e;return this._coordsMap[n]}N(t)&&(e=t.yAxisIndex,t=t.xAxisIndex);for(var i=0,r=this._coordsList;i<r.length;i++)if(r[i].getAxis("x").index===t||r[i].getAxis("y").index===e)return r[i]},Tm.getCartesians=function(){return this._coordsList.slice()},Tm.convertToPixel=function(t,e,n){var i=this._findConvertTarget(t,e);return i.cartesian?i.cartesian.dataToPoint(n):i.axis?i.axis.toGlobalCoord(i.axis.dataToCoord(n)):null},Tm.convertFromPixel=function(t,e,n){var i=this._findConvertTarget(t,e);return i.cartesian?i.cartesian.pointToData(n):i.axis?i.axis.coordToData(i.axis.toLocalCoord(n)):null},Tm._findConvertTarget=function(t,e){var n,i,r=e.seriesModel,o=e.xAxisModel||r&&r.getReferringComponents("xAxis")[0],a=e.yAxisModel||r&&r.getReferringComponents("yAxis")[0],s=e.gridModel,l=this._coordsList;if(r)x(l,n=r.coordinateSystem)<0&&(n=null);else if(o&&a)n=this.getCartesian(o.componentIndex,a.componentIndex);else if(o)i=this.getAxis("x",o.componentIndex);else if(a)i=this.getAxis("y",a.componentIndex);else if(s){s.coordinateSystem===this&&(n=this._coordsList[0])}return{cartesian:n,axis:i}},Tm.containPoint=function(t){var e=this._coordsList[0];if(e)return e.containPoint(t)},Tm._initCartesian=function(a,t,e){var s={left:!1,right:!1,top:!1,bottom:!1},l={x:{},y:{}},u={x:0,y:0};if(t.eachComponent("xAxis",n("x"),this),t.eachComponent("yAxis",n("y"),this),!u.x||!u.y)return this._axesMap={},void(this._axesList=[]);function n(o){return function(t,e){if(Im(t,a)){var n=t.get("position");"x"===o?"top"!==n&&"bottom"!==n&&(n=s.bottom?"top":"bottom"):"left"!==n&&"right"!==n&&(n=s.left?"right":"left"),s[n]=!0;var i=new vm(o,Wp(t),[0,0],t.get("type"),n),r="category"===i.type;i.onBand=r&&t.get("boundaryGap"),i.inverse=t.get("inverse"),(t.axis=i).model=t,i.grid=this,i.index=e,this._axesList.push(i),l[o][e]=i,u[o]++}}}D((this._axesMap=l).x,function(r,o){D(l.y,function(t,e){var n="x"+o+"y"+e,i=new mm(n);i.grid=this,i.model=a,this._coordsMap[n]=i,this._coordsList.push(i),i.addAxis(r),i.addAxis(t)},this)},this)},Tm._updateScale=function(l,u){function h(e,n){D(e.mapDimension(n.dim,!0),function(t){n.scale.unionExtentFromData(e,Kf(e,t))})}D(this._axesList,function(t){t.scale.setExtent(1/0,-1/0)}),l.eachSeries(function(t){if(Lm(t)){var e=Pm(t,l),n=e[0],i=e[1];if(!Im(n,u)||!Im(i,u))return;var r=this.getCartesian(n.componentIndex,i.componentIndex),o=t.getData(),a=r.getAxis("x"),s=r.getAxis("y");"list"===o.type&&(h(o,a,t),h(o,s,t))}},this)},Tm.getTooltipAxes=function(i){var r=[],o=[];return D(this.getCartesians(),function(t){var e=null!=i&&"auto"!==i?t.getAxis(i):t.getBaseAxis(),n=t.getOtherAxis(e);x(r,e)<0&&r.push(e),x(o,n)<0&&o.push(n)}),{baseAxes:r,otherAxes:o}};var km=["xAxis","yAxis"];function Pm(e){return P(km,function(t){return e.getReferringComponents(t)[0]})}function Lm(t){return"cartesian2d"===t.get("coordinateSystem")}Cm.create=function(i,r){var o=[];return i.eachComponent("grid",function(t,e){var n=new Cm(t,i,r);n.name="grid_"+e,n.resize(t,r,!0),t.coordinateSystem=n,o.push(n)}),i.eachSeries(function(t){if(Lm(t)){var e=Pm(t),n=e[0],i=e[1],r=n.getCoordSysModel().coordinateSystem;t.coordinateSystem=r.getCartesian(n.componentIndex,i.componentIndex)}}),o},Cm.dimensions=Cm.prototype.dimensions=mm.prototype.dimensions,ju.register("cartesian2d",Cm);function Om(t,e){this.opt=e,this.axisModel=t,A(e,{labelOffset:0,nameDirection:1,tickDirection:1,labelDirection:1,silent:!0}),this.group=new Mn;var n=new Mn({position:e.position.slice(),rotation:e.rotation});n.updateTransform(),this._transform=n.transform,this._dumbGroup=n}var Em=Math.PI;Om.prototype={constructor:Om,hasBuilder:function(t){return!!zm[t]},add:function(t){zm[t].call(this)},getGroup:function(){return this.group}};var zm={axisLine:function(){var o=this.opt,t=this.axisModel;if(t.get("axisLine.show")){var e=this.axisModel.axis.getExtent(),n=this._transform,a=[e[0],0],i=[e[1],0];n&&(yt(a,a,n),yt(i,i,n));var s=k({lineCap:"round"},t.getModel("axisLine.lineStyle").getLineStyle());this.group.add(new es({anid:"line",subPixelOptimize:!0,shape:{x1:a[0],y1:a[1],x2:i[0],y2:i[1]},style:s,strokeContainThreshold:o.strokeContainThreshold||5,silent:!0,z2:1}));var l=t.get("axisLine.symbol"),r=t.get("axisLine.symbolSize"),u=t.get("axisLine.symbolOffset")||0;if("number"==typeof u&&(u=[u,u]),null!=l){"string"==typeof l&&(l=[l,l]),"string"!=typeof r&&"number"!=typeof r||(r=[r,r]);var h=r[0],c=r[1];D([{rotate:o.rotation+Math.PI/2,offset:u[0],r:0},{rotate:o.rotation-Math.PI/2,offset:u[1],r:Math.sqrt((a[0]-i[0])*(a[0]-i[0])+(a[1]-i[1])*(a[1]-i[1]))}],function(t,e){if("none"!==l[e]&&null!=l[e]){var n=ng(l[e],-h/2,-c/2,h,c,s.stroke,!0),i=t.r+t.offset,r=[a[0]+i*Math.cos(o.rotation),a[1]-i*Math.sin(o.rotation)];n.attr({rotation:t.rotate,position:r,silent:!0,z2:11}),this.group.add(n)}},this)}}},axisTickLabel:function(){var t=this.axisModel,e=this.opt,n=function(t,e,n){var i=e.axis,r=e.getModel("axisTick");if(!r.get("show")||i.scale.isBlank())return;for(var o=r.getModel("lineStyle"),a=n.tickDirection*r.get("length"),s=Wm(i.getTicksCoords(),t._transform,a,A(o.getLineStyle(),{stroke:e.get("axisLine.lineStyle.color")}),"ticks"),l=0;l<s.length;l++)t.group.add(s[l]);return s}(this,t,e);!function(t,e,n){if(Xp(t.axis))return;var i=t.get("axisLabel.showMinLabel"),r=t.get("axisLabel.showMaxLabel");n=n||[];var o=(e=e||[])[0],a=e[1],s=e[e.length-1],l=e[e.length-2],u=n[0],h=n[1],c=n[n.length-1],d=n[n.length-2];!1===i?(Vm(o),Vm(u)):Fm(o,a)&&(i?(Vm(a),Vm(h)):(Vm(o),Vm(u)));!1===r?(Vm(s),Vm(c)):Fm(l,s)&&(r?(Vm(l),Vm(d)):(Vm(s),Vm(c)))}(t,function(u,h,c){var d=h.axis;if(!H(c.axisLabelShow,h.get("axisLabel.show"))||d.scale.isBlank())return;var f=h.getModel("axisLabel"),p=f.get("margin"),t=d.getViewLabels(),e=(H(c.labelRotate,f.get("rotate"))||0)*Em/180,g=Rm(c.rotation,e,c.labelDirection),m=h.getCategories&&h.getCategories(!0),v=[],y=Bm(h),_=h.get("triggerEvent");return D(t,function(t,e){var n=t.tickValue,i=t.formattedLabel,r=t.rawLabel,o=f;m&&m[n]&&m[n].textStyle&&(o=new _l(m[n].textStyle,f,h.ecModel));var a=o.getTextColor()||h.get("axisLine.lineStyle.color"),s=[d.dataToCoord(n),c.labelOffset+c.labelDirection*p],l=new Ba({anid:"label_"+n,position:s,rotation:g.rotation,silent:y,z2:10});js(l.style,o,{text:i,textAlign:o.getShallow("align",!0)||g.textAlign,textVerticalAlign:o.getShallow("verticalAlign",!0)||o.getShallow("baseline",!0)||g.textVerticalAlign,textFill:"function"==typeof a?a("category"===d.type?r:"value"===d.type?n+"":n,e):a}),_&&(l.eventData=Nm(h),l.eventData.targetType="axisLabel",l.eventData.value=r),u._dumbGroup.add(l),l.updateTransform(),v.push(l),u.group.add(l),l.decomposeTransform()}),v}(this,t,e),n),function(t,e,n){var i=e.axis,r=e.getModel("minorTick");if(!r.get("show")||i.scale.isBlank())return;var o=i.getMinorTicksCoords();if(!o.length)return;for(var a=r.getModel("lineStyle"),s=n.tickDirection*r.get("length"),l=A(a.getLineStyle(),A(e.getModel("axisTick").getLineStyle(),{stroke:e.get("axisLine.lineStyle.color")})),u=0;u<o.length;u++)for(var h=Wm(o[u],t._transform,s,l,"minorticks_"+u),c=0;c<h.length;c++)t.group.add(h[c])}(this,t,e)},axisName:function(){var t=this.opt,e=this.axisModel,n=H(t.axisName,e.get("name"));if(n){var i,r,o=e.get("nameLocation"),a=t.nameDirection,s=e.getModel("nameTextStyle"),l=e.get("nameGap")||0,u=this.axisModel.axis.getExtent(),h=u[0]>u[1]?-1:1,c=["start"===o?u[0]-h*l:"end"===o?u[1]+h*l:(u[0]+u[1])/2,Hm(o)?t.labelOffset+a*l:0],d=e.get("nameRotate");null!=d&&(d=d*Em/180),Hm(o)?i=Rm(t.rotation,null!=d?d:t.rotation,a):(i=function(t,e,n,i){var r,o,a=Ol(n-t.rotation),s=i[0]>i[1],l="start"===e&&!s||"start"!==e&&s;r=El(a-Em/2)?(o=l?"bottom":"top","center"):El(a-1.5*Em)?(o=l?"top":"bottom","center"):(o="middle",a<1.5*Em&&Em/2<a?l?"left":"right":l?"right":"left");return{rotation:a,textAlign:r,textVerticalAlign:o}}(t,o,d||0,u),null!=(r=t.axisNameAvailableWidth)&&(r=Math.abs(r/Math.sin(i.rotation)),isFinite(r)||(r=null)));var f=s.getFont(),p=e.get("nameTruncate",!0)||{},g=p.ellipsis,m=H(t.nameTruncateMaxWidth,p.maxWidth,r),v=null!=g&&null!=m?tu(n,m,f,g,{minChar:2,placeholder:p.placeholder}):n,y=e.get("tooltip",!0),_=e.mainType,x={componentType:_,name:n,$vars:["name"]};x[_+"Index"]=e.componentIndex;var w=new Ba({anid:"name",__fullText:n,__truncatedText:v,position:c,rotation:i.rotation,silent:Bm(e),z2:1,tooltip:y&&y.show?k({content:n,formatter:function(){return n},formatterParams:x},y):null});js(w.style,s,{text:v,textFont:f,textFill:s.getTextColor()||e.get("axisLine.lineStyle.color"),textAlign:s.get("align")||i.textAlign,textVerticalAlign:s.get("verticalAlign")||i.textVerticalAlign}),e.get("triggerEvent")&&(w.eventData=Nm(e),w.eventData.targetType="axisName",w.eventData.name=n),this._dumbGroup.add(w),w.updateTransform(),this.group.add(w),w.decomposeTransform()}}},Nm=Om.makeAxisEventDataBase=function(t){var e={componentType:t.mainType,componentIndex:t.componentIndex};return e[t.mainType+"Index"]=t.componentIndex,e},Rm=Om.innerTextLayout=function(t,e,n){var i,r=Ol(e-t);return{rotation:r,textAlign:El(r)?(i=0<n?"top":"bottom","center"):El(r-Em)?(i=0<n?"bottom":"top","center"):(i="middle",0<r&&r<Em?0<n?"right":"left":0<n?"left":"right"),textVerticalAlign:i}};var Bm=Om.isLabelSilent=function(t){var e=t.get("tooltip");return t.get("silent")||!(t.get("triggerEvent")||e&&e.show)};function Vm(t){t&&(t.ignore=!0)}function Fm(t,e){var n=t&&t.getBoundingRect().clone(),i=e&&e.getBoundingRect().clone();if(n&&i){var r=ne([]);return ae(r,r,-t.rotation),n.applyTransform(re([],r,t.getLocalTransform())),i.applyTransform(re([],r,e.getLocalTransform())),n.intersect(i)}}function Hm(t){return"middle"===t||"center"===t}function Wm(t,e,n,i,r){for(var o=[],a=[],s=[],l=0;l<t.length;l++){var u=t[l].coord;a[0]=u,s[a[1]=0]=u,s[1]=n,e&&(yt(a,a,e),yt(s,s,e));var h=new es({anid:r+"_"+t[l].tickValue,subPixelOptimize:!0,shape:{x1:a[0],y1:a[1],x2:s[0],y2:s[1]},style:i,z2:2,silent:!0});o.push(h)}return o}var Gm=D,Zm=T;function Um(t,e){var n={axesInfo:{},seriesInvolved:!1,coordSysAxesInfo:{},coordSysMap:{}};return function(p,g,t){var o=g.getComponent("tooltip"),m=g.getComponent("axisPointer"),v=m.get("link",!0)||[],y=[];Gm(t.getCoordinateSystems(),function(c){if(c.axisPointerEnabled){var t=qm(c.model),d=p.coordSysAxesInfo[t]={},f=(p.coordSysMap[t]=c).model.getModel("tooltip",o);if(Gm(c.getAxes(),Zm(r,!1,null)),c.getTooltipAxes&&o&&f.get("show")){var e="axis"===f.get("trigger"),n="cross"===f.get("axisPointer.type"),i=c.getTooltipAxes(f.get("axisPointer.axis"));(e||n)&&Gm(i.baseAxes,Zm(r,!n||"cross",e)),n&&Gm(i.otherAxes,Zm(r,"cross",!1))}}function r(t,e,n){var i=n.model.getModel("axisPointer",m),r=i.get("show");if(r&&("auto"!==r||t||jm(i))){null==e&&(e=i.get("triggerTooltip"));var o=(i=t?function(t,e,n,i,r,o){var a=e.getModel("axisPointer"),s={};Gm(["type","snap","lineStyle","shadowStyle","label","animation","animationDurationUpdate","animationEasingUpdate","z"],function(t){s[t]=b(a.get(t))}),s.snap="category"!==t.type&&!!o,"cross"===a.get("type")&&(s.type="line");var l=s.label||(s.label={});if(null==l.show&&(l.show=!1),"cross"===r){var u=a.get("label.show");if(l.show=null==u||u,!o){var h=s.lineStyle=a.get("crossStyle");h&&A(l,h.textStyle)}}return t.model.getModel("axisPointer",new _l(s,n,i))}(n,f,m,g,t,e):i).get("snap"),a=qm(n.model),s=e||o||"category"===n.type,l=p.axesInfo[a]={key:a,axis:n,coordSys:c,axisPointerModel:i,triggerTooltip:e,involveSeries:s,snap:o,useHandle:jm(i),seriesModels:[]};d[a]=l,p.seriesInvolved|=s;var u=function(t,e){for(var n=e.model,i=e.dim,r=0;r<t.length;r++){var o=t[r]||{};if(Xm(o[i+"AxisId"],n.id)||Xm(o[i+"AxisIndex"],n.componentIndex)||Xm(o[i+"AxisName"],n.name))return r}}(v,n);if(null!=u){var h=y[u]||(y[u]={axesInfo:{}});h.axesInfo[a]=l,h.mapper=v[u].mapper,l.linkGroup=h}}}})}(n,t,e),n.seriesInvolved&&function(r,t){t.eachSeries(function(n){var i=n.coordinateSystem,t=n.get("tooltip.trigger",!0),e=n.get("tooltip.show",!0);i&&"none"!==t&&!1!==t&&"item"!==t&&!1!==e&&!1!==n.get("axisPointer.show",!0)&&Gm(r.coordSysAxesInfo[qm(i.model)],function(t){var e=t.axis;i.getAxis(e.dim)===e&&(t.seriesModels.push(n),null==t.seriesDataCount&&(t.seriesDataCount=0),t.seriesDataCount+=n.getData().count())})},this)}(n,t),n}function Xm(t,e){return"all"===t||O(t)&&0<=x(t,e)||t===e}function Ym(t){var e=(t.ecModel.getComponent("axisPointer")||{}).coordSysAxesInfo;return e&&e.axesInfo[qm(t)]}function jm(t){return!!t.get("handle.show")}function qm(t){return t.type+"||"+t.id}var $m=cf({type:"axis",_axisPointer:null,axisPointerClass:null,render:function(t,e,n,i){this.axisPointerClass&&function(t){var e=Ym(t);if(e){var n=e.axisPointerModel,i=e.axis.scale,r=n.option,o=n.get("status"),a=n.get("value");null!=a&&(a=i.parse(a));var s=jm(n);null==o&&(r.status=s?"show":"hide");var l=i.getExtent().slice();l[0]>l[1]&&l.reverse(),(null==a||a>l[1])&&(a=l[1]),a<l[0]&&(a=l[0]),r.value=a,s&&(r.status=e.axis.scale.isBlank()?"hide":"show")}}(t),$m.superApply(this,"render",arguments),Km(this,t,e,n,i,!0)},updateAxisPointer:function(t,e,n,i,r){Km(this,t,e,n,i,!1)},remove:function(t,e){var n=this._axisPointer;n&&n.remove(e),$m.superApply(this,"remove",arguments)},dispose:function(t,e){Qm(this,e),$m.superApply(this,"dispose",arguments)}});function Km(t,e,n,i,r,o){var a=$m.getAxisPointerClass(t.axisPointerClass);if(a){var s=function(t){var e=Ym(t);return e&&e.axisPointerModel}(e);s?(t._axisPointer||(t._axisPointer=new a)).render(e,s,i,o):Qm(t,i)}}function Qm(t,e,n){var i=t._axisPointer;i&&i.dispose(e,n),t._axisPointer=null}var Jm=[];function tv(t,e,n){n=n||{};var i=t.coordinateSystem,r=e.axis,o={},a=r.getAxesOnZeroOf()[0],s=r.position,l=a?"onZero":s,u=r.dim,h=i.getRect(),c=[h.x,h.x+h.width,h.y,h.y+h.height],d={left:0,right:1,top:0,bottom:1,onZero:2},f=e.get("offset")||0,p="x"===u?[c[2]-f,c[3]+f]:[c[0]-f,c[1]+f];if(a){var g=a.toGlobalCoord(a.dataToCoord(0));p[d.onZero]=Math.max(Math.min(g,p[1]),p[0])}o.position=["y"===u?p[d[l]]:c[0],"x"===u?p[d[l]]:c[3]],o.rotation=Math.PI/2*("x"===u?0:1);o.labelDirection=o.tickDirection=o.nameDirection={top:-1,bottom:1,left:-1,right:1}[s],o.labelOffset=a?p[d[s]]-p[d.onZero]:0,e.get("axisTick.inside")&&(o.tickDirection=-o.tickDirection),H(n.labelInside,e.get("axisLabel.inside"))&&(o.labelDirection=-o.labelDirection);var m=e.get("axisLabel.rotate");return o.labelRotate="top"===l?-m:m,o.z2=1,o}$m.registerAxisPointerClass=function(t,e){Jm[t]=e},$m.getAxisPointerClass=function(t){return t&&Jm[t]};var ev=["axisLine","axisTickLabel","axisName"],nv=["splitArea","splitLine","minorSplitLine"],iv=$m.extend({type:"cartesianAxis",axisPointerClass:"CartesianAxisPointer",render:function(e,t,n,i){this.group.removeAll();var r=this._axisGroup;if(this._axisGroup=new Mn,this.group.add(this._axisGroup),e.get("show")){var o=e.getCoordSysModel(),a=tv(o,e),s=new Om(e,a);D(ev,s.add,s),this._axisGroup.add(s.getGroup()),D(nv,function(t){e.get(t+".show")&&this["_"+t](e,o)},this),sl(r,this._axisGroup,e),iv.superCall(this,"render",e,t,n,i)}},remove:function(){!function(t){t.__splitAreaColors=null}(this)},_splitLine:function(t,e){var n=t.axis;if(!n.scale.isBlank()){var i=t.getModel("splitLine"),r=i.getModel("lineStyle"),o=r.get("color");o=O(o)?o:[o];for(var a=e.coordinateSystem.getRect(),s=n.isHorizontal(),l=0,u=n.getTicksCoords({tickModel:i}),h=[],c=[],d=r.getLineStyle(),f=0;f<u.length;f++){var p=n.toGlobalCoord(u[f].coord);s?(h[0]=p,h[1]=a.y,c[0]=p,c[1]=a.y+a.height):(h[0]=a.x,h[1]=p,c[0]=a.x+a.width,c[1]=p);var g=l++%o.length,m=u[f].tickValue;this._axisGroup.add(new es({anid:null!=m?"line_"+u[f].tickValue:null,subPixelOptimize:!0,shape:{x1:h[0],y1:h[1],x2:c[0],y2:c[1]},style:A({stroke:o[g]},d),silent:!0}))}}},_minorSplitLine:function(t,e){var n=t.axis,i=t.getModel("minorSplitLine").getModel("lineStyle"),r=e.coordinateSystem.getRect(),o=n.isHorizontal(),a=n.getMinorTicksCoords();if(a.length)for(var s=[],l=[],u=i.getLineStyle(),h=0;h<a.length;h++)for(var c=0;c<a[h].length;c++){var d=n.toGlobalCoord(a[h][c].coord);o?(s[0]=d,s[1]=r.y,l[0]=d,l[1]=r.y+r.height):(s[0]=r.x,s[1]=d,l[0]=r.x+r.width,l[1]=d),this._axisGroup.add(new es({anid:"minor_line_"+a[h][c].tickValue,subPixelOptimize:!0,shape:{x1:s[0],y1:s[1],x2:l[0],y2:l[1]},style:u,silent:!0}))}},_splitArea:function(t,e){!function(t,e,n,i){var r=n.axis;if(!r.scale.isBlank()){var o=n.getModel("splitArea"),a=o.getModel("areaStyle"),s=a.get("color"),l=i.coordinateSystem.getRect(),u=r.getTicksCoords({tickModel:o,clamp:!0});if(u.length){var h=s.length,c=t.__splitAreaColors,d=Q(),f=0;if(c)for(var p=0;p<u.length;p++){var g=c.get(u[p].tickValue);if(null!=g){f=(g+(h-1)*p)%h;break}}var m=r.toGlobalCoord(u[0].coord),v=a.getAreaStyle();s=O(s)?s:[s];for(p=1;p<u.length;p++){var y,_,x,w,b=r.toGlobalCoord(u[p].coord);m=r.isHorizontal()?(y=m,_=l.y,x=b-y,w=l.height,y+x):(y=l.x,_=m,x=l.width,_+(w=b-_));var S=u[p-1].tickValue;null!=S&&d.set(S,f),e.add(new Ja({anid:null!=S?"area_"+S:null,shape:{x:y,y:_,width:x,height:w},style:A({fill:s[f]},v),silent:!0})),f=(f+1)%h}t.__splitAreaColors=d}}}(this,this._axisGroup,t,e)}});function rv(t,e){"outside"===t.textPosition&&(t.textPosition=e)}iv.extend({type:"xAxis"}),iv.extend({type:"yAxis"}),cf({type:"grid",render:function(t,e){this.group.removeAll(),t.get("show")&&this.group.add(new Ja({shape:t.coordinateSystem.getRect(),style:A({fill:t.get("backgroundColor")},t.getItemStyle()),silent:!0,z2:-1}))}}),nf(function(t){t.xAxis&&t.yAxis&&!t.grid&&(t.grid={})}),sf(hm("line","circle","line")),af(cm("line")),rf(gd.PROCESSOR.STATISTIC,{seriesType:"line",modifyOutputEnd:!0,reset:function(t,e,n){var i=t.getData(),r=t.get("sampling"),o=t.coordinateSystem;if("cartesian2d"===o.type&&r){var a,s=o.getBaseAxis(),l=o.getOtherAxis(s),u=s.getExtent(),h=u[1]-u[0],c=Math.round(i.count()/h);1<c&&("string"==typeof r?a=fm[r]:"function"==typeof r&&(a=r),a&&t.setData(i.downSample(i.mapDimension(l.dim),1/c,a,dm)))}}}),Yh.extend({type:"series.__base_bar__",getInitialData:function(t,e){return Qf(this.getSource(),this,{useEncodeDefaulter:!0})},getMarkerPosition:function(t){var e=this.coordinateSystem;if(e){var n=e.dataToPoint(e.clampData(t)),i=this.getData(),r=i.getLayout("offset"),o=i.getLayout("size");return n[e.getBaseAxis().isHorizontal()?0:1]+=r+o/2,n}return[NaN,NaN]},defaultOption:{zlevel:0,z:2,coordinateSystem:"cartesian2d",legendHoverLink:!0,barMinHeight:0,barMinAngle:0,large:!1,largeThreshold:400,progressive:3e3,progressiveChunkMode:"mod",itemStyle:{},emphasis:{}}}).extend({type:"series.bar",dependencies:["grid","polar"],brushSelector:"rect",getProgressive:function(){return!!this.get("large")&&this.get("progressive")},getProgressiveThreshold:function(){var t=this.get("progressiveThreshold"),e=this.get("largeThreshold");return t<e&&(t=e),t},defaultOption:{clip:!0,roundCap:!1,showBackground:!1,backgroundStyle:{color:"rgba(180, 180, 180, 0.2)",borderColor:null,borderWidth:0,borderType:"solid",borderRadius:0,shadowBlur:0,shadowColor:null,shadowOffsetX:0,shadowOffsetY:0,opacity:1}}});var ov=no([["fill","color"],["stroke","borderColor"],["lineWidth","borderWidth"],["stroke","barBorderColor"],["lineWidth","barBorderWidth"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"]]),av={getBarItemStyle:function(t){var e=ov(this,t);if(this.getBorderLineDash){var n=this.getBorderLineDash();n&&(e.lineDash=n)}return e}},sv=ws({type:"sausage",shape:{cx:0,cy:0,r0:0,r:0,startAngle:0,endAngle:2*Math.PI,clockwise:!0},buildPath:function(t,e){var n=e.cx,i=e.cy,r=Math.max(e.r0||0,0),o=Math.max(e.r,0),a=.5*(o-r),s=r+a,l=e.startAngle,u=e.endAngle,h=e.clockwise,c=Math.cos(l),d=Math.sin(l),f=Math.cos(u),p=Math.sin(u);(h?u-l<2*Math.PI:l-u<2*Math.PI)&&(t.moveTo(c*r+n,d*r+i),t.arc(c*s+n,d*s+i,a,-Math.PI+l,l,!h)),t.arc(n,i,o,l,u,!h),t.moveTo(f*o+n,p*o+i),t.arc(f*s+n,p*s+i,a,u-2*Math.PI,u-Math.PI,!h),0!==r&&(t.arc(n,i,r,u,l,h),t.moveTo(c*r+n,p*r+i)),t.closePath()}}),lv=["itemStyle","barBorderWidth"],uv=[0,0];k(_l.prototype,av),ff({type:"bar",render:function(t,e,n){this._updateDrawMode(t);var i=t.get("coordinateSystem");return"cartesian2d"!==i&&"polar"!==i||(this._isLargeDraw?this._renderLarge(t,e,n):this._renderNormal(t,e,n)),this.group},incrementalPrepareRender:function(t,e,n){this._clear(),this._updateDrawMode(t)},incrementalRender:function(t,e,n,i){this._incrementalRenderLarge(t,e)},_updateDrawMode:function(t){var e=t.pipelineContext.large;(null==this._isLargeDraw||e^this._isLargeDraw)&&(this._isLargeDraw=e,this._clear())},_renderNormal:function(s,t,e){var l,u=this.group,h=s.getData(),c=this._data,d=s.coordinateSystem,n=d.getBaseAxis();"cartesian2d"===d.type?l=n.isHorizontal():"polar"===d.type&&(l="angle"===n.dim);var f=s.isAnimationEnabled()?s:null,p=s.get("clip",!0),g=function(t,e){var n=t.getArea&&t.getArea();if("cartesian2d"===t.type){var i=t.getBaseAxis();if("category"!==i.type||!i.onBand){var r=e.getLayout("bandWidth");i.isHorizontal()?(n.x-=r,n.width+=2*r):(n.y-=r,n.height+=2*r)}}return n}(d,h);u.removeClipPath();var m=s.get("roundCap",!0),v=s.get("showBackground",!0),y=s.getModel("backgroundStyle"),_=y.get("barBorderRadius")||0,x=[],w=this._backgroundEls||[];h.diff(c).add(function(t){var e=h.getItemModel(t),n=mv[d.type](h,t,e);if(v){var i=mv[d.type](h,t),r=function(t,e,n){return new("polar"===t.type?Wa:Ja)({shape:bv(e,n,t),silent:!0,z2:0})}(d,l,i);r.useStyle(y.getBarItemStyle()),"cartesian2d"===d.type&&r.setShape("r",_),x[t]=r}if(h.hasValue(t)){if(p)if(dv[d.type](g,n))return void u.remove(o);var o=fv[d.type](t,n,l,f,!1,m);h.setItemGraphicEl(t,o),u.add(o),yv(o,h,t,e,n,s,l,"polar"===d.type)}}).update(function(t,e){var n=h.getItemModel(t),i=mv[d.type](h,t,n);if(v){var r=w[e];r.useStyle(y.getBarItemStyle()),"cartesian2d"===d.type&&r.setShape("r",_),x[t]=r;var o=mv[d.type](h,t);nl(r,{shape:bv(l,o,d)},f,t)}var a=c.getItemGraphicEl(e);if(h.hasValue(t)){if(p)if(dv[d.type](g,i))return void u.remove(a);a?nl(a,{shape:i},f,t):a=fv[d.type](t,i,l,f,!0,m),h.setItemGraphicEl(t,a),u.add(a),yv(a,h,t,n,i,s,l,"polar"===d.type)}else u.remove(a)}).remove(function(t){var e=c.getItemGraphicEl(t);"cartesian2d"===d.type?e&&pv(t,f,e):e&&gv(t,f,e)}).execute();var i=this._backgroundGroup||(this._backgroundGroup=new Mn);i.removeAll();for(var r=0;r<x.length;++r)i.add(x[r]);u.add(i),this._backgroundEls=x,this._data=h},_renderLarge:function(t,e,n){this._clear(),xv(t,this.group);var i=t.get("clip",!0)?function(t,e,n){return t?"polar"===t.type?im(t,e,n):"cartesian2d"===t.type?nm(t,e,n):null:null}(t.coordinateSystem,!1,t):null;i?this.group.setClipPath(i):this.group.removeClipPath()},_incrementalRenderLarge:function(t,e){this._removeBackground(),xv(e,this.group,!0)},dispose:J,remove:function(t){this._clear(t)},_clear:function(e){var t=this.group,n=this._data;e&&e.get("animation")&&n&&!this._isLargeDraw?(this._removeBackground(),this._backgroundEls=[],n.eachItemGraphicEl(function(t){"sector"===t.type?gv(t.dataIndex,e,t):pv(t.dataIndex,e,t)})):t.removeAll(),this._data=null},_removeBackground:function(){this.group.remove(this._backgroundGroup),this._backgroundGroup=null}});var hv=Math.max,cv=Math.min,dv={cartesian2d:function(t,e){var n=e.width<0?-1:1,i=e.height<0?-1:1;n<0&&(e.x+=e.width,e.width=-e.width),i<0&&(e.y+=e.height,e.height=-e.height);var r=hv(e.x,t.x),o=cv(e.x+e.width,t.x+t.width),a=hv(e.y,t.y),s=cv(e.y+e.height,t.y+t.height);e.x=r,e.y=a,e.width=o-r,e.height=s-a;var l=e.width<0||e.height<0;return n<0&&(e.x+=e.width,e.width=-e.width),i<0&&(e.y+=e.height,e.height=-e.height),l},polar:function(t){return!1}},fv={cartesian2d:function(t,e,n,i,r){var o=new Ja({shape:k({},e),z2:1});if(o.name="item",i){var a=n?"height":"width",s={};o.shape[a]=0,s[a]=e[a],dl[r?"updateProps":"initProps"](o,{shape:s},i,t)}return o},polar:function(t,e,n,i,r,o){var a=e.startAngle<e.endAngle,s=new(!n&&o?sv:Wa)({shape:A({clockwise:a},e),z2:1});if(s.name="item",i){var l=n?"r":"endAngle",u={};s.shape[l]=n?0:e.startAngle,u[l]=e[l],dl[r?"updateProps":"initProps"](s,{shape:u},i,t)}return s}};function pv(t,e,n){n.style.text=null,nl(n,{shape:{width:0}},e,t,function(){n.parent&&n.parent.remove(n)})}function gv(t,e,n){n.style.text=null,nl(n,{shape:{r:n.shape.r0}},e,t,function(){n.parent&&n.parent.remove(n)})}var mv={cartesian2d:function(t,e,n){var i=t.getItemLayout(e),r=n?function(t,e){var n=t.get(lv)||0,i=isNaN(e.width)?Number.MAX_VALUE:Math.abs(e.width),r=isNaN(e.height)?Number.MAX_VALUE:Math.abs(e.height);return Math.min(n,i,r)}(n,i):0,o=0<i.width?1:-1,a=0<i.height?1:-1;return{x:i.x+o*r/2,y:i.y+a*r/2,width:i.width-o*r,height:i.height-a*r}},polar:function(t,e,n){var i=t.getItemLayout(e);return{cx:i.cx,cy:i.cy,r0:i.r0,r:i.r,startAngle:i.startAngle,endAngle:i.endAngle}}};function vv(t){return null!=t.startAngle&&null!=t.endAngle&&t.startAngle===t.endAngle}function yv(t,e,n,i,r,o,a,s){var l=e.getItemVisual(n,"color"),u=e.getItemVisual(n,"opacity"),h=e.getVisual("borderColor"),c=i.getModel("itemStyle"),d=i.getModel("emphasis.itemStyle").getBarItemStyle();s||t.setShape("r",c.get("barBorderRadius")||0),t.useStyle(A({stroke:vv(r)?"none":h,fill:vv(r)?"none":l,opacity:u},c.getBarItemStyle()));var f=i.getShallow("cursor");f&&t.attr("cursor",f);a?r.height:r.width;s||function(t,e,n,i,r,o){Ys(t,e,n.getModel("label"),n.getModel("emphasis.label"),{labelFetcher:r,labelDataIndex:o,defaultText:Cg(r.getData(),o),isRectText:!0,autoColor:i}),rv(t),rv(e)}(t.style,d,i,l,o,n),vv(r)&&(d.fill=d.stroke="none"),Gs(t,d)}var _v=xa.extend({type:"largeBar",shape:{points:[]},buildPath:function(t,e){for(var n=e.points,i=this.__startPoint,r=this.__baseDimIdx,o=0;o<n.length;o+=2)i[r]=n[o+r],t.moveTo(i[0],i[1]),t.lineTo(n[o],n[o+1])}});function xv(t,e,n){var i=t.getData(),r=[],o=i.getLayout("valueAxisHorizontal")?1:0;r[1-o]=i.getLayout("valueAxisStart");var a=i.getLayout("largeDataIndices"),s=i.getLayout("barWidth"),l=t.getModel("backgroundStyle");if(t.get("showBackground",!0)){var u=i.getLayout("largeBackgroundPoints"),h=[];h[1-o]=i.getLayout("backgroundStart");var c=new _v({shape:{points:u},incremental:!!n,__startPoint:h,__baseDimIdx:o,__largeDataIndices:a,__barWidth:s,silent:!0,z2:0});!function(t,e,n){var i=e.get("borderColor")||e.get("color"),r=e.getItemStyle(["color","borderColor"]);t.useStyle(r),t.style.fill=null,t.style.stroke=i,t.style.lineWidth=n.getLayout("barWidth")}(c,l,i),e.add(c)}var d=new _v({shape:{points:i.getLayout("largePoints")},incremental:!!n,__startPoint:r,__baseDimIdx:o,__largeDataIndices:a,__barWidth:s});e.add(d),function(t,e,n){var i=n.getVisual("borderColor")||n.getVisual("color"),r=e.getModel("itemStyle").getItemStyle(["color","borderColor"]);t.useStyle(r),t.style.fill=null,t.style.stroke=i,t.style.lineWidth=n.getLayout("barWidth")}(d,t,i),d.seriesIndex=t.seriesIndex,t.get("silent")||(d.on("mousedown",wv),d.on("mousemove",wv))}var wv=mc(function(t){var e=function(t,e,n){var i=t.__baseDimIdx,r=1-i,o=t.shape.points,a=t.__largeDataIndices,s=Math.abs(t.__barWidth/2),l=t.__startPoint[r];uv[0]=e,uv[1]=n;for(var u=uv[i],h=uv[1-i],c=u-s,d=u+s,f=0,p=o.length/2;f<p;f++){var g=2*f,m=o[g+i],v=o[g+r];if(c<=m&&m<=d&&(l<=v?l<=h&&h<=v:v<=h&&h<=l))return a[f]}return-1}(this,t.offsetX,t.offsetY);this.dataIndex=0<=e?e:null},30,!1);function bv(t,e,n){var i,r="polar"===n.type;return i=r?n.getArea():n.grid.getRect(),r?{cx:i.cx,cy:i.cy,r0:t?i.r0:e.r0,r:t?i.r:e.r,startAngle:t?e.startAngle:0,endAngle:t?e.endAngle:2*Math.PI}:{x:t?e.x:i.x,y:t?i.y:e.y,width:t?e.width:i.width,height:t?i.height:e.height}}af(gd.VISUAL.LAYOUT,T(function(t,e){var n=mp(t,e),C=vp(n),T={};D(n,function(t){var e=t.getData(),n=t.coordinateSystem,i=n.getBaseAxis(),r=pp(t),o=C[gp(i)][r],a=o.offset,s=o.width,l=n.getOtherAxis(i),u=t.get("barMinHeight")||0;T[r]=T[r]||[],e.setLayout({bandWidth:o.bandWidth,offset:a,size:s});for(var h=e.mapDimension(l.dim),c=e.mapDimension(i.dim),d=$f(e,h),f=l.isHorizontal(),p=bp(i,l,d),g=0,m=e.count();g<m;g++){var v,y,_,x,w,b=e.get(h,g),S=e.get(c,g),M=0<=b?"p":"n",I=p;if(d&&(T[r][S]||(T[r][S]={p:p,n:p}),I=T[r][S][M]),f)v=I,y=(w=n.dataToPoint([b,S]))[1]+a,_=w[0]-p,x=s,Math.abs(_)<u&&(_=(_<0?-1:1)*u),isNaN(_)||d&&(T[r][S][M]+=_);else v=(w=n.dataToPoint([S,b]))[0]+a,y=I,_=s,x=w[1]-p,Math.abs(x)<u&&(x=(x<=0?-1:1)*u),isNaN(x)||d&&(T[r][S][M]+=x);e.setItemLayout(g,{x:v,y:y,width:_,height:x})}},this)},"bar")),af(gd.VISUAL.PROGRESSIVE_LAYOUT,_p),sf({seriesType:"bar",reset:function(t){t.getData().setVisual("legendSymbol","roundRect")}});var Sv={updateSelectedMap:function(t){this._targetList=O(t)?t.slice():[],this._selectTargetMap=M(t||[],function(t,e){return t.set(e.name,e),t},Q())},select:function(t,e){var n=null!=e?this._targetList[e]:this._selectTargetMap.get(t);"single"===this.get("selectedMode")&&this._selectTargetMap.each(function(t){t.selected=!1}),n&&(n.selected=!0)},unSelect:function(t,e){var n=null!=e?this._targetList[e]:this._selectTargetMap.get(t);n&&(n.selected=!1)},toggleSelected:function(t,e){var n=null!=e?this._targetList[e]:this._selectTargetMap.get(t);if(null!=n)return this[n.selected?"unSelect":"select"](t,e),n.selected},isSelected:function(t,e){var n=null!=e?this._targetList[e]:this._selectTargetMap.get(t);return n&&n.selected}};function Mv(n,e){this.getAllNames=function(){var t=e();return t.mapArray(t.getName)},this.containName=function(t){return 0<=e().indexOfName(t)},this.indexOfName=function(t){return n().indexOfName(t)},this.getItemVisual=function(t,e){return n().getItemVisual(t,e)}}var Iv=df({type:"series.pie",init:function(t){Iv.superApply(this,"init",arguments),this.legendVisualProvider=new Mv(C(this.getData,this),C(this.getRawData,this)),this.updateSelectedMap(this._createSelectableList()),this._defaultLabelLine(t)},mergeOption:function(t){Iv.superCall(this,"mergeOption",t),this.updateSelectedMap(this._createSelectableList())},getInitialData:function(t,e){return function(t,e,n){e=O(e)&&{coordDimensions:e}||k({},e);var i=t.getSource(),r=Uf(i,e),o=new kf(r,t);return o.initData(i,n),o}(this,{coordDimensions:["value"],encodeDefaulter:T(Bu,this)})},_createSelectableList:function(){for(var t=this.getRawData(),e=t.mapDimension("value"),n=[],i=0,r=t.count();i<r;i++)n.push({name:t.getName(i),value:t.get(e,i),selected:kh(t,i,"selected")});return n},getDataParams:function(t){var e=this.getData(),n=Iv.superCall(this,"getDataParams",t),i=[];return e.each(e.mapDimension("value"),function(t){i.push(t)}),n.percent=Ll(i,t,e.hostModel.get("percentPrecision")),n.$vars.push("percent"),n},_defaultLabelLine:function(t){Or(t,"labelLine",["show"]);var e=t.labelLine,n=t.emphasis.labelLine;e.show=e.show&&t.label.show,n.show=n.show&&t.emphasis.label.show},defaultOption:{zlevel:0,z:2,legendHoverLink:!0,hoverAnimation:!0,center:["50%","50%"],radius:[0,"75%"],clockwise:!0,startAngle:90,minAngle:0,minShowLabelAngle:0,selectedOffset:10,hoverOffset:10,avoidLabelOverlap:!0,percentPrecision:2,stillShowZeroSum:!0,left:0,top:0,right:0,bottom:0,width:null,height:null,label:{rotate:!1,show:!0,position:"outer",alignTo:"none",margin:"25%",bleedMargin:10,distanceToLabelLine:5},labelLine:{show:!0,length:15,length2:15,smooth:!1,lineStyle:{width:1,type:"solid"}},itemStyle:{borderWidth:1},animationType:"expansion",animationTypeUpdate:"transition",animationEasing:"cubicOut"}});function Cv(t,e,n,i){var r=e.getData(),o=this.dataIndex,a=r.getName(o),s=e.get("selectedOffset");i.dispatchAction({type:"pieToggleSelect",from:t,name:a,seriesId:e.id}),r.each(function(t){Tv(r.getItemGraphicEl(t),r.getItemLayout(t),e.isSelected(r.getName(t)),s,n)})}function Tv(t,e,n,i,r){var o=(e.startAngle+e.endAngle)/2,a=n?i:0,s=[Math.cos(o)*a,Math.sin(o)*a];r?t.animate().when(200,{position:s}).start("bounceOut"):t.attr("position",s)}function Av(t,e){Mn.call(this);var n=new Wa({z2:2}),i=new Ya,r=new Ba;this.add(n),this.add(i),this.add(r),this.updateData(t,e,!0)}S(Iv,Sv);var Dv=Av.prototype;Dv.updateData=function(t,e,n){var i=this.childAt(0),r=this.childAt(1),o=this.childAt(2),a=t.hostModel,s=t.getItemModel(e),l=t.getItemLayout(e),u=k({},l);u.label=null;var h=a.getShallow("animationTypeUpdate");n?(i.setShape(u),"scale"===a.getShallow("animationType")?(i.shape.r=l.r0,il(i,{shape:{r:l.r}},a,e)):(i.shape.endAngle=l.startAngle,nl(i,{shape:{endAngle:l.endAngle}},a,e))):"expansion"===h?i.setShape(u):nl(i,{shape:u},a,e);var c=t.getItemVisual(e,"color");i.useStyle(A({lineJoin:"bevel",fill:c},s.getModel("itemStyle").getItemStyle())),i.hoverStyle=s.getModel("emphasis.itemStyle").getItemStyle();var d=s.getShallow("cursor");d&&i.attr("cursor",d),Tv(this,t.getItemLayout(e),a.isSelected(t.getName(e)),a.get("selectedOffset"),a.get("animation"));var f=!n&&"transition"===h;this._updateLabel(t,e,f),this.highDownOnUpdate=a.get("silent")?null:function(t,e){var n=a.isAnimationEnabled()&&s.get("hoverAnimation");"emphasis"===e?(r.ignore=r.hoverIgnore,o.ignore=o.hoverIgnore,n&&(i.stopAnimation(!0),i.animateTo({shape:{r:l.r+a.get("hoverOffset")}},300,"elasticOut"))):(r.ignore=r.normalIgnore,o.ignore=o.normalIgnore,n&&(i.stopAnimation(!0),i.animateTo({shape:{r:l.r}},300,"elasticOut")))},Gs(this)},Dv._updateLabel=function(t,e,n){var i=this.childAt(1),r=this.childAt(2),o=t.hostModel,a=t.getItemModel(e),s=t.getItemLayout(e).label,l=t.getItemVisual(e,"color");if(!s||isNaN(s.x)||isNaN(s.y))r.ignore=r.normalIgnore=r.hoverIgnore=i.ignore=i.normalIgnore=i.hoverIgnore=!0;else{var u={points:s.linePoints||[[s.x,s.y],[s.x,s.y],[s.x,s.y]]},h={x:s.x,y:s.y};n?(nl(i,{shape:u},o,e),nl(r,{style:h},o,e)):(i.attr({shape:u}),r.attr({style:h})),r.attr({rotation:s.rotation,origin:[s.x,s.y],z2:10});var c=a.getModel("label"),d=a.getModel("emphasis.label"),f=a.getModel("labelLine"),p=a.getModel("emphasis.labelLine");l=t.getItemVisual(e,"color");Ys(r.style,r.hoverStyle={},c,d,{labelFetcher:t.hostModel,labelDataIndex:e,defaultText:s.text,autoColor:l,useInsideStyle:!!s.inside},{textAlign:s.textAlign,textVerticalAlign:s.verticalAlign,opacity:t.getItemVisual(e,"opacity")}),r.ignore=r.normalIgnore=!c.get("show"),r.hoverIgnore=!d.get("show"),i.ignore=i.normalIgnore=!f.get("show"),i.hoverIgnore=!p.get("show"),i.setStyle({stroke:l,opacity:t.getItemVisual(e,"opacity")}),i.setStyle(f.getModel("lineStyle").getLineStyle()),i.hoverStyle=p.getModel("lineStyle").getLineStyle();var g=f.get("smooth");g&&!0===g&&(g=.4),i.setShape({smooth:g})}},w(Av,Mn);ac.extend({type:"pie",init:function(){var t=new Mn;this._sectorGroup=t},render:function(t,e,n,i){if(!i||i.from!==this.uid){var r=t.getData(),o=this._data,a=this.group,s=e.get("animation"),l=!o,u=t.get("animationType"),h=t.get("animationTypeUpdate"),c=T(Cv,this.uid,t,s,n),d=t.get("selectedMode");if(r.diff(o).add(function(t){var e=new Av(r,t);l&&"scale"!==u&&e.eachChild(function(t){t.stopAnimation(!0)}),d&&e.on("click",c),r.setItemGraphicEl(t,e),a.add(e)}).update(function(t,e){var n=o.getItemGraphicEl(e);l||"transition"===h||n.eachChild(function(t){t.stopAnimation(!0)}),n.updateData(r,t),n.off("click"),d&&n.on("click",c),a.add(n),r.setItemGraphicEl(t,n)}).remove(function(t){var e=o.getItemGraphicEl(t);a.remove(e)}).execute(),s&&0<r.count()&&(l?"scale"!==u:"transition"!==h)){for(var f=r.getItemLayout(0),p=1;isNaN(f.startAngle)&&p<r.count();++p)f=r.getItemLayout(p);var g=Math.max(n.getWidth(),n.getHeight())/2,m=C(a.removeClipPath,a);a.setClipPath(this._createClipPath(f.cx,f.cy,g,f.startAngle,f.clockwise,m,t,l))}else a.removeClipPath();this._data=r}},dispose:function(){},_createClipPath:function(t,e,n,i,r,o,a,s){var l=new Wa({shape:{cx:t,cy:e,r0:0,r:n,startAngle:i,endAngle:i,clockwise:r}});return(s?il:nl)(l,{shape:{endAngle:i+(r?1:-1)*Math.PI*2}},a,o),l},containPoint:function(t,e){var n=e.getData().getItemLayout(0);if(n){var i=t[0]-n.cx,r=t[1]-n.cy,o=Math.sqrt(i*i+r*r);return o<=n.r&&o>=n.r0}}});var kv=Math.PI/180;function Pv(r,t,e,n,i,o,a,s,l,u){function h(t,e,n){for(var i=t;i<e&&!(r[i].y+n>l+a);i++)if(r[i].y+=n,t<i&&i+1<e&&r[i+1].y>r[i].y+r[i].height)return void c(i,n/2);c(e-1,n/2)}function c(t,e){for(var n=t;0<=n&&!(r[n].y-e<l)&&(r[n].y-=e,!(0<n&&r[n].y>r[n-1].y+r[n-1].height));n--);}function d(t,e,n,i,r,o){for(var a=e?Number.MAX_VALUE:0,s=0,l=t.length;s<l;s++)if("none"===t[s].labelAlignTo){var u=Math.abs(t[s].y-i),h=t[s].len,c=t[s].len2,d=u<r+h?Math.sqrt((r+h+c)*(r+h+c)-u*u):Math.abs(t[s].x-n);e&&a<=d&&(d=a-10),!e&&d<=a&&(d=a+10),t[s].x=n+d*o,a=d}}r.sort(function(t,e){return t.y-e.y});for(var f,p=0,g=r.length,m=[],v=[],y=0;y<g;y++){if("outer"===r[y].position&&"labelLine"===r[y].labelAlignTo){var _=r[y].x-u;r[y].linePoints[1][0]+=_,r[y].x=u}(f=r[y].y-p)<0&&h(y,g,-f),p=r[y].y+r[y].height}a-p<0&&c(g-1,p-a);for(y=0;y<g;y++)r[y].y>=e?v.push(r[y]):m.push(r[y]);d(m,!1,t,e,n,i),d(v,!0,t,e,n,i)}function Lv(t){return"center"===t.position}function Ov(k,P,L,t,O,e){var E,z,N=k.getData(),R=[],B=!1,V=(k.get("minShowLabelAngle")||0)*kv;N.each(function(t){var e=N.getItemLayout(t),n=N.getItemModel(t),i=n.getModel("label"),r=i.get("position")||n.get("emphasis.label.position"),o=i.get("distanceToLabelLine"),a=i.get("alignTo"),s=Cl(i.get("margin"),L),l=i.get("bleedMargin"),u=i.getFont(),h=n.getModel("labelLine"),c=h.get("length");c=Cl(c,L);var d=h.get("length2");if(d=Cl(d,L),!(e.angle<V)){var f,p,g,m,v=(e.startAngle+e.endAngle)/2,y=Math.cos(v),_=Math.sin(v);E=e.cx,z=e.cy;var x,w=k.getFormattedLabel(t,"normal")||N.getName(t),b=ui(w,u,m,"top"),S="inside"===r||"inner"===r;if("center"===r)f=e.cx,p=e.cy,m="center";else{var M=(S?(e.r+e.r0)/2*y:e.r*y)+E,I=(S?(e.r+e.r0)/2*_:e.r*_)+z;if(f=M+3*y,p=I+3*_,!S){var C=M+y*(c+P-e.r),T=I+_*(c+P-e.r),A=C+(y<0?-1:1)*d;f="edge"===a?y<0?O+s:O+L-s:A+(y<0?-o:o),g=[[M,I],[C,T],[A,p=T]]}m=S?"center":"edge"===a?0<y?"right":"left":0<y?"left":"right"}var D=i.get("rotate");x="number"==typeof D?D*(Math.PI/180):D?y<0?-v+Math.PI:-v:0,B=!!x,e.label={x:f,y:p,position:r,height:b.height,len:c,len2:d,linePoints:g,textAlign:m,verticalAlign:"middle",rotation:x,inside:S,labelDistance:o,labelAlignTo:a,labelMargin:s,bleedMargin:l,textRect:b,text:w,font:u},S||R.push(e.label)}}),!B&&k.get("avoidLabelOverlap")&&function(t,e,n,i,r,o,a,s){for(var l=[],u=[],h=Number.MAX_VALUE,c=-Number.MAX_VALUE,d=0;d<t.length;d++)Lv(t[d])||(t[d].x<e?(h=Math.min(h,t[d].x),l.push(t[d])):(c=Math.max(c,t[d].x),u.push(t[d])));for(Pv(u,e,n,i,1,0,o,0,s,c),Pv(l,e,n,i,-1,0,o,0,s,h),d=0;d<t.length;d++){var f=t[d];if(!Lv(f)){var p=f.linePoints;if(p){var g,m="edge"===f.labelAlignTo,v=f.textRect.width;(g=m?f.x<e?p[2][0]-f.labelDistance-a-f.labelMargin:a+r-f.labelMargin-p[2][0]-f.labelDistance:f.x<e?f.x-a-f.bleedMargin:a+r-f.x-f.bleedMargin)<f.textRect.width&&(f.text=fi(f.text,g,f.font),"edge"===f.labelAlignTo&&(v=li(f.text,f.font)));var y=p[1][0]-p[2][0];m?f.x<e?p[2][0]=a+f.labelMargin+v+f.labelDistance:p[2][0]=a+r-f.labelMargin-v-f.labelDistance:(f.x<e?p[2][0]=f.x+f.labelDistance:p[2][0]=f.x-f.labelDistance,p[1][0]=p[2][0]+y),p[1][1]=p[2][1]=f.y}}}}(R,E,z,P,L,t,O,e)}var Ev=2*Math.PI,zv=Math.PI/180;var Nv,Rv;Nv="pie",D([{type:"pieToggleSelect",event:"pieselectchanged",method:"toggleSelected"},{type:"pieSelect",event:"pieselected",method:"select"},{type:"pieUnSelect",event:"pieunselected",method:"unSelect"}],function(o){o.update="updateView",of(o,function(t,e){var r={};return e.eachComponent({mainType:"series",subType:Nv,query:t},function(n){n[o.method]&&n[o.method](t.name,t.dataIndex);var i=n.getData();i.each(function(t){var e=i.getName(t);r[e]=n.isSelected(e)||!1})}),{name:t.name,selected:r,seriesId:t.seriesId}})}),sf((Rv="pie",{getTargetSeries:function(t){var e={},n=Q();return t.eachSeriesByType(Rv,function(t){t.__paletteScope=e,n.set(t.uid,t)}),n},reset:function(s,t){var l=s.getRawData(),u={},h=s.getData();h.each(function(t){var e=h.getRawIndex(t);u[e]=t}),l.each(function(t){var e,n=u[t],i=null!=n&&h.getItemVisual(n,"color",!0),r=null!=n&&h.getItemVisual(n,"borderColor",!0);if(i&&r||(e=l.getItemModel(t)),!i){var o=e.get("itemStyle.color")||s.getColorFromPalette(l.getName(t)||t+"",s.__paletteScope,l.count());null!=n&&h.setItemVisual(n,"color",o)}if(!r){var a=e.get("itemStyle.borderColor");null!=n&&h.setItemVisual(n,"borderColor",a)}})}})),af(T(function(t,e,T,n){e.eachSeriesByType(t,function(t){var r=t.getData(),e=r.mapDimension("value"),o=function(t,e){return lu(t.getBoxLayoutParams(),{width:e.getWidth(),height:e.getHeight()})}(t,T),n=t.get("center"),i=t.get("radius");O(i)||(i=[0,i]),O(n)||(n=[n,n]);var a=Cl(o.width,T.getWidth()),s=Cl(o.height,T.getHeight()),l=Math.min(a,s),u=Cl(n[0],a)+o.x,h=Cl(n[1],s)+o.y,c=Cl(i[0],l/2),d=Cl(i[1],l/2),f=-t.get("startAngle")*zv,p=t.get("minAngle")*zv,g=0;r.each(e,function(t){isNaN(t)||g++});var m=r.getSum(e),v=Math.PI/(m||g)*2,y=t.get("clockwise"),_=t.get("roseType"),x=t.get("stillShowZeroSum"),w=r.getDataExtent(e);w[0]=0;var b=Ev,S=0,M=f,I=y?1:-1;if(r.each(e,function(t,e){var n;if(isNaN(t))r.setItemLayout(e,{angle:NaN,startAngle:NaN,endAngle:NaN,clockwise:y,cx:u,cy:h,r0:c,r:_?NaN:d,viewRect:o});else{(n="area"!==_?0===m&&x?v:t*v:Ev/g)<p?b-=n=p:S+=t;var i=M+I*n;r.setItemLayout(e,{angle:n,startAngle:M,endAngle:i,clockwise:y,cx:u,cy:h,r0:c,r:_?Il(t,w,[c,d]):d,viewRect:o}),M=i}}),b<Ev&&g)if(b<=.001){var C=Ev/g;r.each(e,function(t,e){if(!isNaN(t)){var n=r.getItemLayout(e);n.angle=C,n.startAngle=f+I*e*C,n.endAngle=f+I*(e+1)*C}})}else v=b/S,M=f,r.each(e,function(t,e){if(!isNaN(t)){var n=r.getItemLayout(e),i=n.angle===p?p:t*v;n.startAngle=M,n.endAngle=M+I*i,M+=I*i}});Ov(t,d,o.width,o.height,o.x,o.y)})},"pie")),rf({seriesType:"pie",reset:function(t,e){var i=e.findComponents({mainType:"legend"});if(i&&i.length){var r=t.getData();r.filterSelf(function(t){for(var e=r.getName(t),n=0;n<i.length;n++)if(!i[n].isSelected(e))return!1;return!0})}}}),Yh.extend({type:"series.scatter",dependencies:["grid","polar","geo","singleAxis","calendar"],getInitialData:function(t,e){return Qf(this.getSource(),this,{useEncodeDefaulter:!0})},brushSelector:"point",getProgressive:function(){var t=this.option.progressive;return null==t?this.option.large?5e3:this.get("progressive"):t},getProgressiveThreshold:function(){var t=this.option.progressiveThreshold;return null==t?this.option.large?1e4:this.get("progressiveThreshold"):t},defaultOption:{coordinateSystem:"cartesian2d",zlevel:0,z:2,legendHoverLink:!0,hoverAnimation:!0,symbolSize:10,large:!1,largeThreshold:2e3,itemStyle:{opacity:.8},clip:!0}});var Bv=ws({shape:{points:null},symbolProxy:null,softClipShape:null,buildPath:function(t,e){var n=e.points,i=e.size,r=this.symbolProxy,o=r.shape;if(!((t.getContext?t.getContext():t)&&i[0]<4))for(var a=0;a<n.length;){var s=n[a++],l=n[a++];isNaN(s)||isNaN(l)||this.softClipShape&&!this.softClipShape.contain(s,l)||(o.x=s-i[0]/2,o.y=l-i[1]/2,o.width=i[0],o.height=i[1],r.buildPath(t,o,!0))}},afterBrush:function(t){var e=this.shape,n=e.points,i=e.size;if(i[0]<4){this.setTransform(t);for(var r=0;r<n.length;){var o=n[r++],a=n[r++];isNaN(o)||isNaN(a)||this.softClipShape&&!this.softClipShape.contain(o,a)||t.fillRect(o-i[0]/2,a-i[1]/2,i[0],i[1])}this.restoreTransform(t)}},findDataIndex:function(t,e){for(var n=this.shape,i=n.points,r=n.size,o=Math.max(r[0],4),a=Math.max(r[1],4),s=i.length/2-1;0<=s;s--){var l=2*s,u=i[l]-o/2,h=i[1+l]-a/2;if(u<=t&&h<=e&&t<=u+o&&e<=h+a)return s}return-1}});function Vv(){this.group=new Mn}var Fv=Vv.prototype;Fv.isPersistent=function(){return!this._incremental},Fv.updateData=function(t,e){this.group.removeAll();var n=new Bv({rectHover:!0,cursor:"default"});n.setShape({points:t.getLayout("symbolPoints")}),this._setCommon(n,t,!1,e),this.group.add(n),this._incremental=null},Fv.updateLayout=function(t){if(!this._incremental){var i=t.getLayout("symbolPoints");this.group.eachChild(function(t){if(null!=t.startIndex){var e=2*(t.endIndex-t.startIndex),n=4*t.startIndex*2;i=new Float32Array(i.buffer,n,e)}t.setShape("points",i)})}},Fv.incrementalPrepareUpdate=function(t){this.group.removeAll(),this._clearIncremental(),2e6<t.count()?(this._incremental||(this._incremental=new hs({silent:!0})),this.group.add(this._incremental)):this._incremental=null},Fv.incrementalUpdate=function(t,e,n){var i;this._incremental?(i=new Bv,this._incremental.addDisplayable(i,!0)):((i=new Bv({rectHover:!0,cursor:"default",startIndex:t.start,endIndex:t.end})).incremental=!0,this.group.add(i)),i.setShape({points:e.getLayout("symbolPoints")}),this._setCommon(i,e,!!this._incremental,n)},Fv._setCommon=function(n,t,e,i){var r=t.hostModel;i=i||{};var o=t.getVisual("symbolSize");n.setShape("size",o instanceof Array?o:[o,o]),n.softClipShape=i.clipShape||null,n.symbolProxy=ng(t.getVisual("symbol"),0,0,0,0),n.setColor=n.symbolProxy.setColor;var a=n.shape.size[0]<4;n.useStyle(r.getModel("itemStyle").getItemStyle(a?["color","shadowBlur","shadowColor"]:["color"]));var s=t.getVisual("color");s&&n.setColor(s),e||(n.seriesIndex=r.seriesIndex,n.on("mousemove",function(t){n.dataIndex=null;var e=n.findDataIndex(t.offsetX,t.offsetY);0<=e&&(n.dataIndex=e+(n.startIndex||0))}))},Fv.remove=function(){this._clearIncremental(),this._incremental=null,this.group.removeAll()},Fv._clearIncremental=function(){var t=this._incremental;t&&t.clearDisplaybles()},ff({type:"scatter",render:function(t,e,n){var i=t.getData();this._updateSymbolDraw(i,t).updateData(i,{clipShape:this._getClipShape(t)}),this._finished=!0},incrementalPrepareRender:function(t,e,n){var i=t.getData();this._updateSymbolDraw(i,t).incrementalPrepareUpdate(i),this._finished=!1},incrementalRender:function(t,e,n){this._symbolDraw.incrementalUpdate(t,e.getData(),{clipShape:this._getClipShape(e)}),this._finished=t.end===e.getData().count()},updateTransform:function(t,e,n){var i=t.getData();if(this.group.dirty(),!this._finished||1e4<i.count()||!this._symbolDraw.isPersistent())return{update:!0};var r=cm().reset(t);r.progress&&r.progress({start:0,end:i.count()},i),this._symbolDraw.updateLayout(i)},_getClipShape:function(t){var e=t.coordinateSystem,n=e&&e.getArea&&e.getArea();return t.get("clip",!0)?n:null},_updateSymbolDraw:function(t,e){var n=this._symbolDraw,i=e.pipelineContext.large;return n&&i===this._isLargeDraw||(n&&n.remove(),n=this._symbolDraw=i?new Vv:new Rg,this._isLargeDraw=i,this.group.removeAll()),this.group.add(n.group),n},remove:function(t,e){this._symbolDraw&&this._symbolDraw.remove(!0),this._symbolDraw=null},dispose:function(){}}),sf(hm("scatter","circle")),af(cm("scatter"));var Hv={path:null,compoundPath:null,group:Mn,image:Yi,text:Ba};nf(function(t){var e=t.graphic;O(e)?e[0]&&e[0].elements?t.graphic=[t.graphic[0]]:t.graphic=[{elements:e}]:e&&!e.elements&&(t.graphic=[{elements:[e]}])});var Wv=hf({type:"graphic",defaultOption:{elements:[],parentId:null},_elOptionsToUpdate:null,mergeOption:function(t){var e=this.option.elements;this.option.elements=null,Wv.superApply(this,"mergeOption",arguments),this.option.elements=e},optionUpdated:function(t,e){var n=this.option,i=(e?n:t).elements,r=n.elements=e?[]:n.elements,o=[];this._flatten(i,o);var a=Nr(r,o);Rr(a);var s=this._elOptionsToUpdate=[];D(a,function(t,e){var n=t.option;n&&(s.push(n),function(t,e){var n=t.exist;if(e.id=t.keyInfo.id,!e.type&&n&&(e.type=n.type),null==e.parentId){var i=e.parentOption;i?e.parentId=i.id:n&&(e.parentId=n.parentId)}e.parentOption=null}(t,n),function(t,e,n){var i=k({},n),r=t[e],o=n.$action||"merge";"merge"===o?r?(m(r,i,!0),hu(r,i,{ignoreSize:!0}),du(n,r)):t[e]=i:"replace"===o?t[e]=i:"remove"===o&&r&&(t[e]=null)}(r,e,n),function(t,e){if(!t)return;t.hv=e.hv=[Uv(e,["left","right"]),Uv(e,["top","bottom"])],"group"===t.type&&(null==t.width&&(t.width=e.width=0),null==t.height&&(t.height=e.height=0))}(r[e],n))},this);for(var l=r.length-1;0<=l;l--)null==r[l]?r.splice(l,1):delete r[l].$action},_flatten:function(t,n,i){D(t,function(t){if(t){i&&(t.parentOption=i),n.push(t);var e=t.children;"group"===t.type&&e&&this._flatten(e,n,t),delete t.children}},this)},useElOptionsToUpdate:function(){var t=this._elOptionsToUpdate;return this._elOptionsToUpdate=null,t}});function Gv(t,e,n,i){var r=n.type,o=new(Hv.hasOwnProperty(r)?Hv[r]:Ss(r))(n);e.add(o),i.set(t,o),o.__ecGraphicId=t}function Zv(t,e){var n=t&&t.parent;n&&("group"===t.type&&t.traverse(function(t){Zv(t,e)}),e.removeKey(t.__ecGraphicId),n.remove(t))}function Uv(e,t){var n;return D(t,function(t){null!=e[t]&&"auto"!==e[t]&&(n=!0)}),n}cf({type:"graphic",init:function(t,e){this._elMap=Q(),this._lastGraphicModel},render:function(t,e,n){t!==this._lastGraphicModel&&this._clear(),this._lastGraphicModel=t,this._updateElements(t),this._relocate(t,n)},_updateElements:function(u){var t=u.useElOptionsToUpdate();if(t){var h=this._elMap,c=this.group;D(t,function(t){var e=t.$action,n=t.id,i=h.get(n),r=t.parentId,o=null!=r?h.get(r):c,a=t.style;"text"===t.type&&a&&(t.hv&&t.hv[1]&&(a.textVerticalAlign=a.textBaseline=null),!a.hasOwnProperty("textFill")&&a.fill&&(a.textFill=a.fill),!a.hasOwnProperty("textStroke")&&a.stroke&&(a.textStroke=a.stroke));var s=function(e){return e=k({},e),D(["id","parentId","$action","hv","bounding"].concat(ru),function(t){delete e[t]}),e}(t);e&&"merge"!==e?"replace"===e?(Zv(i,h),Gv(n,o,s,h)):"remove"===e&&Zv(i,h):i?i.attr(s):Gv(n,o,s,h);var l=h.get(n);l&&(l.__ecGraphicWidthOption=t.width,l.__ecGraphicHeightOption=t.height,function(t,e){var n=t.eventData;t.silent||t.ignore||n||(n=t.eventData={componentType:"graphic",componentIndex:e.componentIndex,name:t.name});n&&(n.info=t.info)}(l,u))})}},_relocate:function(t,e){for(var n=t.option.elements,i=this.group,r=this._elMap,o=e.getWidth(),a=e.getHeight(),s=0;s<n.length;s++){var l=n[s];if((h=r.get(l.id))&&h.isGroup){var u=(c=h.parent)===i;h.__ecGraphicWidth=Cl(h.__ecGraphicWidthOption,u?o:c.__ecGraphicWidth)||0,h.__ecGraphicHeight=Cl(h.__ecGraphicHeightOption,u?a:c.__ecGraphicHeight)||0}}for(s=n.length-1;0<=s;s--){var h,c;l=n[s];if(h=r.get(l.id))uu(h,l,(c=h.parent)===i?{width:o,height:a}:{width:c.__ecGraphicWidth,height:c.__ecGraphicHeight},null,{hv:l.hv,boundingMode:l.bounding})}},_clear:function(){var e=this._elMap;e.each(function(t){Zv(t,e)}),this._elMap=Q()},dispose:function(){this._clear()}});function Xv(t,e){var n,i=[],r=t.seriesIndex;if(null==r||!(n=e.getSeriesByIndex(r)))return{point:[]};var o=n.getData(),a=Fr(o,t);if(null==a||a<0||O(a))return{point:[]};var s=o.getItemGraphicEl(a),l=n.coordinateSystem;if(n.getTooltipPosition)i=n.getTooltipPosition(a)||[];else if(l&&l.dataToPoint)i=l.dataToPoint(o.getValues(P(l.dimensions,function(t){return o.mapDimension(t)}),a,!0))||[];else if(s){var u=s.getBoundingRect().clone();u.applyTransform(s.transform),i=[u.x+u.width/2,u.y+u.height/2]}return{point:i,el:s}}var Yv=D,jv=T,qv=Hr();function $v(t,e,n,i,r){var o=t.axis;if(!o.scale.isBlank()&&o.containData(e))if(t.involveSeries){var a=function(l,t){var u=t.axis,h=u.dim,c=l,d=[],f=Number.MAX_VALUE,p=-1;return Yv(t.seriesModels,function(e,t){var n,i,r=e.getData().mapDimension(h,!0);if(e.getAxisTooltipData){var o=e.getAxisTooltipData(r,l,u);i=o.dataIndices,n=o.nestestValue}else{if(!(i=e.getData().indicesOfNearest(r[0],l,"category"===u.type?.5:null)).length)return;n=e.getData().get(r[0],i[0])}if(null!=n&&isFinite(n)){var a=l-n,s=Math.abs(a);s<=f&&((s<f||0<=a&&p<0)&&(f=s,p=a,c=n,d.length=0),Yv(i,function(t){d.push({seriesIndex:e.seriesIndex,dataIndexInside:t,dataIndex:e.getData().getRawIndex(t)})}))}}),{payloadBatch:d,snapToValue:c}}(e,t),s=a.payloadBatch,l=a.snapToValue;s[0]&&null==r.seriesIndex&&k(r,s[0]),!i&&t.snap&&o.containData(l)&&null!=l&&(e=l),n.showPointer(t,e,s,r),n.showTooltip(t,a,l)}else n.showPointer(t,e)}function Kv(t,e,n,i){t[e.key]={value:n,payloadBatch:i}}function Qv(t,e,n,i){var r=n.payloadBatch,o=e.axis,a=o.model,s=e.axisPointerModel;if(e.triggerTooltip&&r.length){var l=e.coordSys.model,u=qm(l),h=t.map[u];h||(h=t.map[u]={coordSysId:l.id,coordSysIndex:l.componentIndex,coordSysType:l.type,coordSysMainType:l.mainType,dataByAxis:[]},t.list.push(h)),h.dataByAxis.push({axisDim:o.dim,axisIndex:a.componentIndex,axisType:a.type,axisId:a.id,value:i,valueLabelOpt:{precision:s.get("label.precision"),formatter:s.get("label.formatter")},seriesDataIndices:r.slice()})}}function Jv(t){var e=t.axis.model,n={},i=n.axisDim=t.axis.dim;return n.axisIndex=n[i+"AxisIndex"]=e.componentIndex,n.axisName=n[i+"AxisName"]=e.name,n.axisId=n[i+"AxisId"]=e.id,n}function ty(t){return!t||null==t[0]||isNaN(t[0])||null==t[1]||isNaN(t[1])}hf({type:"axisPointer",coordSysAxesInfo:null,defaultOption:{show:"auto",triggerOn:null,zlevel:0,z:50,type:"line",snap:!1,triggerTooltip:!0,value:null,status:null,link:[],animation:null,animationDurationUpdate:200,lineStyle:{color:"#aaa",width:1,type:"solid"},shadowStyle:{color:"rgba(150,150,150,0.3)"},label:{show:!0,formatter:null,precision:"auto",margin:3,color:"#fff",padding:[5,7,5,7],backgroundColor:"auto",borderColor:null,borderWidth:0,shadowBlur:3,shadowColor:"#aaa"},handle:{show:!1,icon:"M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4h1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7v-1.2h6.6z M13.3,22H6.7v-1.2h6.6z M13.3,19.6H6.7v-1.2h6.6z",size:45,margin:50,color:"#333",shadowBlur:3,shadowColor:"#aaa",shadowOffsetX:0,shadowOffsetY:2,throttle:40}}});var ey=Hr(),ny=D;function iy(t,e,n){if(!v.node){var i=e.getZr();ey(i).records||(ey(i).records={}),function(r,o){if(ey(r).initialized)return;function t(t,i){r.on(t,function(e){var n=function(n){var i={showTip:[],hideTip:[]},r=function(t){var e=i[t.type];e?e.push(t):(t.dispatchAction=r,n.dispatchAction(t))};return{dispatchAction:r,pendings:i}}(o);ny(ey(r).records,function(t){t&&i(t,e,n.dispatchAction)}),function(t,e){var n,i=t.showTip.length,r=t.hideTip.length;i?n=t.showTip[i-1]:r&&(n=t.hideTip[r-1]);n&&(n.dispatchAction=null,e.dispatchAction(n))}(n.pendings,o)})}ey(r).initialized=!0,t("click",T(oy,"click")),t("mousemove",T(oy,"mousemove")),t("globalout",ry)}(i,e),(ey(i).records[t]||(ey(i).records[t]={})).handler=n}}function ry(t,e,n){t.handler("leave",null,n)}function oy(t,e,n,i){e.handler(t,n,i)}function ay(t,e){if(!v.node){var n=e.getZr();(ey(n).records||{})[t]&&(ey(n).records[t]=null)}}var sy=cf({type:"axisPointer",render:function(t,e,n){var i=e.getComponent("tooltip"),r=t.get("triggerOn")||i&&i.get("triggerOn")||"mousemove|click";iy("axisPointer",n,function(t,e,n){"none"!==r&&("leave"===t||0<=r.indexOf(t))&&n({type:"updateAxisPointer",currTrigger:t,x:e&&e.offsetX,y:e&&e.offsetY})})},remove:function(t,e){ay(e.getZr(),"axisPointer"),sy.superApply(this._model,"remove",arguments)},dispose:function(t,e){ay("axisPointer",e),sy.superApply(this._model,"dispose",arguments)}}),ly=Hr(),uy=b,hy=C;function cy(){}function dy(t,e,n,i){!function n(i,t){{if(N(i)&&N(t)){var r=!0;return D(t,function(t,e){r=r&&n(i[e],t)}),!!r}return i===t}}(ly(n).lastProp,i)&&(ly(n).lastProp=i,e?nl(n,i,t):(n.stopAnimation(),n.attr(i)))}function fy(t,e){t[e.get("label.show")?"show":"hide"]()}function py(t){return{position:t.position.slice(),rotation:t.rotation||0}}function gy(t,e,n){var i=e.get("z"),r=e.get("zlevel");t&&t.traverse(function(t){"group"!==t.type&&(null!=i&&(t.z=i),null!=r&&(t.zlevel=r),t.silent=n)})}function my(t,e,n,i,r){var o=vy(n.get("value"),e.axis,e.ecModel,n.get("seriesDataIndices"),{precision:n.get("label.precision"),formatter:n.get("label.formatter")}),a=n.getModel("label"),s=Gl(a.get("padding")||0),l=a.getFont(),u=ui(o,l),h=r.position,c=u.width+s[1]+s[3],d=u.height+s[0]+s[2],f=r.align;"right"===f&&(h[0]-=c),"center"===f&&(h[0]-=c/2);var p=r.verticalAlign;"bottom"===p&&(h[1]-=d),"middle"===p&&(h[1]-=d/2),function(t,e,n,i){var r=i.getWidth(),o=i.getHeight();t[0]=Math.min(t[0]+e,r)-e,t[1]=Math.min(t[1]+n,o)-n,t[0]=Math.max(t[0],0),t[1]=Math.max(t[1],0)}(h,c,d,i);var g=a.get("backgroundColor");g&&"auto"!==g||(g=e.get("axisLine.lineStyle.color")),t.label={shape:{x:0,y:0,width:c,height:d,r:a.get("borderRadius")},position:h.slice(),style:{text:o,textFont:l,textFill:a.getTextColor(),textPosition:"inside",textPadding:s,fill:g,stroke:a.get("borderColor")||"transparent",lineWidth:a.get("borderWidth")||0,shadowBlur:a.get("shadowBlur"),shadowColor:a.get("shadowColor"),shadowOffsetX:a.get("shadowOffsetX"),shadowOffsetY:a.get("shadowOffsetY")},z2:10}}function vy(t,e,r,n,i){t=e.scale.parse(t);var o=e.scale.getLabel(t,{precision:i.precision}),a=i.formatter;if(a){var s={value:Zp(e,t),axisDimension:e.dim,axisIndex:e.index,seriesData:[]};D(n,function(t){var e=r.getSeriesByIndex(t.seriesIndex),n=t.dataIndexInside,i=e&&e.getDataParams(n);i&&s.seriesData.push(i)}),z(a)?o=a.replace("{value}",o):E(a)&&(o=a(s))}return o}function yy(t,e,n){var i=ee();return ae(i,i,n.rotation),oe(i,i,n.position),ol([t.dataToCoord(e),(n.labelOffset||0)+(n.labelDirection||1)*(n.labelMargin||0)],i)}$r((cy.prototype={_group:null,_lastGraphicKey:null,_handle:null,_dragging:!1,_lastValue:null,_lastStatus:null,_payloadInfo:null,animationThreshold:15,render:function(t,e,n,i){var r=e.get("value"),o=e.get("status");if(this._axisModel=t,this._axisPointerModel=e,this._api=n,i||this._lastValue!==r||this._lastStatus!==o){this._lastValue=r,this._lastStatus=o;var a=this._group,s=this._handle;if(!o||"hide"===o)return a&&a.hide(),void(s&&s.hide());a&&a.show(),s&&s.show();var l={};this.makeElOption(l,r,t,e,n);var u=l.graphicKey;u!==this._lastGraphicKey&&this.clear(n),this._lastGraphicKey=u;var h=this._moveAnimation=this.determineAnimation(t,e);if(a){var c=T(dy,e,h);this.updatePointerEl(a,l,c,e),this.updateLabelEl(a,l,c,e)}else a=this._group=new Mn,this.createPointerEl(a,l,t,e),this.createLabelEl(a,l,t,e),n.getZr().add(a);gy(a,e,!0),this._renderHandle(r)}},remove:function(t){this.clear(t)},dispose:function(t){this.clear(t)},determineAnimation:function(t,e){var n=e.get("animation"),i=t.axis,r="category"===i.type,o=e.get("snap");if(!o&&!r)return!1;if("auto"!==n&&null!=n)return!0===n;var a=this.animationThreshold;if(r&&i.getBandWidth()>a)return!0;if(o){var s=Ym(t).seriesDataCount,l=i.getExtent();return Math.abs(l[0]-l[1])/s>a}return!1},makeElOption:function(t,e,n,i,r){},createPointerEl:function(t,e,n,i){var r=e.pointer;if(r){var o=ly(t).pointerEl=new dl[r.type](uy(e.pointer));t.add(o)}},createLabelEl:function(t,e,n,i){if(e.label){var r=ly(t).labelEl=new Ja(uy(e.label));t.add(r),fy(r,i)}},updatePointerEl:function(t,e,n){var i=ly(t).pointerEl;i&&e.pointer&&(i.setStyle(e.pointer.style),n(i,{shape:e.pointer.shape}))},updateLabelEl:function(t,e,n,i){var r=ly(t).labelEl;r&&(r.setStyle(e.label.style),n(r,{shape:e.label.shape,position:e.label.position}),fy(r,i))},_renderHandle:function(t){if(!this._dragging&&this.updateHandleTransform){var e,n=this._axisPointerModel,i=this._api.getZr(),r=this._handle,o=n.getModel("handle"),a=n.get("status");if(!o.get("show")||!a||"hide"===a)return r&&i.remove(r),void(this._handle=null);this._handle||(e=!0,r=this._handle=ul(o.get("icon"),{cursor:"move",draggable:!0,onmousemove:function(t){Wt(t.event)},onmousedown:hy(this._onHandleDragMove,this,0,0),drift:hy(this._onHandleDragMove,this),ondragend:hy(this._onHandleDragEnd,this)}),i.add(r)),gy(r,n,!1);r.setStyle(o.getItemStyle(null,["color","borderColor","borderWidth","opacity","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY"]));var s=o.get("size");O(s)||(s=[s,s]),r.attr("scale",[s[0]/2,s[1]/2]),vc(this,"_doDispatchAxisPointer",o.get("throttle")||0,"fixRate"),this._moveHandleToValue(t,e)}},_moveHandleToValue:function(t,e){dy(this._axisPointerModel,!e&&this._moveAnimation,this._handle,py(this.getHandleTransform(t,this._axisModel,this._axisPointerModel)))},_onHandleDragMove:function(t,e){var n=this._handle;if(n){this._dragging=!0;var i=this.updateHandleTransform(py(n),[t,e],this._axisModel,this._axisPointerModel);this._payloadInfo=i,n.stopAnimation(),n.attr(py(i)),ly(n).lastProp=null,this._doDispatchAxisPointer()}},_doDispatchAxisPointer:function(){if(this._handle){var t=this._payloadInfo,e=this._axisModel;this._api.dispatchAction({type:"updateAxisPointer",x:t.cursorPoint[0],y:t.cursorPoint[1],tooltipOption:t.tooltipOption,axesInfo:[{axisDim:e.axis.dim,axisIndex:e.componentIndex}]})}},_onHandleDragEnd:function(t){if(this._dragging=!1,this._handle){var e=this._axisPointerModel.get("value");this._moveHandleToValue(e),this._api.dispatchAction({type:"hideTip"})}},getHandleTransform:null,updateHandleTransform:null,clear:function(t){this._lastValue=null,this._lastStatus=null;var e=t.getZr(),n=this._group,i=this._handle;e&&n&&(this._lastGraphicKey=null,n&&e.remove(n),i&&e.remove(i),this._group=null,this._handle=null,this._payloadInfo=null)},doClear:function(){},buildLabel:function(t,e,n){return{x:t[n=n||0],y:t[1-n],width:e[n],height:e[1-n]}}}).constructor=cy);var _y=cy.extend({makeElOption:function(t,e,n,i,r){var o=n.axis,a=o.grid,s=i.get("type"),l=xy(a,o).getOtherAxis(o).getGlobalExtent(),u=o.toGlobalCoord(o.dataToCoord(e,!0));if(s&&"none"!==s){var h=function(t){var e,n=t.get("type"),i=t.getModel(n+"Style");return"line"===n?(e=i.getLineStyle()).fill=null:"shadow"===n&&((e=i.getAreaStyle()).stroke=null),e}(i),c=wy[s](o,u,l);c.style=h,t.graphicKey=c.type,t.pointer=c}!function(t,e,n,i,r,o){var a=Om.innerTextLayout(n.rotation,0,n.labelDirection);n.labelMargin=r.get("label.margin"),my(e,i,r,o,{position:yy(i.axis,t,n),align:a.textAlign,verticalAlign:a.textVerticalAlign})}(e,t,tv(a.model,n),n,i,r)},getHandleTransform:function(t,e,n){var i=tv(e.axis.grid.model,e,{labelInside:!1});return i.labelMargin=n.get("handle.margin"),{position:yy(e.axis,t,i),rotation:i.rotation+(i.labelDirection<0?Math.PI:0)}},updateHandleTransform:function(t,e,n,i){var r=n.axis,o=r.grid,a=r.getGlobalExtent(!0),s=xy(o,r).getOtherAxis(r).getGlobalExtent(),l="x"===r.dim?0:1,u=t.position;u[l]+=e[l],u[l]=Math.min(a[1],u[l]),u[l]=Math.max(a[0],u[l]);var h=(s[1]+s[0])/2,c=[h,h];c[l]=u[l];return{position:u,rotation:t.rotation,cursorPoint:c,tooltipOption:[{verticalAlign:"middle"},{align:"center"}][l]}}});function xy(t,e){var n={};return n[e.dim+"AxisIndex"]=e.index,t.getCartesian(n)}var wy={line:function(t,e,n){return{type:"Line",subPixelOptimize:!0,shape:function(t,e,n){return{x1:t[n=n||0],y1:t[1-n],x2:e[n],y2:e[1-n]}}([e,n[0]],[e,n[1]],by(t))}},shadow:function(t,e,n){var i=Math.max(1,t.getBandWidth()),r=n[1]-n[0];return{type:"Rect",shape:function(t,e,n){return{x:t[n=n||0],y:t[1-n],width:e[n],height:e[1-n]}}([e-i/2,n[0]],[i,r],by(t))}}};function by(t){return"x"===t.dim?0:1}$m.registerAxisPointerClass("CartesianAxisPointer",_y),nf(function(t){if(t){t.axisPointer&&0!==t.axisPointer.length||(t.axisPointer={});var e=t.axisPointer.link;e&&!O(e)&&(t.axisPointer.link=[e])}}),rf(gd.PROCESSOR.STATISTIC,function(t,e){t.getComponent("axisPointer").coordSysAxesInfo=Um(t,e)}),of({type:"updateAxisPointer",event:"updateAxisPointer",update:":updateAxisPointer"},function(t,e,n){var i=t.currTrigger,a=[t.x,t.y],r=t,o=t.dispatchAction||C(n.dispatchAction,n),s=e.getComponent("axisPointer").coordSysAxesInfo;if(s){ty(a)&&(a=Xv({seriesIndex:r.seriesIndex,dataIndex:r.dataIndex},e).point);var l=ty(a),u=r.axesInfo,h=s.axesInfo,c="leave"===i||ty(a),d={},f={},p={list:[],map:{}},g={showPointer:jv(Kv,f),showTooltip:jv(Qv,p)};Yv(s.coordSysMap,function(t,e){var o=l||t.containPoint(a);Yv(s.coordSysAxesInfo[e],function(t,e){var n=t.axis,i=function(t,e){for(var n=0;n<(t||[]).length;n++){var i=t[n];if(e.axis.dim===i.axisDim&&e.axis.model.componentIndex===i.axisIndex)return i}}(u,t);if(!c&&o&&(!u||i)){var r=i&&i.value;null!=r||l||(r=n.pointToData(a)),null!=r&&$v(t,r,g,!1,d)}})});var m={};return Yv(h,function(r,t){var o=r.linkGroup;o&&!f[t]&&Yv(o.axesInfo,function(t,e){var n=f[e];if(t!==r&&n){var i=n.value;o.mapper&&(i=r.axis.scale.parse(o.mapper(i,Jv(t),Jv(r)))),m[r.key]=i}})}),Yv(m,function(t,e){$v(h[e],t,g,!0,d)}),function(r,t,e){var o=e.axesInfo=[];Yv(t,function(t,e){var n=t.axisPointerModel.option,i=r[e];i?(t.useHandle||(n.status="show"),n.value=i.value,n.seriesDataIndices=(i.payloadBatch||[]).slice()):t.useHandle||(n.status="hide"),"show"===n.status&&o.push({axisDim:t.axis.dim,axisIndex:t.axis.model.componentIndex,value:n.value})})}(f,h,d),function(t,e,n,i){if(ty(e)||!t.list.length)return i({type:"hideTip"});var r=((t.list[0].dataByAxis[0]||{}).seriesDataIndices||[])[0]||{};i({type:"showTip",escapeConnect:!0,x:e[0],y:e[1],tooltipOption:n.tooltipOption,position:n.position,dataIndexInside:r.dataIndexInside,dataIndex:r.dataIndex,seriesIndex:r.seriesIndex,dataByCoordSys:t.list})}(p,a,t,o),function(t,e,n){var i=n.getZr(),r="axisPointerLastHighlights",o=qv(i)[r]||{},a=qv(i)[r]={};Yv(t,function(t,e){var n=t.axisPointerModel.option;"show"===n.status&&Yv(n.seriesDataIndices,function(t){var e=t.seriesIndex+" | "+t.dataIndex;a[e]=t})});var s=[],l=[];D(o,function(t,e){a[e]||l.push(t)}),D(a,function(t,e){o[e]||s.push(t)}),l.length&&n.dispatchAction({type:"downplay",escapeConnect:!0,batch:l}),s.length&&n.dispatchAction({type:"highlight",escapeConnect:!0,batch:s})}(h,0,n),d}}),hf({type:"tooltip",dependencies:["axisPointer"],defaultOption:{zlevel:0,z:60,show:!0,showContent:!0,trigger:"item",triggerOn:"mousemove|click",alwaysShowContent:!1,displayMode:"single",renderMode:"auto",confine:!1,showDelay:0,hideDelay:100,transitionDuration:.4,enterable:!1,backgroundColor:"rgba(50,50,50,0.7)",borderColor:"#333",borderRadius:4,borderWidth:0,padding:5,extraCssText:"",axisPointer:{type:"line",axis:"auto",animation:"auto",animationDurationUpdate:200,animationEasingUpdate:"exponentialOut",crossStyle:{color:"#999",width:1,type:"dashed",textStyle:{}}},textStyle:{color:"#fff",fontSize:14}}});var Sy=D,My=Wl,Iy=["","-webkit-","-moz-","-o-"];function Cy(r){var o=[],t=r.get("transitionDuration"),e=r.get("backgroundColor"),n=r.getModel("textStyle"),i=r.get("padding");return t&&o.push(function(t){var e="cubic-bezier(0.23, 1, 0.32, 1)",n="left "+t+"s "+e+",top "+t+"s "+e;return P(Iy,function(t){return t+"transition:"+n}).join(";")}(t)),e&&(v.canvasSupported?o.push("background-Color:"+e):(o.push("background-Color:#"+Ve(e)),o.push("filter:alpha(opacity=70)"))),Sy(["width","color","radius"],function(t){var e="border-"+t,n=My(e),i=r.get(n);null!=i&&o.push(e+":"+i+("color"===t?"":"px"))}),o.push(function(n){var i=[],t=n.get("fontSize"),e=n.getTextColor();return e&&i.push("color:"+e),i.push("font:"+n.getFont()),t&&i.push("line-height:"+Math.round(3*t/2)+"px"),Sy(["decoration","align"],function(t){var e=n.get(t);e&&i.push("text-"+t+":"+e)}),i.join(";")}(n)),null!=i&&o.push("padding:"+Gl(i).join("px ")+"px"),o.join(";")+";"}function Ty(t,e,n,i,r){var o=e&&e.painter;if(n){var a=o&&o.getViewportRoot();a&&function(t,e,n,i,r){Lt(Pt,e,i,r,!0)&&Lt(t,n,Pt[0],Pt[1])}(t,a,document.body,i,r)}else{t[0]=i,t[1]=r;var s=o&&o.getViewportRootOffset();s&&(t[0]+=s.offsetLeft,t[1]+=s.offsetTop)}}function Ay(t,e,n){if(v.wxa)return null;var i=document.createElement("div");i.domBelongToZr=!0,this.el=i;var r=this._zr=e.getZr(),o=this._appendToBody=n&&n.appendToBody;this._styleCoord=[0,0],Ty(this._styleCoord,r,o,e.getWidth()/2,e.getHeight()/2),o?document.body.appendChild(i):t.appendChild(i),this._container=t,this._show=!1,this._hideTimeout;var a=this;i.onmouseenter=function(){a._enterable&&(clearTimeout(a._hideTimeout),a._show=!0),a._inContent=!0},i.onmousemove=function(t){if(t=t||window.event,!a._enterable){var e=r.handler;Ft(r.painter.getViewportRoot(),t,!0),e.dispatch("mousemove",t)}},i.onmouseleave=function(){a._enterable&&a._show&&a.hideLater(a._hideDelay),a._inContent=!1}}function Dy(t){this._zr=t.getZr(),this._show=!1,this._hideTimeout}Ay.prototype={constructor:Ay,_enterable:!0,update:function(){var t=this._container,e=t.currentStyle||document.defaultView.getComputedStyle(t),n=t.style;"absolute"!==n.position&&"absolute"!==e.position&&(n.position="relative")},show:function(t){clearTimeout(this._hideTimeout);var e=this.el,n=this._styleCoord;e.style.cssText="position:absolute;display:block;border-style:solid;white-space:nowrap;z-index:9999999;"+Cy(t)+";left:"+n[0]+"px;top:"+n[1]+"px;"+(t.get("extraCssText")||""),e.style.display=e.innerHTML?"block":"none",e.style.pointerEvents=this._enterable?"auto":"none",this._show=!0},setContent:function(t){this.el.innerHTML=null==t?"":t},setEnterable:function(t){this._enterable=t},getSize:function(){var t=this.el;return[t.clientWidth,t.clientHeight]},moveTo:function(t,e){var n=this._styleCoord;Ty(n,this._zr,this._appendToBody,t,e);var i=this.el.style;i.left=n[0]+"px",i.top=n[1]+"px"},hide:function(){this.el.style.display="none",this._show=!1},hideLater:function(t){!this._show||this._inContent&&this._enterable||(t?(this._hideDelay=t,this._show=!1,this._hideTimeout=setTimeout(C(this.hide,this),t)):this.hide())},isShow:function(){return this._show},dispose:function(){this.el.parentNode.removeChild(this.el)},getOuterSize:function(){var t=this.el.clientWidth,e=this.el.clientHeight;if(document.defaultView&&document.defaultView.getComputedStyle){var n=document.defaultView.getComputedStyle(this.el);n&&(t+=parseInt(n.borderLeftWidth,10)+parseInt(n.borderRightWidth,10),e+=parseInt(n.borderTopWidth,10)+parseInt(n.borderBottomWidth,10))}return{width:t,height:e}}},Dy.prototype={constructor:Dy,_enterable:!0,update:function(){},show:function(t){this._hideTimeout&&clearTimeout(this._hideTimeout),this.el.attr("show",!0),this._show=!0},setContent:function(t,e,n){this.el&&this._zr.remove(this.el);for(var i={},r=t,o="{marker",a=r.indexOf(o);0<=a;){var s=r.indexOf("|}"),l=r.substr(a+o.length,s-a-o.length);-1<l.indexOf("sub")?i["marker"+l]={textWidth:4,textHeight:4,textBorderRadius:2,textBackgroundColor:e[l],textOffset:[3,0]}:i["marker"+l]={textWidth:10,textHeight:10,textBorderRadius:5,textBackgroundColor:e[l]},a=(r=r.substr(s+1)).indexOf("{marker")}this.el=new Ba({style:{rich:i,text:t,textLineHeight:20,textBackgroundColor:n.get("backgroundColor"),textBorderRadius:n.get("borderRadius"),textFill:n.get("textStyle.color"),textPadding:n.get("padding")},z:n.get("z")}),this._zr.add(this.el);var u=this;this.el.on("mouseover",function(){u._enterable&&(clearTimeout(u._hideTimeout),u._show=!0),u._inContent=!0}),this.el.on("mouseout",function(){u._enterable&&u._show&&u.hideLater(u._hideDelay),u._inContent=!1})},setEnterable:function(t){this._enterable=t},getSize:function(){var t=this.el.getBoundingRect();return[t.width,t.height]},moveTo:function(t,e){this.el&&this.el.attr("position",[t,e])},hide:function(){this.el&&this.el.hide(),this._show=!1},hideLater:function(t){!this._show||this._inContent&&this._enterable||(t?(this._hideDelay=t,this._show=!1,this._hideTimeout=setTimeout(C(this.hide,this),t)):this.hide())},isShow:function(){return this._show},getOuterSize:function(){var t=this.getSize();return{width:t[0],height:t[1]}}};var ky=C,Py=D,Ly=Cl,Oy=new Ja({shape:{x:-1,y:-1,width:2,height:2}});function Ey(t){for(var e=t.pop();t.length;){var n=t.pop();n&&(_l.isInstance(n)&&(n=n.get("tooltip",!0)),"string"==typeof n&&(n={formatter:n}),e=new _l(n,e,e.ecModel))}return e}function zy(t,e){return t.dispatchAction||C(e.dispatchAction,e)}function Ny(t){return"center"===t||"middle"===t}cf({type:"tooltip",init:function(t,e){if(!v.node){var n,i=t.getComponent("tooltip"),r=i.get("renderMode");this._renderMode=Xr(r),"html"===this._renderMode?(n=new Ay(e.getDom(),e,{appendToBody:i.get("appendToBody",!0)}),this._newLine="<br/>"):(n=new Dy(e),this._newLine="\n"),this._tooltipContent=n}},render:function(t,e,n){if(!v.node){this.group.removeAll(),this._tooltipModel=t,this._ecModel=e,this._api=n,this._lastDataByCoordSys=null,this._alwaysShowContent=t.get("alwaysShowContent");var i=this._tooltipContent;i.update(),i.setEnterable(t.get("enterable")),this._initGlobalListener(),this._keepShow()}},_initGlobalListener:function(){var i=this._tooltipModel.get("triggerOn");iy("itemTooltip",this._api,ky(function(t,e,n){"none"!==i&&(0<=i.indexOf(t)?this._tryShow(e,n):"leave"===t&&this._hide(n))},this))},_keepShow:function(){var t=this._tooltipModel,e=this._ecModel,n=this._api;if(null!=this._lastX&&null!=this._lastY&&"none"!==t.get("triggerOn")){var i=this;clearTimeout(this._refreshUpdateTimeout),this._refreshUpdateTimeout=setTimeout(function(){n.isDisposed()||i.manuallyShowTip(t,e,n,{x:i._lastX,y:i._lastY})})}},manuallyShowTip:function(t,e,n,i){if(i.from!==this.uid&&!v.node){var r=zy(i,n);this._ticket="";var o=i.dataByCoordSys;if(i.tooltip&&null!=i.x&&null!=i.y){var a=Oy;a.position=[i.x,i.y],a.update(),a.tooltip=i.tooltip,this._tryShow({offsetX:i.x,offsetY:i.y,target:a},r)}else if(o)this._tryShow({offsetX:i.x,offsetY:i.y,position:i.position,dataByCoordSys:i.dataByCoordSys,tooltipOption:i.tooltipOption},r);else if(null!=i.seriesIndex){if(this._manuallyAxisShowTip(t,e,n,i))return;var s=Xv(i,e),l=s.point[0],u=s.point[1];null!=l&&null!=u&&this._tryShow({offsetX:l,offsetY:u,position:i.position,target:s.el},r)}else null!=i.x&&null!=i.y&&(n.dispatchAction({type:"updateAxisPointer",x:i.x,y:i.y}),this._tryShow({offsetX:i.x,offsetY:i.y,position:i.position,target:n.getZr().findHover(i.x,i.y).target},r))}},manuallyHideTip:function(t,e,n,i){var r=this._tooltipContent;!this._alwaysShowContent&&this._tooltipModel&&r.hideLater(this._tooltipModel.get("hideDelay")),this._lastX=this._lastY=null,i.from!==this.uid&&this._hide(zy(i,n))},_manuallyAxisShowTip:function(t,e,n,i){var r=i.seriesIndex,o=i.dataIndex,a=e.getComponent("axisPointer").coordSysAxesInfo;if(null!=r&&null!=o&&null!=a){var s=e.getSeriesByIndex(r);if(s)if("axis"===(t=Ey([s.getData().getItemModel(o),s,(s.coordinateSystem||{}).model,t])).get("trigger"))return n.dispatchAction({type:"updateAxisPointer",seriesIndex:r,dataIndex:o,position:i.position}),!0}},_tryShow:function(t,e){var n=t.target;if(this._tooltipModel){this._lastX=t.offsetX,this._lastY=t.offsetY;var i=t.dataByCoordSys;i&&i.length?this._showAxisTooltip(i,t):n&&null!=n.dataIndex?(this._lastDataByCoordSys=null,this._showSeriesItemTooltip(t,n,e)):n&&n.tooltip?(this._lastDataByCoordSys=null,this._showComponentItemTooltip(t,n,e)):(this._lastDataByCoordSys=null,this._hide(e))}},_showOrMove:function(t,e){var n=t.get("showDelay");e=C(e,this),clearTimeout(this._showTimout),0<n?this._showTimout=setTimeout(e,n):e()},_showAxisTooltip:function(t,e){var d=this._ecModel,n=this._tooltipModel,i=[e.offsetX,e.offsetY],r=[],f=[],o=Ey([e.tooltipOption,n]),p=this._renderMode,a=this._newLine,g={};Py(t,function(t){Py(t.dataByAxis,function(s){var l=d.getComponent(s.axisDim+"Axis",s.axisIndex),u=s.value,h=[];if(l&&null!=u){var c=vy(u,l.axis,d,s.seriesDataIndices,s.valueLabelOpt);D(s.seriesDataIndices,function(t){var e=d.getSeriesByIndex(t.seriesIndex),n=t.dataIndexInside,i=e&&e.getDataParams(n);if(i.axisDim=s.axisDim,i.axisIndex=s.axisIndex,i.axisType=s.axisType,i.axisId=s.axisId,i.axisValue=Zp(l.axis,u),i.axisValueLabel=c,i){f.push(i);var r,o=e.formatTooltip(n,!0,null,p);if(N(o)){r=o.html;var a=o.markers;m(g,a)}else r=o;h.push(r)}});var t=c;"html"!==p?r.push(h.join(a)):r.push((t?Xl(t)+a:"")+h.join(a))}})},this),r.reverse(),r=r.join(this._newLine+this._newLine);var s=e.position;this._showOrMove(o,function(){this._updateContentNotChangedOnAxis(t)?this._updatePosition(o,s,i[0],i[1],this._tooltipContent,f):this._showTooltipContent(o,r,f,Math.random(),i[0],i[1],s,void 0,g)})},_showSeriesItemTooltip:function(t,e,n){var i=this._ecModel,r=e.seriesIndex,o=i.getSeriesByIndex(r),a=e.dataModel||o,s=e.dataIndex,l=e.dataType,u=a.getData(l),h=Ey([u.getItemModel(s),a,o&&(o.coordinateSystem||{}).model,this._tooltipModel]),c=h.get("trigger");if(null==c||"item"===c){var d,f,p=a.getDataParams(s,l),g=a.formatTooltip(s,!1,l,this._renderMode);f=N(g)?(d=g.html,g.markers):(d=g,null);var m="item_"+a.name+"_"+s;this._showOrMove(h,function(){this._showTooltipContent(h,d,p,m,t.offsetX,t.offsetY,t.position,t.target,f)}),n({type:"showTip",dataIndexInside:s,dataIndex:u.getRawIndex(s),seriesIndex:r,from:this.uid})}},_showComponentItemTooltip:function(t,e,n){var i=e.tooltip;if("string"==typeof i){i={content:i,formatter:i}}var r=new _l(i,this._tooltipModel,this._ecModel),o=r.get("content"),a=Math.random();this._showOrMove(r,function(){this._showTooltipContent(r,o,r.get("formatterParams")||{},a,t.offsetX,t.offsetY,t.position,e)}),n({type:"showTip",from:this.uid})},_showTooltipContent:function(n,t,i,e,r,o,a,s,l){if(this._ticket="",n.get("showContent")&&n.get("show")){var u=this._tooltipContent,h=n.get("formatter");a=a||n.get("position");var c=t;if(h&&"string"==typeof h)c=ql(h,i,!0);else if("function"==typeof h){var d=ky(function(t,e){t===this._ticket&&(u.setContent(e,l,n),this._updatePosition(n,a,r,o,u,i,s))},this);this._ticket=e,c=h(i,e,d)}u.setContent(c,l,n),u.show(n),this._updatePosition(n,a,r,o,u,i,s)}},_updatePosition:function(t,e,n,i,r,o,a){var s=this._api.getWidth(),l=this._api.getHeight();e=e||t.get("position");var u=r.getSize(),h=t.get("align"),c=t.get("verticalAlign"),d=a&&a.getBoundingRect().clone();if(a&&d.applyTransform(a.transform),"function"==typeof e&&(e=e([n,i],o,r.el,d,{viewSize:[s,l],contentSize:u.slice()})),O(e))n=Ly(e[0],s),i=Ly(e[1],l);else if(N(e)){e.width=u[0],e.height=u[1];var f=lu(e,{width:s,height:l});n=f.x,i=f.y,c=h=null}else if("string"==typeof e&&a){n=(p=function(t,e,n){var i=n[0],r=n[1],o=0,a=0,s=e.width,l=e.height;switch(t){case"inside":o=e.x+s/2-i/2,a=e.y+l/2-r/2;break;case"top":o=e.x+s/2-i/2,a=e.y-r-5;break;case"bottom":o=e.x+s/2-i/2,a=e.y+l+5;break;case"left":o=e.x-i-5,a=e.y+l/2-r/2;break;case"right":o=e.x+s+5,a=e.y+l/2-r/2}return[o,a]}(e,d,u))[0],i=p[1]}else{var p;n=(p=function(t,e,n,i,r,o,a){var s=n.getOuterSize(),l=s.width,u=s.height;null!=o&&(i<t+l+o?t-=l+o:t+=o);null!=a&&(r<e+u+a?e-=u+a:e+=a);return[t,e]}(n,i,r,s,l,h?null:20,c?null:20))[0],i=p[1]}h&&(n-=Ny(h)?u[0]/2:"right"===h?u[0]:0),c&&(i-=Ny(c)?u[1]/2:"bottom"===c?u[1]:0),t.get("confine")&&(n=(p=function(t,e,n,i,r){var o=n.getOuterSize(),a=o.width,s=o.height;return t=Math.min(t+a,i)-a,e=Math.min(e+s,r)-s,t=Math.max(t,0),e=Math.max(e,0),[t,e]}(n,i,r,s,l))[0],i=p[1]);r.moveTo(n,i)},_updateContentNotChangedOnAxis:function(i){var t=this._lastDataByCoordSys,a=!!t&&t.length===i.length;return a&&Py(t,function(t,e){var n=t.dataByAxis||{},o=(i[e]||{}).dataByAxis||[];(a&=n.length===o.length)&&Py(n,function(t,e){var n=o[e]||{},i=t.seriesDataIndices||[],r=n.seriesDataIndices||[];(a&=t.value===n.value&&t.axisType===n.axisType&&t.axisId===n.axisId&&i.length===r.length)&&Py(i,function(t,e){var n=r[e];a&=t.seriesIndex===n.seriesIndex&&t.dataIndex===n.dataIndex})})}),this._lastDataByCoordSys=i,!!a},_hide:function(t){this._lastDataByCoordSys=null,t({type:"hideTip",from:this.uid})},dispose:function(t,e){v.node||(this._tooltipContent.dispose(),ay("itemTooltip",e))}}),of({type:"showTip",event:"showTip",update:"tooltip:manuallyShowTip"},function(){}),of({type:"hideTip",event:"hideTip",update:"tooltip:manuallyHideTip"},function(){});var Ry=xc.legend.selector,By={all:{type:"all",title:b(Ry.all)},inverse:{type:"inverse",title:b(Ry.inverse)}},Vy=hf({type:"legend.plain",dependencies:["series"],layoutMode:{type:"box",ignoreSize:!0},init:function(t,e,n){this.mergeDefaultAndTheme(t,n),t.selected=t.selected||{},this._updateSelector(t)},mergeOption:function(t){Vy.superCall(this,"mergeOption",t),this._updateSelector(t)},_updateSelector:function(t){var n=t.selector;!0===n&&(n=t.selector=["all","inverse"]),O(n)&&D(n,function(t,e){z(t)&&(t={type:t}),n[e]=m(t,By[t.type])})},optionUpdated:function(){this._updateData(this.ecModel);var t=this._data;if(t[0]&&"single"===this.get("selectedMode")){for(var e=!1,n=0;n<t.length;n++){var i=t[n].get("name");if(this.isSelected(i)){this.select(i),e=!0;break}}e||this.select(t[0].get("name"))}},_updateData:function(r){var o=[],a=[];r.eachRawSeries(function(t){var e,n=t.name;if(a.push(n),t.legendVisualProvider){var i=t.legendVisualProvider.getAllNames();r.isSeriesFiltered(t)||(a=a.concat(i)),i.length?o=o.concat(i):e=!0}else e=!0;e&&Br(t)&&o.push(t.name)}),this._availableNames=a;var t=P(this.get("data")||o,function(t){return"string"!=typeof t&&"number"!=typeof t||(t={name:t}),new _l(t,this,this.ecModel)},this);this._data=t},getData:function(){return this._data},select:function(t){var e=this.option.selected;"single"===this.get("selectedMode")&&D(this._data,function(t){e[t.get("name")]=!1});e[t]=!0},unSelect:function(t){"single"!==this.get("selectedMode")&&(this.option.selected[t]=!1)},toggleSelected:function(t){var e=this.option.selected;e.hasOwnProperty(t)||(e[t]=!0),this[e[t]?"unSelect":"select"](t)},allSelect:function(){var t=this._data,e=this.option.selected;D(t,function(t){e[t.get("name",!0)]=!0})},inverseSelect:function(){var t=this._data,n=this.option.selected;D(t,function(t){var e=t.get("name",!0);n.hasOwnProperty(e)||(n[e]=!0),n[e]=!n[e]})},isSelected:function(t){var e=this.option.selected;return!(e.hasOwnProperty(t)&&!e[t])&&0<=x(this._availableNames,t)},getOrient:function(){return"vertical"===this.get("orient")?{index:1,name:"vertical"}:{index:0,name:"horizontal"}},defaultOption:{zlevel:0,z:4,show:!0,orient:"horizontal",left:"center",top:0,align:"auto",backgroundColor:"rgba(0,0,0,0)",borderColor:"#ccc",borderRadius:0,borderWidth:0,padding:5,itemGap:10,itemWidth:25,itemHeight:14,inactiveColor:"#ccc",inactiveBorderColor:"#ccc",itemStyle:{borderWidth:0},textStyle:{color:"#333"},selectedMode:!0,selector:!1,selectorLabel:{show:!0,borderRadius:10,padding:[3,5,3,5],fontSize:12,fontFamily:" sans-serif",color:"#666",borderWidth:1,borderColor:"#666"},emphasis:{selectorLabel:{show:!0,color:"#eee",backgroundColor:"#666"}},selectorPosition:"auto",selectorItemGap:7,selectorButtonGap:10,tooltip:{show:!1}}});function Fy(t,e,n){var r,o={},a="toggleSelected"===t;return n.eachComponent("legend",function(i){a&&null!=r?i[r?"select":"unSelect"](e.name):"allSelect"===t||"inverseSelect"===t?i[t]():(i[t](e.name),r=i.isSelected(e.name)),D(i.getData(),function(t){var e=t.get("name");if("\n"!==e&&""!==e){var n=i.isSelected(e);o.hasOwnProperty(e)?o[e]=o[e]&&n:o[e]=n}})}),"allSelect"===t||"inverseSelect"===t?{selected:o}:{name:e.name,selected:o}}function Hy(t,e){var n=Gl(e.get("padding")),i=e.getItemStyle(["color","opacity"]);return i.fill=e.get("backgroundColor"),t=new Ja({shape:{x:t.x-n[3],y:t.y-n[0],width:t.width+n[1]+n[3],height:t.height+n[0]+n[2],r:e.get("borderRadius")},style:i,silent:!0,z2:-1})}of("legendToggleSelect","legendselectchanged",T(Fy,"toggleSelected")),of("legendAllSelect","legendselectall",T(Fy,"allSelect")),of("legendInverseSelect","legendinverseselect",T(Fy,"inverseSelect")),of("legendSelect","legendselected",T(Fy,"select")),of("legendUnSelect","legendunselected",T(Fy,"unSelect"));var Wy=T,Gy=D,Zy=Mn,Uy=cf({type:"legend.plain",newlineDisabled:!1,init:function(){this.group.add(this._contentGroup=new Zy),this._backgroundEl,this.group.add(this._selectorGroup=new Zy),this._isFirstRender=!0},getContentGroup:function(){return this._contentGroup},getSelectorGroup:function(){return this._selectorGroup},render:function(t,e,n){var i=this._isFirstRender;if(this._isFirstRender=!1,this.resetInner(),t.get("show",!0)){var r=t.get("align"),o=t.get("orient");r&&"auto"!==r||(r="right"===t.get("left")&&"vertical"===o?"right":"left");var a=t.get("selector",!0),s=t.get("selectorPosition",!0);!a||s&&"auto"!==s||(s="horizontal"===o?"end":"start"),this.renderInner(r,t,e,n,a,o,s);var l=t.getBoxLayoutParams(),u={width:n.getWidth(),height:n.getHeight()},h=t.get("padding"),c=lu(l,u,h),d=this.layoutInner(t,r,c,i,a,s),f=lu(A({width:d.width,height:d.height},l),u,h);this.group.attr("position",[f.x-d.x,f.y-d.y]),this.group.add(this._backgroundEl=Hy(d,t))}},resetInner:function(){this.getContentGroup().removeAll(),this._backgroundEl&&this.group.remove(this._backgroundEl),this.getSelectorGroup().removeAll()},renderInner:function(u,h,c,d,t,e,n){var f=this.getContentGroup(),p=Q(),g=h.get("selectedMode"),m=[];c.eachRawSeries(function(t){t.get("legendHoverLink")||m.push(t.id)}),Gy(h.getData(),function(o,a){var s=o.get("name");if(this.newlineDisabled||""!==s&&"\n"!==s){var t=c.getSeriesByName(s)[0];if(!p.get(s))if(t){var e=t.getData(),n=e.getVisual("color"),i=e.getVisual("borderColor");"function"==typeof n&&(n=n(t.getDataParams(0))),"function"==typeof i&&(i=i(t.getDataParams(0)));var r=e.getVisual("legendSymbol")||"roundRect",l=e.getVisual("symbol");this._createItem(s,a,o,h,r,l,u,n,i,g).on("click",Wy(Yy,s,null,d,m)).on("mouseover",Wy(jy,t.name,null,d,m)).on("mouseout",Wy(qy,t.name,null,d,m)),p.set(s,!0)}else c.eachRawSeries(function(t){if(!p.get(s)&&t.legendVisualProvider){var e=t.legendVisualProvider;if(!e.containName(s))return;var n=e.indexOfName(s),i=e.getItemVisual(n,"color"),r=e.getItemVisual(n,"borderColor");this._createItem(s,a,o,h,"roundRect",null,u,i,r,g).on("click",Wy(Yy,null,s,d,m)).on("mouseover",Wy(jy,null,s,d,m)).on("mouseout",Wy(qy,null,s,d,m)),p.set(s,!0)}},this)}else f.add(new Zy({newline:!0}))},this),t&&this._createSelector(t,h,d,e,n)},_createSelector:function(t,o,a,e,n){var s=this.getSelectorGroup();Gy(t,function(t){!function(t){var e=t.type,n=new Ba({style:{x:0,y:0,align:"center",verticalAlign:"middle"},onclick:function(){a.dispatchAction({type:"all"===e?"legendAllSelect":"legendInverseSelect"})}});s.add(n);var i=o.getModel("selectorLabel"),r=o.getModel("emphasis.selectorLabel");Ys(n.style,n.hoverStyle={},i,r,{defaultText:t.title,isRectText:!1}),Gs(n)}(t)})},_createItem:function(t,e,n,i,r,o,a,s,l,u){var h=i.get("itemWidth"),c=i.get("itemHeight"),d=i.get("inactiveColor"),f=i.get("inactiveBorderColor"),p=i.get("symbolKeepAspect"),g=i.getModel("itemStyle"),m=i.isSelected(t),v=new Zy,y=n.getModel("textStyle"),_=n.get("icon"),x=n.getModel("tooltip"),w=x.parentModel,b=ng(r=_||r,0,0,h,c,m?s:d,null==p||p);if(v.add(Xy(b,r,g,l,f,m)),!_&&o&&(o!==r||"none"===o)){var S=.8*c;"none"===o&&(o="circle");var M=ng(o,(h-S)/2,(c-S)/2,S,S,m?s:d,null==p||p);v.add(Xy(M,o,g,l,f,m))}var I="left"===a?h+5:-5,C=a,T=i.get("formatter"),A=t;"string"==typeof T&&T?A=T.replace("{name}",null!=t?t:""):"function"==typeof T&&(A=T(t)),v.add(new Ba({style:js({},y,{text:A,x:I,y:c/2,textFill:m?y.getTextColor():d,textAlign:C,textVerticalAlign:"middle"})}));var D=new Ja({shape:v.getBoundingRect(),invisible:!0,tooltip:x.get("show")?k({content:t,formatter:w.get("formatter",!0)||function(){return t},formatterParams:{componentType:"legend",legendIndex:i.componentIndex,name:t,$vars:["name"]}},x.option):null});return v.add(D),v.eachChild(function(t){t.silent=!0}),D.silent=!u,this.getContentGroup().add(v),Gs(v),v.__legendDataIndex=e,v},layoutInner:function(t,e,n,i,r,o){var a=this.getContentGroup(),s=this.getSelectorGroup();su(t.get("orient"),a,t.get("itemGap"),n.width,n.height);var l=a.getBoundingRect(),u=[-l.x,-l.y];if(r){su("horizontal",s,t.get("selectorItemGap",!0));var h=s.getBoundingRect(),c=[-h.x,-h.y],d=t.get("selectorButtonGap",!0),f=t.getOrient().index,p=0===f?"width":"height",g=0===f?"height":"width",m=0===f?"y":"x";"end"===o?c[f]+=l[p]+d:u[f]+=h[p]+d,c[1-f]+=l[g]/2-h[g]/2,s.attr("position",c),a.attr("position",u);var v={x:0,y:0};return v[p]=l[p]+d+h[p],v[g]=Math.max(l[g],h[g]),v[m]=Math.min(0,h[m]+c[1-f]),v}return a.attr("position",u),this.group.getBoundingRect()},remove:function(){this.getContentGroup().removeAll(),this._isFirstRender=!0}});function Xy(t,e,n,i,r,o){var a;return"line"!==e&&e.indexOf("empty")<0?(a=n.getItemStyle(),t.style.stroke=i,o||(a.stroke=r)):a=n.getItemStyle(["borderWidth","borderColor"]),t.setStyle(a)}function Yy(t,e,n,i){qy(t,e,n,i),n.dispatchAction({type:"legendToggleSelect",name:null!=t?t:e}),jy(t,e,n,i)}function jy(t,e,n,i){var r=n.getZr().storage.getDisplayList()[0];r&&r.useHoverLayer||n.dispatchAction({type:"highlight",seriesName:t,name:e,excludeSeriesId:i})}function qy(t,e,n,i){var r=n.getZr().storage.getDisplayList()[0];r&&r.useHoverLayer||n.dispatchAction({type:"downplay",seriesName:t,name:e,excludeSeriesId:i})}rf(gd.PROCESSOR.SERIES_FILTER,function(t){var n=t.findComponents({mainType:"legend"});n&&n.length&&t.filterSeries(function(t){for(var e=0;e<n.length;e++)if(!n[e].isSelected(t.name))return!1;return!0})}),vu.registerSubTypeDefaulter("legend",function(){return"plain"});var $y=Vy.extend({type:"legend.scroll",setScrollDataIndex:function(t){this.option.scrollDataIndex=t},defaultOption:{scrollDataIndex:0,pageButtonItemGap:5,pageButtonGap:null,pageButtonPosition:"end",pageFormatter:"{current}/{total}",pageIcons:{horizontal:["M0,0L12,-10L12,10z","M0,0L-12,-10L-12,10z"],vertical:["M0,0L20,0L10,-20z","M0,0L20,0L10,20z"]},pageIconColor:"#2f4554",pageIconInactiveColor:"#aaa",pageIconSize:15,pageTextStyle:{color:"#333"},animationDurationUpdate:800},init:function(t,e,n,i){var r=cu(t);$y.superCall(this,"init",t,e,n,i),Ky(this,t,r)},mergeOption:function(t,e){$y.superCall(this,"mergeOption",t,e),Ky(this,this.option,t)}});function Ky(t,e,n){var i=[1,1];i[t.getOrient().index]=0,hu(e,n,{type:"box",ignoreSize:i})}var Qy=Mn,Jy=["width","height"],t_=["x","y"],e_=Uy.extend({type:"legend.scroll",newlineDisabled:!0,init:function(){e_.superCall(this,"init"),this._currentIndex=0,this.group.add(this._containerGroup=new Qy),this._containerGroup.add(this.getContentGroup()),this.group.add(this._controllerGroup=new Qy),this._showController},resetInner:function(){e_.superCall(this,"resetInner"),this._controllerGroup.removeAll(),this._containerGroup.removeClipPath(),this._containerGroup.__rectSize=null},renderInner:function(t,r,e,o,n,i,a){var s=this;e_.superCall(this,"renderInner",t,r,e,o,n,i,a);var l=this._controllerGroup,u=r.get("pageIconSize",!0);O(u)||(u=[u,u]),c("pagePrev",0);var h=r.getModel("pageTextStyle");function c(t,e){var n=t+"DataIndex",i=ul(r.get("pageIcons",!0)[r.getOrient().name][e],{onclick:C(s._pageGo,s,n,r,o)},{x:-u[0]/2,y:-u[1]/2,width:u[0],height:u[1]});i.name=t,l.add(i)}l.add(new Ba({name:"pageText",style:{textFill:h.getTextColor(),font:h.getFont(),textVerticalAlign:"middle",textAlign:"center"},silent:!0})),c("pageNext",1)},layoutInner:function(t,e,n,i,r,o){var a=this.getSelectorGroup(),s=t.getOrient().index,l=Jy[s],u=t_[s],h=Jy[1-s],c=t_[1-s];r&&su("horizontal",a,t.get("selectorItemGap",!0));var d=t.get("selectorButtonGap",!0),f=a.getBoundingRect(),p=[-f.x,-f.y],g=b(n);r&&(g[l]=n[l]-f[l]-d);var m=this._layoutContentAndController(t,i,g,s,l,h,c);if(r){if("end"===o)p[s]+=m[l]+d;else{var v=f[l]+d;p[s]-=v,m[u]-=v}m[l]+=f[l]+d,p[1-s]+=m[c]+m[h]/2-f[h]/2,m[h]=Math.max(m[h],f[h]),m[c]=Math.min(m[c],f[c]+p[1-s]),a.attr("position",p)}return m},_layoutContentAndController:function(t,e,n,i,r,o,a){var s=this.getContentGroup(),l=this._containerGroup,u=this._controllerGroup;su(t.get("orient"),s,t.get("itemGap"),i?n.width:null,i?null:n.height),su("horizontal",u,t.get("pageButtonItemGap",!0));var h=s.getBoundingRect(),c=u.getBoundingRect(),d=this._showController=h[r]>n[r],f=[-h.x,-h.y];e||(f[i]=s.position[i]);var p=[0,0],g=[-c.x,-c.y],m=W(t.get("pageButtonGap",!0),t.get("itemGap",!0));d&&("end"===t.get("pageButtonPosition",!0)?g[i]+=n[r]-c[r]:p[i]+=c[r]+m);g[1-i]+=h[o]/2-c[o]/2,s.attr("position",f),l.attr("position",p),u.attr("position",g);var v={x:0,y:0};if(v[r]=d?n[r]:h[r],v[o]=Math.max(h[o],c[o]),v[a]=Math.min(0,c[a]+g[1-i]),l.__rectSize=n[r],d){var y={x:0,y:0};y[r]=Math.max(n[r]-c[r]-m,0),y[o]=v[o],l.setClipPath(new Ja({shape:y})),l.__rectSize=y[r]}else u.eachChild(function(t){t.attr({invisible:!0,silent:!0})});var _=this._getPageInfo(t);return null!=_.pageIndex&&nl(s,{position:_.contentPosition},d&&t),this._updatePageInfoView(t,_),v},_pageGo:function(t,e,n){var i=this._getPageInfo(e)[t];null!=i&&n.dispatchAction({type:"legendScroll",scrollDataIndex:i,legendId:e.id})},_updatePageInfoView:function(i,r){var o=this._controllerGroup;D(["pagePrev","pageNext"],function(t){var e=null!=r[t+"DataIndex"],n=o.childOfName(t);n&&(n.setStyle("fill",e?i.get("pageIconColor",!0):i.get("pageIconInactiveColor",!0)),n.cursor=e?"pointer":"default")});var t=o.childOfName("pageText"),e=i.get("pageFormatter"),n=r.pageIndex,a=null!=n?n+1:0,s=r.pageCount;t&&e&&t.setStyle("text",z(e)?e.replace("{current}",a).replace("{total}",s):e({current:a,total:s}))},_getPageInfo:function(t){var e=t.get("scrollDataIndex",!0),n=this.getContentGroup(),i=this._containerGroup.__rectSize,r=t.getOrient().index,o=Jy[r],a=t_[r],s=this._findTargetItemIndex(e),l=n.children(),u=l[s],h=l.length,c=h?1:0,d={contentPosition:n.position.slice(),pageCount:c,pageIndex:c-1,pagePrevDataIndex:null,pageNextDataIndex:null};if(!u)return d;var f=y(u);d.contentPosition[r]=-f.s;for(var p=s+1,g=f,m=f,v=null;p<=h;++p)(!(v=y(l[p]))&&m.e>g.s+i||v&&!_(v,g.s))&&(g=m.i>g.i?m:v)&&(null==d.pageNextDataIndex&&(d.pageNextDataIndex=g.i),++d.pageCount),m=v;for(p=s-1,g=f,m=f,v=null;-1<=p;--p)(v=y(l[p]))&&_(m,v.s)||!(g.i<m.i)||(m=g,null==d.pagePrevDataIndex&&(d.pagePrevDataIndex=g.i),++d.pageCount,++d.pageIndex),g=v;return d;function y(t){if(t){var e=t.getBoundingRect(),n=e[a]+t.position[r];return{s:n,e:n+e[o],i:t.__legendDataIndex}}}function _(t,e){return t.e>=e&&t.s<=e+i}},_findTargetItemIndex:function(i){return this._showController?(this.getContentGroup().eachChild(function(t,e){var n=t.__legendDataIndex;null==o&&null!=n&&(o=e),n===i&&(r=e)}),null!=r?r:o):0;var r,o}});of("legendScroll","legendscroll",function(t,e){var n=t.scrollDataIndex;null!=n&&e.eachComponent({mainType:"legend",subType:"scroll",query:t},function(t){t.setScrollDataIndex(n)})}),hf({type:"title",layoutMode:{type:"box",ignoreSize:!0},defaultOption:{zlevel:0,z:6,show:!0,text:"",target:"blank",subtext:"",subtarget:"blank",left:0,top:0,backgroundColor:"rgba(0,0,0,0)",borderColor:"#ccc",borderWidth:0,padding:5,itemGap:10,textStyle:{fontSize:18,fontWeight:"bolder",color:"#333"},subtextStyle:{color:"#aaa"}}}),cf({type:"title",render:function(t,e,n){if(this.group.removeAll(),t.get("show")){var i=this.group,r=t.getModel("textStyle"),o=t.getModel("subtextStyle"),a=t.get("textAlign"),s=W(t.get("textBaseline"),t.get("textVerticalAlign")),l=new Ba({style:js({},r,{text:t.get("text"),textFill:r.getTextColor()},{disableBox:!0}),z2:10}),u=l.getBoundingRect(),h=t.get("subtext"),c=new Ba({style:js({},o,{text:h,textFill:o.getTextColor(),y:u.height+t.get("itemGap"),textVerticalAlign:"top"},{disableBox:!0}),z2:10}),d=t.get("link"),f=t.get("sublink"),p=t.get("triggerEvent",!0);l.silent=!d&&!p,c.silent=!f&&!p,d&&l.on("click",function(){eu(d,"_"+t.get("target"))}),f&&c.on("click",function(){eu(d,"_"+t.get("subtarget"))}),l.eventData=c.eventData=p?{componentType:"title",componentIndex:t.componentIndex}:null,i.add(l),h&&i.add(c);var g=i.getBoundingRect(),m=t.getBoxLayoutParams();m.width=g.width,m.height=g.height;var v=lu(m,{width:n.getWidth(),height:n.getHeight()},t.get("padding"));a||("middle"===(a=t.get("left")||t.get("right"))&&(a="center"),"right"===a?v.x+=v.width:"center"===a&&(v.x+=v.width/2)),s||("center"===(s=t.get("top")||t.get("bottom"))&&(s="middle"),"bottom"===s?v.y+=v.height:"middle"===s&&(v.y+=v.height/2),s=s||"top"),i.attr("position",[v.x,v.y]);var y={textAlign:a,textVerticalAlign:s};l.setStyle(y),c.setStyle(y),g=i.getBoundingRect();var _=v.margin,x=t.getItemStyle(["color","opacity"]);x.fill=t.get("backgroundColor");var w=new Ja({shape:{x:g.x-_[3],y:g.y-_[0],width:g.width+_[1]+_[3],height:g.height+_[0]+_[2],r:t.get("borderRadius")},style:x,subPixelOptimize:!0,silent:!0});i.add(w)}}});var n_=Hl,i_=Xl;function r_(t){Or(t,"label",["show"])}var o_=hf({type:"marker",dependencies:["series","grid","polar","geo"],init:function(t,e,n){this.mergeDefaultAndTheme(t,n),this._mergeOption(t,n,!1,!0)},isAnimationEnabled:function(){if(v.node)return!1;var t=this.__hostSeries;return this.getShallow("animation")&&t&&t.isAnimationEnabled()},mergeOption:function(t,e){this._mergeOption(t,e,!1,!1)},_mergeOption:function(t,i,e,r){var o=this.constructor,a=this.mainType+"Model";e||i.eachSeries(function(t){var e=t.get(this.mainType,!0),n=t[a];e&&e.data?(n?n._mergeOption(e,i,!0):(r&&r_(e),D(e.data,function(t){t instanceof Array?(r_(t[0]),r_(t[1])):r_(t)}),k(n=new o(e,this,i),{mainType:this.mainType,seriesIndex:t.seriesIndex,name:t.name,createdBySelf:!0}),n.__hostSeries=t),t[a]=n):t[a]=null},this)},formatTooltip:function(t){var e=this.getData(),n=this.getRawValue(t),i=O(n)?P(n,n_).join(", "):n_(n),r=e.getName(t),o=i_(this.name);return null==n&&!r||(o+="<br />"),r&&(o+=i_(r),null!=n&&(o+=" : ")),null!=n&&(o+=i_(i)),o},getData:function(){return this._data},setData:function(t){this._data=t}});S(o_,Lh),o_.extend({type:"markPoint",defaultOption:{zlevel:0,z:5,symbol:"pin",symbolSize:50,tooltip:{trigger:"item"},label:{show:!0,position:"inside"},itemStyle:{borderWidth:2},emphasis:{label:{show:!0}}}});var a_=x;function s_(t,e,n,i,r,o){var a=[],s=$f(e,i)?e.getCalculationInfo("stackResultDimension"):i,l=p_(e,s,t),u=e.indicesOfNearest(s,l)[0];a[r]=e.get(n,u),a[o]=e.get(s,u);var h=e.get(i,u),c=Dl(e.get(i,u));return 0<=(c=Math.min(c,20))&&(a[o]=+a[o].toFixed(c)),[a,h]}var l_=T,u_={min:l_(s_,"min"),max:l_(s_,"max"),average:l_(s_,"average")};function h_(t,e){var n=t.getData(),i=t.coordinateSystem;if(e&&!function(t){return!isNaN(parseFloat(t.x))&&!isNaN(parseFloat(t.y))}(e)&&!O(e.coord)&&i){var r=i.dimensions,o=c_(e,n,i,t);if((e=b(e)).type&&u_[e.type]&&o.baseAxis&&o.valueAxis){var a=a_(r,o.baseAxis.dim),s=a_(r,o.valueAxis.dim),l=u_[e.type](n,o.baseDataDim,o.valueDataDim,a,s);e.coord=l[0],e.value=l[1]}else{for(var u=[null!=e.xAxis?e.xAxis:e.radiusAxis,null!=e.yAxis?e.yAxis:e.angleAxis],h=0;h<2;h++)u_[u[h]]&&(u[h]=p_(n,n.mapDimension(r[h]),u[h]));e.coord=u}}return e}function c_(t,e,n,i){var r={};return null!=t.valueIndex||null!=t.valueDim?(r.valueDataDim=null!=t.valueIndex?e.getDimension(t.valueIndex):t.valueDim,r.valueAxis=n.getAxis(function(t,e){var n=t.getData(),i=n.dimensions;e=n.getDimension(e);for(var r=0;r<i.length;r++){var o=n.getDimensionInfo(i[r]);if(o.name===e)return o.coordDim}}(i,r.valueDataDim)),r.baseAxis=n.getOtherAxis(r.valueAxis),r.baseDataDim=e.mapDimension(r.baseAxis.dim)):(r.baseAxis=i.getBaseAxis(),r.valueAxis=n.getOtherAxis(r.baseAxis),r.baseDataDim=e.mapDimension(r.baseAxis.dim),r.valueDataDim=e.mapDimension(r.valueAxis.dim)),r}function d_(t,e){return!(t&&t.containData&&e.coord&&!function(t){return!(isNaN(parseFloat(t.x))&&isNaN(parseFloat(t.y)))}(e))||t.containData(e.coord)}function f_(t,e,n,i){return i<2?t.coord&&t.coord[i]:t.value}function p_(t,e,n){if("average"!==n)return"median"===n?t.getMedian(e):t.getDataExtent(e,!0)["max"===n?1:0];var i=0,r=0;return t.each(e,function(t,e){isNaN(t)||(i+=t,r++)}),i/r}var g_=cf({type:"marker",init:function(){this.markerGroupMap=Q()},render:function(t,n,i){var e=this.markerGroupMap;e.each(function(t){t.__keep=!1});var r=this.type+"Model";n.eachSeries(function(t){var e=t[r];e&&this.renderSeries(t,e,n,i)},this),e.each(function(t){t.__keep||this.group.remove(t.group)},this)},renderSeries:function(){}});function m_(s,l,u){var h=l.coordinateSystem;s.each(function(t){var e,n=s.getItemModel(t),i=Cl(n.get("x"),u.getWidth()),r=Cl(n.get("y"),u.getHeight());if(isNaN(i)||isNaN(r)){if(l.getMarkerPosition)e=l.getMarkerPosition(s.getValues(s.dimensions,t));else if(h){var o=s.get(h.dimensions[0],t),a=s.get(h.dimensions[1],t);e=h.dataToPoint([o,a])}}else e=[i,r];isNaN(i)||(e[0]=i),isNaN(r)||(e[1]=r),s.setItemLayout(t,e)})}g_.extend({type:"markPoint",updateTransform:function(t,e,n){e.eachSeries(function(t){var e=t.markPointModel;e&&(m_(e.getData(),t,n),this.markerGroupMap.get(t.id).updateLayout(e))},this)},renderSeries:function(t,l,e,n){var i=t.coordinateSystem,r=t.id,u=t.getData(),o=this.markerGroupMap,a=o.get(r)||o.set(r,new Rg),h=function(t,e,n){var i;i=t?P(t&&t.dimensions,function(t){return A({name:t},e.getData().getDimensionInfo(e.getData().mapDimension(t))||{})}):[{name:"value",type:"float"}];var r=new kf(i,n),o=P(n.get("data"),T(h_,e));t&&(o=I(o,T(d_,t)));return r.initData(o,null,t?f_:function(t){return t.value}),r}(i,t,l);l.setData(h),m_(l.getData(),t,n),h.each(function(t){var e=h.getItemModel(t),n=e.getShallow("symbol"),i=e.getShallow("symbolSize"),r=E(n),o=E(i);if(r||o){var a=l.getRawValue(t),s=l.getDataParams(t);r&&(n=n(a,s)),o&&(i=i(a,s))}h.setItemVisual(t,{symbol:n,symbolSize:i,color:e.get("itemStyle.color")||u.getVisual("color")})}),a.updateData(h),this.group.add(a.group),h.eachItemGraphicEl(function(t){t.traverse(function(t){t.dataModel=l})}),a.__keep=!0,a.group.silent=l.get("silent")||t.get("silent")}}),nf(function(t){t.markPoint=t.markPoint||{}}),o_.extend({type:"markLine",defaultOption:{zlevel:0,z:5,symbol:["circle","arrow"],symbolSize:[8,16],precision:2,tooltip:{trigger:"item"},label:{show:!0,position:"end",distance:5},lineStyle:{type:"dashed"},emphasis:{label:{show:!0},lineStyle:{width:3}},animationEasing:"linear"}});var v_=es.prototype,y_=os.prototype;function __(t){return isNaN(+t.cpx1)||isNaN(+t.cpy1)}var x_=ws({type:"ec-line",style:{stroke:"#000",fill:null},shape:{x1:0,y1:0,x2:0,y2:0,percent:1,cpx1:null,cpy1:null},buildPath:function(t,e){this[__(e)?"_buildPathLine":"_buildPathCurve"](t,e)},_buildPathLine:v_.buildPath,_buildPathCurve:y_.buildPath,pointAt:function(t){return this[__(this.shape)?"_pointAtLine":"_pointAtCurve"](t)},_pointAtLine:v_.pointAt,_pointAtCurve:y_.pointAt,tangentAt:function(t){var e=this.shape,n=__(e)?[e.x2-e.x1,e.y2-e.y1]:this._tangentAtCurve(t);return ft(n,n)},_tangentAtCurve:y_.tangentAt}),w_=["fromSymbol","toSymbol"];function b_(t){return"_"+t+"Type"}function S_(t,e,n){var i=e.getItemVisual(n,"color"),r=e.getItemVisual(n,t),o=e.getItemVisual(n,t+"Size");if(r&&"none"!==r){O(o)||(o=[o,o]);var a=ng(r,-o[0]/2,-o[1]/2,o[0],o[1],i);return a.name=t,a}}function M_(t,e){t.x1=e[0][0],t.y1=e[0][1],t.x2=e[1][0],t.y2=e[1][1],t.percent=1;var n=e[2];n?(t.cpx1=n[0],t.cpy1=n[1]):(t.cpx1=NaN,t.cpy1=NaN)}function I_(t,e,n){Mn.call(this),this._createLine(t,e,n)}var C_=I_.prototype;function T_(t){this._ctor=t||I_,this.group=new Mn}C_.beforeUpdate=function(){var t=this.childOfName("fromSymbol"),e=this.childOfName("toSymbol"),n=this.childOfName("label");if(t||e||!n.ignore){for(var i=1,r=this.parent;r;)r.scale&&(i/=r.scale[0]),r=r.parent;var o=this.childOfName("line");if(this.__dirty||o.__dirty){var a=o.shape.percent,s=o.pointAt(0),l=o.pointAt(a),u=st([],l,s);if(ft(u,u),t){t.attr("position",s);var h=o.tangentAt(0);t.attr("rotation",Math.PI/2-Math.atan2(h[1],h[0])),t.attr("scale",[i*a,i*a])}if(e){e.attr("position",l);h=o.tangentAt(1);e.attr("rotation",-Math.PI/2-Math.atan2(h[1],h[0])),e.attr("scale",[i*a,i*a])}if(!n.ignore){var c,d,f,p;n.attr("position",l);var g=n.__labelDistance,m=g[0]*i,v=g[1]*i,y=a/2,_=[(h=o.tangentAt(y))[1],-h[0]],x=o.pointAt(y);0<_[1]&&(_[0]=-_[0],_[1]=-_[1]);var w,b=h[0]<0?-1:1;if("start"!==n.__position&&"end"!==n.__position){var S=-Math.atan2(h[1],h[0]);l[0]<s[0]&&(S=Math.PI+S),n.attr("rotation",S)}switch(n.__position){case"insideStartTop":case"insideMiddleTop":case"insideEndTop":case"middle":w=-v,f="bottom";break;case"insideStartBottom":case"insideMiddleBottom":case"insideEndBottom":w=v,f="top";break;default:w=0,f="middle"}switch(n.__position){case"end":c=[u[0]*m+l[0],u[1]*v+l[1]],d=.8<u[0]?"left":u[0]<-.8?"right":"center",f=.8<u[1]?"top":u[1]<-.8?"bottom":"middle";break;case"start":c=[-u[0]*m+s[0],-u[1]*v+s[1]],d=.8<u[0]?"right":u[0]<-.8?"left":"center",f=.8<u[1]?"bottom":u[1]<-.8?"top":"middle";break;case"insideStartTop":case"insideStart":case"insideStartBottom":c=[m*b+s[0],s[1]+w],d=h[0]<0?"right":"left",p=[-m*b,-w];break;case"insideMiddleTop":case"insideMiddle":case"insideMiddleBottom":case"middle":c=[x[0],x[1]+w],d="center",p=[0,-w];break;case"insideEndTop":case"insideEnd":case"insideEndBottom":c=[-m*b+l[0],l[1]+w],d=0<=h[0]?"right":"left",p=[m*b,-w]}n.attr({style:{textVerticalAlign:n.__verticalAlign||f,textAlign:n.__textAlign||d},position:c,scale:[i,i],origin:p})}}}},C_._createLine=function(n,i,t){var e=n.hostModel,r=function(t){var e=new x_({name:"line",subPixelOptimize:!0});return M_(e.shape,t),e}(n.getItemLayout(i));r.shape.percent=0,il(r,{shape:{percent:1}},e,i),this.add(r);var o=new Ba({name:"label",lineLabelOriginalOpacity:1});this.add(o),D(w_,function(t){var e=S_(t,n,i);this.add(e),this[b_(t)]=n.getItemVisual(i,t)},this),this._updateCommonStl(n,i,t)},C_.updateData=function(r,o,t){var e=r.hostModel,n=this.childOfName("line"),i=r.getItemLayout(o),a={shape:{}};M_(a.shape,i),nl(n,a,e,o),D(w_,function(t){var e=r.getItemVisual(o,t),n=b_(t);if(this[n]!==e){this.remove(this.childOfName(t));var i=S_(t,r,o);this.add(i)}this[n]=e},this),this._updateCommonStl(r,o,t)},C_._updateCommonStl=function(t,e,n){var i=t.hostModel,r=this.childOfName("line"),o=n&&n.lineStyle,a=n&&n.hoverLineStyle,s=n&&n.labelModel,l=n&&n.hoverLabelModel;if(!n||t.hasItemOption){var u=t.getItemModel(e);o=u.getModel("lineStyle").getLineStyle(),a=u.getModel("emphasis.lineStyle").getLineStyle(),s=u.getModel("label"),l=u.getModel("emphasis.label")}var h=t.getItemVisual(e,"color"),c=G(t.getItemVisual(e,"opacity"),o.opacity,1);r.useStyle(A({strokeNoScale:!0,fill:"none",stroke:h,opacity:c},o)),r.hoverStyle=a,D(w_,function(t){var e=this.childOfName(t);e&&(e.setColor(h),e.setStyle({opacity:c}))},this);var d,f,p=s.getShallow("show"),g=l.getShallow("show"),m=this.childOfName("label");if((p||g)&&(d=h||"#000",null==(f=i.getFormattedLabel(e,"normal",t.dataType)))){var v=i.getRawValue(e);f=null==v?t.getName(e):isFinite(v)?Tl(v):v}var y=p?f:null,_=g?W(i.getFormattedLabel(e,"emphasis",t.dataType),f):null,x=m.style;if(null!=y||null!=_){js(m.style,s,{text:y},{autoColor:d}),m.__textAlign=x.textAlign,m.__verticalAlign=x.textVerticalAlign,m.__position=s.get("position")||"middle";var w=s.get("distance");O(w)||(w=[w,w]),m.__labelDistance=w}m.hoverStyle=null!=_?{text:_,textFill:l.getTextColor(!0),fontStyle:l.getShallow("fontStyle"),fontWeight:l.getShallow("fontWeight"),fontSize:l.getShallow("fontSize"),fontFamily:l.getShallow("fontFamily")}:{text:null},m.ignore=!p&&!g,Gs(this)},C_.highlight=function(){this.trigger("emphasis")},C_.downplay=function(){this.trigger("normal")},C_.updateLayout=function(t,e){this.setLinePoints(t.getItemLayout(e))},C_.setLinePoints=function(t){var e=this.childOfName("line");M_(e.shape,t),e.dirty()},w(I_,Mn);var A_=T_.prototype;function D_(t){var e=t.hostModel;return{lineStyle:e.getModel("lineStyle").getLineStyle(),hoverLineStyle:e.getModel("emphasis.lineStyle").getLineStyle(),labelModel:e.getModel("label"),hoverLabelModel:e.getModel("emphasis.label")}}function k_(t){return isNaN(t[0])||isNaN(t[1])}function P_(t){return!k_(t[0])&&!k_(t[1])}A_.isPersistent=function(){return!0},A_.updateData=function(n){var i=this,e=i.group,r=i._lineData;i._lineData=n,r||e.removeAll();var o=D_(n);n.diff(r).add(function(t){!function(t,e,n,i){if(!P_(e.getItemLayout(n)))return;var r=new t._ctor(e,n,i);e.setItemGraphicEl(n,r),t.group.add(r)}(i,n,t,o)}).update(function(t,e){!function(t,e,n,i,r,o){var a=e.getItemGraphicEl(i);if(!P_(n.getItemLayout(r)))return t.group.remove(a);a?a.updateData(n,r,o):a=new t._ctor(n,r,o);n.setItemGraphicEl(r,a),t.group.add(a)}(i,r,n,e,t,o)}).remove(function(t){e.remove(r.getItemGraphicEl(t))}).execute()},A_.updateLayout=function(){var n=this._lineData;n&&n.eachItemGraphicEl(function(t,e){t.updateLayout(n,e)},this)},A_.incrementalPrepareUpdate=function(t){this._seriesScope=D_(t),this._lineData=null,this.group.removeAll()},A_.incrementalUpdate=function(t,e){function n(t){t.isGroup||function(t){return t.animators&&0<t.animators.length}(t)||(t.incremental=t.useHoverLayer=!0)}for(var i=t.start;i<t.end;i++){if(P_(e.getItemLayout(i))){var r=new this._ctor(e,i,this._seriesScope);r.traverse(n),this.group.add(r),e.setItemGraphicEl(i,r)}}},A_.remove=function(){this._clearIncremental(),this._incremental=null,this.group.removeAll()},A_._clearIncremental=function(){var t=this._incremental;t&&t.clearDisplaybles()};function L_(t,e,n,i){var r=t.getData(),o=i.type;if(!O(i)&&("min"===o||"max"===o||"average"===o||"median"===o||null!=i.xAxis||null!=i.yAxis)){var a,s;if(null!=i.yAxis||null!=i.xAxis)a=e.getAxis(null!=i.yAxis?"y":"x"),s=H(i.yAxis,i.xAxis);else{var l=c_(i,r,e,t);a=l.valueAxis,s=p_(r,Kf(r,l.valueDataDim),o)}var u="x"===a.dim?0:1,h=1-u,c=b(i),d={};c.type=null,c.coord=[],d.coord=[],c.coord[h]=-1/0,d.coord[h]=1/0;var f=n.get("precision");0<=f&&"number"==typeof s&&(s=+s.toFixed(Math.min(f,20))),c.coord[u]=d.coord[u]=s,i=[c,d,{type:o,valueIndex:i.valueIndex,value:s}]}return(i=[h_(t,i[0]),h_(t,i[1]),k({},i[2])])[2].type=i[2].type||"",m(i[2],i[0]),m(i[2],i[1]),i}function O_(t){return!isNaN(t)&&!isFinite(t)}function E_(t,e,n,i){var r=1-t,o=i.dimensions[t];return O_(e[r])&&O_(n[r])&&e[t]===n[t]&&i.getAxis(o).containData(e[t])}function z_(t,e){if("cartesian2d"===t.type){var n=e[0].coord,i=e[1].coord;if(n&&i&&(E_(1,n,i,t)||E_(0,n,i,t)))return!0}return d_(t,e[0])&&d_(t,e[1])}function N_(t,e,n,i,r){var o,a=i.coordinateSystem,s=t.getItemModel(e),l=Cl(s.get("x"),r.getWidth()),u=Cl(s.get("y"),r.getHeight());if(isNaN(l)||isNaN(u)){if(i.getMarkerPosition)o=i.getMarkerPosition(t.getValues(t.dimensions,e));else{var h=a.dimensions,c=t.get(h[0],e),d=t.get(h[1],e);o=a.dataToPoint([c,d])}if("cartesian2d"===a.type){var f=a.getAxis("x"),p=a.getAxis("y");h=a.dimensions;O_(t.get(h[0],e))?o[0]=f.toGlobalCoord(f.getExtent()[n?0:1]):O_(t.get(h[1],e))&&(o[1]=p.toGlobalCoord(p.getExtent()[n?0:1]))}isNaN(l)||(o[0]=l),isNaN(u)||(o[1]=u)}else o=[l,u];t.setItemLayout(e,o)}g_.extend({type:"markLine",updateTransform:function(t,e,o){e.eachSeries(function(e){var t=e.markLineModel;if(t){var n=t.getData(),i=t.__from,r=t.__to;i.each(function(t){N_(i,t,!0,e,o),N_(r,t,!1,e,o)}),n.each(function(t){n.setItemLayout(t,[i.getItemLayout(t),r.getItemLayout(t)])}),this.markerGroupMap.get(e.id).updateLayout()}},this)},renderSeries:function(r,n,t,o){var e=r.coordinateSystem,i=r.id,a=r.getData(),s=this.markerGroupMap,l=s.get(i)||s.set(i,new T_);this.group.add(l.group);var u=function(t,e,n){var i;i=t?P(t&&t.dimensions,function(t){return A({name:t},e.getData().getDimensionInfo(e.getData().mapDimension(t))||{})}):[{name:"value",type:"float"}];var r=new kf(i,n),o=new kf(i,n),a=new kf([],n),s=P(n.get("data"),T(L_,e,t,n));t&&(s=I(s,T(z_,t)));var l=t?f_:function(t){return t.value};return r.initData(P(s,function(t){return t[0]}),null,l),o.initData(P(s,function(t){return t[1]}),null,l),a.initData(P(s,function(t){return t[2]})),a.hasItemOption=!0,{from:r,to:o,line:a}}(e,r,n),h=u.from,c=u.to,d=u.line;n.__from=h,n.__to=c,n.setData(d);var f=n.get("symbol"),p=n.get("symbolSize");function g(t,e,n){var i=t.getItemModel(e);N_(t,e,n,r,o),t.setItemVisual(e,{symbolSize:i.get("symbolSize")||p[n?0:1],symbol:i.get("symbol",!0)||f[n?0:1],color:i.get("itemStyle.color")||a.getVisual("color")})}O(f)||(f=[f,f]),"number"==typeof p&&(p=[p,p]),u.from.each(function(t){g(h,t,!0),g(c,t,!1)}),d.each(function(t){var e=d.getItemModel(t).get("lineStyle.color");d.setItemVisual(t,{color:e||h.getItemVisual(t,"color")}),d.setItemLayout(t,[h.getItemLayout(t),c.getItemLayout(t)]),d.setItemVisual(t,{fromSymbolSize:h.getItemVisual(t,"symbolSize"),fromSymbol:h.getItemVisual(t,"symbol"),toSymbolSize:c.getItemVisual(t,"symbolSize"),toSymbol:c.getItemVisual(t,"symbol")})}),l.updateData(d),u.line.eachItemGraphicEl(function(t,e){t.traverse(function(t){t.dataModel=n})}),l.__keep=!0,l.group.silent=n.get("silent")||r.get("silent")}}),nf(function(t){t.markLine=t.markLine||{}}),o_.extend({type:"markArea",defaultOption:{zlevel:0,z:1,tooltip:{trigger:"item"},animation:!1,label:{show:!0,position:"top"},itemStyle:{borderWidth:0},emphasis:{label:{show:!0,position:"top"}}}});function R_(t,e,n,i){var r=h_(t,i[0]),o=h_(t,i[1]),a=H,s=r.coord,l=o.coord;s[0]=a(s[0],-1/0),s[1]=a(s[1],-1/0),l[0]=a(l[0],1/0),l[1]=a(l[1],1/0);var u=p([{},r,o]);return u.coord=[r.coord,o.coord],u.x0=r.x,u.y0=r.y,u.x1=o.x,u.y1=o.y,u}function B_(t){return!isNaN(t)&&!isFinite(t)}function V_(t,e,n){var i=1-t;return B_(e[i])&&B_(n[i])}function F_(t,e){var n=e.coord[0],i=e.coord[1];return!("cartesian2d"!==t.type||!n||!i||!V_(1,n,i)&&!V_(0,n,i))||(d_(t,{coord:n,x:e.x0,y:e.y0})||d_(t,{coord:i,x:e.x1,y:e.y1}))}function H_(t,e,n,i,r){var o,a=i.coordinateSystem,s=t.getItemModel(e),l=Cl(s.get(n[0]),r.getWidth()),u=Cl(s.get(n[1]),r.getHeight());if(isNaN(l)||isNaN(u)){if(i.getMarkerPosition)o=i.getMarkerPosition(t.getValues(n,e));else{var h=[f=t.get(n[0],e),p=t.get(n[1],e)];a.clampData&&a.clampData(h,h),o=a.dataToPoint(h,!0)}if("cartesian2d"===a.type){var c=a.getAxis("x"),d=a.getAxis("y"),f=t.get(n[0],e),p=t.get(n[1],e);B_(f)?o[0]=c.toGlobalCoord(c.getExtent()["x0"===n[0]?0:1]):B_(p)&&(o[1]=d.toGlobalCoord(d.getExtent()["y0"===n[1]?0:1]))}isNaN(l)||(o[0]=l),isNaN(u)||(o[1]=u)}else o=[l,u];return o}var W_=[["x0","y0"],["x1","y0"],["x1","y1"],["x0","y1"]];g_.extend({type:"markArea",updateTransform:function(t,e,r){e.eachSeries(function(n){var t=n.markAreaModel;if(t){var i=t.getData();i.each(function(e){var t=P(W_,function(t){return H_(i,e,t,n,r)});i.setItemLayout(e,t),i.getItemGraphicEl(e).setShape("points",t)})}},this)},renderSeries:function(n,a,t,i){var e=n.coordinateSystem,r=n.id,o=n.getData(),s=this.markerGroupMap,l=s.get(r)||s.set(r,{group:new Mn});this.group.add(l.group),l.__keep=!0;var u=function(t,n,e){var i,r;r=t?(i=P(t&&t.dimensions,function(t){var e=n.getData();return A({name:t},e.getDimensionInfo(e.mapDimension(t))||{})}),new kf(P(["x0","y0","x1","y1"],function(t,e){return{name:t,type:i[e%2].type}}),e)):new kf(i=[{name:"value",type:"float"}],e);var o=P(e.get("data"),T(R_,n,t,e));t&&(o=I(o,T(F_,t)));var a=t?function(t,e,n,i){return t.coord[Math.floor(i/2)][i%2]}:function(t){return t.value};return r.initData(o,null,a),r.hasItemOption=!0,r}(e,n,a);a.setData(u),u.each(function(e){u.setItemLayout(e,P(W_,function(t){return H_(u,e,t,n,i)})),u.setItemVisual(e,{color:o.getVisual("color")})}),u.diff(l.__data).add(function(t){var e=new Xa({shape:{points:u.getItemLayout(t)}});u.setItemGraphicEl(t,e),l.group.add(e)}).update(function(t,e){var n=l.__data.getItemGraphicEl(e);nl(n,{shape:{points:u.getItemLayout(t)}},a,t),l.group.add(n),u.setItemGraphicEl(t,n)}).remove(function(t){var e=l.__data.getItemGraphicEl(t);l.group.remove(e)}).execute(),u.eachItemGraphicEl(function(t,e){var n=u.getItemModel(e),i=n.getModel("label"),r=n.getModel("emphasis.label"),o=u.getItemVisual(e,"color");t.useStyle(A(n.getModel("itemStyle").getItemStyle(),{fill:Ze(o,.4),stroke:o})),t.hoverStyle=n.getModel("emphasis.itemStyle").getItemStyle(),Ys(t.style,t.hoverStyle,i,r,{labelFetcher:a,labelDataIndex:e,defaultText:u.getName(e)||"",isRectText:!0,autoColor:o}),Gs(t,{}),t.dataModel=a}),l.__data=u,l.group.silent=a.get("silent")||n.get("silent")}}),nf(function(t){t.markArea=t.markArea||{}}),vu.registerSubTypeDefaulter("dataZoom",function(){return"slider"});var G_=["cartesian2d","polar","singleAxis"];var Z_,U_,X_,Y_,j_=(U_=["axisIndex","axis","index","id"],X_=P(Z_=(Z_=["x","y","z","radius","angle","single"]).slice(),Jl),Y_=P(U_=(U_||[]).slice(),Jl),function(r,o){D(Z_,function(t,e){for(var n={name:t,capital:X_[e]},i=0;i<U_.length;i++)n[U_[i]]=t+Y_[i];r.call(o,n)})});function q_(r,o,a){return function(t){var e,n={nodes:[],records:{}};if(o(function(t){n.records[t.name]={}}),!t)return n;for(s(t,n);e=!1,r(i),e;);function i(t){!function(t,e){return 0<=x(e.nodes,t)}(t,n)&&function(t,n){var i=!1;return o(function(e){D(a(t,e)||[],function(t){n.records[e.name][t]&&(i=!0)})}),i}(t,n)&&(s(t,n),e=!0)}return n};function s(t,n){n.nodes.push(t),o(function(e){D(a(t,e)||[],function(t){n.records[e.name][t]=!0})})}}function $_(t,e,n,i,r,o){t=t||0;var a=n[1]-n[0];if(null!=r&&(r=Q_(r,[0,a])),null!=o&&(o=Math.max(o,null!=r?r:0)),"all"===i){var s=Math.abs(e[1]-e[0]);r=o=Q_(s=Q_(s,[0,a]),[r,o]),i=0}e[0]=Q_(e[0],n),e[1]=Q_(e[1],n);var l=K_(e,i);e[i]+=t;var u=r||0,h=n.slice();l.sign<0?h[0]+=u:h[1]-=u,e[i]=Q_(e[i],h);var c=K_(e,i);return null!=r&&(c.sign!==l.sign||c.span<r)&&(e[1-i]=e[i]+l.sign*r),c=K_(e,i),null!=o&&c.span>o&&(e[1-i]=e[i]+c.sign*o),e}function K_(t,e){var n=t[e]-t[1-e];return{span:Math.abs(n),sign:0<n?-1:n<0?1:e?-1:1}}function Q_(t,e){return Math.min(null!=e[1]?e[1]:1/0,Math.max(null!=e[0]?e[0]:-1/0,t))}function J_(t,e,n,i){this._dimName=t,this._axisIndex=e,this._valueWindow,this._percentWindow,this._dataExtent,this._minMaxSpan,this.ecModel=i,this._dataZoomModel=n}var tx=D,ex=Al;function nx(t,e){var n=t.getAxisModel(),i=t._percentWindow,r=t._valueWindow;if(i){var o=Pl(r,[0,500]);o=Math.min(o,20);var a=e||0===i[0]&&100===i[1];n.setRange(a?null:+r[0].toFixed(o),a?null:+r[1].toFixed(o))}}J_.prototype={constructor:J_,hostedBy:function(t){return this._dataZoomModel===t},getDataValueWindow:function(){return this._valueWindow.slice()},getDataPercentWindow:function(){return this._percentWindow.slice()},getTargetSeriesModels:function(){var i=[],r=this.ecModel;return r.eachSeries(function(t){if(function(t){return 0<=x(G_,t)}(t.get("coordinateSystem"))){var e=this._dimName,n=r.queryComponents({mainType:e+"Axis",index:t.get(e+"AxisIndex"),id:t.get(e+"AxisId")})[0];this._axisIndex===(n&&n.componentIndex)&&i.push(t)}},this),i},getAxisModel:function(){return this.ecModel.getComponent(this._dimName+"Axis",this._axisIndex)},getOtherAxisModel:function(){var t,e,n,i=this._dimName,r=this.ecModel,o=this.getAxisModel();return t="x"===i||"y"===i?(e="gridIndex","x"===i?"y":"x"):(e="polarIndex","angle"===i?"radius":"angle"),r.eachComponent(t+"Axis",function(t){(t.get(e)||0)===(o.get(e)||0)&&(n=t)}),n},getMinMaxSpan:function(){return b(this._minMaxSpan)},calculateDataWindow:function(r){var o,a=this._dataExtent,s=this.getAxisModel().axis.scale,l=this._dataZoomModel.getRangePropMode(),u=[0,100],h=[],c=[];tx(["start","end"],function(t,e){var n=r[t],i=r[t+"Value"];"percent"===l[e]?(null==n&&(n=u[e]),i=s.parse(Il(n,u,a))):(o=!0,n=Il(i=null==i?a[e]:s.parse(i),a,u)),c[e]=i,h[e]=n}),ex(c),ex(h);var d=this._minMaxSpan;function t(t,e,n,i,r){var o=r?"Span":"ValueSpan";$_(0,t,n,"all",d["min"+o],d["max"+o]);for(var a=0;a<2;a++)e[a]=Il(t[a],n,i,!0),r&&(e[a]=s.parse(e[a]))}return o?t(c,h,a,u,!1):t(h,c,u,a,!0),{valueWindow:c,percentWindow:h}},reset:function(t){if(t===this._dataZoomModel){var e=this.getTargetSeriesModels();this._dataExtent=function(t,e,n){var i=[1/0,-1/0];tx(n,function(t){var n=t.getData();n&&tx(n.mapDimension(e,!0),function(t){var e=n.getApproximateExtent(t);e[0]<i[0]&&(i[0]=e[0]),e[1]>i[1]&&(i[1]=e[1])})}),i[1]<i[0]&&(i=[NaN,NaN]);return function(t,e){var n=t.getAxisModel(),i=n.getMin(!0),r="category"===n.get("type"),o=r&&n.getCategories().length;null!=i&&"dataMin"!==i&&"function"!=typeof i?e[0]=i:r&&(e[0]=0<o?0:NaN);var a=n.getMax(!0);null!=a&&"dataMax"!==a&&"function"!=typeof a?e[1]=a:r&&(e[1]=0<o?o-1:NaN);n.get("scale",!0)||(0<e[0]&&(e[0]=0),e[1]<0&&(e[1]=0))}(t,i),i}(this,this._dimName,e),function(i){var r=i._minMaxSpan={},o=i._dataZoomModel,a=i._dataExtent;tx(["min","max"],function(t){var e=o.get(t+"Span"),n=o.get(t+"ValueSpan");null!=n&&(n=i.getAxisModel().axis.scale.parse(n)),null!=n?e=Il(a[0]+n,a,[0,100],!0):null!=e&&(n=Il(e,[0,100],a,!0)-a[0]),r[t+"Span"]=e,r[t+"ValueSpan"]=n})}(this);var n=this.calculateDataWindow(t.settledOption);this._valueWindow=n.valueWindow,this._percentWindow=n.percentWindow,nx(this)}},restore:function(t){t===this._dataZoomModel&&(this._valueWindow=this._percentWindow=null,nx(this,!0))},filterData:function(t,e){if(t===this._dataZoomModel){var i=this._dimName,n=this.getTargetSeriesModels(),r=t.get("filterMode"),c=this._valueWindow;"none"!==r&&tx(n,function(n){var u=n.getData(),h=u.mapDimension(i,!0);h.length&&("weakFilter"===r?u.filterSelf(function(t){for(var e,n,i,r=0;r<h.length;r++){var o=u.get(h[r],t),a=!isNaN(o),s=o<c[0],l=o>c[1];if(a&&!s&&!l)return!0;a&&(i=!0),s&&(e=!0),l&&(n=!0)}return i&&e&&n}):tx(h,function(t){if("empty"===r)n.setData(u=u.map(t,function(t){return function(t){return t>=c[0]&&t<=c[1]}(t)?t:NaN}));else{var e={};e[t]=c,u.selectRange(e)}}),tx(h,function(t){u.setApproximateExtent(c,t)}))})}}};var ix=D,rx=j_,ox=hf({type:"dataZoom",dependencies:["xAxis","yAxis","zAxis","radiusAxis","angleAxis","singleAxis","series"],defaultOption:{zlevel:0,z:4,orient:null,xAxisIndex:null,yAxisIndex:null,filterMode:"filter",throttle:null,start:0,end:100,startValue:null,endValue:null,minSpan:null,maxSpan:null,minValueSpan:null,maxValueSpan:null,rangeMode:null},init:function(t,e,n){this._dataIntervalByAxis={},this._dataInfo={},this._axisProxies={},this.textStyleModel,this._autoThrottle=!0,this._rangePropMode=["percent","percent"];var i=ax(t);this.settledOption=i,this.mergeDefaultAndTheme(t,n),this.doInit(i)},mergeOption:function(t){var e=ax(t);m(this.option,t,!0),m(this.settledOption,e,!0),this.doInit(e)},doInit:function(t){var n=this.option;v.canvasSupported||(n.realtime=!1),this._setDefaultThrottle(t),sx(this,t);var i=this.settledOption;ix([["start","startValue"],["end","endValue"]],function(t,e){"value"===this._rangePropMode[e]&&(n[t[0]]=i[t[0]]=null)},this),this.textStyleModel=this.getModel("textStyle"),this._resetTarget(),this._giveAxisProxies()},_giveAxisProxies:function(){var a=this._axisProxies;this.eachTargetAxis(function(t,e,n,i){var r=this.dependentModels[t.axis][e],o=r.__dzAxisProxy||(r.__dzAxisProxy=new J_(t.name,e,this,i));a[t.name+"_"+e]=o},this)},_resetTarget:function(){var n=this.option,t=this._judgeAutoMode();rx(function(t){var e=t.axisIndex;n[e]=Lr(n[e])},this),"axisIndex"===t?this._autoSetAxisIndex():"orient"===t&&this._autoSetOrient()},_judgeAutoMode:function(){var e=this.option,n=!1;rx(function(t){null!=e[t.axisIndex]&&(n=!0)},this);var t=e.orient;return null==t&&n?"orient":n?void 0:(null==t&&(e.orient="horizontal"),"axisIndex")},_autoSetAxisIndex:function(){var o=!0,e=this.get("orient",!0),a=this.option,t=this.dependentModels;if(o){var n="vertical"===e?"y":"x";t[n+"Axis"].length?(a[n+"AxisIndex"]=[0],o=!1):ix(t.singleAxis,function(t){o&&t.get("orient",!0)===e&&(a.singleAxisIndex=[t.componentIndex],o=!1)})}o&&rx(function(t){if(o){var e=[],n=this.dependentModels[t.axis];if(n.length&&!e.length)for(var i=0,r=n.length;i<r;i++)"category"===n[i].get("type")&&e.push(i);(a[t.axisIndex]=e).length&&(o=!1)}},this),o&&this.ecModel.eachSeries(function(r){this._isSeriesHasAllAxesTypeOf(r,"value")&&rx(function(t){var e=a[t.axisIndex],n=r.get(t.axisIndex),i=r.get(t.axisId);x(e,n=r.ecModel.queryComponents({mainType:t.axis,index:n,id:i})[0].componentIndex)<0&&e.push(n)})},this)},_autoSetOrient:function(){var e;this.eachTargetAxis(function(t){e=e||t.name},this),this.option.orient="y"===e?"vertical":"horizontal"},_isSeriesHasAllAxesTypeOf:function(i,r){var o=!0;return rx(function(t){var e=i.get(t.axisIndex),n=this.dependentModels[t.axis][e];n&&n.get("type")===r||(o=!1)},this),o},_setDefaultThrottle:function(t){if(t.hasOwnProperty("throttle")&&(this._autoThrottle=!1),this._autoThrottle){var e=this.ecModel.option;this.option.throttle=e.animation&&0<e.animationDurationUpdate?100:20}},getFirstTargetAxisModel:function(){var n;return rx(function(t){if(null==n){var e=this.get(t.axisIndex);e.length&&(n=this.dependentModels[t.axis][e[0]])}},this),n},eachTargetAxis:function(n,i){var r=this.ecModel;rx(function(e){ix(this.get(e.axisIndex),function(t){n.call(i,e,t,this,r)},this)},this)},getAxisProxy:function(t,e){return this._axisProxies[t+"_"+e]},getAxisModel:function(t,e){var n=this.getAxisProxy(t,e);return n&&n.getAxisModel()},setRawRange:function(e){var n=this.option,i=this.settledOption;ix([["start","startValue"],["end","endValue"]],function(t){null==e[t[0]]&&null==e[t[1]]||(n[t[0]]=i[t[0]]=e[t[0]],n[t[1]]=i[t[1]]=e[t[1]])},this),sx(this,e)},setCalculatedRange:function(e){var n=this.option;ix(["start","startValue","end","endValue"],function(t){n[t]=e[t]})},getPercentRange:function(){var t=this.findRepresentativeAxisProxy();if(t)return t.getDataPercentWindow()},getValueRange:function(t,e){if(null!=t||null!=e)return this.getAxisProxy(t,e).getDataValueWindow();var n=this.findRepresentativeAxisProxy();return n?n.getDataValueWindow():void 0},findRepresentativeAxisProxy:function(t){if(t)return t.__dzAxisProxy;var e=this._axisProxies;for(var n in e)if(e.hasOwnProperty(n)&&e[n].hostedBy(this))return e[n];for(var n in e)if(e.hasOwnProperty(n)&&!e[n].hostedBy(this))return e[n]},getRangePropMode:function(){return this._rangePropMode.slice()}});function ax(e){var n={};return ix(["start","end","startValue","endValue","throttle"],function(t){e.hasOwnProperty(t)&&(n[t]=e[t])}),n}function sx(t,r){var o=t._rangePropMode,a=t.get("rangeMode");ix([["start","startValue"],["end","endValue"]],function(t,e){var n=null!=r[t[0]],i=null!=r[t[1]];n&&!i?o[e]="percent":!n&&i?o[e]="value":a?o[e]=a[e]:n&&(o[e]="percent")})}var lx=ec.extend({type:"dataZoom",render:function(t,e,n,i){this.dataZoomModel=t,this.ecModel=e,this.api=n},getTargetCoordInfo:function(){var t=this.dataZoomModel,r=this.ecModel,o={};return t.eachTargetAxis(function(t,e){var n=r.getComponent(t.axis,e);if(n){var i=n.getCoordSysModel();i&&function(t,e,n,i){for(var r,o=0;o<n.length;o++)if(n[o].model===t){r=n[o];break}r||n.push(r={model:t,axisModels:[],coordIndex:i});r.axisModels.push(e)}(i,n,o[i.mainType]||(o[i.mainType]=[]),i.componentIndex)}},this),o}}),ux=(ox.extend({type:"dataZoom.slider",layoutMode:"box",defaultOption:{show:!0,right:"ph",top:"ph",width:"ph",height:"ph",left:null,bottom:null,backgroundColor:"rgba(47,69,84,0)",dataBackground:{lineStyle:{color:"#2f4554",width:.5,opacity:.3},areaStyle:{color:"rgba(47,69,84,0.3)",opacity:.3}},borderColor:"#ddd",fillerColor:"rgba(167,183,204,0.4)",handleIcon:"M8.2,13.6V3.9H6.3v9.7H3.1v14.9h3.3v9.7h1.8v-9.7h3.3V13.6H8.2z M9.7,24.4H4.8v-1.4h4.9V24.4z M9.7,19.1H4.8v-1.4h4.9V19.1z",handleSize:"100%",handleStyle:{color:"#a7b7cc"},labelPrecision:null,labelFormatter:null,showDetail:!0,showDataShadow:"auto",realtime:!0,zoomLock:!1,textStyle:{color:"#333"}}}),Ja),hx=Il,cx=Al,dx=C,fx=D,px="horizontal",gx="vertical",mx=["line","bar","candlestick","scatter"],vx=lx.extend({type:"dataZoom.slider",init:function(t,e){this._displayables={},this._orient,this._range,this._handleEnds,this._size,this._handleWidth,this._handleHeight,this._location,this._dragging,this._dataShadowInfo,this.api=e},render:function(t,e,n,i){vx.superApply(this,"render",arguments),vc(this,"_dispatchZoomAction",this.dataZoomModel.get("throttle"),"fixRate"),this._orient=t.get("orient"),!1!==this.dataZoomModel.get("show")?(i&&"dataZoom"===i.type&&i.from===this.uid||this._buildView(),this._updateView()):this.group.removeAll()},remove:function(){vx.superApply(this,"remove",arguments),yc(this,"_dispatchZoomAction")},dispose:function(){vx.superApply(this,"dispose",arguments),yc(this,"_dispatchZoomAction")},_buildView:function(){var t=this.group;t.removeAll(),this._resetLocation(),this._resetInterval();var e=this._displayables.barGroup=new Mn;this._renderBackground(),this._renderHandle(),this._renderDataShadow(),t.add(e),this._positionGroup()},_resetLocation:function(){var t=this.dataZoomModel,e=this.api,n=this._findCoordRect(),i={width:e.getWidth(),height:e.getHeight()},r=this._orient===px?{right:i.width-n.x-n.width,top:i.height-30-7,width:n.width,height:30}:{right:7,top:n.y,width:30,height:n.height},o=cu(t.option);D(["right","top","width","height"],function(t){"ph"===o[t]&&(o[t]=r[t])});var a=lu(o,i,t.padding);this._location={x:a.x,y:a.y},this._size=[a.width,a.height],this._orient===gx&&this._size.reverse()},_positionGroup:function(){var t=this.group,e=this._location,n=this._orient,i=this.dataZoomModel.getFirstTargetAxisModel(),r=i&&i.get("inverse"),o=this._displayables.barGroup,a=(this._dataShadowInfo||{}).otherAxisInverse;o.attr(n!==px||r?n===px&&r?{scale:a?[-1,1]:[-1,-1]}:n!==gx||r?{scale:a?[-1,-1]:[-1,1],rotation:Math.PI/2}:{scale:a?[1,-1]:[1,1],rotation:Math.PI/2}:{scale:a?[1,1]:[1,-1]});var s=t.getBoundingRect([o]);t.attr("position",[e.x-s.x,e.y-s.y])},_getViewExtent:function(){return[0,this._size[0]]},_renderBackground:function(){var t=this.dataZoomModel,e=this._size,n=this._displayables.barGroup;n.add(new ux({silent:!0,shape:{x:0,y:0,width:e[0],height:e[1]},style:{fill:t.get("backgroundColor")},z2:-40})),n.add(new ux({shape:{x:0,y:0,width:e[0],height:e[1]},style:{fill:"transparent"},z2:0,onclick:C(this._onClickPanelClick,this)}))},_renderDataShadow:function(){var t=this._dataShadowInfo=this._prepareDataShadowInfo();if(t){var e=this._size,n=t.series,i=n.getRawData(),r=n.getShadowDim?n.getShadowDim():t.otherDim;if(null!=r){var o=i.getDataExtent(r),a=.3*(o[1]-o[0]);o=[o[0]-a,o[1]+a];var s,l=[0,e[1]],u=[0,e[0]],h=[[e[0],0],[0,0]],c=[],d=u[1]/(i.count()-1),f=0,p=Math.round(i.count()/e[0]);i.each([r],function(t,e){if(0<p&&e%p)f+=d;else{var n=null==t||isNaN(t)||""===t,i=n?0:hx(t,o,l,!0);n&&!s&&e?(h.push([h[h.length-1][0],0]),c.push([c[c.length-1][0],0])):!n&&s&&(h.push([f,0]),c.push([f,0])),h.push([f,i]),c.push([f,i]),f+=d,s=n}});var g=this.dataZoomModel;this._displayables.barGroup.add(new Xa({shape:{points:h},style:A({fill:g.get("dataBackgroundColor")},g.getModel("dataBackground.areaStyle").getAreaStyle()),silent:!0,z2:-20})),this._displayables.barGroup.add(new Ya({shape:{points:c},style:g.getModel("dataBackground.lineStyle").getLineStyle(),silent:!0,z2:-19}))}}},_prepareDataShadowInfo:function(){var t=this.dataZoomModel,s=t.get("showDataShadow");if(!1!==s){var l,u=this.ecModel;return t.eachTargetAxis(function(o,a){D(t.getAxisProxy(o.name,a).getTargetSeriesModels(),function(t){if(!(l||!0!==s&&x(mx,t.get("type"))<0)){var e,n=u.getComponent(o.axis,a).axis,i=function(t){return{x:"y",y:"x",radius:"angle",angle:"radius"}[t]}(o.name),r=t.coordinateSystem;null!=i&&r.getOtherAxis&&(e=r.getOtherAxis(n).inverse),i=t.getData().mapDimension(i),l={thisAxis:n,series:t,thisDim:o.name,otherDim:i,otherAxisInverse:e}}},this)},this),l}},_renderHandle:function(){var t=this._displayables,o=t.handles=[],a=t.handleLabels=[],s=this._displayables.barGroup,e=this._size,l=this.dataZoomModel;s.add(t.filler=new ux({draggable:!0,cursor:yx(this._orient),drift:dx(this._onDragMove,this,"all"),ondragstart:dx(this._showDataInfo,this,!0),ondragend:dx(this._onDragEnd,this),onmouseover:dx(this._showDataInfo,this,!0),onmouseout:dx(this._showDataInfo,this,!1),style:{fill:l.get("fillerColor"),textPosition:"inside"}})),s.add(new ux({silent:!0,subPixelOptimize:!0,shape:{x:0,y:0,width:e[0],height:e[1]},style:{stroke:l.get("dataBackgroundColor")||l.get("borderColor"),lineWidth:1,fill:"rgba(0,0,0,0)"}})),fx([0,1],function(t){var e=ul(l.get("handleIcon"),{cursor:yx(this._orient),draggable:!0,drift:dx(this._onDragMove,this,t),ondragend:dx(this._onDragEnd,this),onmouseover:dx(this._showDataInfo,this,!0),onmouseout:dx(this._showDataInfo,this,!1)},{x:-1,y:0,width:2,height:2}),n=e.getBoundingRect();this._handleHeight=Cl(l.get("handleSize"),this._size[1]),this._handleWidth=n.width/n.height*this._handleHeight,e.setStyle(l.getModel("handleStyle").getItemStyle());var i=l.get("handleColor");null!=i&&(e.style.fill=i),s.add(o[t]=e);var r=l.textStyleModel;this.group.add(a[t]=new Ba({silent:!0,invisible:!0,style:{x:0,y:0,text:"",textVerticalAlign:"middle",textAlign:"center",textFill:r.getTextColor(),textFont:r.getFont()},z2:10}))},this)},_resetInterval:function(){var t=this._range=this.dataZoomModel.getPercentRange(),e=this._getViewExtent();this._handleEnds=[hx(t[0],[0,100],e,!0),hx(t[1],[0,100],e,!0)]},_updateInterval:function(t,e){var n=this.dataZoomModel,i=this._handleEnds,r=this._getViewExtent(),o=n.findRepresentativeAxisProxy().getMinMaxSpan(),a=[0,100];$_(e,i,r,n.get("zoomLock")?"all":t,null!=o.minSpan?hx(o.minSpan,a,r,!0):null,null!=o.maxSpan?hx(o.maxSpan,a,r,!0):null);var s=this._range,l=this._range=cx([hx(i[0],r,a,!0),hx(i[1],r,a,!0)]);return!s||s[0]!==l[0]||s[1]!==l[1]},_updateView:function(t){var i=this._displayables,r=this._handleEnds,e=cx(r.slice()),o=this._size;fx([0,1],function(t){var e=i.handles[t],n=this._handleHeight;e.attr({scale:[n/2,n/2],position:[r[t],o[1]/2-n/2]})},this),i.filler.setShape({x:e[0],y:0,width:e[1]-e[0],height:o[1]}),this._updateDataInfo(t)},_updateDataInfo:function(t){var e=this.dataZoomModel,o=this._displayables,a=o.handleLabels,s=this._orient,l=["",""];if(e.get("showDetail")){var n=e.findRepresentativeAxisProxy();if(n){var i=n.getAxisModel().axis,r=this._range,u=t?n.calculateDataWindow({start:r[0],end:r[1]}).valueWindow:n.getDataValueWindow();l=[this._formatLabel(u[0],i),this._formatLabel(u[1],i)]}}var h=cx(this._handleEnds.slice());function c(t){var e=rl(o.handles[t].parent,this.group),n=al(0===t?"right":"left",e),i=this._handleWidth/2+5,r=ol([h[t]+(0===t?-i:i),this._size[1]/2],e);a[t].setStyle({x:r[0],y:r[1],textVerticalAlign:s===px?"middle":n,textAlign:s===px?n:"center",text:l[t]})}c.call(this,0),c.call(this,1)},_formatLabel:function(t,e){var n=this.dataZoomModel,i=n.get("labelFormatter"),r=n.get("labelPrecision");null!=r&&"auto"!==r||(r=e.getPixelPrecision());var o=null==t||isNaN(t)?"":"category"===e.type||"time"===e.type?e.scale.getLabel(Math.round(t)):t.toFixed(Math.min(r,20));return E(i)?i(t,o):z(i)?i.replace("{value}",o):o},_showDataInfo:function(t){t=this._dragging||t;var e=this._displayables.handleLabels;e[0].attr("invisible",!t),e[1].attr("invisible",!t)},_onDragMove:function(t,e,n,i){this._dragging=!0,Wt(i.event);var r=ol([e,n],this._displayables.barGroup.getLocalTransform(),!0),o=this._updateInterval(t,r[0]),a=this.dataZoomModel.get("realtime");this._updateView(!a),o&&a&&this._dispatchZoomAction()},_onDragEnd:function(){this._dragging=!1,this._showDataInfo(!1),this.dataZoomModel.get("realtime")||this._dispatchZoomAction()},_onClickPanelClick:function(t){var e=this._size,n=this._displayables.barGroup.transformCoordToLocal(t.offsetX,t.offsetY);if(!(n[0]<0||n[0]>e[0]||n[1]<0||n[1]>e[1])){var i=this._handleEnds,r=(i[0]+i[1])/2,o=this._updateInterval("all",n[0]-r);this._updateView(),o&&this._dispatchZoomAction()}},_dispatchZoomAction:function(){var t=this._range;this.api.dispatchAction({type:"dataZoom",from:this.uid,dataZoomId:this.dataZoomModel.id,start:t[0],end:t[1]})},_findCoordRect:function(){var n;if(fx(this.getTargetCoordInfo(),function(t){if(!n&&t.length){var e=t[0].model.coordinateSystem;n=e.getRect&&e.getRect()}}),!n){var t=this.api.getWidth(),e=this.api.getHeight();n={x:.2*t,y:.2*e,width:.6*t,height:.6*e}}return n}});function yx(t){return"vertical"===t?"ns-resize":"ew-resize"}rf({getTargetSeries:function(t){var i=Q();return t.eachComponent("dataZoom",function(t){t.eachTargetAxis(function(t,e,n){D(n.getAxisProxy(t.name,e).getTargetSeriesModels(),function(t){i.set(t.uid,t)})})}),i},modifyOutputEnd:!0,overallReset:function(t,i){t.eachComponent("dataZoom",function(t){t.eachTargetAxis(function(t,e,n){n.getAxisProxy(t.name,e).reset(n,i)}),t.eachTargetAxis(function(t,e,n){n.getAxisProxy(t.name,e).filterData(n,i)})}),t.eachComponent("dataZoom",function(t){var e=t.findRepresentativeAxisProxy(),n=e.getDataPercentWindow(),i=e.getDataValueWindow();t.setCalculatedRange({start:n[0],end:n[1],startValue:i[0],endValue:i[1]})})}}),of("dataZoom",function(n,t){var i=q_(C(t.eachComponent,t,"dataZoom"),j_,function(t,e){return t.get(e.axisIndex)}),r=[];t.eachComponent({mainType:"dataZoom",query:n},function(t,e){r.push.apply(r,i(t).nodes)}),D(r,function(t,e){t.setRawRange({start:n.start,end:n.end,startValue:n.startValue,endValue:n.endValue})})}),ox.extend({type:"dataZoom.inside",defaultOption:{disabled:!1,zoomLock:!1,zoomOnMouseWheel:!0,moveOnMouseMove:!0,moveOnMouseWheel:!1,preventDefaultMouseMove:!0}});var _x="\0_ec_interaction_mutex";function xx(t,e){return!!bx(t)[e]}function bx(t){return t[_x]||(t[_x]={})}function Sx(n){this.pointerChecker,this._zr=n,this._opt={};var t=C,i=t(Mx,this),r=t(Ix,this),o=t(Cx,this),a=t(Tx,this),s=t(Ax,this);It.call(this),this.setPointerChecker=function(t){this.pointerChecker=t},this.enable=function(t,e){this.disable(),this._opt=A(b(e)||{},{zoomOnMouseWheel:!0,moveOnMouseMove:!0,moveOnMouseWheel:!1,preventDefaultMouseMove:!0}),null==t&&(t=!0),!0!==t&&"move"!==t&&"pan"!==t||(n.on("mousedown",i),n.on("mousemove",r),n.on("mouseup",o)),!0!==t&&"scale"!==t&&"zoom"!==t||(n.on("mousewheel",a),n.on("pinch",s))},this.disable=function(){n.off("mousedown",i),n.off("mousemove",r),n.off("mouseup",o),n.off("mousewheel",a),n.off("pinch",s)},this.dispose=this.disable,this.isDragging=function(){return this._dragging},this.isPinching=function(){return this._pinching}}function Mx(t){if(!(Gt(t)||t.target&&t.target.draggable)){var e=t.offsetX,n=t.offsetY;this.pointerChecker&&this.pointerChecker(t,e,n)&&(this._x=e,this._y=n,this._dragging=!0)}}function Ix(t){if(this._dragging&&Px("moveOnMouseMove",t,this._opt)&&"pinch"!==t.gestureEvent&&!xx(this._zr,"globalPan")){var e=t.offsetX,n=t.offsetY,i=this._x,r=this._y,o=e-i,a=n-r;this._x=e,this._y=n,this._opt.preventDefaultMouseMove&&Wt(t.event),kx(this,"pan","moveOnMouseMove",t,{dx:o,dy:a,oldX:i,oldY:r,newX:e,newY:n})}}function Cx(t){Gt(t)||(this._dragging=!1)}function Tx(t){var e=Px("zoomOnMouseWheel",t,this._opt),n=Px("moveOnMouseWheel",t,this._opt),i=t.wheelDelta,r=Math.abs(i),o=t.offsetX,a=t.offsetY;if(0!==i&&(e||n)){if(e){var s=3<r?1.4:1<r?1.2:1.1;Dx(this,"zoom","zoomOnMouseWheel",t,{scale:0<i?s:1/s,originX:o,originY:a})}if(n){var l=Math.abs(i);Dx(this,"scrollMove","moveOnMouseWheel",t,{scrollDelta:(0<i?1:-1)*(3<l?.4:1<l?.15:.05),originX:o,originY:a})}}}function Ax(t){xx(this._zr,"globalPan")||Dx(this,"zoom",null,t,{scale:1<t.pinchScale?1.1:1/1.1,originX:t.pinchX,originY:t.pinchY})}function Dx(t,e,n,i,r){t.pointerChecker&&t.pointerChecker(i,r.originX,r.originY)&&(Wt(i.event),kx(t,e,n,i,r))}function kx(t,e,n,i,r){r.isAvailableBehavior=C(Px,null,n,i),t.trigger(e,r)}function Px(t,e,n){var i=n[t];return!t||i&&(!z(i)||e.event[i+"Key"])}of({type:"takeGlobalCursor",event:"globalCursorTaken",update:"update"},function(){}),S(Sx,It);var Lx="\0_ec_dataZoom_roams";function Ox(t,i){var e=zx(t),r=i.dataZoomId,o=i.coordId;D(e,function(t,e){var n=t.dataZoomInfos;n[r]&&x(i.allCoordIds,o)<0&&(delete n[r],t.count--)}),Nx(e);var n=e[o];n||((n=e[o]={coordId:o,dataZoomInfos:{},count:0}).controller=function(t,a){var e=new Sx(t.getZr());return D(["pan","zoom","scrollMove"],function(o){e.on(o,function(i){var r=[];D(a.dataZoomInfos,function(t){if(i.isAvailableBehavior(t.dataZoomModel.option)){var e=(t.getRange||{})[o],n=e&&e(a.controller,i);!t.dataZoomModel.get("disabled",!0)&&n&&r.push({dataZoomId:t.dataZoomId,start:n[0],end:n[1]})}}),r.length&&a.dispatchAction(r)})}),e}(t,n),n.dispatchAction=T(Rx,t)),n.dataZoomInfos[r]||n.count++,n.dataZoomInfos[r]=i;var a=function(t){var i,r={type_true:2,type_move:1,type_false:0,type_undefined:-1},o=!0;return D(t,function(t){var e=t.dataZoomModel,n=!e.get("disabled",!0)&&(!e.get("zoomLock",!0)||"move");r["type_"+i]<r["type_"+n]&&(i=n),o&=e.get("preventDefaultMouseMove",!0)}),{controlType:i,opt:{zoomOnMouseWheel:!0,moveOnMouseMove:!0,moveOnMouseWheel:!0,preventDefaultMouseMove:!!o}}}(n.dataZoomInfos);n.controller.enable(a.controlType,a.opt),n.controller.setPointerChecker(i.containsPoint),vc(n,"dispatchAction",i.dataZoomModel.get("throttle",!0),"fixRate")}function Ex(t){return t.type+"\0_"+t.id}function zx(t){var e=t.getZr();return e[Lx]||(e[Lx]={})}function Nx(n){D(n,function(t,e){t.count||(t.controller.dispose(),delete n[e])})}function Rx(t,e){t.dispatchAction({type:"dataZoom",batch:e})}var Bx=C,Vx=lx.extend({type:"dataZoom.inside",init:function(t,e){this._range},render:function(a,t,s,e){Vx.superApply(this,"render",arguments),this._range=a.getPercentRange(),D(this.getTargetCoordInfo(),function(t,r){var o=P(t,function(t){return Ex(t.model)});D(t,function(e){var i=e.model,n={};D(["pan","zoom","scrollMove"],function(t){n[t]=Bx(Fx[t],this,e,r)},this),Ox(s,{coordId:Ex(i),allCoordIds:o,containsPoint:function(t,e,n){return i.coordinateSystem.containPoint([e,n])},dataZoomId:a.id,dataZoomModel:a,getRange:n})},this)},this)},dispose:function(){!function(t,n){var e=zx(t);D(e,function(t){t.controller.dispose();var e=t.dataZoomInfos;e[n]&&(delete e[n],t.count--)}),Nx(e)}(this.api,this.dataZoomModel.id),Vx.superApply(this,"dispose",arguments),this._range=null}}),Fx={zoom:function(t,e,n,i){var r=this._range,o=r.slice(),a=t.axisModels[0];if(a){var s=Wx[e](null,[i.originX,i.originY],a,n,t),l=(0<s.signal?s.pixelStart+s.pixelLength-s.pixel:s.pixel-s.pixelStart)/s.pixelLength*(o[1]-o[0])+o[0],u=Math.max(1/i.scale,0);o[0]=(o[0]-l)*u+l,o[1]=(o[1]-l)*u+l;var h=this.dataZoomModel.findRepresentativeAxisProxy().getMinMaxSpan();return $_(0,o,[0,100],0,h.minSpan,h.maxSpan),this._range=o,r[0]!==o[0]||r[1]!==o[1]?o:void 0}},pan:Hx(function(t,e,n,i,r,o){var a=Wx[i]([o.oldX,o.oldY],[o.newX,o.newY],e,r,n);return a.signal*(t[1]-t[0])*a.pixel/a.pixelLength}),scrollMove:Hx(function(t,e,n,i,r,o){return Wx[i]([0,0],[o.scrollDelta,o.scrollDelta],e,r,n).signal*(t[1]-t[0])*o.scrollDelta})};function Hx(l){return function(t,e,n,i){var r=this._range,o=r.slice(),a=t.axisModels[0];if(a){var s=l(o,a,t,e,n,i);return $_(s,o,[0,100],"all"),this._range=o,r[0]!==o[0]||r[1]!==o[1]?o:void 0}}}var Wx={grid:function(t,e,n,i,r){var o=n.axis,a={},s=r.model.coordinateSystem.getRect();return t=t||[0,0],"x"===o.dim?(a.pixel=e[0]-t[0],a.pixelLength=s.width,a.pixelStart=s.x,a.signal=o.inverse?1:-1):(a.pixel=e[1]-t[1],a.pixelLength=s.height,a.pixelStart=s.y,a.signal=o.inverse?-1:1),a},polar:function(t,e,n,i,r){var o=n.axis,a={},s=r.model.coordinateSystem,l=s.getRadiusAxis().getExtent(),u=s.getAngleAxis().getExtent();return t=t?s.pointToCoord(t):[0,0],e=s.pointToCoord(e),"radiusAxis"===n.mainType?(a.pixel=e[0]-t[0],a.pixelLength=l[1]-l[0],a.pixelStart=l[0],a.signal=o.inverse?1:-1):(a.pixel=e[1]-t[1],a.pixelLength=u[1]-u[0],a.pixelStart=u[0],a.signal=o.inverse?-1:1),a},singleAxis:function(t,e,n,i,r){var o=n.axis,a=r.model.coordinateSystem.getRect(),s={};return t=t||[0,0],"horizontal"===o.orient?(s.pixel=e[0]-t[0],s.pixelLength=a.width,s.pixelStart=a.x,s.signal=o.inverse?1:-1):(s.pixel=e[1]-t[1],s.pixelLength=a.height,s.pixelStart=a.y,s.signal=o.inverse?-1:1),s}},Gx={};function Zx(t,e){Gx[t]=e}function Ux(t){return Gx[t]}var Xx=hf({type:"toolbox",layoutMode:{type:"box",ignoreSize:!0},optionUpdated:function(){Xx.superApply(this,"optionUpdated",arguments),D(this.option.feature,function(t,e){var n=Ux(e);n&&m(t,n.defaultOption)})},defaultOption:{show:!0,z:6,zlevel:0,orient:"horizontal",left:"right",top:"top",backgroundColor:"transparent",borderColor:"#ccc",borderRadius:0,borderWidth:0,padding:5,itemSize:15,itemGap:8,showTitle:!0,iconStyle:{borderColor:"#666",color:"none"},emphasis:{iconStyle:{borderColor:"#3E98C5"}},tooltip:{show:!1}}});cf({type:"toolbox",render:function(h,c,d,l){var f=this.group;if(f.removeAll(),h.get("show")){var p=+h.get("itemSize"),u=h.get("feature")||{},g=this._features||(this._features={}),m=[];D(u,function(t,e){m.push(e)}),new gf(this._featureNames||[],m).add(t).update(t).remove(T(t,null)).execute(),this._featureNames=m,function(t,e,n){var i=e.getBoxLayoutParams(),r=e.get("padding"),o={width:n.getWidth(),height:n.getHeight()},a=lu(i,o,r);su(e.get("orient"),t,e.get("itemGap"),a.width,a.height),uu(t,i,o,r)}(f,h,d),f.add(Hy(f.getBoundingRect(),h)),f.eachChild(function(t){var e=t.__title,n=t.hoverStyle;if(n&&e){var i=ui(e,wi(n)),r=t.position[0]+f.position[0],o=!1;t.position[1]+f.position[1]+p+i.height>d.getHeight()&&(n.textPosition="top",o=!0);var a=o?-5-i.height:p+8;r+i.width/2>d.getWidth()?(n.textPosition=["100%",a],n.textAlign="right"):r-i.width/2<0&&(n.textPosition=[0,a],n.textAlign="left")}})}function t(t,e){var n,i=m[t],r=m[e],o=u[i],a=new _l(o,h,h.ecModel);if(l&&null!=l.newTitle&&l.featureName===i&&(o.title=l.newTitle),i&&!r){if(function(t){return 0===t.indexOf("my")}(i))n={model:a,onclick:a.option.onclick,featureName:i};else{var s=Ux(i);if(!s)return;n=new s(a,c,d)}g[i]=n}else{if(!(n=g[r]))return;n.model=a,n.ecModel=c,n.api=d}i||!r?a.get("show")&&!n.unusable?(function(r,o,t){var a=r.getModel("iconStyle"),s=r.getModel("emphasis.iconStyle"),e=o.getIcons?o.getIcons():r.get("icon"),l=r.get("title")||{};if("string"==typeof e){var n=e,i=l;l={},(e={})[t]=n,l[t]=i}var u=r.iconPaths={};D(e,function(t,e){var n=ul(t,{},{x:-p/2,y:-p/2,width:p,height:p});n.setStyle(a.getItemStyle()),n.hoverStyle=s.getItemStyle(),n.setStyle({text:l[e],textAlign:s.get("textAlign"),textBorderRadius:s.get("textBorderRadius"),textPadding:s.get("textPadding"),textFill:null});var i=h.getModel("tooltip");i&&i.get("show")&&n.attr("tooltip",k({content:l[e],formatter:i.get("formatter",!0)||function(){return l[e]},formatterParams:{componentType:"toolbox",name:e,title:l[e],$vars:["name","title"]},position:i.get("position",!0)||"bottom"},i.option)),Gs(n),h.get("showTitle")&&(n.__title=l[e],n.on("mouseover",function(){var t=s.getItemStyle(),e="vertical"===h.get("orient")?null==h.get("right")?"right":"left":null==h.get("bottom")?"bottom":"top";n.setStyle({textFill:s.get("textFill")||t.fill||t.stroke||"#000",textBackgroundColor:s.get("textBackgroundColor"),textPosition:s.get("textPosition")||e})}).on("mouseout",function(){n.setStyle({textFill:null,textBackgroundColor:null})})),n.trigger(r.get("iconStatus."+e)||"normal"),f.add(n),n.on("click",C(o.onclick,o,c,d,e)),u[e]=n})}(a,n,i),a.setIconStatus=function(t,e){var n=this.option,i=this.iconPaths;n.iconStatus=n.iconStatus||{},n.iconStatus[t]=e,i[t]&&i[t].trigger(e)},n.render&&n.render(a,c,d,l)):n.remove&&n.remove(c,d):n.dispose&&n.dispose(c,d)}},updateView:function(t,e,n,i){D(this._features,function(t){t.updateView&&t.updateView(t.model,e,n,i)})},remove:function(e,n){D(this._features,function(t){t.remove&&t.remove(e,n)}),this.group.removeAll()},dispose:function(e,n){D(this._features,function(t){t.dispose&&t.dispose(e,n)})}});var Yx=xc.toolbox.saveAsImage;function jx(t){this.model=t}jx.defaultOption={show:!0,icon:"M4.7,22.9L29.3,45.5L54.7,23.4M4.6,43.6L4.6,58L53.8,58L53.8,43.6M29.2,45.1L29.2,0",title:Yx.title,type:"png",connectedBackgroundColor:"#fff",name:"",excludeComponents:["toolbox"],pixelRatio:1,lang:Yx.lang.slice()},jx.prototype.unusable=!v.canvasSupported,jx.prototype.onclick=function(t,e){var n=this.model,i=n.get("name")||t.get("title.0.text")||"echarts",r="svg"===e.getZr().painter.getType()?"svg":n.get("type",!0)||"png",o=e.getConnectedDataURL({type:r,backgroundColor:n.get("backgroundColor",!0)||t.get("backgroundColor")||"#fff",connectedBackgroundColor:n.get("connectedBackgroundColor"),excludeComponents:n.get("excludeComponents"),pixelRatio:n.get("pixelRatio")});if("function"!=typeof MouseEvent||v.browser.ie||v.browser.edge)if(window.navigator.msSaveOrOpenBlob){for(var a=atob(o.split(",")[1]),s=a.length,l=new Uint8Array(s);s--;)l[s]=a.charCodeAt(s);var u=new Blob([l]);window.navigator.msSaveOrOpenBlob(u,i+"."+r)}else{var h=n.get("lang"),c='<body style="margin:0;"><img src="'+o+'" style="max-width:100%;" title="'+(h&&h[0]||"")+'" /></body>';window.open().document.write(c)}else{var d=document.createElement("a");d.download=i+"."+r,d.target="_blank",d.href=o;var f=new MouseEvent("click",{view:window,bubbles:!0,cancelable:!1});d.dispatchEvent(f)}},Zx("saveAsImage",jx);var qx=xc.toolbox.magicType,$x="__ec_magicType_stack__";function Kx(t){this.model=t}Kx.defaultOption={show:!0,type:[],icon:{line:"M4.1,28.9h7.1l9.3-22l7.4,38l9.7-19.7l3,12.8h14.9M4.1,58h51.4",bar:"M6.7,22.9h10V48h-10V22.9zM24.9,13h10v35h-10V13zM43.2,2h10v46h-10V2zM3.1,58h53.7",stack:"M8.2,38.4l-8.4,4.1l30.6,15.3L60,42.5l-8.1-4.1l-21.5,11L8.2,38.4z M51.9,30l-8.1,4.2l-13.4,6.9l-13.9-6.9L8.2,30l-8.4,4.2l8.4,4.2l22.2,11l21.5-11l8.1-4.2L51.9,30z M51.9,21.7l-8.1,4.2L35.7,30l-5.3,2.8L24.9,30l-8.4-4.1l-8.3-4.2l-8.4,4.2L8.2,30l8.3,4.2l13.9,6.9l13.4-6.9l8.1-4.2l8.1-4.1L51.9,21.7zM30.4,2.2L-0.2,17.5l8.4,4.1l8.3,4.2l8.4,4.2l5.5,2.7l5.3-2.7l8.1-4.2l8.1-4.2l8.1-4.1L30.4,2.2z"},title:b(qx.title),option:{},seriesIndex:{}};var Qx=Kx.prototype;Qx.getIcons=function(){var t=this.model,e=t.get("icon"),n={};return D(t.get("type"),function(t){e[t]&&(n[t]=e[t])}),n};var Jx={line:function(t,e,n,i){if("bar"===t)return m({id:e,type:"line",data:n.get("data"),stack:n.get("stack"),markPoint:n.get("markPoint"),markLine:n.get("markLine")},i.get("option.line")||{},!0)},bar:function(t,e,n,i){if("line"===t)return m({id:e,type:"bar",data:n.get("data"),stack:n.get("stack"),markPoint:n.get("markPoint"),markLine:n.get("markLine")},i.get("option.bar")||{},!0)},stack:function(t,e,n,i){var r=n.get("stack")===$x;if("line"===t||"bar"===t)return i.setIconStatus("stack",r?"normal":"emphasis"),m({id:e,stack:r?"":$x},i.get("option.stack")||{},!0)}},tw=[["line","bar"],["stack"]];Qx.onclick=function(u,t,h){var c=this.model,e=c.get("seriesIndex."+h);if(Jx[h]){var n,d={series:[]};if(D(tw,function(t){0<=x(t,h)&&D(t,function(t){c.setIconStatus(t,"normal")})}),c.setIconStatus(h,"emphasis"),u.eachComponent({mainType:"series",query:null==e?null:{seriesIndex:e}},function(t){var e=t.subType,n=t.id,i=Jx[h](e,n,t,c);i&&(A(i,t.option),d.series.push(i));var r=t.coordinateSystem;if(r&&"cartesian2d"===r.type&&("line"===h||"bar"===h)){var o=r.getAxesByScale("ordinal")[0];if(o){var a=o.dim+"Axis",s=u.queryComponents({mainType:a,index:t.get(name+"Index"),id:t.get(name+"Id")})[0].componentIndex;d[a]=d[a]||[];for(var l=0;l<=s;l++)d[a][s]=d[a][s]||{};d[a][s].boundaryGap="bar"===h}}}),"stack"===h)n=d.series&&d.series[0]&&d.series[0].stack===$x?m({stack:qx.title.tiled},qx.title):b(qx.title);t.dispatchAction({type:"changeMagicType",currentType:h,newOption:d,newTitle:n,featureName:"magicType"})}},of({type:"changeMagicType",event:"magicTypeChanged",update:"prepareAndUpdate"},function(t,e){e.mergeOption(t.newOption)}),Zx("magicType",Kx);var ew=xc.toolbox.dataView,nw=new Array(60).join("-"),iw="\t";function rw(t){var e=function(t){var r={},o=[],a=[];return t.eachRawSeries(function(t){var e=t.coordinateSystem;if(!e||"cartesian2d"!==e.type&&"polar"!==e.type)o.push(t);else{var n=e.getBaseAxis();if("category"===n.type){var i=n.dim+"_"+n.index;r[i]||(r[i]={categoryAxis:n,valueAxis:e.getOtherAxis(n),series:[]},a.push({axisDim:n.dim,axisIndex:n.index})),r[i].series.push(t)}else o.push(t)}}),{seriesGroupByCategoryAxis:r,other:o,meta:a}}(t);return{value:I([function(t){var h=[];return D(t,function(t,e){var n=t.categoryAxis,i=t.valueAxis.dim,r=[" "].concat(P(t.series,function(t){return t.name})),o=[n.model.getCategories()];D(t.series,function(t){o.push(t.getRawData().mapArray(i,function(t){return t}))});for(var a=[r.join(iw)],s=0;s<o[0].length;s++){for(var l=[],u=0;u<o.length;u++)l.push(o[u][s]);a.push(l.join(iw))}h.push(a.join("\n"))}),h.join("\n\n"+nw+"\n\n")}(e.seriesGroupByCategoryAxis),function(t){return P(t,function(t){var r=t.getRawData(),o=[t.name],a=[];return r.each(r.dimensions,function(){for(var t=arguments.length,e=arguments[t-1],n=r.getName(e),i=0;i<t-1;i++)a[i]=arguments[i];o.push((n?n+iw:"")+a.join(iw))}),o.join("\n")}).join("\n\n"+nw+"\n\n")}(e.other)],function(t){return t.replace(/[\n\t\s]/g,"")}).join("\n\n"+nw+"\n\n"),meta:e.meta}}function ow(t){return t.replace(/^\s\s*/,"").replace(/\s\s*$/,"")}var aw=new RegExp("["+iw+"]+","g");function sw(t,o){var e=t.split(new RegExp("\n*"+nw+"\n*","g")),a={series:[]};return D(e,function(t,e){if(function(t){if(0<=t.slice(0,t.indexOf("\n")).indexOf(iw))return!0}(t)){var n=function(t){for(var e=t.split(/\n+/g),n=[],i=P(ow(e.shift()).split(aw),function(t){return{name:t,data:[]}}),r=0;r<e.length;r++){var o=ow(e[r]).split(aw);n.push(o.shift());for(var a=0;a<o.length;a++)i[a]&&(i[a].data[r]=o[a])}return{series:i,categories:n}}(t),i=o[e],r=i.axisDim+"Axis";i&&(a[r]=a[r]||[],a[r][i.axisIndex]={data:n.categories},a.series=a.series.concat(n.series))}else{n=function(t){for(var e=t.split(/\n+/g),n=ow(e.shift()),i=[],r=0;r<e.length;r++){var o,a=ow(e[r]).split(aw),s="",l=!1;o=isNaN(a[0])?(l=!0,s=a[0],a=a.slice(1),i[r]={name:s,value:[]},i[r].value):i[r]=[];for(var u=0;u<a.length;u++)o.push(+a[u]);1===o.length&&(l?i[r].value=o[0]:i[r]=o[0])}return{name:n,data:i}}(t);a.series.push(n)}}),a}function lw(t){this._dom=null,this.model=t}lw.defaultOption={show:!0,readOnly:!1,optionToContent:null,contentToOption:null,icon:"M17.5,17.3H33 M17.5,17.3H33 M45.4,29.5h-28 M11.5,2v56H51V14.8L38.4,2H11.5z M38.4,2.2v12.7H51 M45.4,41.7h-28",title:b(ew.title),lang:b(ew.lang),backgroundColor:"#fff",textColor:"#000",textareaColor:"#fff",textareaBorderColor:"#333",buttonColor:"#c23531",buttonTextColor:"#fff"},lw.prototype.onclick=function(t,e){var n=e.getDom(),i=this.model;this._dom&&n.removeChild(this._dom);var r=document.createElement("div");r.style.cssText="position:absolute;left:5px;top:5px;bottom:5px;right:5px;",r.style.backgroundColor=i.get("backgroundColor")||"#fff";var o=document.createElement("h4"),a=i.get("lang")||[];o.innerHTML=a[0]||i.get("title"),o.style.cssText="margin: 10px 20px;",o.style.color=i.get("textColor");var s=document.createElement("div"),l=document.createElement("textarea");s.style.cssText="display:block;width:100%;overflow:auto;";var u=i.get("optionToContent"),h=i.get("contentToOption"),c=rw(t);if("function"==typeof u){var d=u(e.getOption());"string"==typeof d?s.innerHTML=d:V(d)&&s.appendChild(d)}else s.appendChild(l),l.readOnly=i.get("readOnly"),l.style.cssText="width:100%;height:100%;font-family:monospace;font-size:14px;line-height:1.6rem;",l.style.color=i.get("textColor"),l.style.borderColor=i.get("textareaBorderColor"),l.style.backgroundColor=i.get("textareaColor"),l.value=c.value;var f=c.meta,p=document.createElement("div");p.style.cssText="position:absolute;bottom:0;left:0;right:0;";var g="float:right;margin-right:20px;border:none;cursor:pointer;padding:2px 5px;font-size:12px;border-radius:3px",m=document.createElement("div"),v=document.createElement("div");g+=";background-color:"+i.get("buttonColor"),g+=";color:"+i.get("buttonTextColor");var y=this;function _(){n.removeChild(r),y._dom=null}Ht(m,"click",_),Ht(v,"click",function(){var t;try{t="function"==typeof h?h(s,e.getOption()):sw(l.value,f)}catch(t){throw _(),new Error("Data view format error "+t)}t&&e.dispatchAction({type:"changeDataView",newOption:t}),_()}),m.innerHTML=a[1],v.innerHTML=a[2],v.style.cssText=g,m.style.cssText=g,i.get("readOnly")||p.appendChild(v),p.appendChild(m),r.appendChild(o),r.appendChild(s),r.appendChild(p),s.style.height=n.clientHeight-80+"px",n.appendChild(r),this._dom=r},lw.prototype.remove=function(t,e){this._dom&&e.getDom().removeChild(this._dom)},lw.prototype.dispose=function(t,e){this.remove(t,e)},Zx("dataView",lw),of({type:"changeDataView",event:"dataViewChanged",update:"prepareAndUpdate"},function(t,i){var r=[];D(t.newOption.series,function(t){var e=i.getSeriesByName(t.name)[0];if(e){var n=e.get("data");r.push({name:t.name,data:function(t,i){return P(t,function(t,e){var n=i&&i[e];return N(n)&&!O(n)?(N(t)&&!O(t)&&(t=t.value),A({value:t},n)):t})}(t.data,n)})}else r.push(k({type:"scatter"},t))}),i.mergeOption(A({series:r},t.newOption))});var uw=T,hw=D,cw=P,dw=Math.min,fw=Math.max,pw=Math.pow,gw=1e4,mw=6,vw=6,yw="globalPan",_w={w:[0,0],e:[0,1],n:[1,0],s:[1,1]},xw={w:"ew",e:"ew",n:"ns",s:"ns",ne:"nesw",sw:"nesw",nw:"nwse",se:"nwse"},ww={brushStyle:{lineWidth:2,stroke:"rgba(0,0,0,0.3)",fill:"rgba(0,0,0,0.1)"},transformable:!0,brushMode:"single",removeOnClick:!1},bw=0;function Sw(t){It.call(this),this._zr=t,this.group=new Mn,this._brushType,this._brushOption,this._panels,this._track=[],this._dragging,this._covers=[],this._creatingCover,this._creatingPanel,this._enableGlobalPan,this._uid="brushController_"+bw++,this._handlers={},hw(qw,function(t,e){this._handlers[e]=C(t,this)},this)}function Mw(t,e){var n=Kw[e.brushType].createCover(t,e);return n.__brushOption=e,Tw(n,e),t.group.add(n),n}function Iw(t,e){var n=Dw(e);return n.endCreating&&(n.endCreating(t,e),Tw(e,e.__brushOption)),e}function Cw(t,e){var n=e.__brushOption;Dw(e).updateCoverShape(t,e,n.range,n)}function Tw(t,e){var n=e.z;null==n&&(n=gw),t.traverse(function(t){t.z=n,t.z2=n})}function Aw(t,e){Dw(e).updateCommon(t,e),Cw(t,e)}function Dw(t){return Kw[t.__brushOption.brushType]}function kw(t,e,n){var i,r=t._panels;if(!r)return!0;var o=t._transform;return hw(r,function(t){t.isTargetByCursor(e,n,o)&&(i=t)}),i}function Pw(t,e){var n=t._panels;if(!n)return!0;var i=e.__brushOption.panelId;return null==i||n[i]}function Lw(e){var t=e._covers,n=t.length;return hw(t,function(t){e.group.remove(t)},e),t.length=0,!!n}function Ow(t,e){var n=cw(t._covers,function(t){var e=t.__brushOption,n=b(e.range);return{brushType:e.brushType,panelId:e.panelId,range:n}});t.trigger("brush",n,{isEnd:!!e.isEnd,removeOnClick:!!e.removeOnClick})}function Ew(t){var e=t.length-1;return e<0&&(e=0),[t[0],t[e]]}function zw(e,n,t,i){var r=new Mn;return r.add(new Ja({name:"main",style:Vw(t),silent:!0,draggable:!0,cursor:"move",drift:uw(e,n,r,"nswe"),ondragend:uw(Ow,n,{isEnd:!0})})),hw(i,function(t){r.add(new Ja({name:t,style:{opacity:0},draggable:!0,silent:!0,invisible:!0,drift:uw(e,n,r,t),ondragend:uw(Ow,n,{isEnd:!0})}))}),r}function Nw(t,e,n,i){var r=i.brushStyle.lineWidth||0,o=fw(r,vw),a=n[0][0],s=n[1][0],l=a-r/2,u=s-r/2,h=n[0][1],c=n[1][1],d=h-o+r/2,f=c-o+r/2,p=h-a,g=c-s,m=p+r,v=g+r;Bw(t,e,"main",a,s,p,g),i.transformable&&(Bw(t,e,"w",l,u,o,v),Bw(t,e,"e",d,u,o,v),Bw(t,e,"n",l,u,m,o),Bw(t,e,"s",l,f,m,o),Bw(t,e,"nw",l,u,o,o),Bw(t,e,"ne",d,u,o,o),Bw(t,e,"sw",l,f,o,o),Bw(t,e,"se",d,f,o,o))}function Rw(i,r){var t=r.__brushOption,o=t.transformable,e=r.childAt(0);e.useStyle(Vw(t)),e.attr({silent:!o,cursor:o?"move":"default"}),hw(["w","e","n","s","se","sw","ne","nw"],function(t){var e=r.childOfName(t),n=function t(e,n){{if(1<n.length){n=n.split("");var i=[t(e,n[0]),t(e,n[1])];return"e"!==i[0]&&"w"!==i[0]||i.reverse(),i.join("")}var r={w:"left",e:"right",n:"top",s:"bottom"},o={left:"w",right:"e",top:"n",bottom:"s"},i=al(r[n],rl(e.group));return o[i]}}(i,t);e&&e.attr({silent:!o,invisible:!o,cursor:o?xw[n]+"-resize":null})})}function Bw(t,e,n,i,r,o,a){var s=e.childOfName(n);s&&s.setShape(function(t){var e=dw(t[0][0],t[1][0]),n=dw(t[0][1],t[1][1]),i=fw(t[0][0],t[1][0]),r=fw(t[0][1],t[1][1]);return{x:e,y:n,width:i-e,height:r-n}}(Zw(t,e,[[i,r],[i+o,r+a]])))}function Vw(t){return A({strokeNoScale:!0},t.brushStyle)}function Fw(t,e,n,i){var r=[dw(t,n),dw(e,i)],o=[fw(t,n),fw(e,i)];return[[r[0],o[0]],[r[1],o[1]]]}function Hw(t,e,n,i,r,o,a,s){var l=i.__brushOption,u=t(l.range),h=Gw(n,o,a);hw(r.split(""),function(t){var e=_w[t];u[e[0]][e[1]]+=h[e[0]]}),l.range=e(Fw(u[0][0],u[1][0],u[0][1],u[1][1])),Aw(n,i),Ow(n,{isEnd:!1})}function Ww(t,e,n,i,r){var o=e.__brushOption.range,a=Gw(t,n,i);hw(o,function(t){t[0]+=a[0],t[1]+=a[1]}),Aw(t,e),Ow(t,{isEnd:!1})}function Gw(t,e,n){var i=t.group,r=i.transformCoordToLocal(e,n),o=i.transformCoordToLocal(0,0);return[r[0]-o[0],r[1]-o[1]]}function Zw(t,e,n){var i=Pw(t,e);return i&&!0!==i?i.clipPath(n,t._transform):b(n)}function Uw(t){var e=t.event;e.preventDefault&&e.preventDefault()}function Xw(t,e,n){return t.childOfName("main").contain(e,n)}function Yw(t,e,n,i){var r,o=t._creatingCover,a=t._creatingPanel,s=t._brushOption;if(t._track.push(n.slice()),function(t){var e=t._track;if(!e.length)return!1;var n=e[e.length-1],i=e[0],r=n[0]-i[0],o=n[1]-i[1],a=pw(r*r+o*o,.5);return mw<a}(t)||o){if(a&&!o){"single"===s.brushMode&&Lw(t);var l=b(s);l.brushType=jw(l.brushType,a),l.panelId=!0===a?null:a.panelId,o=t._creatingCover=Mw(t,l),t._covers.push(o)}if(o){var u=Kw[jw(t._brushType,a)];o.__brushOption.range=u.getCreatingRange(Zw(t,o,t._track)),i&&(Iw(t,o),u.updateCommon(t,o)),Cw(t,o),r={isEnd:i}}}else i&&"single"===s.brushMode&&s.removeOnClick&&kw(t,e,n)&&Lw(t)&&(r={isEnd:i,removeOnClick:!0});return r}function jw(t,e){return"auto"===t?e.defaultBrushType:t}Sw.prototype={constructor:Sw,enableBrush:function(t){return this._brushType&&function(t){var e=t._zr;(function(t,e,n){var i=bx(t);i[e]===n&&(i[e]=null)})(e,yw,t._uid),function(n,t){hw(t,function(t,e){n.off(e,t)})}(e,t._handlers),t._brushType=t._brushOption=null}(this),t.brushType&&function(t,e){var n=t._zr;t._enableGlobalPan||function(t,e,n){bx(t)[e]=n}(n,yw,t._uid);(function(n,t){hw(t,function(t,e){n.on(e,t)})})(n,t._handlers),t._brushType=e.brushType,t._brushOption=m(b(ww),e,!0)}(this,t),this},setPanels:function(t){if(t&&t.length){var e=this._panels={};D(t,function(t){e[t.panelId]=b(t)})}else this._panels=null;return this},mount:function(t){t=t||{},this._enableGlobalPan=t.enableGlobalPan;var e=this.group;return this._zr.add(e),e.attr({position:t.position||[0,0],rotation:t.rotation||0,scale:t.scale||[1,1]}),this._transform=e.getLocalTransform(),this},eachCover:function(t,e){hw(this._covers,t,e)},updateCovers:function(r){r=P(r,function(t){return m(b(ww),t,!0)});var n="\0-brush-index-",o=this._covers,a=this._covers=[],s=this,l=this._creatingCover;return new gf(o,r,function(t,e){return i(t.__brushOption,e)},i).add(t).update(t).remove(function(t){o[t]!==l&&s.group.remove(o[t])}).execute(),this;function i(t,e){return(null!=t.id?t.id:n+e)+"-"+t.brushType}function t(t,e){var n=r[t];if(null!=e&&o[e]===l)a[t]=o[e];else{var i=a[t]=null!=e?(o[e].__brushOption=n,o[e]):Iw(s,Mw(s,n));Aw(s,i)}}},unmount:function(){return this.enableBrush(!1),Lw(this),this._zr.remove(this.group),this},dispose:function(){this.unmount(),this.off()}},S(Sw,It);var qw={mousedown:function(t){if(this._dragging)$w(this,t);else if(!t.target||!t.target.draggable){Uw(t);var e=this.group.transformCoordToLocal(t.offsetX,t.offsetY);this._creatingCover=null,(this._creatingPanel=kw(this,t,e))&&(this._dragging=!0,this._track=[e.slice()])}},mousemove:function(t){var e=t.offsetX,n=t.offsetY,i=this.group.transformCoordToLocal(e,n);if(function(t,e,n){if(t._brushType&&!function(t,e,n){var i=t._zr;return e<0||e>i.getWidth()||n<0||n>i.getHeight()}(t,e)){var i=t._zr,r=t._covers,o=kw(t,e,n);if(!t._dragging)for(var a=0;a<r.length;a++){var s=r[a].__brushOption;if(o&&(!0===o||s.panelId===o.panelId)&&Kw[s.brushType].contain(r[a],n[0],n[1]))return}o&&i.setCursorStyle("crosshair")}}(this,t,i),this._dragging){Uw(t);var r=Yw(this,t,i,!1);r&&Ow(this,r)}},mouseup:function(t){$w(this,t)}};function $w(t,e){if(t._dragging){Uw(e);var n=e.offsetX,i=e.offsetY,r=t.group.transformCoordToLocal(n,i),o=Yw(t,e,r,!0);t._dragging=!1,t._track=[],t._creatingCover=null,o&&Ow(t,o)}}var Kw={lineX:Qw(0),lineY:Qw(1),rect:{createCover:function(t,e){return zw(uw(Hw,function(t){return t},function(t){return t}),t,e,["w","e","n","s","se","sw","ne","nw"])},getCreatingRange:function(t){var e=Ew(t);return Fw(e[1][0],e[1][1],e[0][0],e[0][1])},updateCoverShape:function(t,e,n,i){Nw(t,e,n,i)},updateCommon:Rw,contain:Xw},polygon:{createCover:function(t,e){var n=new Mn;return n.add(new Ya({name:"main",style:Vw(e),silent:!0})),n},getCreatingRange:function(t){return t},endCreating:function(t,e){e.remove(e.childAt(0)),e.add(new Xa({name:"main",draggable:!0,drift:uw(Ww,t,e),ondragend:uw(Ow,t,{isEnd:!0})}))},updateCoverShape:function(t,e,n,i){e.childAt(0).setShape({points:Zw(t,e,n)})},updateCommon:Rw,contain:Xw}};function Qw(l){return{createCover:function(t,e){return zw(uw(Hw,function(t){var e=[t,[0,100]];return l&&e.reverse(),e},function(t){return t[l]}),t,e,[["w","e"],["n","s"]][l])},getCreatingRange:function(t){var e=Ew(t);return[dw(e[0][l],e[1][l]),fw(e[0][l],e[1][l])]},updateCoverShape:function(t,e,n,i){var r,o=Pw(t,e);if(!0!==o&&o.getLinearBrushOtherExtent)r=o.getLinearBrushOtherExtent(l,t._transform);else{var a=t._zr;r=[0,[a.getWidth(),a.getHeight()][1-l]]}var s=[n,r];l&&s.reverse(),Nw(t,e,s,i)},updateCommon:Rw,contain:Xw}}var Jw={axisPointer:1,tooltip:1,brush:1};function tb(i,r,o){return i=eb(i),function(t,e,n){return i.contain(e[0],e[1])&&!function(t,e,n){var i=e.getComponentByElement(t.topTarget),r=i&&i.coordinateSystem;return i&&i!==n&&!Jw[i.mainType]&&r&&r.model!==n}(t,r,o)}}function eb(t){return Sn.create(t)}var nb=D,ib=x,rb=T,ob=["dataToPoint","pointToData"],ab=["grid","xAxis","yAxis","geo","graph","polar","radiusAxis","angleAxis","bmap"];function sb(t,e,n){var i=this._targetInfoList=[],r={},o=hb(e,t);nb(cb,function(t,e){n&&n.include&&!(0<=ib(n.include,e))||t(o,i,r)})}var lb=sb.prototype;function ub(t){return t[0]>t[1]&&t.reverse(),t}function hb(t,e){return Gr(t,e,{includeMainTypes:ab})}lb.setOutputRanges=function(t,e){this.matchOutputRanges(t,e,function(t,e,n){if((t.coordRanges||(t.coordRanges=[])).push(e),!t.coordRange){t.coordRange=e;var i=pb[t.brushType](0,n,e);t.__rangeOffset={offset:mb[t.brushType](i.values,t.range,[1,1]),xyMinMax:i.xyMinMax}}})},lb.matchOutputRanges=function(t,i,r){nb(t,function(n){var t=this.findTargetInfo(n,i);t&&!0!==t&&D(t.coordSyses,function(t){var e=pb[n.brushType](1,t,n.range);r(n,e.values,t,i)})},this)},lb.setInputRanges=function(t,r){nb(t,function(t){var e=this.findTargetInfo(t,r);if(t.range=t.range||[],e&&!0!==e){t.panelId=e.panelId;var n=pb[t.brushType](0,e.coordSys,t.coordRange),i=t.__rangeOffset;t.range=i?mb[t.brushType](n.values,i.offset,function(t,e){var n=yb(t),i=yb(e),r=[n[0]/i[0],n[1]/i[1]];return isNaN(r[0])&&(r[0]=1),isNaN(r[1])&&(r[1]=1),r}(n.xyMinMax,i.xyMinMax)):n.values}},this)},lb.makePanelOpts=function(n,i){return P(this._targetInfoList,function(t){var e=t.getPanelRect();return{panelId:t.panelId,defaultBrushType:i&&i(t),clipPath:function(n){return n=eb(n),function(t,e){return ll(t,n)}}(e),isTargetByCursor:tb(e,n,t.coordSysModel),getLinearBrushOtherExtent:function(r,o){return r=eb(r),function(t){var e=null!=o?o:t,n=e?r.width:r.height,i=e?r.x:r.y;return[i,i+(n||0)]}}(e)}})},lb.controlSeries=function(t,e,n){var i=this.findTargetInfo(t,n);return!0===i||i&&0<=ib(i.coordSyses,e.coordinateSystem)},lb.findTargetInfo=function(t,e){for(var n=this._targetInfoList,i=hb(e,t),r=0;r<n.length;r++){var o=n[r],a=t.panelId;if(a){if(o.panelId===a)return o}else for(r=0;r<db.length;r++)if(db[r](i,o))return o}return!0};var cb={grid:function(t,i){var r=t.xAxisModels,o=t.yAxisModels,e=t.gridModels,n=Q(),a={},s={};(r||o||e)&&(nb(r,function(t){var e=t.axis.grid.model;n.set(e.id,e),a[e.id]=!0}),nb(o,function(t){var e=t.axis.grid.model;n.set(e.id,e),s[e.id]=!0}),nb(e,function(t){n.set(t.id,t),a[t.id]=!0,s[t.id]=!0}),n.each(function(t){var e=t.coordinateSystem,n=[];nb(e.getCartesians(),function(t,e){(0<=ib(r,t.getAxis("x").model)||0<=ib(o,t.getAxis("y").model))&&n.push(t)}),i.push({panelId:"grid--"+t.id,gridModel:t,coordSysModel:t,coordSys:n[0],coordSyses:n,getPanelRect:fb.grid,xAxisDeclared:a[t.id],yAxisDeclared:s[t.id]})}))},geo:function(t,n){nb(t.geoModels,function(t){var e=t.coordinateSystem;n.push({panelId:"geo--"+t.id,geoModel:t,coordSysModel:t,coordSys:e,coordSyses:[e],getPanelRect:fb.geo})})}},db=[function(t,e){var n=t.xAxisModel,i=t.yAxisModel,r=t.gridModel;return!r&&n&&(r=n.axis.grid.model),!r&&i&&(r=i.axis.grid.model),r&&r===e.gridModel},function(t,e){var n=t.geoModel;return n&&n===e.geoModel}],fb={grid:function(){return this.coordSys.grid.getRect().clone()},geo:function(){var t=this.coordSys,e=t.getBoundingRect().clone();return e.applyTransform(rl(t)),e}},pb={lineX:rb(gb,0),lineY:rb(gb,1),rect:function(t,e,n){var i=e[ob[t]]([n[0][0],n[1][0]]),r=e[ob[t]]([n[0][1],n[1][1]]),o=[ub([i[0],r[0]]),ub([i[1],r[1]])];return{values:o,xyMinMax:o}},polygon:function(n,i,t){var r=[[1/0,-1/0],[1/0,-1/0]];return{values:P(t,function(t){var e=i[ob[n]](t);return r[0][0]=Math.min(r[0][0],e[0]),r[1][0]=Math.min(r[1][0],e[1]),r[0][1]=Math.max(r[0][1],e[0]),r[1][1]=Math.max(r[1][1],e[1]),e}),xyMinMax:r}}};function gb(t,e,n,i){var r=n.getAxis(["x","y"][t]),o=ub(P([0,1],function(t){return e?r.coordToData(r.toLocalCoord(i[t])):r.toGlobalCoord(r.dataToCoord(i[t]))})),a=[];return a[t]=o,a[1-t]=[NaN,NaN],{values:o,xyMinMax:a}}var mb={lineX:rb(vb,0),lineY:rb(vb,1),rect:function(t,e,n){return[[t[0][0]-n[0]*e[0][0],t[0][1]-n[0]*e[0][1]],[t[1][0]-n[1]*e[1][0],t[1][1]-n[1]*e[1][1]]]},polygon:function(t,n,i){return P(t,function(t,e){return[t[0]-i[0]*n[e][0],t[1]-i[1]*n[e][1]]})}};function vb(t,e,n,i){return[e[0]-i[t]*n[0],e[1]-i[t]*n[1]]}function yb(t){return t?[t[0][1]-t[0][0],t[1][1]-t[1][0]]:[NaN,NaN]}var _b=D,xb="\0_ec_hist_store";function wb(t){var e=t[xb];return e=e||(t[xb]=[{}])}ox.extend({type:"dataZoom.select"}),lx.extend({type:"dataZoom.select"});var bb=xc.toolbox.dataZoom,Sb=D;function Mb(t,e,n){(this._brushController=new Sw(n.getZr())).on("brush",C(this._onBrush,this)).mount(),this._isZoomActive}Mb.defaultOption={show:!0,filterMode:"filter",icon:{zoom:"M0,13.5h26.9 M13.5,26.9V0 M32.1,13.5H58V58H13.5 V32.1",back:"M22,1.4L9.9,13.5l12.3,12.3 M10.3,13.5H54.9v44.6 H10.3v-26"},title:b(bb.title)};var Ib=Mb.prototype;Ib.render=function(t,e,n,i){this.model=t,this.ecModel=e,this.api=n,function(t,e,n,i,r){var o=n._isZoomActive;i&&"takeGlobalCursor"===i.type&&(o="dataZoomSelect"===i.key&&i.dataZoomSelectActive);n._isZoomActive=o,t.setIconStatus("zoom",o?"emphasis":"normal");var a=new sb(Tb(t.option),e,{include:["grid"]});n._brushController.setPanels(a.makePanelOpts(r,function(t){return t.xAxisDeclared&&!t.yAxisDeclared?"lineX":!t.xAxisDeclared&&t.yAxisDeclared?"lineY":"rect"})).enableBrush(!!o&&{brushType:"auto",brushStyle:{lineWidth:0,fill:"rgba(0,0,0,0.2)"}})}(t,e,this,i,n),function(t,e){t.setIconStatus("back",1<function(t){return wb(t).length}(e)?"emphasis":"normal")}(t,e)},Ib.onclick=function(t,e,n){Cb[n].call(this)},Ib.remove=function(t,e){this._brushController.unmount()},Ib.dispose=function(t,e){this._brushController.dispose()};var Cb={zoom:function(){var t=!this._isZoomActive;this.api.dispatchAction({type:"takeGlobalCursor",key:"dataZoomSelect",dataZoomSelectActive:t})},back:function(){this._dispatchZoomAction(function(t){var i=wb(t),e=i[i.length-1];1<i.length&&i.pop();var r={};return _b(e,function(t,e){for(var n=i.length-1;0<=n;n--){if(t=i[n][e]){r[e]=t;break}}}),r}(this.ecModel))}};function Tb(e){var n={};return D(["xAxisIndex","yAxisIndex"],function(t){n[t]=e[t],null==n[t]&&(n[t]="all"),!1!==n[t]&&"none"!==n[t]||(n[t]=[])}),n}Ib._onBrush=function(t,e){if(e.isEnd&&t.length){var s={},l=this.ecModel;this._brushController.updateCovers([]),new sb(Tb(this.model.option),l,{include:["grid"]}).matchOutputRanges(t,l,function(t,e,n){if("cartesian2d"===n.type){var i=t.brushType;"rect"===i?(r("x",n,e[0]),r("y",n,e[1])):r({lineX:"x",lineY:"y"}[i],n,e)}}),function(o,t){var a=wb(o);_b(t,function(t,e){for(var n=a.length-1;0<=n;n--){if(a[n][e])break}if(n<0){var i=o.queryComponents({mainType:"dataZoom",subType:"select",id:e})[0];if(i){var r=i.getPercentRange();a[0][e]={dataZoomId:e,start:r[0],end:r[1]}}}}),a.push(t)}(l,s),this._dispatchZoomAction(s)}function r(t,e,n){var i=e.getAxis(t),r=i.model,o=function(e,n,t){var i;return t.eachComponent({mainType:"dataZoom",subType:"select"},function(t){t.getAxisModel(e,n.componentIndex)&&(i=t)}),i}(t,r,l),a=o.findRepresentativeAxisProxy(r).getMinMaxSpan();null==a.minValueSpan&&null==a.maxValueSpan||(n=$_(0,n.slice(),i.scale.getExtent(),0,a.minValueSpan,a.maxValueSpan)),o&&(s[o.id]={dataZoomId:o.id,startValue:n[0],endValue:n[1]})}},Ib._dispatchZoomAction=function(t){var n=[];Sb(t,function(t,e){n.push(b(t))}),n.length&&this.api.dispatchAction({type:"dataZoom",from:this.uid,batch:n})},Zx("dataZoom",Mb),nf(function(s){if(s){var l=s.dataZoom||(s.dataZoom=[]);O(l)||(s.dataZoom=l=[l]);var t=s.toolbox;if(t&&(O(t)&&(t=t[0]),t&&t.feature)){var e=t.feature.dataZoom;n("xAxis",e),n("yAxis",e)}}function n(i,r){if(r){var o=i+"Index",a=r[o];null==a||"all"===a||O(a)||(a=!1===a||"none"===a?[]:[a]),function(t,e){var n=s[t];O(n)||(n=n?[n]:[]);Sb(n,e)}(i,function(t,e){if(null==a||"all"===a||-1!==x(a,e)){var n={type:"select",$fromToolbox:!0,filterMode:r.filterMode||"filter",id:"\0_ec_\0toolbox-dataZoom_"+i+e};n[o]=e,l.push(n)}})}}});var Ab=xc.toolbox.restore;function Db(t){this.model=t}Db.defaultOption={show:!0,icon:"M3.8,33.4 M47,18.9h9.8V8.7 M56.3,20.1 C52.1,9,40.5,0.6,26.8,2.1C12.6,3.7,1.6,16.2,2.1,30.6 M13,41.1H3.1v10.2 M3.7,39.9c4.2,11.1,15.8,19.5,29.5,18 c14.2-1.6,25.2-14.1,24.7-28.5",title:Ab.title},Db.prototype.onclick=function(t,e,n){!function(t){t[xb]=null}(t),e.dispatchAction({type:"restore",from:this.uid})},Zx("restore",Db),of({type:"restore",event:"restore",update:"prepareAndUpdate"},function(t,e){e.resetOption("recreate")});var kb,Pb="urn:schemas-microsoft-com:vml",Lb="undefined"==typeof window?null:window,Ob=!1,Eb=Lb&&Lb.document;function zb(t){return kb(t)}if(Eb&&!v.canvasSupported)try{Eb.namespaces.zrvml||Eb.namespaces.add("zrvml",Pb),kb=function(t){return Eb.createElement("<zrvml:"+t+' class="zrvml">')}}catch(t){kb=function(t){return Eb.createElement("<"+t+' xmlns="'+Pb+'" class="zrvml">')}}var Nb,Rb=Jo.CMD,Bb=Math.round,Vb=Math.sqrt,Fb=Math.abs,Hb=Math.cos,Wb=Math.sin,Gb=Math.max;if(!v.canvasSupported){var Zb=",",Ub="progid:DXImageTransform.Microsoft",Xb=21600,Yb=Xb/2,jb=function(t){t.style.cssText="position:absolute;left:0;top:0;width:1px;height:1px;",t.coordsize=Xb+","+Xb,t.coordorigin="0,0"},qb=function(t,e,n){return"rgb("+[t,e,n].join(",")+")"},$b=function(t,e){e&&t&&e.parentNode!==t&&t.appendChild(e)},Kb=function(t,e){e&&t&&e.parentNode===t&&t.removeChild(e)},Qb=function(t,e,n){return 1e5*(parseFloat(t)||0)+1e3*(parseFloat(e)||0)+n},Jb=Hi,tS=function(t,e,n){var i=Ne(e);n=+n,isNaN(n)&&(n=1),i&&(t.color=qb(i[0],i[1],i[2]),t.opacity=n*i[3])},eS=function(t,e,n,i){var r="fill"===e,o=t.getElementsByTagName(e)[0];null!=n[e]&&"none"!==n[e]&&(r||!r&&n.lineWidth)?(t[r?"filled":"stroked"]="true",n[e]instanceof rs&&Kb(t,o),o=o||zb(e),r?function(t,e,n){var i,r,o=e.fill;if(null!=o)if(o instanceof rs){var a,s=0,l=[0,0],u=0,h=1,c=n.getBoundingRect(),d=c.width,f=c.height;if("linear"===o.type){a="gradient";var p=n.transform,g=[o.x*d,o.y*f],m=[o.x2*d,o.y2*f];p&&(yt(g,g,p),yt(m,m,p));var v=m[0]-g[0],y=m[1]-g[1];(s=180*Math.atan2(v,y)/Math.PI)<0&&(s+=360),s<1e-6&&(s=0)}else{a="gradientradial";g=[o.x*d,o.y*f],p=n.transform;var _=n.scale,x=d,w=f;l=[(g[0]-c.x)/x,(g[1]-c.y)/w],p&&yt(g,g,p),x/=_[0]*Xb,w/=_[1]*Xb;var b=Gb(x,w);u=0/b,h=2*o.r/b-u}var S=o.colorStops.slice();S.sort(function(t,e){return t.offset-e.offset});for(var M=S.length,I=[],C=[],T=0;T<M;T++){var A=S[T],D=(i=A.color,void 0,r=Ne(i),[qb(r[0],r[1],r[2]),r[3]]);C.push(A.offset*h+u+" "+D[0]),0!==T&&T!==M-1||I.push(D)}if(2<=M){var k=I[0][0],P=I[1][0],L=I[0][1]*e.opacity,O=I[1][1]*e.opacity;t.type=a,t.method="none",t.focus="100%",t.angle=s,t.color=k,t.color2=P,t.colors=C.join(","),t.opacity=O,t.opacity2=L}"radial"===a&&(t.focusposition=l.join(","))}else tS(t,o,e.opacity)}(o,n,i):function(t,e){e.lineDash&&(t.dashstyle=e.lineDash.join(" ")),null==e.stroke||e.stroke instanceof rs||tS(t,e.stroke,e.opacity)}(o,n),$b(t,o)):(t[r?"filled":"stroked"]="false",Kb(t,o))},nS=[[],[],[]];xa.prototype.brushVML=function(t){var e=this.style,n=this._vmlEl;n||(n=zb("shape"),jb(n),this._vmlEl=n),eS(n,"fill",e,this),eS(n,"stroke",e,this);var i=this.transform,r=null!=i,o=n.getElementsByTagName("stroke")[0];if(o){var a=e.lineWidth;if(r&&!e.strokeNoScale){var s=i[0]*i[3]-i[1]*i[2];a*=Vb(Fb(s))}o.weight=a+"px"}var l=this.path||(this.path=new Jo);this.__dirtyPath&&(l.beginPath(),l.subPixelOptimize=!1,this.buildPath(l,this.shape),l.toStatic(),this.__dirtyPath=!1),n.path=function(t,e){var n,i,r,o,a,s,l=Rb.M,u=Rb.C,h=Rb.L,c=Rb.A,d=Rb.Q,f=[],p=t.data,g=t.len();for(o=0;o<g;){switch(i="",n=0,r=p[o++]){case l:i=" m ",n=1,a=p[o++],s=p[o++],nS[0][0]=a,nS[0][1]=s;break;case h:i=" l ",n=1,a=p[o++],s=p[o++],nS[0][0]=a,nS[0][1]=s;break;case d:case u:i=" c ",n=3;var m,v,y=p[o++],_=p[o++],x=p[o++],w=p[o++];r===d?(x=((m=x)+2*y)/3,w=((v=w)+2*_)/3,y=(a+2*y)/3,_=(s+2*_)/3):(m=p[o++],v=p[o++]),nS[0][0]=y,nS[0][1]=_,nS[1][0]=x,nS[1][1]=w,a=nS[2][0]=m,s=nS[2][1]=v;break;case c:var b=0,S=0,M=1,I=1,C=0;e&&(b=e[4],S=e[5],M=Vb(e[0]*e[0]+e[1]*e[1]),I=Vb(e[2]*e[2]+e[3]*e[3]),C=Math.atan2(-e[1]/I,e[0]/M));var T=p[o++],A=p[o++],D=p[o++],k=p[o++],P=p[o++]+C,L=p[o++]+P+C;o++;var O=p[o++],E=T+Hb(P)*D,z=A+Wb(P)*k,N=(y=T+Hb(L)*D,_=A+Wb(L)*k,O?" wa ":" at ");Math.abs(E-y)<1e-4&&(.01<Math.abs(L-P)?O&&(E+=.0125):Math.abs(z-A)<1e-4?O&&E<T||!O&&T<E?_-=.0125:_+=.0125:O&&z<A||!O&&A<z?y+=.0125:y-=.0125),f.push(N,Bb(((T-D)*M+b)*Xb-Yb),Zb,Bb(((A-k)*I+S)*Xb-Yb),Zb,Bb(((T+D)*M+b)*Xb-Yb),Zb,Bb(((A+k)*I+S)*Xb-Yb),Zb,Bb((E*M+b)*Xb-Yb),Zb,Bb((z*I+S)*Xb-Yb),Zb,Bb((y*M+b)*Xb-Yb),Zb,Bb((_*I+S)*Xb-Yb)),a=y,s=_;break;case Rb.R:var R=nS[0],B=nS[1];R[0]=p[o++],R[1]=p[o++],B[0]=R[0]+p[o++],B[1]=R[1]+p[o++],e&&(yt(R,R,e),yt(B,B,e)),R[0]=Bb(R[0]*Xb-Yb),B[0]=Bb(B[0]*Xb-Yb),R[1]=Bb(R[1]*Xb-Yb),B[1]=Bb(B[1]*Xb-Yb),f.push(" m ",R[0],Zb,R[1]," l ",B[0],Zb,R[1]," l ",B[0],Zb,B[1]," l ",R[0],Zb,B[1]);break;case Rb.Z:f.push(" x ")}if(0<n){f.push(i);for(var V=0;V<n;V++){var F=nS[V];e&&yt(F,F,e),f.push(Bb(F[0]*Xb-Yb),Zb,Bb(F[1]*Xb-Yb),V<n-1?Zb:"")}}}return f.join("")}(l,this.transform),n.style.zIndex=Qb(this.zlevel,this.z,this.z2),$b(t,n),null!=e.text?this.drawRectText(t,this.getBoundingRect()):this.removeRectText(t)},xa.prototype.onRemove=function(t){Kb(t,this._vmlEl),this.removeRectText(t)},xa.prototype.onAdd=function(t){$b(t,this._vmlEl),this.appendRectText(t)};Yi.prototype.brushVML=function(t){var e,n,i=this.style,r=i.image;if(function(t){return"object"==typeof t&&t.tagName&&"IMG"===t.tagName.toUpperCase()}(r)){var o=r.src;if(o===this._imageSrc)e=this._imageWidth,n=this._imageHeight;else{var a=r.runtimeStyle,s=a.width,l=a.height;a.width="auto",a.height="auto",e=r.width,n=r.height,a.width=s,a.height=l,this._imageSrc=o,this._imageWidth=e,this._imageHeight=n}r=o}else r===this._imageSrc&&(e=this._imageWidth,n=this._imageHeight);if(r){var u=i.x||0,h=i.y||0,c=i.width,d=i.height,f=i.sWidth,p=i.sHeight,g=i.sx||0,m=i.sy||0,v=f&&p,y=this._vmlEl;y||(y=Eb.createElement("div"),jb(y),this._vmlEl=y);var _,x=y.style,w=!1,b=1,S=1;if(this.transform&&(_=this.transform,b=Vb(_[0]*_[0]+_[1]*_[1]),S=Vb(_[2]*_[2]+_[3]*_[3]),w=_[1]||_[2]),w){var M=[u,h],I=[u+c,h],C=[u,h+d],T=[u+c,h+d];yt(M,M,_),yt(I,I,_),yt(C,C,_),yt(T,T,_);var A=Gb(M[0],I[0],C[0],T[0]),D=Gb(M[1],I[1],C[1],T[1]),k=[];k.push("M11=",_[0]/b,Zb,"M12=",_[2]/S,Zb,"M21=",_[1]/b,Zb,"M22=",_[3]/S,Zb,"Dx=",Bb(u*b+_[4]),Zb,"Dy=",Bb(h*S+_[5])),x.padding="0 "+Bb(A)+"px "+Bb(D)+"px 0",x.filter=Ub+".Matrix("+k.join("")+", SizingMethod=clip)"}else _&&(u=u*b+_[4],h=h*S+_[5]),x.filter="",x.left=Bb(u)+"px",x.top=Bb(h)+"px";var P=this._imageEl,L=this._cropEl;P||(P=Eb.createElement("div"),this._imageEl=P);var O=P.style;if(v){if(e&&n)O.width=Bb(b*e*c/f)+"px",O.height=Bb(S*n*d/p)+"px";else{var E=new Image,z=this;E.onload=function(){E.onload=null,e=E.width,n=E.height,O.width=Bb(b*e*c/f)+"px",O.height=Bb(S*n*d/p)+"px",z._imageWidth=e,z._imageHeight=n,z._imageSrc=r},E.src=r}L||((L=Eb.createElement("div")).style.overflow="hidden",this._cropEl=L);var N=L.style;N.width=Bb((c+g*c/f)*b),N.height=Bb((d+m*d/p)*S),N.filter=Ub+".Matrix(Dx="+-g*c/f*b+",Dy="+-m*d/p*S+")",L.parentNode||y.appendChild(L),P.parentNode!==L&&L.appendChild(P)}else O.width=Bb(b*c)+"px",O.height=Bb(S*d)+"px",y.appendChild(P),L&&L.parentNode&&(y.removeChild(L),this._cropEl=null);var R="",B=i.opacity;B<1&&(R+=".Alpha(opacity="+Bb(100*B)+") "),R+=Ub+".AlphaImageLoader(src="+r+", SizingMethod=scale)",O.filter=R,y.style.zIndex=Qb(this.zlevel,this.z,this.z2),$b(t,y),null!=i.text&&this.drawRectText(t,this.getBoundingRect())}},Yi.prototype.onRemove=function(t){Kb(t,this._vmlEl),this._vmlEl=null,this._cropEl=null,this._imageEl=null,this.removeRectText(t)},Yi.prototype.onAdd=function(t){$b(t,this._vmlEl),this.appendRectText(t)};var iS,rS="normal",oS={},aS=0,sS=document.createElement("div");Nb=function(t,e){var n=Eb;iS||((iS=n.createElement("div")).style.cssText="position:absolute;top:-20000px;left:0;padding:0;margin:0;border:none;white-space:pre;",Eb.body.appendChild(iS));try{iS.style.font=e}catch(t){}return iS.innerHTML="",iS.appendChild(n.createTextNode(t)),{width:iS.offsetWidth}},si["measureText"]=Nb;for(var lS=new Sn,uS=function(t,e,n,i){var r=this.style;this.__dirty&&Di(r);var o=r.text;if(null!=o&&(o+=""),o){if(r.rich){var a=_i(o,r);o=[];for(var s=0;s<a.lines.length;s++){for(var l=a.lines[s].tokens,u=[],h=0;h<l.length;h++)u.push(l[h].text);o.push(u.join(""))}o=o.join("\n")}var c,d,f=r.textAlign,p=r.textVerticalAlign,g=function(t){var e=oS[t];if(!e){100<aS&&(aS=0,oS={});var n,i=sS.style;try{i.font=t,n=i.fontFamily.split(",")[0]}catch(t){}e={style:i.fontStyle||rS,variant:i.fontVariant||rS,weight:i.fontWeight||rS,size:0|parseFloat(i.fontSize||12),family:n||"Microsoft YaHei"},oS[t]=e,aS++}return e}(r.font),m=g.style+" "+g.variant+" "+g.weight+" "+g.size+'px "'+g.family+'"';n=n||ui(o,m,f,p,r.textPadding,r.textLineHeight);var v=this.transform;if(v&&!i&&(lS.copy(e),lS.applyTransform(v),e=lS),i)c=e.x,d=e.y;else{var y=r.textPosition;if(y instanceof Array)c=e.x+Jb(y[0],e.width),d=e.y+Jb(y[1],e.height),f=f||"left";else{var _=this.calculateTextPosition?this.calculateTextPosition({},r,e):di({},r,e);c=_.x,d=_.y,f=f||_.textAlign,p=p||_.textVerticalAlign}}c=hi(c,n.width,f),d=ci(d,n.height,p),d+=n.height/2;var x,w,b,S=zb,M=this._textVmlEl;M?w=(x=(b=M.firstChild).nextSibling).nextSibling:(M=S("line"),x=S("path"),w=S("textpath"),b=S("skew"),w.style["v-text-align"]="left",jb(M),x.textpathok=!0,w.on=!0,M.from="0 0",M.to="1000 0.05",$b(M,b),$b(M,x),$b(M,w),this._textVmlEl=M);var I=[c,d],C=M.style;v&&i?(yt(I,I,v),b.on=!0,b.matrix=v[0].toFixed(3)+Zb+v[2].toFixed(3)+Zb+v[1].toFixed(3)+Zb+v[3].toFixed(3)+",0,0",b.offset=(Bb(I[0])||0)+","+(Bb(I[1])||0),b.origin="0 0",C.left="0px",C.top="0px"):(b.on=!1,C.left=Bb(c)+"px",C.top=Bb(d)+"px"),w.string=function(t){return String(t).replace(/&/g,"&amp;").replace(/"/g,"&quot;")}(o);try{w.style.font=m}catch(t){}eS(M,"fill",{fill:r.textFill,opacity:r.opacity},this),eS(M,"stroke",{stroke:r.textStroke,opacity:r.opacity,lineDash:r.lineDash||null},this),M.style.zIndex=Qb(this.zlevel,this.z,this.z2),$b(t,M)}},hS=function(t){Kb(t,this._textVmlEl),this._textVmlEl=null},cS=function(t){$b(t,this._textVmlEl)},dS=[Zi,Xi,Yi,xa,Ba],fS=0;fS<dS.length;fS++){var pS=dS[fS].prototype;pS.drawRectText=uS,pS.removeRectText=hS,pS.appendRectText=cS}Ba.prototype.brushVML=function(t){var e=this.style;null!=e.text?this.drawRectText(t,{x:e.x||0,y:e.y||0,width:0,height:0},this.getBoundingRect(),!0):this.removeRectText(t)},Ba.prototype.onRemove=function(t){this.removeRectText(t)},Ba.prototype.onAdd=function(t){this.appendRectText(t)}}function gS(t){return parseInt(t,10)}function mS(t,e){!function(){if(!Ob&&Eb){Ob=!0;var t=Eb.styleSheets;t.length<31?Eb.createStyleSheet().addRule(".zrvml","behavior:url(#default#VML)"):t[0].addRule(".zrvml","behavior:url(#default#VML)")}}(),this.root=t,this.storage=e;var n=document.createElement("div"),i=document.createElement("div");n.style.cssText="display:inline-block;overflow:hidden;position:relative;width:300px;height:150px;",i.style.cssText="position:absolute;left:0;top:0;",t.appendChild(n),this._vmlRoot=i,this._vmlViewport=n,this.resize();var r=e.delFromStorage,o=e.addToStorage;e.delFromStorage=function(t){r.call(e,t),t&&t.onRemove&&t.onRemove(i)},e.addToStorage=function(t){t.onAdd&&t.onAdd(i),o.call(e,t)},this._firstPaint=!0}mS.prototype={constructor:mS,getType:function(){return"vml"},getViewportRoot:function(){return this._vmlViewport},getViewportRootOffset:function(){var t=this.getViewportRoot();if(t)return{offsetLeft:t.offsetLeft||0,offsetTop:t.offsetTop||0}},refresh:function(){var t=this.storage.getDisplayList(!0,!0);this._paintList(t)},_paintList:function(t){for(var e=this._vmlRoot,n=0;n<t.length;n++){var i=t[n];i.invisible||i.ignore?(i.__alreadyNotVisible||i.onRemove(e),i.__alreadyNotVisible=!0):(i.__alreadyNotVisible&&i.onAdd(e),i.__alreadyNotVisible=!1,i.__dirty&&(i.beforeBrush&&i.beforeBrush(),(i.brushVML||i.brush).call(i,e),i.afterBrush&&i.afterBrush())),i.__dirty=!1}this._firstPaint&&(this._vmlViewport.appendChild(e),this._firstPaint=!1)},resize:function(t,e){t=null==t?this._getWidth():t,e=null==e?this._getHeight():e;if(this._width!==t||this._height!==e){this._width=t,this._height=e;var n=this._vmlViewport.style;n.width=t+"px",n.height=e+"px"}},dispose:function(){this.root.innerHTML="",this._vmlRoot=this._vmlViewport=this.storage=null},getWidth:function(){return this._width},getHeight:function(){return this._height},clear:function(){this._vmlViewport&&this.root.removeChild(this._vmlViewport)},_getWidth:function(){var t=this.root,e=t.currentStyle;return(t.clientWidth||gS(e.width))-gS(e.paddingLeft)-gS(e.paddingRight)|0},_getHeight:function(){var t=this.root,e=t.currentStyle;return(t.clientHeight||gS(e.height))-gS(e.paddingTop)-gS(e.paddingBottom)|0}},D(["getLayer","insertLayer","eachLayer","eachBuiltinLayer","eachOtherLayer","getLayers","modLayer","delLayer","clearLayer","toDataURL","pathToImage"],function(t){mS.prototype[t]=function(t){return function(){dn('In IE8.0 VML mode painter not support method "'+t+'"')}}(t)}),Ir("vml",mS);function vS(t){return document.createElementNS("http://www.w3.org/2000/svg",t)}var yS=Jo.CMD,_S=Array.prototype.join,xS="none",wS=Math.round,bS=Math.sin,SS=Math.cos,MS=Math.PI,IS=2*Math.PI,CS=180/MS,TS=1e-4;function AS(t){return wS(1e4*t)/1e4}function DS(t){return t<TS&&-TS<t}function kS(t,e){e&&PS(t,"transform","matrix("+_S.call(e,",")+")")}function PS(t,e,n){n&&("linear"===n.type||"radial"===n.type)||t.setAttribute(e,n)}function LS(t,e,n,i){if(function(t,e){var n=e?t.textFill:t.fill;return null!=n&&n!==xS}(e,n)){var r=n?e.textFill:e.fill;PS(t,"fill",r="transparent"===r?xS:r),PS(t,"fill-opacity",null!=e.fillOpacity?e.fillOpacity*e.opacity:e.opacity)}else PS(t,"fill",xS);if(function(t,e){var n=e?t.textStroke:t.stroke;return null!=n&&n!==xS}(e,n)){var o=n?e.textStroke:e.stroke;PS(t,"stroke",o="transparent"===o?xS:o),PS(t,"stroke-width",(n?e.textStrokeWidth:e.lineWidth)/(!n&&e.strokeNoScale?i.getLineScale():1)),PS(t,"paint-order",n?"stroke":"fill"),PS(t,"stroke-opacity",null!=e.strokeOpacity?e.strokeOpacity:e.opacity),e.lineDash?(PS(t,"stroke-dasharray",e.lineDash.join(",")),PS(t,"stroke-dashoffset",wS(e.lineDashOffset||0))):PS(t,"stroke-dasharray",""),e.lineCap&&PS(t,"stroke-linecap",e.lineCap),e.lineJoin&&PS(t,"stroke-linejoin",e.lineJoin),e.miterLimit&&PS(t,"stroke-miterlimit",e.miterLimit)}else PS(t,"stroke",xS)}var OS={};OS.brush=function(t){var e=t.style,n=t.__svgEl;n||(n=vS("path"),t.__svgEl=n),t.path||t.createPathProxy();var i=t.path;if(t.__dirtyPath){i.beginPath(),i.subPixelOptimize=!1,t.buildPath(i,t.shape),t.__dirtyPath=!1;var r=function(t){for(var e=[],n=t.data,i=t.len(),r=0;r<i;){var o="",a=0;switch(n[r++]){case yS.M:o="M",a=2;break;case yS.L:o="L",a=2;break;case yS.Q:o="Q",a=4;break;case yS.C:o="C",a=6;break;case yS.A:var s=n[r++],l=n[r++],u=n[r++],h=n[r++],c=n[r++],d=n[r++],f=n[r++],p=n[r++],g=Math.abs(d),m=DS(g-IS)||(p?IS<=d:IS<=-d),v=0<d?d%IS:d%IS+IS,y=!1;y=!!m||!DS(g)&&MS<=v==!!p;var _=AS(s+u*SS(c)),x=AS(l+h*bS(c));m&&(d=p?IS-1e-4:1e-4-IS,y=!0,9===r&&e.push("M",_,x));var w=AS(s+u*SS(c+d)),b=AS(l+h*bS(c+d));e.push("A",AS(u),AS(h),wS(f*CS),+y,+p,w,b);break;case yS.Z:o="Z";break;case yS.R:w=AS(n[r++]),b=AS(n[r++]);var S=AS(n[r++]),M=AS(n[r++]);e.push("M",w,b,"L",w+S,b,"L",w+S,b+M,"L",w,b+M,"L",w,b)}o&&e.push(o);for(var I=0;I<a;I++)e.push(AS(n[r++]))}return e.join(" ")}(i);r.indexOf("NaN")<0&&PS(n,"d",r)}LS(n,e,!1,t),kS(n,t.transform),null!=e.text?FS(t,t.getBoundingRect()):WS(t)};var ES={brush:function(t){var e=t.style,n=e.image;n instanceof HTMLImageElement&&(n=n.src);if(n){var i=e.x||0,r=e.y||0,o=e.width,a=e.height,s=t.__svgEl;s||(s=vS("image"),t.__svgEl=s),n!==t.__imageSrc&&(function(t,e,n){t.setAttributeNS("http://www.w3.org/1999/xlink",e,n)}(s,"href",n),t.__imageSrc=n),PS(s,"width",o),PS(s,"height",a),PS(s,"x",i),PS(s,"y",r),kS(s,t.transform),null!=e.text?FS(t,t.getBoundingRect()):WS(t)}}},zS={},NS=new Sn,RS={},BS=[],VS={left:"start",right:"end",center:"middle",middle:"middle"},FS=function(t,e){var n=t.style,i=t.transform,r=t instanceof Ba||n.transformText;t.__dirty&&Di(n);var o=n.text;if(null!=o&&(o+=""),Gi(o,n)){null==o&&(o=""),!r&&i&&(NS.copy(e),NS.applyTransform(i),e=NS);var a=t.__textSvgEl;a||(a=vS("text"),t.__textSvgEl=a);var s=a.style,l=n.font||ai,u=a.__computedFont;l!==a.__styleFont&&(s.font=a.__styleFont=l,u=a.__computedFont=s.font);var h=n.textPadding,c=n.textLineHeight,d=t.__textCotentBlock;d&&!t.__dirtyText||(d=t.__textCotentBlock=yi(o,u,h,c,n.truncate));var f=d.outerHeight,p=d.lineHeight;Ri(RS,t,n,e);var g=RS.baseX,m=RS.baseY,v=RS.textAlign||"left",y=RS.textVerticalAlign;!function(t,e,n,i,r,o,a){ne(BS),e&&n&&ie(BS,n);var s=i.textRotation;if(r&&s){var l=i.textOrigin;"center"===l?(o=r.width/2+r.x,a=r.height/2+r.y):l&&(o=l[0]+r.x,a=l[1]+r.y),BS[4]-=o,BS[5]-=a,ae(BS,BS,s),BS[4]+=o,BS[5]+=a}kS(t,BS)}(a,r,i,n,e,g,m);var _=g,x=ci(m,f,y);h&&(_=function(t,e,n){return"right"===e?t-n[1]:"center"===e?t+n[3]/2-n[1]/2:t+n[3]}(g,v,h),x+=h[0]),x+=p/2,LS(a,n,!0,t);var w=d.canCacheByTextString,b=t.__tspanList||(t.__tspanList=[]),S=b.length;if(w&&t.__canCacheByTextString&&t.__text===o){if(t.__dirtyText&&S)for(var M=0;M<S;++M)HS(b[M],v,_,x+M*p)}else{t.__text=o,t.__canCacheByTextString=w;var I=d.lines,C=I.length;for(M=0;M<C;M++){var T=b[M],A=I[M];T?T.__zrText!==A&&(T.innerHTML="",T.appendChild(document.createTextNode(A))):(T=b[M]=vS("tspan"),a.appendChild(T),T.appendChild(document.createTextNode(A))),HS(T,v,_,x+M*p)}if(C<S){for(;M<S;M++)a.removeChild(b[M]);b.length=C}}}};function HS(t,e,n,i){PS(t,"dominant-baseline","middle"),PS(t,"text-anchor",VS[e]),PS(t,"x",n),PS(t,"y",i)}function WS(t){t&&t.__textSvgEl&&(t.__textSvgEl.parentNode&&t.__textSvgEl.parentNode.removeChild(t.__textSvgEl),t.__textSvgEl=null,t.__tspanList=[],t.__text=null)}function GS(){}function ZS(t,e){for(var n=0,i=e.length,r=0,o=0;n<i;n++){var a=e[n];if(a.removed){for(s=[],l=o;l<o+a.count;l++)s.push(l);a.indices=s,o+=a.count}else{for(var s=[],l=r;l<r+a.count;l++)s.push(l);a.indices=s,r+=a.count,a.added||(o+=a.count)}}return e}zS.drawRectText=FS,zS.brush=function(t){null!=t.style.text?FS(t,!1):WS(t)},GS.prototype={diff:function(l,u,t){t=t||function(t,e){return t===e},this.equals=t;var h=this;l=l.slice();var c=(u=u.slice()).length,d=l.length,f=1,e=c+d,p=[{newPos:-1,components:[]}],n=this.extractCommon(p[0],u,l,0);if(p[0].newPos+1>=c&&d<=n+1){for(var i=[],r=0;r<u.length;r++)i.push(r);return[{indices:i,count:u.length}]}function o(){for(var t=-1*f;t<=f;t+=2){var e,n=p[t-1],i=p[t+1],r=(i?i.newPos:0)-t;n&&(p[t-1]=void 0);var o=n&&n.newPos+1<c,a=i&&0<=r&&r<d;if(o||a){if(!o||a&&n.newPos<i.newPos?(e={newPos:(s=i).newPos,components:s.components.slice(0)},h.pushComponent(e.components,void 0,!0)):((e=n).newPos++,h.pushComponent(e.components,!0,void 0)),r=h.extractCommon(e,u,l,t),e.newPos+1>=c&&d<=r+1)return ZS(h,e.components,u,l);p[t]=e}else p[t]=void 0}var s;f++}for(;f<=e;){var a=o();if(a)return a}},pushComponent:function(t,e,n){var i=t[t.length-1];i&&i.added===e&&i.removed===n?t[t.length-1]={count:i.count+1,added:e,removed:n}:t.push({count:1,added:e,removed:n})},extractCommon:function(t,e,n,i){for(var r=e.length,o=n.length,a=t.newPos,s=a-i,l=0;a+1<r&&s+1<o&&this.equals(e[a+1],n[s+1]);)a++,s++,l++;return l&&t.components.push({count:l}),t.newPos=a,s},tokenize:function(t){return t.slice()},join:function(t){return t.slice()}};var US=new GS;function XS(t,e,n,i,r){this._zrId=t,this._svgRoot=e,this._tagNames="string"==typeof n?[n]:n,this._markLabel=i,this._domName=r||"_dom",this.nextId=0}function YS(t,e){XS.call(this,t,e,["linearGradient","radialGradient"],"__gradient_in_use__")}function jS(t,e){XS.call(this,t,e,"clipPath","__clippath_in_use__")}function qS(t,e){XS.call(this,t,e,["filter"],"__filter_in_use__","_shadowDom")}function $S(t){return t&&(t.shadowBlur||t.shadowOffsetX||t.shadowOffsetY||t.textShadowBlur||t.textShadowOffsetX||t.textShadowOffsetY)}function KS(t){return parseInt(t,10)}function QS(t,e){return e&&t&&e.parentNode!==t}function JS(t,e,n){if(QS(t,e)&&n){var i=n.nextSibling;i?t.insertBefore(e,i):t.appendChild(e)}}function tM(t,e){if(QS(t,e)){var n=t.firstChild;n?t.insertBefore(e,n):t.appendChild(e)}}function eM(t,e){e&&t&&e.parentNode===t&&t.removeChild(e)}function nM(t){return t.__textSvgEl}function iM(t){return t.__svgEl}XS.prototype.createElement=vS,XS.prototype.getDefs=function(t){var e=this._svgRoot,i=this._svgRoot.getElementsByTagName("defs");return 0===i.length?t?((i=e.insertBefore(this.createElement("defs"),e.firstChild)).contains||(i.contains=function(t){var e=i.children;if(!e)return!1;for(var n=e.length-1;0<=n;--n)if(e[n]===t)return!0;return!1}),i):null:i[0]},XS.prototype.update=function(t,e){if(t){var n=this.getDefs(!1);if(t[this._domName]&&n.contains(t[this._domName]))"function"==typeof e&&e(t);else{var i=this.add(t);i&&(t[this._domName]=i)}}},XS.prototype.addDom=function(t){this.getDefs(!0).appendChild(t)},XS.prototype.removeDom=function(t){var e=this.getDefs(!1);e&&t[this._domName]&&(e.removeChild(t[this._domName]),t[this._domName]=null)},XS.prototype.getDoms=function(){var n=this.getDefs(!1);if(!n)return[];var i=[];return D(this._tagNames,function(t){var e=n.getElementsByTagName(t);i=i.concat([].slice.call(e))}),i},XS.prototype.markAllUnused=function(){var t=this.getDoms(),e=this;D(t,function(t){t[e._markLabel]="0"})},XS.prototype.markUsed=function(t){t&&(t[this._markLabel]="1")},XS.prototype.removeUnused=function(){var e=this.getDefs(!1);if(e){var t=this.getDoms(),n=this;D(t,function(t){"1"!==t[n._markLabel]&&e.removeChild(t)})}},XS.prototype.getSvgProxy=function(t){return t instanceof xa?OS:t instanceof Yi?ES:t instanceof Ba?zS:OS},XS.prototype.getTextSvgElement=function(t){return t.__textSvgEl},XS.prototype.getSvgElement=function(t){return t.__svgEl},w(YS,XS),YS.prototype.addWithoutUpdate=function(o,a){if(a&&a.style){var s=this;D(["fill","stroke"],function(t){if(a.style[t]&&("linear"===a.style[t].type||"radial"===a.style[t].type)){var e,n=a.style[t],i=s.getDefs(!0);n._dom?(e=n._dom,i.contains(n._dom)||s.addDom(e)):e=s.add(n),s.markUsed(a);var r=e.getAttribute("id");o.setAttribute(t,"url(#"+r+")")}})}},YS.prototype.add=function(t){var e;if("linear"===t.type)e=this.createElement("linearGradient");else{if("radial"!==t.type)return dn("Illegal gradient type."),null;e=this.createElement("radialGradient")}return t.id=t.id||this.nextId++,e.setAttribute("id","zr"+this._zrId+"-gradient-"+t.id),this.updateDom(t,e),this.addDom(e),e},YS.prototype.update=function(n){var i=this;XS.prototype.update.call(this,n,function(){var t=n.type,e=n._dom.tagName;"linear"===t&&"linearGradient"===e||"radial"===t&&"radialGradient"===e?i.updateDom(n,n._dom):(i.removeDom(n),i.add(n))})},YS.prototype.updateDom=function(t,e){if("linear"===t.type)e.setAttribute("x1",t.x),e.setAttribute("y1",t.y),e.setAttribute("x2",t.x2),e.setAttribute("y2",t.y2);else{if("radial"!==t.type)return void dn("Illegal gradient type.");e.setAttribute("cx",t.x),e.setAttribute("cy",t.y),e.setAttribute("r",t.r)}t.global?e.setAttribute("gradientUnits","userSpaceOnUse"):e.setAttribute("gradientUnits","objectBoundingBox"),e.innerHTML="";for(var n=t.colorStops,i=0,r=n.length;i<r;++i){var o=this.createElement("stop");o.setAttribute("offset",100*n[i].offset+"%");var a=n[i].color;if(a.indexOf(!1)){var s=Ne(a)[3],l=Ve(a);o.setAttribute("stop-color","#"+l),o.setAttribute("stop-opacity",s)}else o.setAttribute("stop-color",n[i].color);e.appendChild(o)}t._dom=e},YS.prototype.markUsed=function(t){if(t.style){var e=t.style.fill;e&&e._dom&&XS.prototype.markUsed.call(this,e._dom),(e=t.style.stroke)&&e._dom&&XS.prototype.markUsed.call(this,e._dom)}},w(jS,XS),jS.prototype.update=function(t){var e=this.getSvgElement(t);e&&this.updateDom(e,t.__clipPaths,!1);var n=this.getTextSvgElement(t);n&&this.updateDom(n,t.__clipPaths,!0),this.markUsed(t)},jS.prototype.updateDom=function(t,e,n){if(e&&0<e.length){var i,r,o=this.getDefs(!0),a=e[0],s=n?"_textDom":"_dom";a[s]?(r=a[s].getAttribute("id"),i=a[s],o.contains(i)||o.appendChild(i)):(r="zr"+this._zrId+"-clip-"+this.nextId,++this.nextId,(i=this.createElement("clipPath")).setAttribute("id",r),o.appendChild(i),a[s]=i);var l=this.getSvgProxy(a);if(a.transform&&a.parent.invTransform&&!n){var u=Array.prototype.slice.call(a.transform);re(a.transform,a.parent.invTransform,a.transform),l.brush(a),a.transform=u}else l.brush(a);var h=this.getSvgElement(a);i.innerHTML="",i.appendChild(h.cloneNode()),t.setAttribute("clip-path","url(#"+r+")"),1<e.length&&this.updateDom(i,e.slice(1),n)}else t&&t.setAttribute("clip-path","none")},jS.prototype.markUsed=function(t){var e=this;t.__clipPaths&&D(t.__clipPaths,function(t){t._dom&&XS.prototype.markUsed.call(e,t._dom),t._textDom&&XS.prototype.markUsed.call(e,t._textDom)})},w(qS,XS),qS.prototype.addWithoutUpdate=function(t,e){if(e&&$S(e.style)){var n;if(e._shadowDom)n=e._shadowDom,this.getDefs(!0).contains(e._shadowDom)||this.addDom(n);else n=this.add(e);this.markUsed(e);var i=n.getAttribute("id");t.style.filter="url(#"+i+")"}},qS.prototype.add=function(t){var e=this.createElement("filter");return t._shadowDomId=t._shadowDomId||this.nextId++,e.setAttribute("id","zr"+this._zrId+"-shadow-"+t._shadowDomId),this.updateDom(t,e),this.addDom(e),e},qS.prototype.update=function(t,e){if($S(e.style)){var n=this;XS.prototype.update.call(this,e,function(){n.updateDom(e,e._shadowDom)})}else this.remove(t,e)},qS.prototype.remove=function(t,e){null!=e._shadowDomId&&(this.removeDom(t),t.style.filter="")},qS.prototype.updateDom=function(t,e){var n=e.getElementsByTagName("feDropShadow");n=0===n.length?this.createElement("feDropShadow"):n[0];var i,r,o,a,s=t.style,l=t.scale&&t.scale[0]||1,u=t.scale&&t.scale[1]||1;if(s.shadowBlur||s.shadowOffsetX||s.shadowOffsetY)i=s.shadowOffsetX||0,r=s.shadowOffsetY||0,o=s.shadowBlur,a=s.shadowColor;else{if(!s.textShadowBlur)return void this.removeDom(e,s);i=s.textShadowOffsetX||0,r=s.textShadowOffsetY||0,o=s.textShadowBlur,a=s.textShadowColor}n.setAttribute("dx",i/l),n.setAttribute("dy",r/u),n.setAttribute("flood-color",a);var h=o/2/l+" "+o/2/u;n.setAttribute("stdDeviation",h),e.setAttribute("x","-100%"),e.setAttribute("y","-100%"),e.setAttribute("width",Math.ceil(o/2*200)+"%"),e.setAttribute("height",Math.ceil(o/2*200)+"%"),e.appendChild(n),t._shadowDom=e},qS.prototype.markUsed=function(t){t._shadowDom&&XS.prototype.markUsed.call(this,t._shadowDom)};function rM(t,e,n,i){this.root=t,this.storage=e,this._opts=n=k({},n||{});var r=vS("svg");r.setAttribute("xmlns","http://www.w3.org/2000/svg"),r.setAttribute("version","1.1"),r.setAttribute("baseProfile","full"),r.style.cssText="user-select:none;position:absolute;left:0;top:0;";var o=vS("g");r.appendChild(o);var a=vS("g");r.appendChild(a),this.gradientManager=new YS(i,a),this.clipPathManager=new jS(i,a),this.shadowManager=new qS(i,a);var s=document.createElement("div");s.style.cssText="overflow:hidden;position:relative",this._svgDom=r,this._svgRoot=a,this._backgroundRoot=o,this._viewport=s,t.appendChild(s),s.appendChild(r),this.resize(n.width,n.height),this._visibleList=[]}rM.prototype={constructor:rM,getType:function(){return"svg"},getViewportRoot:function(){return this._viewport},getSvgDom:function(){return this._svgDom},getSvgRoot:function(){return this._svgRoot},getViewportRootOffset:function(){var t=this.getViewportRoot();if(t)return{offsetLeft:t.offsetLeft||0,offsetTop:t.offsetTop||0}},refresh:function(){var t=this.storage.getDisplayList(!0);this._paintList(t)},setBackgroundColor:function(t){this._backgroundRoot&&this._backgroundNode&&this._backgroundRoot.removeChild(this._backgroundNode);var e=vS("rect");e.setAttribute("width",this.getWidth()),e.setAttribute("height",this.getHeight()),e.setAttribute("x",0),e.setAttribute("y",0),e.setAttribute("id",0),e.style.fill=t,this._backgroundRoot.appendChild(e),this._backgroundNode=e},_paintList:function(t){this.gradientManager.markAllUnused(),this.clipPathManager.markAllUnused(),this.shadowManager.markAllUnused();var e,n,i=this._svgRoot,r=this._visibleList,o=t.length,a=[];for(e=0;e<o;e++){var s=t[e],l=(n=s)instanceof xa?OS:n instanceof Yi?ES:n instanceof Ba?zS:OS,u=iM(s)||nM(s);s.invisible||(s.__dirty&&(l&&l.brush(s),this.clipPathManager.update(s),s.style&&(this.gradientManager.update(s.style.fill),this.gradientManager.update(s.style.stroke),this.shadowManager.update(u,s)),s.__dirty=!1),a.push(s))}var h,c=function(t,e,n){return US.diff(t,e,n)}(r,a);for(e=0;e<c.length;e++){if((p=c[e]).removed)for(var d=0;d<p.count;d++){u=iM(s=r[p.indices[d]]);var f=nM(s);eM(i,u),eM(i,f)}}for(e=0;e<c.length;e++){var p;if((p=c[e]).added)for(d=0;d<p.count;d++){u=iM(s=a[p.indices[d]]),f=nM(s);h?JS(i,u,h):tM(i,u),u?JS(i,f,u):h?JS(i,f,h):tM(i,f),JS(i,f,u),h=f||u||h,this.gradientManager.addWithoutUpdate(u||f,s),this.shadowManager.addWithoutUpdate(u||f,s),this.clipPathManager.markUsed(s)}else if(!p.removed)for(d=0;d<p.count;d++){u=iM(s=a[p.indices[d]]),f=nM(s),u=iM(s),f=nM(s);this.gradientManager.markUsed(s),this.gradientManager.addWithoutUpdate(u||f,s),this.shadowManager.markUsed(s),this.shadowManager.addWithoutUpdate(u||f,s),this.clipPathManager.markUsed(s),f&&JS(i,f,u),h=u||f||h}}this.gradientManager.removeUnused(),this.clipPathManager.removeUnused(),this.shadowManager.removeUnused(),this._visibleList=a},_getDefs:function(t){var i,e=this._svgDom;return 0!==(i=e.getElementsByTagName("defs")).length?i[0]:t?((i=e.insertBefore(vS("defs"),e.firstChild)).contains||(i.contains=function(t){var e=i.children;if(!e)return!1;for(var n=e.length-1;0<=n;--n)if(e[n]===t)return!0;return!1}),i):null},resize:function(t,e){var n=this._viewport;n.style.display="none";var i=this._opts;if(null!=t&&(i.width=t),null!=e&&(i.height=e),t=this._getSize(0),e=this._getSize(1),n.style.display="",this._width!==t||this._height!==e){this._width=t,this._height=e;var r=n.style;r.width=t+"px",r.height=e+"px";var o=this._svgDom;o.setAttribute("width",t),o.setAttribute("height",e)}this._backgroundNode&&(this._backgroundNode.setAttribute("width",t),this._backgroundNode.setAttribute("height",e))},getWidth:function(){return this._width},getHeight:function(){return this._height},_getSize:function(t){var e=this._opts,n=["width","height"][t],i=["clientWidth","clientHeight"][t],r=["paddingLeft","paddingTop"][t],o=["paddingRight","paddingBottom"][t];if(null!=e[n]&&"auto"!==e[n])return parseFloat(e[n]);var a=this.root,s=document.defaultView.getComputedStyle(a);return(a[i]||KS(s[n])||KS(a.style[n]))-(KS(s[r])||0)-(KS(s[o])||0)|0},dispose:function(){this.root.innerHTML="",this._svgRoot=this._backgroundRoot=this._svgDom=this._backgroundNode=this._viewport=this.storage=null},clear:function(){this._viewport&&this.root.removeChild(this._viewport)},toDataURL:function(){return this.refresh(),"data:image/svg+xml;charset=UTF-8,"+encodeURIComponent(this._svgDom.outerHTML.replace(/></g,">\n\r<"))}},D(["getLayer","insertLayer","eachLayer","eachBuiltinLayer","eachOtherLayer","getLayers","modLayer","delLayer","clearLayer","pathToImage"],function(t){rM.prototype[t]=function(t){return function(){dn('In SVG mode painter not support method "'+t+'"')}}(t)}),Ir("svg",rM),t.version="4.8.0",t.dependencies={zrender:"4.3.1"},t.PRIORITY=gd,t.init=function(t,e,n){var i=tf(t);if(i)return i;var r=new wd(t,e,n);return r.id="ec_"+qd++,Yd[r.id]=r,Ur(t,Kd,r.id),function(i){var r="__connectUpdateStatus";function o(t,e){for(var n=0;n<t.length;n++){t[n][r]=e}}cd(Fd,function(t,e){i._messageCenter.on(e,function(t){if(jd[i.group]&&0!==i[r]){if(t&&t.escapeConnect)return;var e=i.makeActionFromEvent(t),n=[];cd(Yd,function(t){t!==i&&t.group===i.group&&n.push(t)}),o(n,0),cd(n,function(t){1!==t[r]&&t.dispatchAction(e)}),o(n,2)}})})}(r),r},t.connect=function(e){if(O(e)){var t=e;e=null,cd(t,function(t){null!=t.group&&(e=t.group)}),e=e||"g_"+$d++,cd(t,function(t){t.group=e})}return jd[e]=!0,e},t.disConnect=Qd,t.disconnect=Jd,t.dispose=function(t){"string"==typeof t?t=Yd[t]:t instanceof wd||(t=tf(t)),t instanceof wd&&!t.isDisposed()&&t.dispose()},t.getInstanceByDom=tf,t.getInstanceById=function(t){return Yd[t]},t.registerTheme=ef,t.registerPreprocessor=nf,t.registerProcessor=rf,t.registerPostUpdate=function(t){Gd.push(t)},t.registerAction=of,t.registerCoordinateSystem=function(t,e){ju.register(t,e)},t.getCoordinateSystemDimensions=function(t){var e=ju.get(t);if(e)return e.getDimensionsInfo?e.getDimensionsInfo():e.dimensions.slice()},t.registerLayout=af,t.registerVisual=sf,t.registerLoading=uf,t.extendComponentModel=hf,t.extendComponentView=cf,t.extendSeriesModel=df,t.extendChartView=ff,t.setCanvasCreator=function(t){f("createCanvas",t)},t.registerMap=function(t,e,n){sd(t,e,n)},t.getMap=function(t){var e=ld(t);return e&&e[0]&&{geoJson:e[0].geoJSON,specialAreas:e[0].specialAreas}},t.dataTool={},t.zrender=Tr,t.number=Fl,t.format=nu,t.throttle=mc,t.helper=rg,t.matrix=ue,t.vector=wt,t.color=Xe,t.parseGeoJSON=hg,t.parseGeoJson=Sg,t.util=Mg,t.graphic=Ig,t.List=kf,t.Model=_l,t.Axis=xg,t.env=v});
