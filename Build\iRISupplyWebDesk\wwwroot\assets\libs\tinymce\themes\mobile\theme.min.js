/**
 * Copyright (c) Tiny Technologies, Inc. All rights reserved.
 * Licensed under the LGPL or a commercial license.
 * For LGPL see License.txt in the project root for license information.
 * For commercial licenses see https://www.tiny.cloud/
 *
 * Version: 5.3.2 (2020-06-10)
 */
!function(v){"use strict";var x=function(){return(x=Object.assign||function(n){for(var e,t=1,r=arguments.length;t<r;t++)for(var o in e=arguments[t])Object.prototype.hasOwnProperty.call(e,o)&&(n[o]=e[o]);return n}).apply(this,arguments)};function c(n,e){var t={};for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&e.indexOf(r)<0&&(t[r]=n[r]);if(null!=n&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(n);o<r.length;o++)e.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(n,r[o])&&(t[r[o]]=n[r[o]])}return t}function u(){for(var n=0,e=0,t=arguments.length;e<t;e++)n+=arguments[e].length;var r=Array(n),o=0;for(e=0;e<t;e++)for(var i=arguments[e],u=0,c=i.length;u<c;u++,o++)r[o]=i[u];return r}var w=function(){},d=function(t,r){return function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];return t(r.apply(null,n))}},y=function(n){return function(){return n}},b=function(n){return n};function S(r){for(var o=[],n=1;n<arguments.length;n++)o[n-1]=arguments[n];return function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];var t=o.concat(n);return r.apply(null,t)}}var n,e,t,r,o,i,m=function(e){return function(n){return!e(n)}},a=function(n){return function(){throw new Error(n)}},f=function(n){return n()},s=y(!1),l=y(!0),g=function(){return p},p=(n=function(n){return n.isNone()},{fold:function(n,e){return n()},is:s,isSome:s,isNone:l,getOr:t=function(n){return n},getOrThunk:e=function(n){return n()},getOrDie:function(n){throw new Error(n||"error: getOrDie called on none.")},getOrNull:y(null),getOrUndefined:y(undefined),or:t,orThunk:e,map:g,each:w,bind:g,exists:s,forall:l,filter:g,equals:n,equals_:n,toArray:function(){return[]},toString:y("none()")}),h=function(t){var n=y(t),e=function(){return o},r=function(n){return n(t)},o={fold:function(n,e){return e(t)},is:function(n){return t===n},isSome:l,isNone:s,getOr:n,getOrThunk:n,getOrDie:n,getOrNull:n,getOrUndefined:n,or:e,orThunk:e,map:function(n){return h(n(t))},each:function(n){n(t)},bind:r,exists:r,forall:r,filter:function(n){return n(t)?o:p},toArray:function(){return[t]},toString:function(){return"some("+t+")"},equals:function(n){return n.is(t)},equals_:function(n,e){return n.fold(s,function(n){return e(t,n)})}};return o},O={some:h,none:g,from:function(n){return null===n||n===undefined?p:h(n)}},T=Object.keys,k=Object.hasOwnProperty,E=function(n,e){for(var t=T(n),r=0,o=t.length;r<o;r++){var i=t[r];e(n[i],i)}},C=function(n,t){return M(n,function(n,e){return{k:e,v:t(n,e)}})},M=function(n,r){var o={};return E(n,function(n,e){var t=r(n,e);o[t.k]=t.v}),o},_=function(n,e){var t,r,o,i,u={};return t=e,i=u,r=function(n,e){i[e]=n},o=w,E(n,function(n,e){(t(n,e)?r:o)(n,e)}),u},D=function(n,t){var r=[];return E(n,function(n,e){r.push(t(n,e))}),r},I=function(n,e){return F(n,e)?O.from(n[e]):O.none()},F=function(n,e){return k.call(n,e)},R=function(n,e){return F(n,e)&&n[e]!==undefined&&null!==n[e]},V=function(t){var r,o=!1;return function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];return o||(o=!0,r=t.apply(null,n)),r}},B=function(n,e){var t=function(n,e){for(var t=0;t<n.length;t++){var r=n[t];if(r.test(e))return r}return undefined}(n,e);if(!t)return{major:0,minor:0};var r=function(n){return Number(e.replace(t,"$"+n))};return j(r(1),r(2))},A=function(){return j(0,0)},j=function(n,e){return{major:n,minor:e}},H={nu:j,detect:function(n,e){var t=String(e).toLowerCase();return 0===n.length?A():B(n,t)},unknown:A},N="Edge",P="Chrome",z="Opera",L="Firefox",G="Safari",$=function(n){var e=n.current,t=n.version,r=function(n){return function(){return e===n}};return{current:e,version:t,isEdge:r(N),isChrome:r(P),isIE:r("IE"),isOpera:r(z),isFirefox:r(L),isSafari:r(G)}},U={unknown:function(){return $({current:undefined,version:H.unknown()})},nu:$,edge:y(N),chrome:y(P),ie:y("IE"),opera:y(z),firefox:y(L),safari:y(G)},W="Windows",X="Android",q="Linux",Y="Solaris",K="FreeBSD",J="ChromeOS",Q=function(n){var e=n.current,t=n.version,r=function(n){return function(){return e===n}};return{current:e,version:t,isWindows:r(W),isiOS:r("iOS"),isAndroid:r(X),isOSX:r("OSX"),isLinux:r(q),isSolaris:r(Y),isFreeBSD:r(K),isChromeOS:r(J)}},Z={unknown:function(){return Q({current:undefined,version:H.unknown()})},nu:Q,windows:y(W),ios:y("iOS"),android:y(X),linux:y(q),osx:y("OSX"),solaris:y(Y),freebsd:y(K),chromeos:y(J)},nn=function(r){return function(n){return t=typeof(e=n),(null===e?"null":"object"==t&&(Array.prototype.isPrototypeOf(e)||e.constructor&&"Array"===e.constructor.name)?"array":"object"==t&&(String.prototype.isPrototypeOf(e)||e.constructor&&"String"===e.constructor.name)?"string":t)===r;var e,t}},en=function(e){return function(n){return typeof n===e}},tn=nn("string"),rn=nn("object"),on=nn("array"),un=en("boolean"),cn=en("function"),an=en("number"),fn=Array.prototype.slice,sn=Array.prototype.indexOf,ln=Array.prototype.push,dn=function(n,e){return t=n,r=e,-1<sn.call(t,r);var t,r},mn=function(n,e){for(var t=0,r=n.length;t<r;t++){if(e(n[t],t))return!0}return!1},gn=function(n,e){for(var t=n.length,r=new Array(t),o=0;o<t;o++){var i=n[o];r[o]=e(i,o)}return r},pn=function(n,e){for(var t=0,r=n.length;t<r;t++){e(n[t],t)}},hn=function(n,e){for(var t=[],r=0,o=n.length;r<o;r++){var i=n[r];e(i,r)&&t.push(i)}return t},vn=function(n,e,t){return function(n,e){for(var t=n.length-1;0<=t;t--){e(n[t],t)}}(n,function(n){t=e(t,n)}),t},yn=function(n,e,t){return pn(n,function(n){t=e(t,n)}),t},bn=function(n,e){return function(n,e,t){for(var r=0,o=n.length;r<o;r++){var i=n[r];if(e(i,r))return O.some(i);if(t(i,r))break}return O.none()}(n,e,s)},xn=function(n,e){for(var t=0,r=n.length;t<r;t++){if(e(n[t],t))return O.some(t)}return O.none()},wn=function(n){for(var e=[],t=0,r=n.length;t<r;++t){if(!on(n[t]))throw new Error("Arr.flatten item "+t+" was not an array, input: "+n);ln.apply(e,n[t])}return e},Sn=function(n,e){return wn(gn(n,e))},On=function(n,e){for(var t=0,r=n.length;t<r;++t){if(!0!==e(n[t],t))return!1}return!0},Tn=function(n){var e=fn.call(n,0);return e.reverse(),e},kn=function(n,e){return hn(n,function(n){return!dn(e,n)})},En=function(n){return[n]},Cn=function(n,e){for(var t=0;t<n.length;t++){var r=e(n[t],t);if(r.isSome())return r}return O.none()},Mn=function(n,e){var t=String(e).toLowerCase();return bn(n,function(n){return n.search(t)})},_n=function(n,t){return Mn(n,t).map(function(n){var e=H.detect(n.versionRegexes,t);return{current:n.name,version:e}})},Dn=function(n,t){return Mn(n,t).map(function(n){var e=H.detect(n.versionRegexes,t);return{current:n.name,version:e}})},In=function(n,o){return n.replace(/\$\{([^{}]*)\}/g,function(n,e){var t,r=o[e];return"string"==(t=typeof r)||"number"==t?r.toString():n})},Fn=function(n,e){return-1!==n.indexOf(e)},Rn=(r=/^\s+|\s+$/g,function(n){return n.replace(r,"")}),Vn=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,Bn=function(e){return function(n){return Fn(n,e)}},An=[{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:function(n){return Fn(n,"edge/")&&Fn(n,"chrome")&&Fn(n,"safari")&&Fn(n,"applewebkit")}},{name:"Chrome",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,Vn],search:function(n){return Fn(n,"chrome")&&!Fn(n,"chromeframe")}},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:function(n){return Fn(n,"msie")||Fn(n,"trident")}},{name:"Opera",versionRegexes:[Vn,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:Bn("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:Bn("firefox")},{name:"Safari",versionRegexes:[Vn,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:function(n){return(Fn(n,"safari")||Fn(n,"mobile/"))&&Fn(n,"applewebkit")}}],jn=[{name:"Windows",search:Bn("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:function(n){return Fn(n,"iphone")||Fn(n,"ipad")},versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:Bn("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"OSX",search:Bn("mac os x"),versionRegexes:[/.*?mac\ os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:Bn("linux"),versionRegexes:[]},{name:"Solaris",search:Bn("sunos"),versionRegexes:[]},{name:"FreeBSD",search:Bn("freebsd"),versionRegexes:[]},{name:"ChromeOS",search:Bn("cros"),versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/]}],Hn={browsers:y(An),oses:y(jn)},Nn=function(n,e){var t,r,o,i,u,c,a,f,s,l,d,m,g=Hn.browsers(),p=Hn.oses(),h=_n(g,n).fold(U.unknown,U.nu),v=Dn(p,n).fold(Z.unknown,Z.nu);return{browser:h,os:v,deviceType:(r=h,o=n,i=e,u=(t=v).isiOS()&&!0===/ipad/i.test(o),c=t.isiOS()&&!u,a=t.isiOS()||t.isAndroid(),f=a||i("(pointer:coarse)"),s=u||!c&&a&&i("(min-device-width:768px)"),l=c||a&&!s,d=r.isSafari()&&t.isiOS()&&!1===/safari/i.test(o),m=!l&&!s&&!d,{isiPad:y(u),isiPhone:y(c),isTablet:y(s),isPhone:y(l),isTouch:y(f),isAndroid:t.isAndroid,isiOS:t.isiOS,isWebView:y(d),isDesktop:y(m)})}},Pn=function(n){return v.window.matchMedia(n).matches},zn=V(function(){return Nn(v.navigator.userAgent,Pn)}),Ln=function(){return zn()},Gn=y("touchstart"),$n=y("touchmove"),Un=y("touchend"),Wn=y("mousedown"),Xn=y("mousemove"),qn=y("mouseup"),Yn=y("mouseover"),Kn=y("keydown"),Jn=y("keyup"),Qn=y("input"),Zn=y("change"),ne=y("click"),ee=y("transitionend"),te=y("selectstart"),re={tap:y("alloy.tap")},oe=y("alloy.focus"),ie=y("alloy.blur.post"),ue=y("alloy.paste.post"),ce=y("alloy.receive"),ae=y("alloy.execute"),fe=y("alloy.focus.item"),se=re.tap,le=y("alloy.longpress"),de=y("alloy.system.init"),me=y("alloy.system.attached"),ge=y("alloy.system.detached"),pe=y("alloy.focusmanager.shifted"),he=y("alloy.highlight"),ve=y("alloy.dehighlight"),ye=function(n,e){Se(n,n.element(),e,{})},be=function(n,e,t){Se(n,n.element(),e,t)},xe=function(n){ye(n,ae())},we=function(n,e,t){Se(n,e,t,{})},Se=function(n,e,t,r){var o=x({target:e},r);n.getSystem().triggerEvent(t,e,C(o,y))},Oe=function(n){if(null===n||n===undefined)throw new Error("Node cannot be null or undefined");return{dom:y(n)}},Te={fromHtml:function(n,e){var t=(e||v.document).createElement("div");if(t.innerHTML=n,!t.hasChildNodes()||1<t.childNodes.length)throw v.console.error("HTML does not have a single root node",n),new Error("HTML must have a single root node");return Oe(t.childNodes[0])},fromTag:function(n,e){var t=(e||v.document).createElement(n);return Oe(t)},fromText:function(n,e){var t=(e||v.document).createTextNode(n);return Oe(t)},fromDom:Oe,fromPoint:function(n,e,t){var r=n.dom();return O.from(r.elementFromPoint(e,t)).map(Oe)}},ke=("undefined"!=typeof v.window?v.window:Function("return this;")(),function(n){return n.dom().nodeName.toLowerCase()}),Ee=function(e){return function(n){return n.dom().nodeType===e}},Ce=Ee(1),Me=Ee(3),_e=function(n){var e=Me(n)?n.dom().parentNode:n.dom();return e!==undefined&&null!==e&&e.ownerDocument.body.contains(e)},De=function(){return Ie(Te.fromDom(v.document))},Ie=function(n){var e=n.dom().body;if(null===e||e===undefined)throw new Error("Body is not available yet");return Te.fromDom(e)},Fe=function(n,e){var t=n.dom();if(1!==t.nodeType)return!1;var r=t;if(r.matches!==undefined)return r.matches(e);if(r.msMatchesSelector!==undefined)return r.msMatchesSelector(e);if(r.webkitMatchesSelector!==undefined)return r.webkitMatchesSelector(e);if(r.mozMatchesSelector!==undefined)return r.mozMatchesSelector(e);throw new Error("Browser lacks native selectors")},Re=function(n){return 1!==n.nodeType&&9!==n.nodeType||0===n.childElementCount},Ve=function(n,e){var t=e===undefined?v.document:e.dom();return Re(t)?[]:gn(t.querySelectorAll(n),Te.fromDom)},Be=function(n,e){var t=e===undefined?v.document:e.dom();return Re(t)?O.none():O.from(t.querySelector(n)).map(Te.fromDom)},Ae=function(n,e){return n.dom()===e.dom()},je=function(n){return Te.fromDom(n.dom().ownerDocument)},He=function(n){return O.from(n.dom().parentNode).map(Te.fromDom)},Ne=function(n){return gn(n.dom().childNodes,Te.fromDom)},Pe=function(n,e){var t=n.dom().childNodes;return O.from(t[e]).map(Te.fromDom)},ze=function(e,t){He(e).each(function(n){n.dom().insertBefore(t.dom(),e.dom())})},Le=function(n,e){var t;(t=n,O.from(t.dom().nextSibling).map(Te.fromDom)).fold(function(){He(n).each(function(n){$e(n,e)})},function(n){ze(n,e)})},Ge=function(e,t){Pe(e,0).fold(function(){$e(e,t)},function(n){e.dom().insertBefore(t.dom(),n.dom())})},$e=function(n,e){n.dom().appendChild(e.dom())},Ue=function(e,n){pn(n,function(n){$e(e,n)})},We=function(n){n.dom().textContent="",pn(Ne(n),function(n){Xe(n)})},Xe=function(n){var e=n.dom();null!==e.parentNode&&e.parentNode.removeChild(e)},qe=function(n){ye(n,ge());var e=n.components();pn(e,qe)},Ye=function(n){var e=n.components();pn(e,Ye),ye(n,me())},Ke=function(n,e){$e(n.element(),e.element())},Je=function(e,n){var t,r=e.components();pn((t=e).components(),function(n){return Xe(n.element())}),We(t.element()),t.syncComponents();var o=kn(r,n);pn(o,function(n){qe(n),e.getSystem().removeFromWorld(n)}),pn(n,function(n){n.getSystem().isConnected()?Ke(e,n):(e.getSystem().addToWorld(n),Ke(e,n),_e(e.element())&&Ye(n)),e.syncComponents()})},Qe=function(n,e,t){n.getSystem().addToWorld(e),t(n.element(),e.element()),_e(n.element())&&Ye(e),n.syncComponents()},Ze=function(e){var n,t=He(e.element()).bind(function(n){return e.getSystem().getByDom(n).toOption()});qe(n=e),Xe(n.element()),n.getSystem().removeFromWorld(n),t.each(function(n){n.syncComponents()})},nt=function(n,e,t){t(n,e.element());var r=Ne(e.element());pn(r,function(n){e.getByDom(n).each(Ye)})},et=function(t){return{is:function(n){return t===n},isValue:l,isError:s,getOr:y(t),getOrThunk:y(t),getOrDie:y(t),or:function(n){return et(t)},orThunk:function(n){return et(t)},fold:function(n,e){return e(t)},map:function(n){return et(n(t))},mapError:function(n){return et(t)},each:function(n){n(t)},bind:function(n){return n(t)},exists:function(n){return n(t)},forall:function(n){return n(t)},toOption:function(){return O.some(t)}}},tt=function(t){return{is:s,isValue:s,isError:l,getOr:b,getOrThunk:function(n){return n()},getOrDie:function(){return a(String(t))()},or:function(n){return n},orThunk:function(n){return n()},fold:function(n,e){return n(t)},map:function(n){return tt(t)},mapError:function(n){return tt(n(t))},each:w,bind:function(n){return tt(t)},exists:s,forall:l,toOption:O.none}},rt={value:et,error:tt,fromOption:function(n,e){return n.fold(function(){return tt(e)},et)}},ot=function(u){if(!on(u))throw new Error("cases must be an array");if(0===u.length)throw new Error("there must be at least one case");var c=[],t={};return pn(u,function(n,r){var e=T(n);if(1!==e.length)throw new Error("one and only one name per case");var o=e[0],i=n[o];if(t[o]!==undefined)throw new Error("duplicate key detected:"+o);if("cata"===o)throw new Error("cannot have a case named cata (sorry)");if(!on(i))throw new Error("case arguments must be an array");c.push(o),t[o]=function(){var n=arguments.length;if(n!==i.length)throw new Error("Wrong number of arguments to case "+o+". Expected "+i.length+" ("+i+"), got "+n);for(var t=new Array(n),e=0;e<t.length;e++)t[e]=arguments[e];return{fold:function(){if(arguments.length!==u.length)throw new Error("Wrong number of arguments to fold. Expected "+u.length+", got "+arguments.length);return arguments[r].apply(null,t)},match:function(n){var e=T(n);if(c.length!==e.length)throw new Error("Wrong number of arguments to match. Expected: "+c.join(",")+"\nActual: "+e.join(","));if(!On(c,function(n){return dn(e,n)}))throw new Error("Not all branches were specified when using match. Specified: "+e.join(", ")+"\nRequired: "+c.join(", "));return n[o].apply(null,t)},log:function(n){v.console.log(n,{constructors:c,constructor:o,params:t})}}}}),t},it=Object.prototype.hasOwnProperty,ut=function(u){return function(){for(var n=new Array(arguments.length),e=0;e<n.length;e++)n[e]=arguments[e];if(0===n.length)throw new Error("Can't merge zero objects");for(var t={},r=0;r<n.length;r++){var o=n[r];for(var i in o)it.call(o,i)&&(t[i]=u(t[i],o[i]))}return t}},ct=ut(function(n,e){return rn(n)&&rn(e)?ct(n,e):e}),at=ut(function(n,e){return e});(i=o=o||{})[i.Error=0]="Error",i[i.Value=1]="Value";var ft,st,lt,dt=function(n,e,t){return n.stype===o.Error?e(n.serror):t(n.svalue)},mt=function(n){return{stype:o.Value,svalue:n}},gt=function(n){return{stype:o.Error,serror:n}},pt=function(n){return n.fold(gt,mt)},ht=function(n){return dt(n,rt.error,rt.value)},vt=mt,yt=function(n){var e=[],t=[];return pn(n,function(n){dt(n,function(n){return t.push(n)},function(n){return e.push(n)})}),{values:e,errors:t}},bt=gt,xt=function(n,e){return n.stype===o.Value?e(n.svalue):n},wt=function(n,e){return n.stype===o.Error?e(n.serror):n},St=function(n,e){return n.stype===o.Value?{stype:o.Value,svalue:e(n.svalue)}:n},Ot=function(n,e){return n.stype===o.Error?{stype:o.Error,serror:e(n.serror)}:n},Tt=ot([{strict:[]},{defaultedThunk:["fallbackThunk"]},{asOption:[]},{asDefaultedOptionThunk:["fallbackThunk"]},{mergeWithThunk:["baseThunk"]}]),kt=function(n){return Tt.defaultedThunk(y(n))},Et=Tt.strict,Ct=Tt.asOption,Mt=Tt.defaultedThunk,_t=(Tt.asDefaultedOptionThunk,Tt.mergeWithThunk),Dt=function(n,e){var t;return(t={})[n]=e,t},It=(ot([{bothErrors:["error1","error2"]},{firstError:["error1","value2"]},{secondError:["value1","error2"]},{bothValues:["value1","value2"]}]),function(n,e){return t=e,r={},E(n,function(n,e){dn(t,e)||(r[e]=n)}),r;var t,r}),Ft=function(n,e){return Dt(n,e)},Rt=function(n){return e={},pn(n,function(n){e[n.key]=n.value}),e;var e},Vt=function(n,e){var t,r,o,i,u,c=(t=[],r=[],pn(n,function(n){n.fold(function(n){t.push(n)},function(n){r.push(n)})}),{errors:t,values:r});return 0<c.errors.length?(u=c.errors,rt.error(wn(u))):(i=e,0===(o=c.values).length?rt.value(i):rt.value(ct(i,at.apply(undefined,o))))},Bt=function(n){return d(bt,wn)(n)},At=function(n,e){var t,r,o=yt(n);return 0<o.errors.length?Bt(o.errors):(t=o.values,r=e,0<t.length?vt(ct(r,at.apply(undefined,t))):vt(r))},jt=function(n){var e=yt(n);return 0<e.errors.length?Bt(e.errors):vt(e.values)},Ht=function(n){return rn(n)&&100<T(n).length?" removed due to size":JSON.stringify(n,null,2)},Nt=function(n,e){return bt([{path:n,getErrorInfo:e}])},Pt=ot([{field:["key","okey","presence","prop"]},{state:["okey","instantiator"]}]),zt=function(t,r,o){return I(r,o).fold(function(){return n=o,e=r,Nt(t,function(){return'Could not find valid *strict* value for "'+n+'" in '+Ht(e)});var n,e},vt)},Lt=function(n,e,t){var r=I(n,e).fold(function(){return t(n)},b);return vt(r)},Gt=function(c,a,n,f){return n.fold(function(o,t,n,r){var i=function(n){var e=r.extract(c.concat([o]),f,n);return St(e,function(n){return Dt(t,f(n))})},u=function(n){return n.fold(function(){var n=Dt(t,f(O.none()));return vt(n)},function(n){var e=r.extract(c.concat([o]),f,n);return St(e,function(n){return Dt(t,f(O.some(n)))})})};return n.fold(function(){return xt(zt(c,a,o),i)},function(n){return xt(Lt(a,o,n),i)},function(){return xt(vt(I(a,o)),u)},function(n){return xt((t=n,r=I(e=a,o).map(function(n){return!0===n?t(e):n}),vt(r)),u);var e,t,r},function(n){var e=n(a),t=St(Lt(a,o,y({})),function(n){return ct(e,n)});return xt(t,i)})},function(n,e){var t=e(a);return vt(Dt(n,f(t)))})},$t=function(r){return{extract:function(t,n,e){return wt(r(e,n),function(n){return e=n,Nt(t,function(){return e});var e})},toString:function(){return"val"}}},Ut=function(n){var u=Wt(n),c=vn(n,function(e,n){return n.fold(function(n){return ct(e,Ft(n,!0))},y(e))},{});return{extract:function(n,e,t){var r,o=un(t)?[]:T(_(t,function(n){return n!==undefined&&null!==n})),i=hn(o,function(n){return!R(c,n)});return 0===i.length?u.extract(n,e,t):(r=i,Nt(n,function(){return"There are unsupported fields: ["+r.join(", ")+"] specified"}))},toString:u.toString}},Wt=function(c){return{extract:function(n,e,t){return r=n,o=t,i=e,u=gn(c,function(n){return Gt(r,o,n,i)}),At(u,{});var r,o,i,u},toString:function(){return"obj{\n"+gn(c,function(n){return n.fold(function(n,e,t,r){return n+" -> "+r.toString()},function(n,e){return"state("+n+")"})}).join("\n")+"}"}}},Xt=function(t,i){var u=function(n,e){return o=$t(t),function(t,r,n){var e=gn(n,function(n,e){return o.extract(t.concat(["["+e+"]"]),r,n)});return jt(e)}(n,b,e);var o};return{extract:function(t,r,o){var n=T(o),e=u(t,n);return xt(e,function(n){var e=gn(n,function(n){return Pt.field(n,n,Et(),i)});return Wt(e).extract(t,r,o)})},toString:function(){return"setOf("+i.toString()+")"}}},qt=y($t(vt)),Yt=Pt.state,Kt=Pt.field,Jt=function(t,e,r,o,i){return I(o,i).fold(function(){return n=o,e=i,Nt(t,function(){return'The chosen schema: "'+e+'" did not exist in branches: '+Ht(n)});var n,e},function(n){return n.extract(t.concat(["branch: "+i]),e,r)})},Qt=function(o,i){return{extract:function(e,t,r){return I(r,o).fold(function(){return n=o,Nt(e,function(){return'Choice schema did not contain choice key: "'+n+'"'});var n},function(n){return Jt(e,t,r,i,n)})},toString:function(){return"chooseOn("+o+"). Possible values: "+T(i)}}},Zt=$t(vt),nr=function(e){return $t(function(n){return e(n).fold(bt,vt)})},er=function(e,n){return Xt(function(n){return pt(e(n))},n)},tr=function(n,e,t){return ht((r=n,o=b,i=t,u=e.extract([r],o,i),Ot(u,function(n){return{input:i,errors:n}})));var r,o,i,u},rr=function(n){return n.fold(function(n){throw new Error(ir(n))},b)},or=function(n,e,t){return rr(tr(n,e,t))},ir=function(n){return"Errors: \n"+(e=n.errors,t=10<e.length?e.slice(0,10).concat([{path:[],getErrorInfo:function(){return"... (only showing first ten failures)"}}]):e,gn(t,function(n){return"Failed path: ("+n.path.join(" > ")+")\n"+n.getErrorInfo()}).join("\n"))+"\n\nInput object: "+Ht(n.input);var e,t},ur=function(n,e){return Qt(n,C(e,Wt))},cr=y(Zt),ar=(ft=cn,st="function",$t(function(n){var e=typeof n;return ft(n)?vt(n):bt("Expected type: "+st+" but got: "+e)})),fr=function(n){return Kt(n,n,Et(),qt())},sr=function(n,e){return Kt(n,n,Et(),e)},lr=function(n,e){return Kt(n,n,Et(),Wt(e))},dr=function(n){return Kt(n,n,Ct(),qt())},mr=function(n,e){return Kt(n,n,Ct(),e)},gr=function(n,e){return mr(n,Wt(e))},pr=function(n,e){return mr(n,Ut(e))},hr=function(n,e){return Kt(n,n,kt(e),qt())},vr=function(n,e,t){return Kt(n,n,kt(e),t)},yr=function(n,e){return Yt(n,e)},br=function(n){return cn(n)?n:y(!1)},xr=function(n,e,t){var r=e(n),o=br(t);return r.orThunk(function(){return o(n)?O.none():function(n,e,t){for(var r=n.dom(),o=br(t);r.parentNode;){r=r.parentNode;var i=Te.fromDom(r),u=e(i);if(u.isSome())return u;if(o(i))break}return O.none()}(n,e,o)})},wr=function(n,e){return Ae(n.element(),e.event().target())},Sr=function(n){if(!R(n,"can")&&!R(n,"abort")&&!R(n,"run"))throw new Error("EventHandler defined by: "+JSON.stringify(n,null,2)+" does not have can, abort, or run!");return or("Extracting event.handler",Ut([hr("can",y(!0)),hr("abort",y(!1)),hr("run",w)]),n)},Or=function(t){var e,r,o,i,n=(r=function(n){return n.can},function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return yn(e,function(n,e){return n&&r(e).apply(undefined,t)},!0)}),u=(o=e=t,i=function(n){return n.abort},function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return yn(o,function(n,e){return n||i(e).apply(undefined,t)},!1)});return Sr({can:n,abort:u,run:function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];pn(t,function(n){n.run.apply(undefined,e)})}})},Tr=function(n){return Rt(n)},kr=function(n,e){return{key:n,value:Sr({abort:e})}},Er=function(n,e){return{key:n,value:Sr({run:e})}},Cr=function(n,t,r){return{key:n,value:Sr({run:function(n,e){t.apply(undefined,[n,e].concat(r))}})}},Mr=function(n){return function(t){return{key:n,value:Sr({run:function(n,e){wr(n,e)&&t(n,e)}})}}},_r=function(u,e){return Er(u,function(n,i){n.getSystem().getByUid(e).each(function(n){var e,t,r,o;t=(e=n).element(),r=u,o=i,e.getSystem().triggerEvent(r,t,o.event())})})},Dr=function(n,e,t){var r=e.partUids[t];return _r(n,r)},Ir=function(n){return Er(n,function(n,e){e.cut()})},Fr=Mr(me()),Rr=Mr(ge()),Vr=Mr(de()),Br=(lt=ae(),function(n){return Er(lt,n)}),Ar=function(n){return gn(n,function(n){return r=e="/*",o=(t=n).length-e.length,""===r||t.length>=r.length&&t.substr(o,o+r.length)===r?n.substring(0,n.length-"/*".length):n;var e,t,r,o})},jr=function(n,e){var t=n.toString(),r=t.indexOf(")")+1,o=t.indexOf("("),i=t.substring(o+1,r-1).split(/,\s*/);return n.toFunctionAnnotation=function(){return{name:e,parameters:Ar(i)}},n},Hr=function(n){return{classes:n.classes!==undefined?n.classes:[],attributes:n.attributes!==undefined?n.attributes:{},styles:n.styles!==undefined?n.styles:{}}},Nr=function(t,r,o){return Vr(function(n,e){o(n,t,r)})},Pr=function(o,i,u){var n,e,t,r,c,a;return n=function(t){for(var n=[],e=1;e<arguments.length;e++)n[e-1]=arguments[e];var r=[t].concat(n);return t.config({name:y(o)}).fold(function(){throw new Error("We could not find any behaviour configuration for: "+o+". Using API: "+u)},function(n){var e=Array.prototype.slice.call(r,1);return i.apply(undefined,[t,n.config,n.state].concat(e))})},e=u,t=i.toString(),r=t.indexOf(")")+1,c=t.indexOf("("),a=t.substring(c+1,r-1).split(/,\s*/),n.toFunctionAnnotation=function(){return{name:e,parameters:Ar(a.slice(0,1).concat(a.slice(3)))}},n},zr=function(n){return{key:n,value:undefined}},Lr=function(t,n,r,o,e,i,u){var c=function(n){return R(n,r)?n[r]():O.none()},a=C(e,function(n,e){return Pr(r,n,e)}),f=C(i,function(n,e){return jr(n,e)}),s=x(x(x({},f),a),{revoke:S(zr,r),config:function(n){var e=or(r+"-config",t,n);return{key:r,value:{config:e,me:s,configAsRaw:V(function(){return or(r+"-config",t,n)}),initialConfig:n,state:u}}},schema:function(){return n},exhibit:function(n,t){return c(n).bind(function(e){return I(o,"exhibit").map(function(n){return n(t,e.config,e.state)})}).getOr(Hr({}))},name:function(){return r},handlers:function(n){return c(n).map(function(n){return I(o,"events").getOr(function(){return{}})(n.config,n.state)}).getOr({})}});return s},Gr={init:function(){return $r({readState:function(){return"No State required"}})}},$r=function(n){return n},Ur=function(n){return Rt(n)},Wr=Ut([fr("fields"),fr("name"),hr("active",{}),hr("apis",{}),hr("state",Gr),hr("extra",{})]),Xr=function(n){var e,t,r,o,i,u,c,a,f=or("Creating behaviour: "+n.name,Wr,n);return e=f.fields,t=f.name,r=f.active,o=f.apis,i=f.extra,u=f.state,c=Ut(e),a=gr(t,[pr("config",e)]),Lr(c,a,t,r,o,i,u)},qr=Ut([fr("branchKey"),fr("branches"),fr("name"),hr("active",{}),hr("apis",{}),hr("state",Gr),hr("extra",{})]),Yr=y(undefined),Kr=function(n,e,t){if(!(tn(t)||un(t)||an(t)))throw v.console.error("Invalid call to Attr.set. Key ",e,":: Value ",t,":: Element ",n),new Error("Attribute value was not simple");n.setAttribute(e,t+"")},Jr=function(n,e,t){Kr(n.dom(),e,t)},Qr=function(n,e){var t=n.dom();E(e,function(n,e){Kr(t,e,n)})},Zr=function(n,e){var t=n.dom().getAttribute(e);return null===t?undefined:t},no=function(n,e){return O.from(Zr(n,e))},eo=function(n,e){var t=n.dom();return!(!t||!t.hasAttribute)&&t.hasAttribute(e)},to=function(n,e){n.dom().removeAttribute(e)},ro=function(n,e){var t=Zr(n,e);return t===undefined||""===t?[]:t.split(" ")},oo=function(n){return n.dom().classList!==undefined},io=function(n,e){return o=e,i=ro(t=n,r="class").concat([o]),Jr(t,r,i.join(" ")),!0;var t,r,o,i},uo=function(n,e){return o=e,0<(i=hn(ro(t=n,r="class"),function(n){return n!==o})).length?Jr(t,r,i.join(" ")):to(t,r),!1;var t,r,o,i},co=function(n,e){oo(n)?n.dom().classList.add(e):io(n,e)},ao=function(n){0===(oo(n)?n.dom().classList:ro(n,"class")).length&&to(n,"class")},fo=function(n,e){oo(n)?n.dom().classList.remove(e):uo(n,e);ao(n)},so=function(n,e){return oo(n)&&n.dom().classList.contains(e)},lo=function(n,e,t){fo(n,t),co(n,e)},mo=/* */Object.freeze({__proto__:null,toAlpha:function(n,e,t){lo(n.element(),e.alpha,e.omega)},toOmega:function(n,e,t){lo(n.element(),e.omega,e.alpha)},isAlpha:function(n,e,t){return so(n.element(),e.alpha)},isOmega:function(n,e,t){return so(n.element(),e.omega)},clear:function(n,e,t){fo(n.element(),e.alpha),fo(n.element(),e.omega)}}),go=[fr("alpha"),fr("omega")],po=Xr({fields:go,name:"swapping",apis:mo}),ho=function(n){var e=n;return{get:function(){return e},set:function(n){e=n}}};function vo(n,e,t,r,o){return n(t,r)?O.some(t):cn(o)&&o(t)?O.none():e(t,r,o)}var yo,bo,xo=function(n,e,t){for(var r=n.dom(),o=cn(t)?t:y(!1);r.parentNode;){r=r.parentNode;var i=Te.fromDom(r);if(e(i))return O.some(i);if(o(i))break}return O.none()},wo=function(n){return n.dom().focus()},So=function(n){return n.dom().blur()},Oo=function(n){var e=n!==undefined?n.dom():v.document;return O.from(e.activeElement).map(Te.fromDom)},To=function(e){return Oo(je(e)).filter(function(n){return e.dom().contains(n.dom())})},ko=tinymce.util.Tools.resolve("tinymce.dom.DOMUtils"),Eo=tinymce.util.Tools.resolve("tinymce.ThemeManager"),Co="formatChanged",Mo="orientationChanged",_o="dropupDismissed",Do=function(n){return n.dom().innerHTML},Io=function(n,e){var t,r,o=je(n).dom(),i=Te.fromDom(o.createDocumentFragment()),u=(t=e,(r=(o||v.document).createElement("div")).innerHTML=t,Ne(Te.fromDom(r)));Ue(i,u),We(n),$e(n,i)},Fo=function(n){return e=n,t=!1,Te.fromDom(e.dom().cloneNode(t));var e,t},Ro=function(n){var e,t,r,o=Fo(n);return e=o,t=Te.fromTag("div"),r=Te.fromDom(e.dom().cloneNode(!0)),$e(t,r),Do(t)},Vo=function(n){return Ro(n)},Bo=/* */Object.freeze({__proto__:null,events:function(a){return Tr([Er(ce(),function(o,n){var e,t,i=a.channels,r=T(i),u=n,c=(e=r,(t=u).universal()?e:hn(e,function(n){return dn(t.channels(),n)}));pn(c,function(n){var e=i[n],t=e.schema,r=or("channel["+n+"] data\nReceiver: "+Vo(o.element()),t,u.data());e.onReceive(o,r)})})])}}),Ao="unknown";(bo=yo=yo||{})[bo.STOP=0]="STOP",bo[bo.NORMAL=1]="NORMAL",bo[bo.LOGGING=2]="LOGGING";var jo,Ho=ho({}),No=function(e,n,t){var r,o,i,u;switch(I(Ho.get(),e).orThunk(function(){var n=T(Ho.get());return Cn(n,function(n){return-1<e.indexOf(n)?O.some(Ho.get()[n]):O.none()})}).getOr(yo.NORMAL)){case yo.NORMAL:return t(Lo());case yo.LOGGING:var c=(r=e,o=n,i=[],u=(new Date).getTime(),{logEventCut:function(n,e,t){i.push({outcome:"cut",target:e,purpose:t})},logEventStopped:function(n,e,t){i.push({outcome:"stopped",target:e,purpose:t})},logNoParent:function(n,e,t){i.push({outcome:"no-parent",target:e,purpose:t})},logEventNoHandlers:function(n,e){i.push({outcome:"no-handlers-left",target:e})},logEventResponse:function(n,e,t){i.push({outcome:"response",purpose:t,target:e})},write:function(){var n=(new Date).getTime();dn(["mousemove","mouseover","mouseout",de()],r)||v.console.log(r,{event:r,time:n-u,target:o.dom(),sequence:gn(i,function(n){return dn(["cut","stopped","response"],n.outcome)?"{"+n.purpose+"} "+n.outcome+" at ("+Vo(n.target)+")":n.outcome})})}}),a=t(c);return c.write(),a;case yo.STOP:return!0}},Po=["alloy/data/Fields","alloy/debugging/Debugging"],zo=function(n,e,t){return No(n,e,t)},Lo=y({logEventCut:w,logEventStopped:w,logNoParent:w,logEventNoHandlers:w,logEventResponse:w,write:w}),Go=y([fr("menu"),fr("selectedMenu")]),$o=y([fr("item"),fr("selectedItem")]),Uo=(y(Wt($o().concat(Go()))),y(Wt($o()))),Wo=lr("initSize",[fr("numColumns"),fr("numRows")]),Xo=function(n,e,t){!function(){var n=new Error;if(n.stack===undefined)return;var e=n.stack.split("\n");bn(e,function(e){return 0<e.indexOf("alloy")&&!mn(Po,function(n){return-1<e.indexOf(n)})}).getOr(Ao)}();return Kt(e,e,t,nr(function(t){return rt.value(function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];return t.apply(undefined,n)})}))},qo=function(n){return Xo(0,n,kt(w))},Yo=function(n){return Xo(0,n,kt(O.none))},Ko=function(n){return Xo(0,n,Et())},Jo=function(n){return Xo(0,n,Et())},Qo=function(n,e){return yr(n,y(e))},Zo=function(n){return yr(n,b)},ni=y(Wo),ei=[sr("channels",er(rt.value,Ut([Ko("onReceive"),hr("schema",cr())])))],ti=Xr({fields:ei,name:"receiving",active:Bo}),ri=function(n,e,t){var r=e.aria;r.update(n,r,t.get())},oi=function(e,n,t){n.toggleClass.each(function(n){(t.get()?co:fo)(e.element(),n)})},ii=function(n,e,t){ai(n,e,t,!t.get())},ui=function(n,e,t){t.set(!0),oi(n,e,t),ri(n,e,t)},ci=function(n,e,t){t.set(!1),oi(n,e,t),ri(n,e,t)},ai=function(n,e,t,r){(r?ui:ci)(n,e,t)},fi=function(n,e,t){ai(n,e,t,e.selected)},si=/* */Object.freeze({__proto__:null,onLoad:fi,toggle:ii,isOn:function(n,e,t){return t.get()},on:ui,off:ci,set:ai}),li=/* */Object.freeze({__proto__:null,exhibit:function(){return Hr({})},events:function(n,e){var t,r,o,i=(t=n,r=e,o=ii,Br(function(n){o(n,t,r)})),u=Nr(n,e,fi);return Tr(wn([n.toggleOnExecute?[i]:[],[u]]))}}),di=function(n,e,t){Jr(n.element(),"aria-expanded",t)},mi=[hr("selected",!1),dr("toggleClass"),hr("toggleOnExecute",!0),vr("aria",{mode:"none"},ur("mode",{pressed:[hr("syncWithExpanded",!1),Qo("update",function(n,e,t){Jr(n.element(),"aria-pressed",t),e.syncWithExpanded&&di(n,e,t)})],checked:[Qo("update",function(n,e,t){Jr(n.element(),"aria-checked",t)})],expanded:[Qo("update",di)],selected:[Qo("update",function(n,e,t){Jr(n.element(),"aria-selected",t)})],none:[Qo("update",w)]}))],gi=Xr({fields:mi,name:"toggling",active:li,apis:si,state:(jo=!1,{init:function(){var e=ho(jo);return{get:function(){return e.get()},set:function(n){return e.set(n)},clear:function(){return e.set(jo)},readState:function(){return e.get()}}}})}),pi=function(t,r){return ti.config({channels:Ft(Co,{onReceive:function(n,e){e.command===t&&r(n,e.state)}})})},hi=function(n){return ti.config({channels:Ft(Mo,{onReceive:n})})},vi=function(n,e){return{key:n,value:{onReceive:e}}},yi="tinymce-mobile",bi=function(n){return yi+"-"+n},xi=function(){var n=function(n,e){e.stop(),xe(n)};return[Er(ne(),n),Er(se(),n),Ir(Gn()),Ir(Wn())]},wi=function(n,e){e.ignore||(wo(n.element()),e.onFocus(n))},Si=/* */Object.freeze({__proto__:null,focus:wi,blur:function(n,e){e.ignore||So(n.element())},isFocused:function(n){return e=n.element(),t=je(e).dom(),e.dom()===t.activeElement;var e,t}}),Oi=/* */Object.freeze({__proto__:null,exhibit:function(n,e){var t=e.ignore?{}:{attributes:{tabindex:"-1"}};return Hr(t)},events:function(t){return Tr([Er(oe(),function(n,e){wi(n,t),e.stop()})].concat(t.stopMousedown?[Er(Wn(),function(n,e){e.event().prevent()})]:[]))}}),Ti=[qo("onFocus"),hr("stopMousedown",!1),hr("ignore",!1)],ki=Xr({fields:Ti,name:"focusing",active:Oi,apis:Si}),Ei=function(n){return n.style!==undefined&&cn(n.style.getPropertyValue)},Ci=function(n,e,t){if(!tn(t))throw v.console.error("Invalid call to CSS.set. Property ",e,":: Value ",t,":: Element ",n),new Error("CSS value must be a string: "+t);Ei(n)&&n.style.setProperty(e,t)},Mi=function(n,e,t){var r=n.dom();Ci(r,e,t)},_i=function(n,e){var t=n.dom();E(e,function(n,e){Ci(t,e,n)})},Di=function(n,e){var t=n.dom(),r=v.window.getComputedStyle(t).getPropertyValue(e);return""!==r||_e(n)?r:Ii(t,e)},Ii=function(n,e){return Ei(n)?n.style.getPropertyValue(e):""},Fi=function(n,e){var t=n.dom(),r=Ii(t,e);return O.from(r).filter(function(n){return 0<n.length})},Ri=function(n,e){var t,r,o=n.dom();r=e,Ei(t=o)&&t.style.removeProperty(r),no(n,"style").map(Rn).is("")&&to(n,"style")},Vi=function(n){return n.dom().offsetWidth};function Bi(r,o){var n=function(n){var e=o(n);if(e<=0||null===e){var t=Di(n,r);return parseFloat(t)||0}return e},i=function(o,n){return yn(n,function(n,e){var t=Di(o,e),r=t===undefined?0:parseInt(t,10);return isNaN(r)?n:n+r},0)};return{set:function(n,e){if(!an(e)&&!e.match(/^[0-9]+$/))throw new Error(r+".set accepts only positive integer values. Value was "+e);var t=n.dom();Ei(t)&&(t.style[r]=e+"px")},get:n,getOuter:n,aggregate:i,max:function(n,e,t){var r=i(n,t);return r<e?e-r:0}}}var Ai,ji,Hi=Bi("height",function(n){var e=n.dom();return _e(n)?e.getBoundingClientRect().height:e.offsetHeight}),Ni=function(n){return Hi.get(n)},Pi=function(n,e,t){return hn(function(n,e){for(var t=cn(e)?e:s,r=n.dom(),o=[];null!==r.parentNode&&r.parentNode!==undefined;){var i=r.parentNode,u=Te.fromDom(i);if(o.push(u),!0===t(u))break;r=i}return o}(n,t),e)},zi=function(n,e){return hn(He(t=n).map(Ne).map(function(n){return hn(n,function(n){return!Ae(t,n)})}).getOr([]),e);var t},Li=function(n,e){return Ve(e,n)},Gi=function(n){return Be(n)},$i=function(n,e,t){return xo(n,function(n){return Fe(n,e)},t)},Ui=function(n,e){return Be(e,n)},Wi=function(n,e,t){return vo(function(n,e){return Fe(n,e)},$i,n,e,t)},Xi=function(){return[13]},qi=function(){return[27]},Yi=function(){return[32]},Ki=function(){return[37]},Ji=function(){return[38]},Qi=function(){return[39]},Zi=function(){return[40]},nu=function(n,e,t){var r=Tn(n.slice(0,e)),o=Tn(n.slice(e+1));return bn(r.concat(o),t)},eu=function(n,e,t){var r=Tn(n.slice(0,e));return bn(r,t)},tu=function(n,e,t){var r=n.slice(0,e),o=n.slice(e+1);return bn(o.concat(r),t)},ru=function(n,e,t){var r=n.slice(e+1);return bn(r,t)},ou=function(t){return function(n){var e=n.raw();return dn(t,e.which)}},iu=function(n){return function(e){return On(n,function(n){return n(e)})}},uu=function(n){return!0===n.raw().shiftKey},cu=function(n){return!0===n.raw().ctrlKey},au=m(uu),fu=function(n,e){return{matches:n,classification:e}},su=function(n){for(var e=[],t=function(n){e.push(n)},r=0;r<n.length;r++)n[r].each(t);return e},lu=function(n,e,t,r){var o=n+e;return r<o?t:o<t?r:o},du=function(n,e,t){return Math.min(Math.max(n,e),t)},mu=function(t,r,n,o){var e=Li(t.element(),"."+r.highlightClass);pn(e,function(e){mn(o,function(n){return n.element()===e})||(fo(e,r.highlightClass),t.getSystem().getByDom(e).each(function(n){r.onDehighlight(t,n),ye(n,ve())}))})},gu=function(n,e,t,r){mu(n,e,0,[r]),pu(n,e,t,r)||(co(r.element(),e.highlightClass),e.onHighlight(n,r),ye(r,he()))},pu=function(n,e,t,r){return so(r.element(),e.highlightClass)},hu=function(n,e,t,r){var o=Li(n.element(),"."+e.itemClass);return O.from(o[r]).fold(function(){return rt.error("No element found with index "+r)},n.getSystem().getByDom)},vu=function(e,n,t){return Ui(e.element(),"."+n.itemClass).bind(function(n){return e.getSystem().getByDom(n).toOption()})},yu=function(e,n,t){var r=Li(e.element(),"."+n.itemClass);return(0<r.length?O.some(r[r.length-1]):O.none()).bind(function(n){return e.getSystem().getByDom(n).toOption()})},bu=function(t,e,n,r){var o=Li(t.element(),"."+e.itemClass);return xn(o,function(n){return so(n,e.highlightClass)}).bind(function(n){var e=lu(n,r,0,o.length-1);return t.getSystem().getByDom(o[e]).toOption()})},xu=function(e,n,t){var r=Li(e.element(),"."+n.itemClass);return su(gn(r,function(n){return e.getSystem().getByDom(n).toOption()}))},wu=/* */Object.freeze({__proto__:null,dehighlightAll:function(n,e,t){return mu(n,e,0,[])},dehighlight:function(n,e,t,r){pu(n,e,t,r)&&(fo(r.element(),e.highlightClass),e.onDehighlight(n,r),ye(r,ve()))},highlight:gu,highlightFirst:function(e,t,r){vu(e,t).each(function(n){gu(e,t,r,n)})},highlightLast:function(e,t,r){yu(e,t).each(function(n){gu(e,t,r,n)})},highlightAt:function(e,t,r,n){hu(e,t,r,n).fold(function(n){throw new Error(n)},function(n){gu(e,t,r,n)})},highlightBy:function(e,t,r,n){var o=xu(e,t);bn(o,n).each(function(n){gu(e,t,r,n)})},isHighlighted:pu,getHighlighted:function(e,n,t){return Ui(e.element(),"."+n.highlightClass).bind(function(n){return e.getSystem().getByDom(n).toOption()})},getFirst:vu,getLast:yu,getPrevious:function(n,e,t){return bu(n,e,0,-1)},getNext:function(n,e,t){return bu(n,e,0,1)},getCandidates:xu}),Su=[fr("highlightClass"),fr("itemClass"),qo("onHighlight"),qo("onDehighlight")],Ou=Xr({fields:Su,name:"highlighting",apis:wu}),Tu=function(n,e,t){e.exists(function(e){return t.exists(function(n){return Ae(n,e)})})||be(n,pe(),{prevFocus:e,newFocus:t})},ku=function(){var o=function(n){return To(n.element())};return{get:o,set:function(n,e){var t=o(n);n.getSystem().triggerFocus(e,n.element());var r=o(n);Tu(n,t,r)}}};(ji=Ai=Ai||{}).OnFocusMode="onFocus",ji.OnEnterOrSpaceMode="onEnterOrSpace",ji.OnApiMode="onApi";var Eu,Cu,Mu,_u,Du,Iu,Fu,Ru,Vu,Bu,Au=function(n,e,t,r,c){var a=function(e,t,n,r,o){var i,u,c=n(e,t,r,o);return i=c,u=t.event(),bn(i,function(n){return n.matches(u)}).map(function(n){return n.classification}).bind(function(n){return n(e,t,r,o)})},o={schema:function(){return n.concat([hr("focusManager",ku()),vr("focusInside","onFocus",nr(function(n){return dn(["onFocus","onEnterOrSpace","onApi"],n)?rt.value(n):rt.error("Invalid value for focusInside")})),Qo("handler",o),Qo("state",e),Qo("sendFocusIn",c)])},processKey:a,toEvents:function(i,u){var n=i.focusInside!==Ai.OnFocusMode?O.none():c(i).map(function(t){return Er(oe(),function(n,e){t(n,i,u),e.stop()})}),e=[Er(Kn(),function(r,o){a(r,o,t,i,u).fold(function(){var e,t,n;e=r,t=o,n=ou(Yi().concat(Xi()))(t.event()),i.focusInside===Ai.OnEnterOrSpaceMode&&n&&wr(e,t)&&c(i).each(function(n){n(e,i,u),t.stop()})},function(n){o.stop()})}),Er(Jn(),function(n,e){a(n,e,r,i,u).each(function(n){e.stop()})})];return Tr(n.toArray().concat(e))}};return o},ju=function(n){var e=[dr("onEscape"),dr("onEnter"),hr("selector",'[data-alloy-tabstop="true"]:not(:disabled)'),hr("firstTabstop",0),hr("useTabstopAt",y(!0)),dr("visibilitySelector")].concat([n]),u=function(n,e){var t=n.visibilitySelector.bind(function(n){return Wi(e,n)}).getOr(e);return 0<Ni(t)},t=function(e,t,n){var r,o,i;r=t,o=Li(e.element(),r.selector),i=hn(o,function(n){return u(r,n)}),O.from(i[r.firstTabstop]).each(function(n){t.focusManager.set(e,n)})},c=function(e,n,t,r,o){return o(n,t,function(n){return u(e=r,t=n)&&e.useTabstopAt(t);var e,t}).fold(function(){return r.cyclic?O.some(!0):O.none()},function(n){return r.focusManager.set(e,n),O.some(!0)})},o=function(e,n,t,r){var o,i,u=Li(e.element(),t.selector);return o=e,(i=t).focusManager.get(o).bind(function(n){return Wi(n,i.selector)}).bind(function(n){return xn(u,S(Ae,n)).bind(function(n){return c(e,u,n,t,r)})})},r=y([fu(iu([uu,ou([9])]),function(n,e,t){var r=t.cyclic?nu:eu;return o(n,0,t,r)}),fu(ou([9]),function(n,e,t){var r=t.cyclic?tu:ru;return o(n,0,t,r)}),fu(ou(qi()),function(e,t,n){return n.onEscape.bind(function(n){return n(e,t)})}),fu(iu([au,ou(Xi())]),function(e,t,n){return n.onEnter.bind(function(n){return n(e,t)})})]),i=y([]);return Au(e,Gr.init,r,i,function(){return O.some(t)})},Hu=ju(yr("cyclic",y(!1))),Nu=ju(yr("cyclic",y(!0))),Pu=function(n){return"input"===ke(n)&&"radio"!==Zr(n,"type")||"textarea"===ke(n)},zu=function(n,e,t){return Pu(t)&&ou(Yi())(e.event())?O.none():(we(n,t,ae()),O.some(!0))},Lu=function(n,e){return O.some(!0)},Gu=[hr("execute",zu),hr("useSpace",!1),hr("useEnter",!0),hr("useControlEnter",!1),hr("useDown",!1)],$u=function(n,e,t){return t.execute(n,e,n.element())},Uu=Au(Gu,Gr.init,function(n,e,t,r){var o=t.useSpace&&!Pu(n.element())?Yi():[],i=t.useEnter?Xi():[],u=t.useDown?Zi():[],c=o.concat(i).concat(u);return[fu(ou(c),$u)].concat(t.useControlEnter?[fu(iu([cu,ou(Xi())]),$u)]:[])},function(n,e,t,r){return t.useSpace&&!Pu(n.element())?[fu(ou(Yi()),Lu)]:[]},function(){return O.none()}),Wu=function(){var t=ho(O.none());return $r({readState:function(){return t.get().map(function(n){return{numRows:String(n.numRows),numColumns:String(n.numColumns)}}).getOr({numRows:"?",numColumns:"?"})},setGridSize:function(n,e){t.set(O.some({numRows:n,numColumns:e}))},getNumRows:function(){return t.get().map(function(n){return n.numRows})},getNumColumns:function(){return t.get().map(function(n){return n.numColumns})}})},Xu=/* */Object.freeze({__proto__:null,flatgrid:Wu,init:function(n){return n.state(n)}}),qu=function(e,t){return function(n){return"rtl"===Yu(n)?t:e}},Yu=function(n){return"rtl"===Di(n,"direction")?"rtl":"ltr"},Ku=function(i){return function(n,e,t,r){var o=i(n.element());return nc(o,n,e,t,r)}},Ju=function(n,e){var t=qu(n,e);return Ku(t)},Qu=function(n,e){var t=qu(e,n);return Ku(t)},Zu=function(o){return function(n,e,t,r){return nc(o,n,e,t,r)}},nc=function(e,t,n,r,o){return r.focusManager.get(t).bind(function(n){return e(t.element(),n,r,o)}).map(function(n){return r.focusManager.set(t,n),!0})},ec=Zu,tc=Zu,rc=Zu,oc=function(n){return!((e=n.dom()).offsetWidth<=0&&e.offsetHeight<=0);var e},ic=function(n,e,t){var r,o=Li(n,t),i=hn(o,oc);return xn(r=i,function(n){return Ae(n,e)}).map(function(n){return{index:y(n),candidates:y(r)}})},uc=function(n,e){return xn(n,function(n){return Ae(e,n)})},cc=function(t,n,r,e){return e(Math.floor(n/r),n%r).bind(function(n){var e=n.row()*r+n.column();return 0<=e&&e<t.length?O.some(t[e]):O.none()})},ac=function(o,n,i,u,c){return cc(o,n,u,function(n,e){var t=n===i-1?o.length-n*u:u,r=lu(e,c,0,t-1);return O.some({row:y(n),column:y(r)})})},fc=function(i,n,u,c,a){return cc(i,n,c,function(n,e){var t=lu(n,a,0,u-1),r=t===u-1?i.length-t*c:c,o=du(e,0,r-1);return O.some({row:y(t),column:y(o)})})},sc=[fr("selector"),hr("execute",zu),Yo("onEscape"),hr("captureTab",!1),ni()],lc=function(e,t,n){Ui(e.element(),t.selector).each(function(n){t.focusManager.set(e,n)})},dc=function(o){return function(n,e,t,r){return ic(n,e,t.selector).bind(function(n){return o(n.candidates(),n.index(),r.getNumRows().getOr(t.initSize.numRows),r.getNumColumns().getOr(t.initSize.numColumns))})}},mc=function(n,e,t){return t.captureTab?O.some(!0):O.none()},gc=dc(function(n,e,t,r){return ac(n,e,t,r,-1)}),pc=dc(function(n,e,t,r){return ac(n,e,t,r,1)}),hc=dc(function(n,e,t,r){return fc(n,e,t,r,-1)}),vc=dc(function(n,e,t,r){return fc(n,e,t,r,1)}),yc=y([fu(ou(Ki()),Ju(gc,pc)),fu(ou(Qi()),Qu(gc,pc)),fu(ou(Ji()),ec(hc)),fu(ou(Zi()),tc(vc)),fu(iu([uu,ou([9])]),mc),fu(iu([au,ou([9])]),mc),fu(ou(qi()),function(n,e,t){return t.onEscape(n,e)}),fu(ou(Yi().concat(Xi())),function(e,t,r,n){return o=e,(i=r).focusManager.get(o).bind(function(n){return Wi(n,i.selector)}).bind(function(n){return r.execute(e,t,n)});var o,i})]),bc=y([fu(ou(Yi()),Lu)]),xc=Au(sc,Wu,yc,bc,function(){return O.some(lc)}),wc=function(n,e,t,i){var u=function(n,e,t){var r,o=lu(e,i,0,t.length-1);return o===n?O.none():(r=t[o],"button"===ke(r)&&"disabled"===Zr(r,"disabled")?u(n,o,t):O.from(t[o]))};return ic(n,t,e).bind(function(n){var e=n.index(),t=n.candidates();return u(e,e,t)})},Sc=[fr("selector"),hr("getInitial",O.none),hr("execute",zu),Yo("onEscape"),hr("executeOnMove",!1),hr("allowVertical",!0)],Oc=function(e,t,r){return n=e,(o=r).focusManager.get(n).bind(function(n){return Wi(n,o.selector)}).bind(function(n){return r.execute(e,t,n)});var n,o},Tc=function(e,t,n){t.getInitial(e).orThunk(function(){return Ui(e.element(),t.selector)}).each(function(n){t.focusManager.set(e,n)})},kc=function(n,e,t){return wc(n,t.selector,e,-1)},Ec=function(n,e,t){return wc(n,t.selector,e,1)},Cc=function(o){return function(n,e,t,r){return o(n,e,t,r).bind(function(){return t.executeOnMove?Oc(n,e,t):O.some(!0)})}},Mc=function(n,e,t){return t.onEscape(n,e)},_c=y([fu(ou(Yi()),Lu)]),Dc=Au(Sc,Gr.init,function(n,e,t,r){var o=Ki().concat(t.allowVertical?Ji():[]),i=Qi().concat(t.allowVertical?Zi():[]);return[fu(ou(o),Cc(Ju(kc,Ec))),fu(ou(i),Cc(Qu(kc,Ec))),fu(ou(Xi()),Oc),fu(ou(Yi()),Oc),fu(ou(qi()),Mc)]},_c,function(){return O.some(Tc)}),Ic=function(n,e,t){return O.from(n[e]).bind(function(n){return O.from(n[t]).map(function(n){return{rowIndex:e,columnIndex:t,cell:n}})})},Fc=function(n,e,t,r){var o=n[e].length,i=lu(t,r,0,o-1);return Ic(n,e,i)},Rc=function(n,e,t,r){var o=lu(t,r,0,n.length-1),i=n[o].length,u=du(e,0,i-1);return Ic(n,o,u)},Vc=function(n,e,t,r){var o=n[e].length,i=du(t+r,0,o-1);return Ic(n,e,i)},Bc=function(n,e,t,r){var o=du(t+r,0,n.length-1),i=n[o].length,u=du(e,0,i-1);return Ic(n,o,u)},Ac=[lr("selectors",[fr("row"),fr("cell")]),hr("cycles",!0),hr("previousSelector",O.none),hr("execute",zu)],jc=function(e,t,n){t.previousSelector(e).orThunk(function(){var n=t.selectors;return Ui(e.element(),n.cell)}).each(function(n){t.focusManager.set(e,n)})},Hc=function(n,e){return function(t,r,i){var u=i.cycles?n:e;return Wi(r,i.selectors.row).bind(function(n){var e=Li(n,i.selectors.cell);return uc(e,r).bind(function(r){var o=Li(t,i.selectors.row);return uc(o,n).bind(function(n){var e,t=(e=i,gn(o,function(n){return Li(n,e.selectors.cell)}));return u(t,n,r).map(function(n){return n.cell})})})})}},Nc=Hc(function(n,e,t){return Fc(n,e,t,-1)},function(n,e,t){return Vc(n,e,t,-1)}),Pc=Hc(function(n,e,t){return Fc(n,e,t,1)},function(n,e,t){return Vc(n,e,t,1)}),zc=Hc(function(n,e,t){return Rc(n,t,e,-1)},function(n,e,t){return Bc(n,t,e,-1)}),Lc=Hc(function(n,e,t){return Rc(n,t,e,1)},function(n,e,t){return Bc(n,t,e,1)}),Gc=y([fu(ou(Ki()),Ju(Nc,Pc)),fu(ou(Qi()),Qu(Nc,Pc)),fu(ou(Ji()),ec(zc)),fu(ou(Zi()),tc(Lc)),fu(ou(Yi().concat(Xi())),function(e,t,r){return To(e.element()).bind(function(n){return r.execute(e,t,n)})})]),$c=y([fu(ou(Yi()),Lu)]),Uc=Au(Ac,Gr.init,Gc,$c,function(){return O.some(jc)}),Wc=[fr("selector"),hr("execute",zu),hr("moveOnTab",!1)],Xc=function(e,t,r){return r.focusManager.get(e).bind(function(n){return r.execute(e,t,n)})},qc=function(e,t,n){Ui(e.element(),t.selector).each(function(n){t.focusManager.set(e,n)})},Yc=function(n,e,t){return wc(n,t.selector,e,-1)},Kc=function(n,e,t){return wc(n,t.selector,e,1)},Jc=y([fu(ou(Ji()),rc(Yc)),fu(ou(Zi()),rc(Kc)),fu(iu([uu,ou([9])]),function(n,e,t,r){return t.moveOnTab?rc(Yc)(n,e,t,r):O.none()}),fu(iu([au,ou([9])]),function(n,e,t,r){return t.moveOnTab?rc(Kc)(n,e,t,r):O.none()}),fu(ou(Xi()),Xc),fu(ou(Yi()),Xc)]),Qc=y([fu(ou(Yi()),Lu)]),Zc=Au(Wc,Gr.init,Jc,Qc,function(){return O.some(qc)}),na=[Yo("onSpace"),Yo("onEnter"),Yo("onShiftEnter"),Yo("onLeft"),Yo("onRight"),Yo("onTab"),Yo("onShiftTab"),Yo("onUp"),Yo("onDown"),Yo("onEscape"),hr("stopSpaceKeyup",!1),dr("focusIn")],ea=Au(na,Gr.init,function(n,e,t){return[fu(ou(Yi()),t.onSpace),fu(iu([au,ou(Xi())]),t.onEnter),fu(iu([uu,ou(Xi())]),t.onShiftEnter),fu(iu([uu,ou([9])]),t.onShiftTab),fu(iu([au,ou([9])]),t.onTab),fu(ou(Ji()),t.onUp),fu(ou(Zi()),t.onDown),fu(ou(Ki()),t.onLeft),fu(ou(Qi()),t.onRight),fu(ou(Yi()),t.onSpace),fu(ou(qi()),t.onEscape)]},function(n,e,t){return t.stopSpaceKeyup?[fu(ou(Yi()),Lu)]:[]},function(n){return n.focusIn}),ta=Hu.schema(),ra=Nu.schema(),oa=Dc.schema(),ia=xc.schema(),ua=Uc.schema(),ca=Uu.schema(),aa=Zc.schema(),fa=ea.schema(),sa=(Bu=or("Creating behaviour: "+(Eu={branchKey:"mode",branches:/* */Object.freeze({__proto__:null,acyclic:ta,cyclic:ra,flow:oa,flatgrid:ia,matrix:ua,execution:ca,menu:aa,special:fa}),name:"keying",active:{events:function(n,e){return n.handler.toEvents(n,e)}},apis:{focusIn:function(e,t,r){t.sendFocusIn(t).fold(function(){e.getSystem().triggerFocus(e.element(),e.element())},function(n){n(e,t,r)})},setGridSize:function(n,e,t,r,o){R(t,"setGridSize")?t.setGridSize(r,o):v.console.error("Layout does not support setGridSize")}},state:Xu}).name,qr,Eu),Cu=ur(Bu.branchKey,Bu.branches),Mu=Bu.name,_u=Bu.active,Du=Bu.apis,Iu=Bu.extra,Fu=Bu.state,Vu=gr(Mu,[mr("config",Ru=Cu)]),Lr(Ru,Vu,Mu,_u,Du,Iu,Fu)),la=function(r,n){return e=r,t={},o=gn(n,function(n){return e=n.name(),t="Cannot configure "+n.name()+" for "+r,Kt(e,e,Ct(),$t(function(n){return bt("The field: "+e+" is forbidden. "+t)}));var e,t}).concat([yr("dump",b)]),vr(e,t,Wt(o));var e,t,o},da=function(n){return n.dump},ma=function(n,e){return x(x({},n.dump),Ur(e))},ga=la,pa=ma,ha="placeholder",va=ot([{single:["required","valueThunk"]},{multiple:["required","valueThunks"]}]),ya=function(n){return F(n,"uiType")},ba=function(n,e,t,r){return ya(t)&&t.uiType===ha?(i=t,u=r,(o=n).exists(function(n){return n!==i.owner})?va.single(!0,y(i)):I(u,i.name).fold(function(){throw new Error("Unknown placeholder component: "+i.name+"\nKnown: ["+T(u)+"]\nNamespace: "+o.getOr("none")+"\nSpec: "+JSON.stringify(i,null,2))},function(n){return n.replace()})):va.single(!1,y(t));var o,i,u},xa=function(i,u,c,a){return ba(i,0,c,a).fold(function(n,e){var t=ya(c)?e(u,c.config,c.validated):e(u),r=I(t,"components").getOr([]),o=Sn(r,function(n){return xa(i,u,n,a)});return[x(x({},t),{components:o})]},function(n,e){if(ya(c)){var t=e(u,c.config,c.validated);return c.validated.preprocess.getOr(b)(t)}return e(u)})},wa=function(e,t,n,r){var o,i,u,c=C(r,function(n,e){return r=n,o=!1,{name:y(t=e),required:function(){return r.fold(function(n,e){return n},function(n,e){return n})},used:function(){return o},replace:function(){if(o)throw new Error("Trying to use the same placeholder more than once: "+t);return o=!0,r}};var t,r,o}),a=(o=e,i=t,u=c,Sn(n,function(n){return xa(o,i,n,u)}));return E(c,function(n){if(!1===n.used()&&n.required())throw new Error("Placeholder: "+n.name()+" was not found in components list\nNamespace: "+e.getOr("none")+"\nComponents: "+JSON.stringify(t.components,null,2))}),a},Sa=va.single,Oa=va.multiple,Ta=y(ha),ka=0,Ea=function(n){var e=(new Date).getTime();return n+"_"+Math.floor(1e9*Math.random())+ ++ka+String(e)},Ca=ot([{required:["data"]},{external:["data"]},{optional:["data"]},{group:["data"]}]),Ma=hr("factory",{sketch:b}),_a=hr("schema",[]),Da=fr("name"),Ia=Kt("pname","pname",Mt(function(n){return"<alloy."+Ea(n.name)+">"}),cr()),Fa=yr("schema",function(){return[dr("preprocess")]}),Ra=hr("defaults",y({})),Va=hr("overrides",y({})),Ba=Wt([Ma,_a,Da,Ia,Ra,Va]),Aa=Wt([Ma,_a,Da,Ra,Va]),ja=Wt([Ma,_a,Da,Ia,Ra,Va]),Ha=Wt([Ma,Fa,Da,fr("unit"),Ia,Ra,Va]),Na=function(n){var e=function(n){return n.name};return n.fold(e,e,e,e)},Pa=function(t,r){return function(n){var e=or("Converting part type",r,n);return t(e)}},za=Pa(Ca.required,Ba),La=(Pa(Ca.external,Aa),Pa(Ca.optional,ja)),Ga=Pa(Ca.group,Ha),$a=y("entirety"),Ua=function(n,e,t,r){return ct(e.defaults(n,t,r),t,{uid:n.partUids[e.name]},e.overrides(n,t,r))},Wa=function(o,n){var e={};return pn(n,function(n){n.fold(O.some,O.none,O.some,O.some).each(function(t){var r=Xa(o,t.pname);e[t.name]=function(n){var e=or("Part: "+t.name+" in "+o,Wt(t.schema),n);return x(x({},r),{config:n,validated:e})}})}),e},Xa=function(n,e){return{uiType:Ta(),owner:n,name:e}},qa=function(n,e,t){return r=e,i={},o={},pn(t,function(n){n.fold(function(r){i[r.pname]=Sa(!0,function(n,e,t){return r.factory.sketch(Ua(n,r,e,t))})},function(n){var e=r.parts[n.name];o[n.name]=y(n.factory.sketch(Ua(r,n,e[$a()]),e))},function(r){i[r.pname]=Sa(!1,function(n,e,t){return r.factory.sketch(Ua(n,r,e,t))})},function(o){i[o.pname]=Oa(!0,function(e,n,t){var r=e[o.name];return gn(r,function(n){return o.factory.sketch(ct(o.defaults(e,n,t),n,o.overrides(e,n)))})})})}),{internals:y(i),externals:y(o)};var r,i,o},Ya=function(n,e,t){return wa(O.some(n),e,e.components,t)},Ka=function(n,e,t){var r=e.partUids[t];return n.getSystem().getByUid(r).toOption()},Ja=function(n,e,t){return Ka(n,e,t).getOrDie("Could not find part: "+t)},Qa=function(e,n){var t=gn(n,Na);return Rt(gn(t,function(n){return{key:n,value:e+"-"+n}}))},Za=function(e){return Kt("partUids","partUids",_t(function(n){return Qa(n.uid,e)}),cr())},nf=Ea("alloy-premade"),ef=function(n){return Ft(nf,n)},tf=function(r){return n=function(n){for(var e=[],t=1;t<arguments.length;t++)e[t-1]=arguments[t];return r.apply(void 0,u([n.getApis(),n],e))},e=r.toString(),t=e.indexOf(")")+1,o=e.indexOf("("),i=e.substring(o+1,t-1).split(/,\s*/),n.toFunctionAnnotation=function(){return{name:"OVERRIDE",parameters:Ar(i.slice(1))}},n;var n,e,t,o,i},rf=y("alloy-id-"),of=y("data-alloy-id"),uf=rf(),cf=of(),af=function(n,e){Object.defineProperty(n.dom(),cf,{value:e,writable:!0})},ff=function(n){var e=Ce(n)?n.dom()[cf]:null;return O.from(e)},sf=function(n){return Ea(n)},lf=function(n,e,t,r,o){var i,u,c=(u=o,(0<(i=r).length?[lr("parts",i)]:[]).concat([fr("uid"),hr("dom",{}),hr("components",[]),Zo("originalSpec"),hr("debug.sketcher",{})]).concat(u));return or(n+" [SpecSchema]",Ut(c.concat(e)),t)},df=function(n,e,t,r,o){var i=mf(o),u=Sn(t,function(n){return n.fold(O.none,O.some,O.none,O.none).map(function(n){return lr(n.name,n.schema.concat([Zo($a())]))}).toArray()}),c=Za(t),a=lf(n,e,i,u,[c]),f=qa(0,a,t);return r(a,Ya(n,a,f.internals()),i,f.externals())},mf=function(n){return F(n,"uid")?n:x(x({},n),{uid:sf("uid")})};var gf,pf,hf,vf=Ut([fr("name"),fr("factory"),fr("configFields"),hr("apis",{}),hr("extraApis",{})]),yf=Ut([fr("name"),fr("factory"),fr("configFields"),fr("partFields"),hr("apis",{}),hr("extraApis",{})]),bf=function(n){var i=or("Sketcher for "+n.name,vf,n),e=C(i.apis,tf),t=C(i.extraApis,function(n,e){return jr(n,e)});return x(x({name:y(i.name),configFields:y(i.configFields),sketch:function(n){return e=i.name,t=i.configFields,r=i.factory,o=mf(n),r(lf(e,t,o,[],[]),o);var e,t,r,o}},e),t)},xf=function(n){var e=or("Sketcher for "+n.name,yf,n),t=Wa(e.name,e.partFields),r=C(e.apis,tf),o=C(e.extraApis,function(n,e){return jr(n,e)});return x(x({name:y(e.name),partFields:y(e.partFields),configFields:y(e.configFields),sketch:function(n){return df(e.name,e.configFields,e.partFields,e.factory,n)},parts:y(t)},r),o)},wf=bf({name:"Button",factory:function(n){var e,t=(e=n.action,Tr(wn([e.map(function(t){return Br(function(n,e){t(n),e.stop()})}).toArray(),xi()]))),r=n.dom.tag,o=function(e){return I(n.dom,"attributes").bind(function(n){return I(n,e)})};return{uid:n.uid,dom:n.dom,components:n.components,events:t,behaviours:pa(n.buttonBehaviours,[ki.config({}),sa.config({mode:"execution",useSpace:!0,useEnter:!0})]),domModification:{attributes:function(){if("button"!==r)return{role:o("role").getOr("button")};var n=o("type").getOr("button"),e=o("role").map(function(n){return{role:n}}).getOr({});return x({type:n},e)}()},eventOrder:n.eventOrder}},configFields:[hr("uid",undefined),fr("dom"),hr("components",[]),ga("buttonBehaviours",[ki,sa]),dr("action"),dr("role"),hr("eventOrder",{})]}),Sf=Xr({fields:[],name:"unselecting",active:/* */Object.freeze({__proto__:null,events:function(){return Tr([kr(te(),y(!0))])},exhibit:function(){return Hr({styles:{"-webkit-user-select":"none","user-select":"none","-ms-user-select":"none","-moz-user-select":"-moz-none"},attributes:{unselectable:"on"}})}})}),Of=function(n){var e,t,r,o=Te.fromHtml(n),i=Ne(o),u=(t=(e=o).dom().attributes!==undefined?e.dom().attributes:[],yn(t,function(n,e){var t;return"class"===e.name?n:x(x({},n),((t={})[e.name]=e.value,t))},{})),c=(r=o,Array.prototype.slice.call(r.dom().classList,0)),a=0===i.length?{}:{innerHtml:Do(o)};return x({tag:ke(o),classes:c,attributes:u},a)},Tf=function(n){var e=In(n,{prefix:yi});return Of(e)},kf=function(n){return{dom:Tf(n)}},Ef=function(n){return Ur([gi.config({toggleClass:bi("toolbar-button-selected"),toggleOnExecute:!1,aria:{mode:"pressed"}}),pi(n,function(n,e){(e?gi.on:gi.off)(n)})])},Cf=function(n,e,t,r){var o=Ef(t);return _f(e,r,o,n)},Mf=function(n,e){var t=e.ui.registry.getAll().icons;return O.from(t[n]).fold(function(){return Tf('<span class="${prefix}-toolbar-button ${prefix}-toolbar-group-item ${prefix}-icon-'+n+' ${prefix}-icon"></span>')},function(n){return Tf('<span class="${prefix}-toolbar-button ${prefix}-toolbar-group-item">'+n+"</span>")})},_f=function(n,e,t,r){return wf.sketch({dom:Mf(n,r),action:e,buttonBehaviours:ct(Ur([Sf.config({})]),t)})},Df=La({schema:[fr("dom")],name:"label"}),If=function(e){return La({name:e+"-edge",overrides:function(n){return n.model.manager.edgeActions[e].fold(function(){return{}},function(r){return{events:Tr([Cr(Gn(),function(n,e,t){return r(n,t)},[n]),Cr(Wn(),function(n,e,t){return r(n,t)},[n]),Cr(Xn(),function(n,e,t){t.mouseIsDown.get()&&r(n,t)},[n])])}})}})},Ff=If("top-left"),Rf=If("top"),Vf=If("top-right"),Bf=If("right"),Af=If("bottom-right"),jf=If("bottom"),Hf=If("bottom-left"),Nf=[Df,If("left"),Bf,Rf,jf,Ff,Vf,Hf,Af,za({name:"thumb",defaults:y({dom:{styles:{position:"absolute"}}}),overrides:function(n){return{events:Tr([Dr(Gn(),n,"spectrum"),Dr($n(),n,"spectrum"),Dr(Un(),n,"spectrum"),Dr(Wn(),n,"spectrum"),Dr(Xn(),n,"spectrum"),Dr(qn(),n,"spectrum")])}}}),za({schema:[yr("mouseIsDown",function(){return ho(!1)})],name:"spectrum",overrides:function(t){var r=t.model.manager,o=function(e,n){return r.getValueFromEvent(n).map(function(n){return r.setValueFrom(e,t,n)})};return{behaviours:Ur([sa.config({mode:"special",onLeft:function(n){return r.onLeft(n,t)},onRight:function(n){return r.onRight(n,t)},onUp:function(n){return r.onUp(n,t)},onDown:function(n){return r.onDown(n,t)}}),ki.config({})]),events:Tr([Er(Gn(),o),Er($n(),o),Er(Wn(),o),Er(Xn(),function(n,e){t.mouseIsDown.get()&&o(n,e)})])}}})],Pf=function(n,e,t){e.store.manager.onLoad(n,e,t)},zf=function(n,e,t){e.store.manager.onUnload(n,e,t)},Lf=/* */Object.freeze({__proto__:null,onLoad:Pf,onUnload:zf,setValue:function(n,e,t,r){e.store.manager.setValue(n,e,t,r)},getValue:function(n,e,t){return e.store.manager.getValue(n,e,t)},getState:function(n,e,t){return t}}),Gf=/* */Object.freeze({__proto__:null,events:function(t,r){var n=t.resetOnDom?[Fr(function(n,e){Pf(n,t,r)}),Rr(function(n,e){zf(n,t,r)})]:[Nr(t,r,Pf)];return Tr(n)}}),$f=function(){var n=ho(null);return $r({set:n.set,get:n.get,isNotSet:function(){return null===n.get()},clear:function(){n.set(null)},readState:function(){return{mode:"memory",value:n.get()}}})},Uf=function(){var i=ho({}),u=ho({});return $r({readState:function(){return{mode:"dataset",dataByValue:i.get(),dataByText:u.get()}},lookup:function(n){return I(i.get(),n).orThunk(function(){return I(u.get(),n)})},update:function(n){var e=i.get(),t=u.get(),r={},o={};pn(n,function(e){r[e.value]=e,I(e,"meta").each(function(n){I(n,"text").each(function(n){o[n]=e})})}),i.set(x(x({},e),r)),u.set(x(x({},t),o))},clear:function(){i.set({}),u.set({})}})},Wf=/* */Object.freeze({__proto__:null,memory:$f,dataset:Uf,manual:function(){return $r({readState:function(){}})},init:function(n){return n.store.manager.state(n)}}),Xf=function(n,e,t,r){var o=e.store;t.update([r]),o.setValue(n,r),e.onSetValue(n,r)},qf=[dr("initialValue"),fr("getFallbackEntry"),fr("getDataKey"),fr("setValue"),Qo("manager",{setValue:Xf,getValue:function(n,e,t){var r=e.store,o=r.getDataKey(n);return t.lookup(o).fold(function(){return r.getFallbackEntry(o)},function(n){return n})},onLoad:function(e,t,r){t.store.initialValue.each(function(n){Xf(e,t,r,n)})},onUnload:function(n,e,t){t.clear()},state:Uf})],Yf=[fr("getValue"),hr("setValue",w),dr("initialValue"),Qo("manager",{setValue:function(n,e,t,r){e.store.setValue(n,r),e.onSetValue(n,r)},getValue:function(n,e,t){return e.store.getValue(n)},onLoad:function(e,t,n){t.store.initialValue.each(function(n){t.store.setValue(e,n)})},onUnload:w,state:Gr.init})],Kf=[dr("initialValue"),Qo("manager",{setValue:function(n,e,t,r){t.set(r),e.onSetValue(n,r)},getValue:function(n,e,t){return t.get()},onLoad:function(n,e,t){e.store.initialValue.each(function(n){t.isNotSet()&&t.set(n)})},onUnload:function(n,e,t){t.clear()},state:$f})],Jf=[vr("store",{mode:"memory"},ur("mode",{memory:Kf,manual:Yf,dataset:qf})),qo("onSetValue"),hr("resetOnDom",!1)],Qf=Xr({fields:Jf,name:"representing",active:Gf,apis:Lf,extra:{setValueFrom:function(n,e){var t=Qf.getValue(e);Qf.setValue(n,t)}},state:Wf}),Zf=Bi("width",function(n){return n.dom().offsetWidth}),ns=function(n,e){return Zf.set(n,e)},es=function(n){return Zf.get(n)},ts=function(t,r){return{left:y(t),top:y(r),translate:function(n,e){return ts(t+n,r+e)}}},rs=ts,os=y("slider.change.value"),is=function(n){var e=n.event().raw();if(-1!==e.type.indexOf("touch")){return e.touches!==undefined&&1===e.touches.length?O.some(e.touches[0]).map(function(n){return rs(n.clientX,n.clientY)}):O.none()}return e.clientX!==undefined?O.some(e).map(function(n){return rs(n.clientX,n.clientY)}):O.none()},us=function(n){return n.model.minX},cs=function(n){return n.model.minY},as=function(n){return n.model.minX-1},fs=function(n){return n.model.minY-1},ss=function(n){return n.model.maxX},ls=function(n){return n.model.maxY},ds=function(n){return n.model.maxX+1},ms=function(n){return n.model.maxY+1},gs=function(n,e,t){return e(n)-t(n)},ps=function(n){return gs(n,ss,us)},hs=function(n){return gs(n,ls,cs)},vs=function(n){return ps(n)/2},ys=function(n){return hs(n)/2},bs=function(n){return n.stepSize},xs=function(n){return n.snapToGrid},ws=function(n){return n.snapStart},Ss=function(n){return n.rounded},Os=function(n,e){return n[e+"-edge"]!==undefined},Ts=function(n){return Os(n,"left")},ks=function(n){return Os(n,"right")},Es=function(n){return Os(n,"top")},Cs=function(n){return Os(n,"bottom")},Ms=function(n){return n.model.value.get()},_s=function(n){return{x:y(n)}},Ds=function(n){return{y:y(n)}},Is=function(n,e){return{x:y(n),y:y(e)}},Fs=function(n,e){be(n,os(),{value:e})},Rs=function(n,e,t,r){return n<e?n:t<n?t:n===e?e-1:Math.max(e,n-r)},Vs=function(n,e,t,r){return t<n?n:n<e?e:n===t?t+1:Math.min(t,n+r)},Bs=function(n,e,t){return Math.max(e,Math.min(t,n))},As=function(n){var e=n.min,t=n.max,r=n.range,o=n.value,i=n.step,u=n.snap,c=n.snapStart,a=n.rounded,f=n.hasMinEdge,s=n.hasMaxEdge,l=n.minBound,d=n.maxBound,m=n.screenRange,g=f?e-1:e,p=s?t+1:t;if(o<l)return g;if(d<o)return p;var h,v,y,b,x,w,S,O=(x=o,w=l,S=d,Math.min(S,Math.max(x,w))-w),T=Bs(O/m*r+e,g,p);return u&&e<=T&&T<=t?(h=T,v=e,y=t,b=i,c.fold(function(){var n=h-v,e=Math.round(n/b)*b;return Bs(v+e,v-1,y+1)},function(n){var e=(h-n)%b,t=Math.round(e/b),r=Math.floor((h-n)/b),o=Math.floor((y-n)/b),i=n+Math.min(o,r+t)*b;return Math.max(n,i)})):a?Math.round(T):T},js=function(n){var e=n.min,t=n.max,r=n.range,o=n.value,i=n.hasMinEdge,u=n.hasMaxEdge,c=n.maxBound,a=n.maxOffset,f=n.centerMinEdge,s=n.centerMaxEdge;return o<e?i?0:f:t<o?u?c:s:(o-e)/r*a},Hs="left",Ns=function(n){return n.element().dom().getBoundingClientRect()},Ps=function(n,e){return n[e]},zs=function(n){var e=Ns(n);return Ps(e,Hs)},Ls=function(n){var e=Ns(n);return Ps(e,"right")},Gs=function(n){var e=Ns(n);return Ps(e,"top")},$s=function(n){var e=Ns(n);return Ps(e,"bottom")},Us=function(n){var e=Ns(n);return Ps(e,"width")},Ws=function(n){var e=Ns(n);return Ps(e,"height")},Xs=function(n,e,t){return(n+e)/2-t},qs=function(n,e){var t=Ns(n),r=Ns(e),o=Ps(t,Hs),i=Ps(t,"right"),u=Ps(r,Hs);return Xs(o,i,u)},Ys=function(n,e){var t=Ns(n),r=Ns(e),o=Ps(t,"top"),i=Ps(t,"bottom"),u=Ps(r,"top");return Xs(o,i,u)},Ks=function(n,e){be(n,os(),{value:e})},Js=function(n){return{x:y(n)}},Qs=function(n,e,t){var r={min:us(e),max:ss(e),range:ps(e),value:t,step:bs(e),snap:xs(e),snapStart:ws(e),rounded:Ss(e),hasMinEdge:Ts(e),hasMaxEdge:ks(e),minBound:zs(n),maxBound:Ls(n),screenRange:Us(n)};return As(r)},Zs=function(i){return function(n,e){return t=n,o=(0<i?Vs:Rs)(Ms(r=e).x(),us(r),ss(r),bs(r)),Ks(t,Js(o)),O.some(o).map(function(){return!0});var t,r,o}},nl=function(n,e,t,r,o,i){var u,c,a,f,s,l,d,m,g,p=(c=i,a=t,f=r,s=o,l=Us(u=e),d=f.bind(function(n){return O.some(qs(n,u))}).getOr(0),m=s.bind(function(n){return O.some(qs(n,u))}).getOr(l),g={min:us(c),max:ss(c),range:ps(c),value:a,hasMinEdge:Ts(c),hasMaxEdge:ks(c),minBound:zs(u),minOffset:0,maxBound:Ls(u),maxOffset:l,centerMinEdge:d,centerMaxEdge:m},js(g));return zs(e)-zs(n)+p},el=Zs(-1),tl=Zs(1),rl=O.none,ol=O.none,il={"top-left":O.none(),top:O.none(),"top-right":O.none(),right:O.some(function(n,e){Fs(n,_s(ds(e)))}),"bottom-right":O.none(),bottom:O.none(),"bottom-left":O.none(),left:O.some(function(n,e){Fs(n,_s(as(e)))})},ul=/* */Object.freeze({__proto__:null,setValueFrom:function(n,e,t){var r=Qs(n,e,t),o=Js(r);return Ks(n,o),r},setToMin:function(n,e){var t=us(e);Ks(n,Js(t))},setToMax:function(n,e){var t=ss(e);Ks(n,Js(t))},findValueOfOffset:Qs,getValueFromEvent:function(n){return is(n).map(function(n){return n.left()})},findPositionOfValue:nl,setPositionFromValue:function(n,e,t,r){var o=Ms(t),i=nl(n,r.getSpectrum(n),o.x(),r.getLeftEdge(n),r.getRightEdge(n),t),u=es(e.element())/2;Mi(e.element(),"left",i-u+"px")},onLeft:el,onRight:tl,onUp:rl,onDown:ol,edgeActions:il}),cl=function(n,e){be(n,os(),{value:e})},al=function(n){return{y:y(n)}},fl=function(n,e,t){var r={min:cs(e),max:ls(e),range:hs(e),value:t,step:bs(e),snap:xs(e),snapStart:ws(e),rounded:Ss(e),hasMinEdge:Es(e),hasMaxEdge:Cs(e),minBound:Gs(n),maxBound:$s(n),screenRange:Ws(n)};return As(r)},sl=function(i){return function(n,e){return t=n,o=(0<i?Vs:Rs)(Ms(r=e).y(),cs(r),ls(r),bs(r)),cl(t,al(o)),O.some(o).map(function(){return!0});var t,r,o}},ll=function(n,e,t,r,o,i){var u,c,a,f,s,l,d,m,g,p=(c=i,a=t,f=r,s=o,l=Ws(u=e),d=f.bind(function(n){return O.some(Ys(n,u))}).getOr(0),m=s.bind(function(n){return O.some(Ys(n,u))}).getOr(l),g={min:cs(c),max:ls(c),range:hs(c),value:a,hasMinEdge:Es(c),hasMaxEdge:Cs(c),minBound:Gs(u),minOffset:0,maxBound:$s(u),maxOffset:l,centerMinEdge:d,centerMaxEdge:m},js(g));return Gs(e)-Gs(n)+p},dl=O.none,ml=O.none,gl=sl(-1),pl=sl(1),hl={"top-left":O.none(),top:O.some(function(n,e){Fs(n,Ds(fs(e)))}),"top-right":O.none(),right:O.none(),"bottom-right":O.none(),bottom:O.some(function(n,e){Fs(n,Ds(ms(e)))}),"bottom-left":O.none(),left:O.none()},vl=/* */Object.freeze({__proto__:null,setValueFrom:function(n,e,t){var r=fl(n,e,t),o=al(r);return cl(n,o),r},setToMin:function(n,e){var t=cs(e);cl(n,al(t))},setToMax:function(n,e){var t=ls(e);cl(n,al(t))},findValueOfOffset:fl,getValueFromEvent:function(n){return is(n).map(function(n){return n.top()})},findPositionOfValue:ll,setPositionFromValue:function(n,e,t,r){var o=Ms(t),i=ll(n,r.getSpectrum(n),o.y(),r.getTopEdge(n),r.getBottomEdge(n),t),u=Ni(e.element())/2;Mi(e.element(),"top",i-u+"px")},onLeft:dl,onRight:ml,onUp:gl,onDown:pl,edgeActions:hl}),yl=function(n,e){be(n,os(),{value:e})},bl=function(n,e){return{x:y(n),y:y(e)}},xl=function(a,f){return function(n,e){return r=n,o=e,i=0<a?Vs:Rs,u=(t=f)?Ms(o).x():i(Ms(o).x(),us(o),ss(o),bs(o)),c=t?i(Ms(o).y(),cs(o),ls(o),bs(o)):Ms(o).y(),yl(r,bl(u,c)),O.some(u).map(function(){return!0});var t,r,o,i,u,c}},wl=xl(-1,!1),Sl=xl(1,!1),Ol=xl(-1,!0),Tl=xl(1,!0),kl={"top-left":O.some(function(n,e){Fs(n,Is(as(e),fs(e)))}),top:O.some(function(n,e){Fs(n,Is(vs(e),fs(e)))}),"top-right":O.some(function(n,e){Fs(n,Is(ds(e),fs(e)))}),right:O.some(function(n,e){Fs(n,Is(ds(e),ys(e)))}),"bottom-right":O.some(function(n,e){Fs(n,Is(ds(e),ms(e)))}),bottom:O.some(function(n,e){Fs(n,Is(vs(e),ms(e)))}),"bottom-left":O.some(function(n,e){Fs(n,Is(as(e),ms(e)))}),left:O.some(function(n,e){Fs(n,Is(as(e),ys(e)))})},El=/* */Object.freeze({__proto__:null,setValueFrom:function(n,e,t){var r=Qs(n,e,t.left()),o=fl(n,e,t.top()),i=bl(r,o);return yl(n,i),i},setToMin:function(n,e){var t=us(e),r=cs(e);yl(n,bl(t,r))},setToMax:function(n,e){var t=ss(e),r=ls(e);yl(n,bl(t,r))},getValueFromEvent:function(n){return is(n)},setPositionFromValue:function(n,e,t,r){var o=Ms(t),i=nl(n,r.getSpectrum(n),o.x(),r.getLeftEdge(n),r.getRightEdge(n),t),u=ll(n,r.getSpectrum(n),o.y(),r.getTopEdge(n),r.getBottomEdge(n),t),c=es(e.element())/2,a=Ni(e.element())/2;Mi(e.element(),"left",i-c+"px"),Mi(e.element(),"top",u-a+"px")},onLeft:wl,onRight:Sl,onUp:Ol,onDown:Tl,edgeActions:kl}),Cl=[hr("stepSize",1),hr("onChange",w),hr("onChoose",w),hr("onInit",w),hr("onDragStart",w),hr("onDragEnd",w),hr("snapToGrid",!1),hr("rounded",!0),dr("snapStart"),sr("model",ur("mode",{x:[hr("minX",0),hr("maxX",100),yr("value",function(n){return ho(n.mode.minX)}),fr("getInitialValue"),Qo("manager",ul)],y:[hr("minY",0),hr("maxY",100),yr("value",function(n){return ho(n.mode.minY)}),fr("getInitialValue"),Qo("manager",vl)],xy:[hr("minX",0),hr("maxX",100),hr("minY",0),hr("maxY",100),yr("value",function(n){return ho({x:y(n.mode.minX),y:y(n.mode.minY)})}),fr("getInitialValue"),Qo("manager",El)]})),la("sliderBehaviours",[sa,Qf]),yr("mouseIsDown",function(){return ho(!1)})],Ml=y("mouse.released"),_l=xf({name:"Slider",configFields:Cl,partFields:Nf,factory:function(i,n,e,t){var r,u=function(n){return Ja(n,i,"thumb")},c=function(n){return Ja(n,i,"spectrum")},o=function(n){return Ka(n,i,"left-edge")},a=function(n){return Ka(n,i,"right-edge")},f=function(n){return Ka(n,i,"top-edge")},s=function(n){return Ka(n,i,"bottom-edge")},l=i.model,d=l.manager,m=function(n,e){d.setPositionFromValue(n,e,i,{getLeftEdge:o,getRightEdge:a,getTopEdge:f,getBottomEdge:s,getSpectrum:c})},g=function(n,e){l.value.set(e);var t=u(n);return m(n,t),i.onChange(n,t,e),O.some(!0)},p=function(t){var n=i.mouseIsDown.get();i.mouseIsDown.set(!1),n&&Ka(t,i,"thumb").each(function(n){var e=l.value.get();i.onChoose(t,n,e)})},h=function(n,e){e.stop(),i.mouseIsDown.set(!0),i.onDragStart(n,u(n))},v=function(n,e){e.stop(),i.onDragEnd(n,u(n)),p(n)};return{uid:i.uid,dom:i.dom,components:n,behaviours:ma(i.sliderBehaviours,[sa.config({mode:"special",focusIn:function(n){return Ka(n,i,"spectrum").map(sa.focusIn).map(y(!0))}}),Qf.config({store:{mode:"manual",getValue:function(n){return l.value.get()}}}),ti.config({channels:((r={})[Ml()]={onReceive:p},r)})]),events:Tr([Er(os(),function(n,e){g(n,e.event().value())}),Fr(function(n,e){var t=l.getInitialValue();l.value.set(t);var r=u(n);m(n,r);var o=c(n);i.onInit(n,r,o,l.value.get())}),Er(Gn(),h),Er(Un(),v),Er(Wn(),h),Er(qn(),v)]),apis:{resetToMin:function(n){d.setToMin(n,i)},resetToMax:function(n){d.setToMax(n,i)},changeValue:g,refresh:m},domModification:{styles:{position:"relative"}}}},apis:{resetToMin:function(n,e){n.resetToMin(e)},resetToMax:function(n,e){n.resetToMax(e)},refresh:function(n,e){n.refresh(e)}}}),Dl=function(e,t,r,n){return _f(t,function(){var n=r();e.setContextToolbar([{label:t+" group",items:n}])},{},n)},Il=function(n){return[(o=n,i=function(n){return n<0?"black":360<n?"white":"hsl("+n+", 100%, 50%)"},_l.sketch({dom:Tf('<div class="${prefix}-slider ${prefix}-hue-slider-container"></div>'),components:[_l.parts()["left-edge"](kf('<div class="${prefix}-hue-slider-black"></div>')),_l.parts().spectrum({dom:Tf('<div class="${prefix}-slider-gradient-container"></div>'),components:[kf('<div class="${prefix}-slider-gradient"></div>')],behaviours:Ur([gi.config({toggleClass:bi("thumb-active")})])}),_l.parts()["right-edge"](kf('<div class="${prefix}-hue-slider-white"></div>')),_l.parts().thumb({dom:Tf('<div class="${prefix}-slider-thumb"></div>'),behaviours:Ur([gi.config({toggleClass:bi("thumb-active")})])})],onChange:function(n,e,t){var r=i(t.x());Mi(e.element(),"background-color",r),o.onChange(n,e,r)},onDragStart:function(n,e){gi.on(e)},onDragEnd:function(n,e){gi.off(e)},onInit:function(n,e,t,r){var o=i(r.x());Mi(e.element(),"background-color",o)},stepSize:10,model:{mode:"x",minX:0,maxX:360,getInitialValue:function(){return{x:function(){return o.getInitialValue()}}}},sliderBehaviours:Ur([hi(_l.refresh)])}))];var o,i},Fl=Ut([fr("getInitialValue"),fr("onChange"),fr("category"),fr("sizes")]),Rl=function(n){var i=or("SizeSlider",Fl,n);return _l.sketch({dom:{tag:"div",classes:[bi("slider-"+i.category+"-size-container"),bi("slider"),bi("slider-size-container")]},onChange:function(n,e,t){var r,o=t.x();0<=(r=o)&&r<i.sizes.length&&i.onChange(o)},onDragStart:function(n,e){gi.on(e)},onDragEnd:function(n,e){gi.off(e)},model:{mode:"x",minX:0,maxX:i.sizes.length-1,getInitialValue:function(){return{x:function(){return i.getInitialValue()}}}},stepSize:1,snapToGrid:!0,sliderBehaviours:Ur([hi(_l.refresh)]),components:[_l.parts().spectrum({dom:Tf('<div class="${prefix}-slider-size-container"></div>'),components:[kf('<div class="${prefix}-slider-size-line"></div>')]}),_l.parts().thumb({dom:Tf('<div class="${prefix}-slider-thumb"></div>'),behaviours:Ur([gi.config({toggleClass:bi("thumb-active")})])})]})},Vl=["9px","10px","11px","12px","14px","16px","18px","20px","24px","32px","36px"],Bl=function(e,n){return(Ce(n)?O.some(n):He(n).filter(Ce)).map(function(n){return vo(function(n,e){return e(n)},xo,n,function(n){return Fi(n,"font-size").isSome()},e).bind(function(n){return Fi(n,"font-size")}).getOrThunk(function(){return Di(n,"font-size")})}).getOr("")},Al=function(n){var e=n.selection.getStart(),t=Te.fromDom(e),r=Te.fromDom(n.getBody()),o=Bl(function(n){return Ae(r,n)},t);return bn(Vl,function(n){return o===n}).getOr("medium")},jl=function(n){var e,t=Al(n);return e=t,xn(Vl,function(n){return n===e}).getOr(2)},Hl=function(r,n){var e;e=n,O.from(Vl[e]).each(function(n){var e,t;t=n,Al(e=r)!==t&&e.execCommand("fontSize",!1,t)})},Nl=y(Vl)(),Pl=function(n){return[kf('<span class="${prefix}-toolbar-button ${prefix}-icon-small-font ${prefix}-icon"></span>'),Rl({onChange:(e=n).onChange,sizes:Nl,category:"font",getInitialValue:e.getInitialValue}),kf('<span class="${prefix}-toolbar-button ${prefix}-icon-large-font ${prefix}-icon"></span>')];var e},zl=function(n){var e=function t(n){return n.uid!==undefined}(n)&&R(n,"uid")?n.uid:sf("memento");return{get:function(n){return n.getSystem().getByUid(e).getOrDie()},getOpt:function(n){return n.getSystem().getByUid(e).toOption()},asSpec:function(){return x(x({},n),{uid:e})}}},Ll=window.Promise?window.Promise:(pf=(gf=function(n){if("object"!=typeof this)throw new TypeError("Promises must be constructed via new");if("function"!=typeof n)throw new TypeError("not a function");this._state=null,this._value=null,this._deferreds=[],Yl(n,Gl(Ul,this),Gl(Wl,this))}).immediateFn||"function"==typeof window.setImmediate&&window.setImmediate||function(n){v.setTimeout(n,1)},hf=Array.isArray||function(n){return"[object Array]"===Object.prototype.toString.call(n)},gf.prototype["catch"]=function(n){return this.then(null,n)},gf.prototype.then=function(t,r){var o=this;return new gf(function(n,e){$l.call(o,new ql(t,r,n,e))})},gf.all=function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];var a=Array.prototype.slice.call(1===n.length&&hf(n[0])?n[0]:n);return new gf(function(o,i){if(0===a.length)return o([]);var u=a.length;function c(e,n){try{if(n&&("object"==typeof n||"function"==typeof n)){var t=n.then;if("function"==typeof t)return void t.call(n,function(n){c(e,n)},i)}a[e]=n,0==--u&&o(a)}catch(r){i(r)}}for(var n=0;n<a.length;n++)c(n,a[n])})},gf.resolve=function(e){return e&&"object"==typeof e&&e.constructor===gf?e:new gf(function(n){n(e)})},gf.reject=function(t){return new gf(function(n,e){e(t)})},gf.race=function(o){return new gf(function(n,e){for(var t=0,r=o;t<r.length;t++)r[t].then(n,e)})},gf);function Gl(n,e){return function(){return n.apply(e,arguments)}}function $l(r){var o=this;null!==this._state?pf(function(){var n=o._state?r.onFulfilled:r.onRejected;if(null!==n){var e;try{e=n(o._value)}catch(t){return void r.reject(t)}r.resolve(e)}else(o._state?r.resolve:r.reject)(o._value)}):this._deferreds.push(r)}function Ul(n){try{if(n===this)throw new TypeError("A promise cannot be resolved with itself.");if(n&&("object"==typeof n||"function"==typeof n)){var e=n.then;if("function"==typeof e)return void Yl(Gl(e,n),Gl(Ul,this),Gl(Wl,this))}this._state=!0,this._value=n,Xl.call(this)}catch(t){Wl.call(this,t)}}function Wl(n){this._state=!1,this._value=n,Xl.call(this)}function Xl(){for(var n=0,e=this._deferreds;n<e.length;n++){var t=e[n];$l.call(this,t)}this._deferreds=[]}function ql(n,e,t,r){this.onFulfilled="function"==typeof n?n:null,this.onRejected="function"==typeof e?e:null,this.resolve=t,this.reject=r}function Yl(n,e,t){var r=!1;try{n(function(n){r||(r=!0,e(n))},function(n){r||(r=!0,t(n))})}catch(o){if(r)return;r=!0,t(o)}}function Kl(n){return function e(t){return new Ll(function(n){var e=new v.FileReader;e.onloadend=function(){n(e.result)},e.readAsDataURL(t)})}(n).then(function(n){return n.split(",")[1]})}var Jl=function(o,i){Kl(i).then(function(r){o.undoManager.transact(function(){var n=o.editorUpload.blobCache,e=n.create(Ea("mceu"),i,r);n.add(e);var t=o.dom.createHTML("img",{src:e.blobUri()});o.insertContent(t)})})},Ql=function(o){var e=zl({dom:{tag:"input",attributes:{accept:"image/*",type:"file",title:""},styles:{visibility:"hidden",position:"absolute"}},events:Tr([Ir(ne()),Er(Zn(),function(n,e){var t,r;t=e.event(),r=t.raw().target.files||t.raw().dataTransfer.files,O.from(r[0]).each(function(n){Jl(o,n)})})])});return wf.sketch({dom:Mf("image",o),components:[e.asSpec()],action:function(n){e.get(n).element().dom().click()}})},Zl=function(n){return n.dom().textContent},nd=function(n){return 0<n.length},ed=function(n){return n===undefined||null===n?"":n},td=function(n){return id(n).fold(function(){return{url:"",text:n.selection.getContent({format:"text"}),title:"",target:"",link:O.none()}},function(n){return t=Zl(e=n),r=Zr(e,"href"),o=Zr(e,"title"),i=Zr(e,"target"),{url:ed(r),text:t!==r?ed(t):"",title:ed(o),target:ed(i),link:O.some(e)};var e,t,r,o,i})},rd=function(e,t,n){return n.text.toOption().filter(nd).fold(function(){return Zr(n=e,"href")===Zl(n)?O.some(t):O.none();var n},O.some)},od=function(o,i){i.url.toOption().filter(nd).fold(function(){var e;e=o,i.link.bind(b).each(function(n){e.execCommand("unlink")})},function(e){var n,t,r=(n=i,(t={}).href=e,n.title.toOption().filter(nd).each(function(n){t.title=n}),n.target.toOption().filter(nd).each(function(n){t.target=n}),t);i.link.bind(b).fold(function(){var n=i.text.toOption().filter(nd).getOr(e);o.insertContent(o.dom.createHTML("a",r,o.dom.encode(n)))},function(t){var n=rd(t,e,i);Qr(t,r),n.each(function(n){var e;e=n,t.dom().textContent=e})})})},id=function(n){var e=Te.fromDom(n.selection.getStart());return Wi(e,"a")},ud=Ln(),cd=function(n,e){var t=e.selection.getRng();n(),e.selection.setRng(t)},ad=function(n,e){var t,r;return{key:n,value:{config:{},me:(t=n,r=Tr(e),Xr({fields:[fr("enabled")],name:t,active:{events:y(r)}})),configAsRaw:y({}),initialConfig:{},state:Gr}}},fd=/* */Object.freeze({__proto__:null,getCurrent:function(n,e,t){return e.find(n)}}),sd=[fr("find")],ld=Xr({fields:sd,name:"composing",apis:fd}),dd=bf({name:"Container",factory:function(n){var e=n.dom,t=e.attributes,r=c(e,["attributes"]);return{uid:n.uid,dom:x({tag:"div",attributes:x({role:"presentation"},t)},r),components:n.components,behaviours:da(n.containerBehaviours),events:n.events,domModification:n.domModification,eventOrder:n.eventOrder}},configFields:[hr("components",[]),la("containerBehaviours",[]),hr("events",{}),hr("domModification",{}),hr("eventOrder",{})]}),md=bf({name:"DataField",factory:function(t){return{uid:t.uid,dom:t.dom,behaviours:pa(t.dataBehaviours,[Qf.config({store:{mode:"memory",initialValue:t.getInitialValue()}}),ld.config({find:O.some})]),events:Tr([Fr(function(n,e){Qf.setValue(n,t.getInitialValue())})])}},configFields:[fr("uid"),fr("dom"),fr("getInitialValue"),ga("dataBehaviours",[Qf,ld])]}),gd=function(n){return n.dom().value},pd=function(n,e){if(e===undefined)throw new Error("Value.set was undefined");n.dom().value=e},hd=y([dr("data"),hr("inputAttributes",{}),hr("inputStyles",{}),hr("tag","input"),hr("inputClasses",[]),qo("onSetValue"),hr("styles",{}),hr("eventOrder",{}),la("inputBehaviours",[Qf,ki]),hr("selectOnFocus",!0)]),vd=function(n){return x(x({},(e=n,Ur([ki.config({onFocus:e.selectOnFocus?function(n){var e=n.element(),t=gd(e);e.dom().setSelectionRange(0,t.length)}:w})]))),ma(n.inputBehaviours,[Qf.config({store:x(x({mode:"manual"},n.data.map(function(n){return{initialValue:n}}).getOr({})),{getValue:function(n){return gd(n.element())},setValue:function(n,e){gd(n.element())!==e&&pd(n.element(),e)}}),onSetValue:n.onSetValue})]));var e},yd=bf({name:"Input",configFields:hd(),factory:function(n,e){return{uid:n.uid,dom:{tag:(t=n).tag,attributes:x({type:"text"},t.inputAttributes),styles:t.inputStyles,classes:t.inputClasses},components:[],behaviours:vd(n),eventOrder:n.eventOrder};var t}}),bd=/* */Object.freeze({__proto__:null,exhibit:function(n,e){return Hr({attributes:Rt([{key:e.tabAttr,value:"true"}])})}}),xd=[hr("tabAttr","data-alloy-tabstop")],wd=Xr({fields:xd,name:"tabstopping",active:bd}),Sd=tinymce.util.Tools.resolve("tinymce.util.I18n"),Od=function(n,e){var t=zl(yd.sketch({inputAttributes:{placeholder:Sd.translate(e)},onSetValue:function(n,e){ye(n,Qn())},inputBehaviours:Ur([ld.config({find:O.some}),wd.config({}),sa.config({mode:"execution"})]),selectOnFocus:!1})),r=zl(wf.sketch({dom:Tf('<button class="${prefix}-input-container-x ${prefix}-icon-cancel-circle ${prefix}-icon"></button>'),action:function(n){var e=t.get(n);Qf.setValue(e,"")}}));return{name:n,spec:dd.sketch({dom:Tf('<div class="${prefix}-input-container"></div>'),components:[t.asSpec(),r.asSpec()],containerBehaviours:Ur([gi.config({toggleClass:bi("input-container-empty")}),ld.config({find:function(n){return O.some(t.get(n))}}),ad("input-clearing",[Er(Qn(),function(n){var e=t.get(n);(0<Qf.getValue(e).length?gi.off:gi.on)(n)})])])})}},Td=["input","button","textarea","select"],kd=function(n,e,t){(e.disabled()?Id:Fd)(n,e,t)},Ed=function(n,e){return!0===e.useNative&&dn(Td,ke(n.element()))},Cd=function(n){Jr(n.element(),"disabled","disabled")},Md=function(n){to(n.element(),"disabled")},_d=function(n){Jr(n.element(),"aria-disabled","true")},Dd=function(n){Jr(n.element(),"aria-disabled","false")},Id=function(e,n,t){n.disableClass.each(function(n){co(e.element(),n)}),(Ed(e,n)?Cd:_d)(e),n.onDisabled(e)},Fd=function(e,n,t){n.disableClass.each(function(n){fo(e.element(),n)}),(Ed(e,n)?Md:Dd)(e),n.onEnabled(e)},Rd=function(n,e){return Ed(n,e)?eo(n.element(),"disabled"):"true"===Zr(n.element(),"aria-disabled")},Vd=/* */Object.freeze({__proto__:null,enable:Fd,disable:Id,isDisabled:Rd,onLoad:kd,set:function(n,e,t,r){(r?Id:Fd)(n,e,t)}}),Bd=/* */Object.freeze({__proto__:null,exhibit:function(n,e){return Hr({classes:e.disabled?e.disableClass.map(En).getOr([]):[]})},events:function(t,n){return Tr([kr(ae(),function(n,e){return Rd(n,t)}),Nr(t,n,kd)])}}),Ad=[vr("disabled",s,ar),hr("useNative",!0),dr("disableClass"),qo("onDisabled"),qo("onEnabled")],jd=Xr({fields:Ad,name:"disabling",active:Bd,apis:Vd}),Hd=[la("formBehaviours",[Qf])],Nd=function(n){return"<alloy.field."+n+">"},Pd=function(o,n){return{uid:o.uid,dom:o.dom,components:n,behaviours:ma(o.formBehaviours,[Qf.config({store:{mode:"manual",getValue:function(n){var e,t,r=(e=o,t=n.getSystem(),C(e.partUids,function(n,e){return y(t.getByUid(n))}));return C(r,function(n,o){return n().bind(function(n){var e,t,r=ld.getCurrent(n);return e=r,t=new Error("Cannot find a current component to extract the value from for form part '"+o+"': "+Vo(n.element())),e.fold(function(){return rt.error(t)},rt.value)}).map(Qf.getValue)})},setValue:function(t,n){E(n,function(e,n){Ka(t,o,n).each(function(n){ld.getCurrent(n).each(function(n){Qf.setValue(n,e)})})})}}})]),apis:{getField:function(n,e){return Ka(n,o,e).bind(ld.getCurrent)}}}},zd=(tf(function(n,e,t){return n.getField(e,t)}),function(n){var i,e=(i=[],{field:function(n,e){return i.push(n),t="form",r=Nd(n),o=e,{uiType:Ta(),owner:t,name:r,config:o,validated:{}};var t,r,o},record:function(){return i}}),t=n(e),r=e.record(),o=gn(r,function(n){return za({name:n,pname:Nd(n)})});return df("form",Hd,o,Pd,t)}),Ld=function(){var e=ho(O.none()),t=function(){e.get().each(function(n){n.destroy()})};return{clear:function(){t(),e.set(O.none())},isSet:function(){return e.get().isSome()},set:function(n){t(),e.set(O.some(n))},run:function(n){e.get().each(n)}}},Gd=function(){var e=ho(O.none());return{clear:function(){e.set(O.none())},set:function(n){e.set(O.some(n))},isSet:function(){return e.get().isSome()},on:function(n){e.get().each(n)}}},$d=function(n){var r="navigateEvent",e=Wt([fr("fields"),hr("maxFieldIndex",n.fields.length-1),fr("onExecute"),fr("getInitialValue"),yr("state",function(){return{dialogSwipeState:Gd(),currentScreen:ho(0)}})]),u=or("SerialisedDialog",e,n),o=function(e,n,t){return wf.sketch({dom:Tf('<span class="${prefix}-icon-'+n+' ${prefix}-icon"></span>'),action:function(n){be(n,r,{direction:e})},buttonBehaviours:Ur([jd.config({disableClass:bi("toolbar-navigation-disabled"),disabled:function(){return!t}})])})},i=function(n,o){var i=Li(n.element(),"."+bi("serialised-dialog-screen"));Ui(n.element(),"."+bi("serialised-dialog-chain")).each(function(r){0<=u.state.currentScreen.get()+o&&u.state.currentScreen.get()+o<i.length&&(Fi(r,"left").each(function(n){var e=parseInt(n,10),t=es(i[0]);Mi(r,"left",e-o*t+"px")}),u.state.currentScreen.set(u.state.currentScreen.get()+o))})},c=function(r){var n=Li(r.element(),"input");O.from(n[u.state.currentScreen.get()]).each(function(n){r.getSystem().getByDom(n).each(function(n){var e,t;e=r,t=n.element(),e.getSystem().triggerFocus(t,e.element())})});var e=f.get(r);Ou.highlightAt(e,u.state.currentScreen.get())},a=zl(zd(function(t){return{dom:Tf('<div class="${prefix}-serialised-dialog"></div>'),components:[dd.sketch({dom:Tf('<div class="${prefix}-serialised-dialog-chain" style="left: 0px; position: absolute;"></div>'),components:gn(u.fields,function(n,e){return e<=u.maxFieldIndex?dd.sketch({dom:Tf('<div class="${prefix}-serialised-dialog-screen"></div>'),components:[o(-1,"previous",0<e),t.field(n.name,n.spec),o(1,"next",e<u.maxFieldIndex)]}):t.field(n.name,n.spec)})})],formBehaviours:Ur([hi(function(n,e){var t;t=e,Ui(n.element(),"."+bi("serialised-dialog-chain")).each(function(n){Mi(n,"left",-u.state.currentScreen.get()*t.width+"px")})}),sa.config({mode:"special",focusIn:function(n,e){c(n)},onTab:function(n,e){return i(n,1),O.some(!0)},onShiftTab:function(n,e){return i(n,-1),O.some(!0)}}),ad("form-events",[Fr(function(e,n){u.state.currentScreen.set(0),u.state.dialogSwipeState.clear();var t=f.get(e);Ou.highlightFirst(t),u.getInitialValue(e).each(function(n){Qf.setValue(e,n)})}),Br(u.onExecute),Er(ee(),function(n,e){"left"===e.event().raw().propertyName&&c(n)}),Er(r,function(n,e){var t=e.event().direction();i(n,t)})])])}})),f=zl({dom:Tf('<div class="${prefix}-dot-container"></div>'),behaviours:Ur([Ou.config({highlightClass:bi("dot-active"),itemClass:bi("dot-item")})]),components:Sn(u.fields,function(n,e){return e<=u.maxFieldIndex?[kf('<div class="${prefix}-dot-item ${prefix}-icon-full-dot ${prefix}-icon"></div>')]:[]})});return{dom:Tf('<div class="${prefix}-serializer-wrapper"></div>'),components:[a.asSpec(),f.asSpec()],behaviours:Ur([sa.config({mode:"special",focusIn:function(n){var e=a.get(n);sa.focusIn(e)}}),ad("serializer-wrapper-events",[Er(Gn(),function(n,e){var t=e.event();u.state.dialogSwipeState.set({xValue:t.raw().touches[0].clientX,points:[]})}),Er($n(),function(n,e){var t=e.event();u.state.dialogSwipeState.on(function(n){e.event().prevent(),u.state.dialogSwipeState.set(function(n,e){if(e===n.xValue)return n;var t=0<e-n.xValue?1:-1,r={direction:t,xValue:e};return{xValue:e,points:(0===n.points.length?[]:n.points[n.points.length-1].direction===t?n.points.slice(0,n.points.length-1):n.points).concat([r])}}(n,t.raw().touches[0].clientX))})}),Er(Un(),function(r,n){u.state.dialogSwipeState.on(function(n){var e=a.get(r),t=-1*function(n){if(0===n.points.length)return 0;var e=n.points[0].direction,t=n.points[n.points.length-1].direction;return-1===e&&-1===t?-1:1===e&&1===t?1:0}(n);i(e,t)})})])])}},Ud=V(function(r,o){return[{label:"the link group",items:[$d({fields:[Od("url","Type or paste URL"),Od("text","Link text"),Od("title","Link title"),Od("target","Link target"),{name:"link",spec:md.sketch({dom:{tag:"span",styles:{display:"none"}},getInitialValue:function(){return O.none()}})}],maxFieldIndex:["url","text","title","target"].length-1,getInitialValue:function(){return O.some(td(o))},onExecute:function(n,e){var t=Qf.getValue(n);od(o,t),r.restoreToolbar(),o.focus()}})]}]}),Wd=function(r,o){return Cf(o,"link","link",function(){var n,e,t=Ud(r,o);r.setContextToolbar(t),n=o,e=function(){r.focusToolbar()},(ud.os.isAndroid()?cd:f)(e,n),id(o).each(function(n){o.selection.select(n.dom())})})},Xd=[{title:"Headings",items:[{title:"Heading 1",format:"h1"},{title:"Heading 2",format:"h2"},{title:"Heading 3",format:"h3"},{title:"Heading 4",format:"h4"},{title:"Heading 5",format:"h5"},{title:"Heading 6",format:"h6"}]},{title:"Inline",items:[{title:"Bold",icon:"bold",format:"bold"},{title:"Italic",icon:"italic",format:"italic"},{title:"Underline",icon:"underline",format:"underline"},{title:"Strikethrough",icon:"strikethrough",format:"strikethrough"},{title:"Superscript",icon:"superscript",format:"superscript"},{title:"Subscript",icon:"subscript",format:"subscript"},{title:"Code",icon:"code",format:"code"}]},{title:"Blocks",items:[{title:"Paragraph",format:"p"},{title:"Blockquote",format:"blockquote"},{title:"Div",format:"div"},{title:"Pre",format:"pre"}]},{title:"Alignment",items:[{title:"Left",icon:"alignleft",format:"alignleft"},{title:"Center",icon:"aligncenter",format:"aligncenter"},{title:"Right",icon:"alignright",format:"alignright"},{title:"Justify",icon:"alignjustify",format:"alignjustify"}]}],qd=Tr([{key:oe(),value:Sr({can:function(n,e){var t,r,o=e.event().originator(),i=e.event().target();return r=i,!(Ae(t=o,n.element())&&!Ae(t,r))||(v.console.warn(oe()+" did not get interpreted by the desired target. \nOriginator: "+Vo(o)+"\nTarget: "+Vo(i)+"\nCheck the "+oe()+" event handlers"),!1)}})}]),Yd=/* */Object.freeze({__proto__:null,events:qd}),Kd=b,Jd=function(e){var n=function(n){return function(){throw new Error("The component must be in a context to send: "+n+(e?"\n"+Vo(e().element())+" is not in context.":""))}};return{debugInfo:y("fake"),triggerEvent:n("triggerEvent"),triggerFocus:n("triggerFocus"),triggerEscape:n("triggerEscape"),build:n("build"),addToWorld:n("addToWorld"),removeFromWorld:n("removeFromWorld"),addToGui:n("addToGui"),removeFromGui:n("removeFromGui"),getByUid:n("getByUid"),getByDom:n("getByDom"),broadcast:n("broadcast"),broadcastOn:n("broadcastOn"),broadcastEvent:n("broadcastEvent"),isConnected:y(!1)}},Qd=Jd(),Zd=function(n,o){var i={};return E(n,function(n,r){E(n,function(n,e){var t=I(i,e).getOr([]);i[e]=t.concat([o(r,n)])})}),i},nm=function(u,c,n,a){try{var e=(t=n,r=function(n,e){var t=n[c](),r=e[c](),o=a.indexOf(t),i=a.indexOf(r);if(-1===o)throw new Error("The ordering for "+u+" does not have an entry for "+t+".\nOrder specified: "+JSON.stringify(a,null,2));if(-1===i)throw new Error("The ordering for "+u+" does not have an entry for "+r+".\nOrder specified: "+JSON.stringify(a,null,2));return o<i?-1:i<o?1:0},(o=fn.call(t,0)).sort(r),o);return rt.value(e)}catch(i){return rt.error([i])}var t,r,o},em=function(n,e){return t=S.apply(undefined,[n.handler].concat(e)),r=n.purpose(),{cHandler:t,purpose:y(r)};var t,r},tm=function(n){return n.cHandler},rm=function(n,e){return{name:y(n),handler:y(e)}},om=function(n,e,t){var r,o,i=x(x({},t),(r=n,o={},pn(e,function(n){o[n.name()]=n.handlers(r)}),o));return Zd(i,rm)},im=function(n){var e,i=cn(e=n)?{can:y(!0),abort:y(!1),run:e}:e;return function(n,e){for(var t=[],r=2;r<arguments.length;r++)t[r-2]=arguments[r];var o=[n,e].concat(t);i.abort.apply(undefined,o)?e.stop():i.can.apply(undefined,o)&&i.run.apply(undefined,o)}},um=function(n,e,t){var r,o,i=e[t];return i?nm("Event: "+t,"name",n,i).map(function(n){var e=gn(n,function(n){return n.handler()});return Or(e)}):(r=t,o=n,rt.error(["The event ("+r+') has more than one behaviour that listens to it.\nWhen this occurs, you must specify an event ordering for the behaviours in your spec (e.g. [ "listing", "toggling" ]).\nThe behaviours that can trigger it are: '+JSON.stringify(gn(o,function(n){return n.name()}),null,2)]))},cm=function(n,i){var e=D(n,function(r,o){return(1===r.length?rt.value(r[0].handler()):um(r,i,o)).map(function(n){var e=im(n),t=1<r.length?hn(i[o],function(e){return mn(r,function(n){return n.name()===e})}).join(" > "):r[0].name();return Ft(o,{handler:e,purpose:y(t)})})});return Vt(e,{})},am=function(n){return tr("custom.definition",Wt([Kt("dom","dom",Et(),Wt([fr("tag"),hr("styles",{}),hr("classes",[]),hr("attributes",{}),dr("value"),dr("innerHtml")])),fr("components"),fr("uid"),hr("events",{}),hr("apis",{}),Kt("eventOrder","eventOrder",Tt.mergeWithThunk(y({"alloy.execute":["disabling","alloy.base.behaviour","toggling","typeaheadevents"],"alloy.focus":["alloy.base.behaviour","focusing","keying"],"alloy.system.init":["alloy.base.behaviour","disabling","toggling","representing"],input:["alloy.base.behaviour","representing","streaming","invalidating"],"alloy.system.detached":["alloy.base.behaviour","representing","item-events","tooltipping"],mousedown:["focusing","alloy.base.behaviour","item-type-events"],touchstart:["focusing","alloy.base.behaviour","item-type-events"],mouseover:["item-type-events","tooltipping"],"alloy.receive":["receiving","reflecting","tooltipping"]})),cr()),dr("domModification")]),n)},fm=function(e,n){pn(n,function(n){co(e,n)})},sm=function(e,n){pn(n,function(n){fo(e,n)})},lm=function(n,e){return t=n,o=gn(r=e,function(n){return gr(n.name(),[fr("config"),hr("state",Gr)])}),i=tr("component.behaviours",Wt(o),t.behaviours).fold(function(n){throw new Error(ir(n)+"\nComplete spec:\n"+JSON.stringify(t,null,2))},function(n){return n}),{list:r,data:C(i,function(n){var e=n.map(function(n){return{config:n.config,state:n.state.init(n.config)}});return function(){return e}})};var t,r,o,i},dm=function(n){var e,t,r=(e=I(n,"behaviours").getOr({}),t=hn(T(e),function(n){return e[n]!==undefined}),gn(t,function(n){return e[n].me}));return lm(n,r)},mm=function(n,e,t){var r,o,i,u=x(x({},(r=n).dom),{uid:r.uid,domChildren:gn(r.components,function(n){return n.element()})}),c=n.domModification.fold(function(){return Hr({})},Hr),a={"alloy.base.modification":c},f=0<e.length?function(e,n,t,r){var o=x({},n);pn(t,function(n){o[n.name()]=n.exhibit(e,r)});var i=Zd(o,function(n,e){return{name:n,modification:e}}),u=function(n){return vn(n,function(n,e){return x(x({},e.modification),n)},{})},c=vn(i.classes,function(n,e){return e.modification.concat(n)},[]),a=u(i.attributes),f=u(i.styles);return Hr({classes:c,attributes:a,styles:f})}(t,a,e,u):c;return i=f,x(x({},o=u),{attributes:x(x({},o.attributes),i.attributes),styles:x(x({},o.styles),i.styles),classes:o.classes.concat(i.classes)})},gm=function(n,e,t){var r,o,i,u={"alloy.base.behaviour":n.events};return r=t,o=n.eventOrder,i=om(r,e,u),cm(i,o).getOrDie()},pm=function(t){var n=function(){return s},r=ho(Qd),e=rr(am(t)),o=dm(t),i=o.list,u=o.data,c=function(n){var e=Te.fromTag(n.tag);Qr(e,n.attributes),fm(e,n.classes),_i(e,n.styles),n.innerHtml.each(function(n){return Io(e,n)});var t=n.domChildren;return Ue(e,t),n.value.each(function(n){pd(e,n)}),n.uid,af(e,n.uid),e}(mm(e,i,u)),a=gm(e,i,u),f=ho(e.components),s={getSystem:r.get,config:function(n){var e=u;return(cn(e[n.name()])?e[n.name()]:function(){throw new Error("Could not find "+n.name()+" in "+JSON.stringify(t,null,2))})()},hasConfigured:function(n){return cn(u[n.name()])},spec:y(t),readState:function(n){return u[n]().map(function(n){return n.state.readState()}).getOr("not enabled")},getApis:function(){return e.apis},connect:function(n){r.set(n)},disconnect:function(){r.set(Jd(n))},element:y(c),syncComponents:function(){var n=Ne(c),e=Sn(n,function(n){return r.get().getByDom(n).fold(function(){return[]},function(n){return[n]})});f.set(e)},components:f.get,events:y(a)};return s},hm=function(n){var e,t=Kd(n),r=t.events,o=c(t,["events"]),i=(e=I(o,"components").getOr([]),gn(e,xm)),u=x(x({},o),{events:x(x({},Yd),r),components:i});return rt.value(pm(u))},vm=function(n){var e=Te.fromText(n);return ym({element:e})},ym=function(n){var e=or("external.component",Ut([fr("element"),dr("uid")]),n),t=ho(Jd());e.uid.each(function(n){af(e.element,n)});var r={getSystem:t.get,config:O.none,hasConfigured:y(!1),connect:function(n){t.set(n)},disconnect:function(){t.set(Jd(function(){return r}))},getApis:function(){return{}},element:y(e.element),spec:y(n),readState:y("No state"),syncComponents:w,components:y([]),events:y({})};return ef(r)},bm=sf,xm=function(e){return I(e,nf).fold(function(){var n=e.hasOwnProperty("uid")?e:x({uid:bm("")},e);return hm(n).getOrDie()},function(n){return n})},wm=ef,Sm="alloy.item-hover",Om="alloy.item-focus",Tm=function(n){(To(n.element()).isNone()||ki.isFocused(n))&&(ki.isFocused(n)||ki.focus(n),be(n,Sm,{item:n}))},km=function(n){be(n,Om,{item:n})},Em=y(Sm),Cm=y(Om),Mm=[fr("data"),fr("components"),fr("dom"),hr("hasSubmenu",!1),dr("toggling"),ga("itemBehaviours",[gi,ki,sa,Qf]),hr("ignoreFocus",!1),hr("domModification",{}),Qo("builder",function(n){return{dom:n.dom,domModification:x(x({},n.domModification),{attributes:x(x(x({role:n.toggling.isSome()?"menuitemcheckbox":"menuitem"},n.domModification.attributes),{"aria-haspopup":n.hasSubmenu}),n.hasSubmenu?{"aria-expanded":!1}:{})}),behaviours:pa(n.itemBehaviours,[n.toggling.fold(gi.revoke,function(n){return gi.config(x({aria:{mode:"checked"}},n))}),ki.config({ignore:n.ignoreFocus,stopMousedown:n.ignoreFocus,onFocus:function(n){km(n)}}),sa.config({mode:"execution"}),Qf.config({store:{mode:"memory",initialValue:n.data}}),ad("item-type-events",u(xi(),[Er(Yn(),Tm),Er(fe(),ki.focus)]))]),components:n.components,eventOrder:n.eventOrder}}),hr("eventOrder",{})],_m=[fr("dom"),fr("components"),Qo("builder",function(n){return{dom:n.dom,components:n.components,events:Tr([(e=fe(),Er(e,function(n,e){e.stop()}))])};var e})],Dm=function(){return"item-widget"},Im=y([za({name:"widget",overrides:function(e){return{behaviours:Ur([Qf.config({store:{mode:"manual",getValue:function(n){return e.data},setValue:function(){}}})])}}})]),Fm=[fr("uid"),fr("data"),fr("components"),fr("dom"),hr("autofocus",!1),hr("ignoreFocus",!1),ga("widgetBehaviours",[Qf,ki,sa]),hr("domModification",{}),Za(Im()),Qo("builder",function(t){var n=qa(Dm(),t,Im()),e=Ya(Dm(),t,n.internals()),r=function(n){return Ka(n,t,"widget").map(function(n){return sa.focusIn(n),n})},o=function(n,e){return Pu(e.event().target())||t.autofocus&&e.setSource(n.element()),O.none()};return{dom:t.dom,components:e,domModification:t.domModification,events:Tr([Br(function(n,e){r(n).each(function(n){e.stop()})}),Er(Yn(),Tm),Er(fe(),function(n,e){t.autofocus?r(n):ki.focus(n)})]),behaviours:pa(t.widgetBehaviours,[Qf.config({store:{mode:"memory",initialValue:t.data}}),ki.config({ignore:t.ignoreFocus,onFocus:function(n){km(n)}}),sa.config({mode:"special",focusIn:t.autofocus?function(n){r(n)}:Yr(),onLeft:o,onRight:o,onEscape:function(n,e){return ki.isFocused(n)||t.autofocus?(t.autofocus&&e.setSource(n.element()),O.none()):(ki.focus(n),O.some(!0))}})])}})],Rm=ur("type",{widget:Fm,item:Mm,separator:_m}),Vm=y([Ga({factory:{sketch:function(n){var e=or("menu.spec item",Rm,n);return e.builder(e)}},name:"items",unit:"item",defaults:function(n,e){return e.hasOwnProperty("uid")?e:x(x({},e),{uid:sf("item")})},overrides:function(n,e){return{type:e.type,ignoreFocus:n.fakeFocus,domModification:{classes:[n.markers.item]}}}})]),Bm=y([fr("value"),fr("items"),fr("dom"),fr("components"),hr("eventOrder",{}),la("menuBehaviours",[Ou,Qf,ld,sa]),vr("movement",{mode:"menu",moveOnTab:!0},ur("mode",{grid:[ni(),Qo("config",function(n,e){return{mode:"flatgrid",selector:"."+n.markers.item,initSize:{numColumns:e.initSize.numColumns,numRows:e.initSize.numRows},focusManager:n.focusManager}})],matrix:[Qo("config",function(n,e){return{mode:"matrix",selectors:{row:e.rowSelector,cell:"."+n.markers.item},focusManager:n.focusManager}}),fr("rowSelector")],menu:[hr("moveOnTab",!0),Qo("config",function(n,e){return{mode:"menu",selector:"."+n.markers.item,moveOnTab:e.moveOnTab,focusManager:n.focusManager}})]})),sr("markers",Uo()),hr("fakeFocus",!1),hr("focusManager",ku()),qo("onHighlight")]),Am=y("alloy.menu-focus"),jm=xf({name:"Menu",configFields:Bm(),partFields:Vm(),factory:function(n,e,t,r){return{uid:n.uid,dom:n.dom,markers:n.markers,behaviours:ma(n.menuBehaviours,[Ou.config({highlightClass:n.markers.selectedItem,itemClass:n.markers.item,onHighlight:n.onHighlight}),Qf.config({store:{mode:"memory",initialValue:n.value}}),ld.config({find:O.some}),sa.config(n.movement.config(n,n.movement))]),events:Tr([Er(Cm(),function(e,t){var n=t.event();e.getSystem().getByDom(n.target()).each(function(n){Ou.highlight(e,n),t.stop(),be(e,Am(),{menu:e,item:n})})}),Er(Em(),function(n,e){var t=e.event().item();Ou.highlight(n,t)})]),components:e,eventOrder:n.eventOrder,domModification:{attributes:{role:"menu"}}}}}),Hm=function(n,t){var r=je(t),e=Oo(r).bind(function(e){var o,i,n=function(n){return Ae(e,n)};return n(t)?O.some(t):(o=n,(i=function(n){for(var e=0;e<n.childNodes.length;e++){var t=Te.fromDom(n.childNodes[e]);if(o(t))return O.some(t);var r=i(n.childNodes[e]);if(r.isSome())return r}return O.none()})(t.dom()))}),o=n(t);return e.each(function(e){Oo(r).filter(function(n){return Ae(n,e)}).fold(function(){wo(e)},w)}),o},Nm=function(n,e,t,r){var o=n.getSystem().build(r);Qe(n,o,t)},Pm=function(n,e,t,r){var o=zm(n);bn(o,function(n){return Ae(r.element(),n.element())}).each(Ze)},zm=function(n,e){return n.components()},Lm=function(e,n,t,o,r){var i=zm(e);return O.from(i[o]).map(function(n){return Pm(e,0,0,n),r.each(function(n){Nm(e,0,function(n,e){var t,r;r=e,Pe(t=n,o).fold(function(){$e(t,r)},function(n){ze(n,r)})},n)}),n})},Gm=Xr({fields:[],name:"replacing",apis:/* */Object.freeze({__proto__:null,append:function(n,e,t,r){Nm(n,0,$e,r)},prepend:function(n,e,t,r){Nm(n,0,Ge,r)},remove:Pm,replaceAt:Lm,replaceBy:function(e,n,t,r,o){var i=zm(e);return xn(i,r).bind(function(n){return Lm(e,0,0,n,o)})},set:function(e,n,t,r){Hm(function(){var n=gn(r,e.getSystem().build);Je(e,n)},e.element())},contents:zm})}),$m=function(t,r,o,n){return I(o,n).bind(function(n){return I(t,n).bind(function(n){var e=$m(t,r,o,n);return O.some([n].concat(e))})}).getOr([])},Um=function(n,e){var t={};E(n,function(n,e){pn(n,function(n){t[n]=e})});var r=e,o=M(e,function(n,e){return{k:n,v:e}}),i=C(o,function(n,e){return[e].concat($m(t,r,o,e))});return C(t,function(n){return I(i,n).getOr([n])})},Wm=function(n){return"prepared"===n.type?O.some(n.menu):O.none()},Xm={init:function(){var i=ho({}),u=ho({}),c=ho({}),a=ho(O.none()),f=ho({}),s=function(t){return function(n,e){for(var t=T(n),r=0,o=t.length;r<o;r++){var i=t[r],u=n[i];if(e(u,i,n))return O.some(u)}return O.none()}(i.get(),function(n,e){return n===t})},l=function(n){return e(n).bind(Wm)},e=function(n){return I(u.get(),n)},t=function(n){return I(i.get(),n)};return{setMenuBuilt:function(n,e){var t;u.set(x(x({},u.get()),((t={})[n]={type:"prepared",menu:e},t)))},setContents:function(n,e,t,r){a.set(O.some(n)),i.set(t),u.set(e),f.set(r);var o=Um(r,t);c.set(o)},expand:function(t){return I(i.get(),t).map(function(n){var e=I(c.get(),t).getOr([]);return[n].concat(e)})},refresh:function(n){return I(c.get(),n)},collapse:function(n){return I(c.get(),n).bind(function(n){return 1<n.length?O.some(n.slice(1)):O.none()})},lookupMenu:e,lookupItem:t,otherMenus:function(n){var e=f.get();return kn(T(e),n)},getPrimary:function(){return a.get().bind(l)},getMenus:function(){return u.get()},clear:function(){i.set({}),u.set({}),c.set({}),a.set(O.none())},isClear:function(){return a.get().isNone()},getTriggeringPath:function(n,u){var e=hn(t(n).toArray(),function(n){return l(n).isSome()});return I(c.get(),n).bind(function(n){var i=Tn(e.concat(n));return function(n){for(var e=[],t=0;t<n.length;t++){var r=n[t];if(!r.isSome())return O.none();e.push(r.getOrDie())}return O.some(e)}(Sn(i,function(n,e){return t=n,r=u,o=i.slice(0,e+1),l(t).bind(function(e){return s(t).bind(function(n){return r(n).map(function(n){return{triggeredMenu:e,triggeringItem:n,triggeringPath:o}})})}).fold(function(){return a.get().is(n)?[]:[O.none()]},function(n){return[O.some(n)]});var t,r,o}))})}}},extractPreparedMenu:Wm},qm=y("collapse-item"),Ym=bf({name:"TieredMenu",configFields:[Jo("onExecute"),Jo("onEscape"),Ko("onOpenMenu"),Ko("onOpenSubmenu"),qo("onRepositionMenu"),qo("onCollapseMenu"),hr("highlightImmediately",!0),lr("data",[fr("primary"),fr("menus"),fr("expansions")]),hr("fakeFocus",!1),qo("onHighlight"),qo("onHover"),lr("markers",[fr("backgroundMenu")].concat(Go()).concat($o())),fr("dom"),hr("navigateOnHover",!0),hr("stayInDom",!1),la("tmenuBehaviours",[sa,Ou,ld,Gm]),hr("eventOrder",{})],apis:{collapseMenu:function(n,e){n.collapseMenu(e)},highlightPrimary:function(n,e){n.highlightPrimary(e)},repositionMenus:function(n,e){n.repositionMenus(e)}},factory:function(c,n){var a,e,i=ho(O.none()),o=function(r,o,n){return C(n,function(n,e){var t=function(){return jm.sketch(x(x({dom:n.dom},n),{value:e,items:n.items,markers:c.markers,fakeFocus:c.fakeFocus,onHighlight:c.onHighlight,focusManager:c.fakeFocus?{get:o=function(n){return Ou.getHighlighted(n).map(function(n){return n.element()})},set:function(e,n){var t=o(e);e.getSystem().getByDom(n).fold(w,function(n){Ou.highlight(e,n)});var r=o(e);Tu(e,t,r)}}:ku()}));var o};return e===o?{type:"prepared",menu:r.getSystem().build(t())}:{type:"notbuilt",nbMenu:t}})},f=Xm.init(),s=function(n){return Qf.getValue(n).value},u=function(n){return C(c.data.menus,function(n,e){return Sn(n.items,function(n){return"separator"===n.type?[]:[n.data.value]})})},l=function(e,n){Ou.highlight(e,n),Ou.getHighlighted(n).orThunk(function(){return Ou.getFirst(n)}).each(function(n){we(e,n.element(),fe())})},d=function(e,n){return su(gn(n,function(n){return e.lookupMenu(n).bind(function(n){return"prepared"===n.type?O.some(n.menu):O.none()})}))},m=function(e,n,t){var r=d(n,n.otherMenus(t));pn(r,function(n){sm(n.element(),[c.markers.backgroundMenu]),c.stayInDom||Gm.remove(e,n)})},g=function(n,r){var o,e=(o=n,i.get().getOrThunk(function(){var t={},n=Li(o.element(),"."+c.markers.item),e=hn(n,function(n){return"true"===Zr(n,"aria-haspopup")});return pn(e,function(n){o.getSystem().getByDom(n).each(function(n){var e=s(n);t[e]=n})}),i.set(O.some(t)),t}));E(e,function(n,e){var t=dn(r,e);Jr(n.element(),"aria-expanded",t)})},p=function(r,o,i){return O.from(i[0]).bind(function(n){return o.lookupMenu(n).bind(function(n){if("notbuilt"===n.type)return O.none();var e=n.menu,t=d(o,i.slice(1));return pn(t,function(n){co(n.element(),c.markers.backgroundMenu)}),_e(e.element())||Gm.append(r,wm(e)),sm(e.element(),[c.markers.backgroundMenu]),l(r,e),m(r,o,i),O.some(e)})})};(e=a=a||{})[e.HighlightSubmenu=0]="HighlightSubmenu",e[e.HighlightParent=1]="HighlightParent";var h=function(o,i,u){void 0===u&&(u=a.HighlightSubmenu);var n=s(i);return f.expand(n).bind(function(r){return g(o,r),O.from(r[0]).bind(function(t){return f.lookupMenu(t).bind(function(n){var e=function(n,e,t){if("notbuilt"!==t.type)return t.menu;var r=n.getSystem().build(t.nbMenu());return f.setMenuBuilt(e,r),r}(o,t,n);return _e(e.element())||Gm.append(o,wm(e)),c.onOpenSubmenu(o,i,e,Tn(r)),u===a.HighlightSubmenu?(Ou.highlightFirst(e),p(o,f,r)):(Ou.dehighlightAll(e),O.some(i))})})})},r=function(e,t){var n=s(t);return f.collapse(n).bind(function(n){return g(e,n),p(e,f,n).map(function(n){return c.onCollapseMenu(e,t,n),n})})},t=function(t){return function(e,n){return Wi(n.getSource(),"."+c.markers.item).bind(function(n){return e.getSystem().getByDom(n).toOption().bind(function(n){return t(e,n).map(function(){return!0})})})}},v=Tr([Er(Am(),function(t,r){var n=r.event().item();f.lookupItem(s(n)).each(function(){var n=r.event().menu();Ou.highlight(t,n);var e=s(r.event().item());f.refresh(e).each(function(n){return m(t,f,n)})})}),Br(function(e,n){var t=n.event().target();e.getSystem().getByDom(t).each(function(n){0===s(n).indexOf("collapse-item")&&r(e,n),h(e,n,a.HighlightSubmenu).fold(function(){c.onExecute(e,n)},function(){})})}),Fr(function(e,n){var t,r;t=o(e,c.data.primary,c.data.menus),r=u(),f.setContents(c.data.primary,t,c.data.expansions,r),f.getPrimary().each(function(n){Gm.append(e,wm(n)),c.onOpenMenu(e,n),c.highlightImmediately&&l(e,n)})})].concat(c.navigateOnHover?[Er(Em(),function(n,e){var t,r,o=e.event().item();t=n,r=s(o),f.refresh(r).bind(function(n){return g(t,n),p(t,f,n)}),h(n,o,a.HighlightParent),c.onHover(n,o)})]:[])),y=function(n){return Ou.getHighlighted(n).bind(Ou.getHighlighted)},b={collapseMenu:function(e){y(e).each(function(n){r(e,n)})},highlightPrimary:function(e){f.getPrimary().each(function(n){l(e,n)})},repositionMenus:function(r){f.getPrimary().bind(function(e){return y(r).bind(function(n){var e,t=s(n),r=(e=f.getMenus(),D(e,function(n){return n})),o=su(gn(r,Xm.extractPreparedMenu));return f.getTriggeringPath(t,function(n){return t=n,Cn(o,function(n){if(!n.getSystem().isConnected())return O.none();var e=Ou.getCandidates(n);return bn(e,function(n){return s(n)===t})});var t})}).map(function(n){return{primary:e,triggeringPath:n}})}).fold(function(){var n;n=r,O.from(n.components()[0]).filter(function(n){return"menu"===Zr(n.element(),"role")}).each(function(n){c.onRepositionMenu(r,n,[])})},function(n){var e=n.primary,t=n.triggeringPath;c.onRepositionMenu(r,e,t)})}};return{uid:c.uid,dom:c.dom,markers:c.markers,behaviours:ma(c.tmenuBehaviours,[sa.config({mode:"special",onRight:t(function(n,e){return Pu(e.element())?O.none():h(n,e,a.HighlightSubmenu)}),onLeft:t(function(n,e){return Pu(e.element())?O.none():r(n,e)}),onEscape:t(function(n,e){return r(n,e).orThunk(function(){return c.onEscape(n,e).map(function(){return n})})}),focusIn:function(e,n){f.getPrimary().each(function(n){we(e,n.element(),fe())})}}),Ou.config({highlightClass:c.markers.selectedMenu,itemClass:c.markers.menu}),ld.config({find:function(n){return Ou.getHighlighted(n)}}),Gm.config({})]),eventOrder:c.eventOrder,apis:b,events:v}},extraApis:{tieredData:function(n,e,t){return{primary:n,menus:e,expansions:t}},singleData:function(n,e){return{primary:n,menus:Ft(n,e),expansions:{}}},collapseItem:function(n){return{value:Ea(qm()),meta:{text:n}}}}}),Km=function(n,e,t,r){return I(e.routes,r.start).bind(function(n){return I(n,r.destination)})},Jm=function(n,e,t,r){return Km(0,e,0,r).bind(function(e){return e.transition.map(function(n){return{transition:n,route:e}})})},Qm=function(t,r,n){var e,o,i;i=n,Zm(e=t,o=r).bind(function(n){return Jm(e,o,i,n)}).each(function(n){var e=n.transition;fo(t.element(),e.transitionClass),to(t.element(),r.destinationAttr)})},Zm=function(e,t,n){var r=e.element();return no(r,t.destinationAttr).map(function(n){return{start:Zr(e.element(),t.stateAttr),destination:n}})},ng=function(n,e,t,r){Qm(n,e,t),eo(n.element(),e.stateAttr)&&Zr(n.element(),e.stateAttr)!==r&&e.onFinish(n,r),Jr(n.element(),e.stateAttr,r)},eg=/* */Object.freeze({__proto__:null,findRoute:Km,disableTransition:Qm,getCurrentRoute:Zm,jumpTo:ng,progressTo:function(t,r,o,i){var e,u;u=r,eo((e=t).element(),u.destinationAttr)&&(no(e.element(),u.destinationAttr).each(function(n){Jr(e.element(),u.stateAttr,n)}),to(e.element(),u.destinationAttr));var n,c,a=(n=r,c=i,{start:Zr(t.element(),n.stateAttr),destination:c});Jm(t,r,o,a).fold(function(){ng(t,r,o,i)},function(n){Qm(t,r,o);var e=n.transition;co(t.element(),e.transitionClass),Jr(t.element(),r.destinationAttr,i)})},getState:function(n,e,t){return no(n.element(),e.stateAttr)}}),tg=/* */Object.freeze({__proto__:null,events:function(o,i){return Tr([Er(ee(),function(t,n){var r=n.event().raw();Zm(t,o).each(function(e){Km(0,o,0,e).each(function(n){n.transition.each(function(n){r.propertyName===n.property&&(ng(t,o,i,e.destination),o.onTransition(t,e))})})})}),Fr(function(n,e){ng(n,o,i,o.initialState)})])}}),rg=[hr("destinationAttr","data-transitioning-destination"),hr("stateAttr","data-transitioning-state"),fr("initialState"),qo("onTransition"),qo("onFinish"),sr("routes",er(rt.value,er(rt.value,Ut([pr("transition",[fr("property"),fr("transitionClass")])]))))],og=Xr({fields:rg,name:"transitioning",active:tg,apis:eg,extra:{createRoutes:function(n){var r={};return E(n,function(n,e){var t=e.split("<->");r[t[0]]=Ft(t[1],n),r[t[1]]=Ft(t[0],n)}),r},createBistate:function(n,e,t){return Rt([{key:n,value:Ft(e,t)},{key:e,value:Ft(n,t)}])},createTristate:function(n,e,t,r){return Rt([{key:n,value:Rt([{key:e,value:r},{key:t,value:r}])},{key:e,value:Rt([{key:n,value:r},{key:t,value:r}])},{key:t,value:Rt([{key:n,value:r},{key:e,value:r}])}])}}}),ig=bi("scrollable"),ug=function(n){co(n,ig)},cg=function(n){fo(n,ig)},ag=ig,fg=function(n){return I(n,"format").getOr(n.title)},sg=function(n,e,t,r,o){return{data:{value:n,text:e},type:"item",dom:{tag:"div",classes:o?[bi("styles-item-is-menu")]:[]},toggling:{toggleOnExecute:!1,toggleClass:bi("format-matches"),selected:t},itemBehaviours:Ur(o?[]:[pi(n,function(n,e){(e?gi.on:gi.off)(n)})]),components:[{dom:{tag:"div",attributes:{style:r},innerHtml:e}}]}},lg=function(n,e,t,r){return{value:n,dom:{tag:"div"},components:[wf.sketch({dom:{tag:"div",classes:[bi("styles-collapser")]},components:r?[{dom:{tag:"span",classes:[bi("styles-collapse-icon")]}},vm(n)]:[vm(n)],action:function(n){if(r){var e=t().get(n);Ym.collapseMenu(e)}}}),{dom:{tag:"div",classes:[bi("styles-menu-items-container")]},components:[jm.parts().items({})],behaviours:Ur([ad("adhoc-scrollable-menu",[Fr(function(n,e){Mi(n.element(),"overflow-y","auto"),Mi(n.element(),"-webkit-overflow-scrolling","touch"),ug(n.element())}),Rr(function(n){Ri(n.element(),"overflow-y"),Ri(n.element(),"-webkit-overflow-scrolling"),cg(n.element())})])])}],items:e,menuBehaviours:Ur([og.config({initialState:"after",routes:og.createTristate("before","current","after",{transition:{property:"transform",transitionClass:"transitioning"}})})])}},dg=function(r){var o,i,n,e,t,u=(o=r.formats,i=function(){return c},n=lg("Styles",[].concat(gn(o.items,function(n){return sg(fg(n),n.title,n.isSelected(),n.getPreview(),R(o.expansions,fg(n)))})),i,!1),e=C(o.menus,function(n,e){var t=gn(n,function(n){return sg(fg(n),n.title,n.isSelected!==undefined&&n.isSelected(),n.getPreview!==undefined?n.getPreview():"",R(o.expansions,fg(n)))});return lg(e,t,i,!0)}),t=ct(e,Ft("styles",n)),{tmenu:Ym.tieredData("styles",t,o.expansions)}),c=zl(Ym.sketch({dom:{tag:"div",classes:[bi("styles-menu")]},components:[],fakeFocus:!0,stayInDom:!0,onExecute:function(n,e){var t=Qf.getValue(e);return r.handle(e,t.value),O.none()},onEscape:function(){return O.none()},onOpenMenu:function(n,e){var t=es(n.element());ns(e.element(),t),og.jumpTo(e,"current")},onOpenSubmenu:function(n,e,t){var r=es(n.element()),o=$i(e.element(),'[role="menu"]').getOrDie("hacky"),i=n.getSystem().getByDom(o).getOrDie();ns(t.element(),r),og.progressTo(i,"before"),og.jumpTo(t,"after"),og.progressTo(t,"current")},onCollapseMenu:function(n,e,t){var r=$i(e.element(),'[role="menu"]').getOrDie("hacky"),o=n.getSystem().getByDom(r).getOrDie();og.progressTo(o,"after"),og.progressTo(t,"current")},navigateOnHover:!1,highlightImmediately:!0,data:u.tmenu,markers:{backgroundMenu:bi("styles-background-menu"),menu:bi("styles-menu"),selectedMenu:bi("styles-selected-menu"),item:bi("styles-item"),selectedItem:bi("styles-selected-item")}}));return c.asSpec()},mg=function(n){return R(n,"items")?(t=ct(It(e=n,["items"]),{menu:!0}),r=gg(e.items),{item:t,menus:ct(r.menus,Ft(e.title,r.items)),expansions:ct(r.expansions,Ft(e.title,e.title))}):{item:n,menus:{},expansions:{}};var e,t,r},gg=function(n){return vn(n,function(n,e){var t=mg(e);return{menus:ct(n.menus,t.menus),items:[t.item].concat(n.items),expansions:ct(n.expansions,t.expansions)}},{menus:{},expansions:{},items:[]})},pg=function(u,n){var c=function(n){return function(){return u.formatter.match(n)}},a=function(n){return function(){return u.formatter.getCssText(n)}},e=I(n,"style_formats").getOr(Xd),f=function(n){return gn(n,function(n){if(R(n,"items")){var e=f(n.items);return ct(ct(n,{isSelected:y(!1),getPreview:y("")}),{items:e})}return R(n,"format")?ct(i=n,{isSelected:c(i.format),getPreview:a(i.format)}):(r=Ea((t=n).title),o=ct(t,{format:r,isSelected:c(r),getPreview:a(r)}),u.formatter.register(r,o),o);var t,r,o,i})};return f(e)},hg=function(t,n,r){var e,o,i,u=(e=t,i=(o=function(n){return Sn(n,function(n){return n.items===undefined?!R(n,"format")||e.formatter.canApply(n.format)?[n]:[]:0<o(n.items).length?[n]:[]})})(n),gg(i));return dg({formats:u,handle:function(n,e){t.undoManager.transact(function(){gi.isOn(n)?t.formatter.remove(e):t.formatter.apply(e)}),r()}})},vg=["undo","bold","italic","link","image","bullist","styleselect"],yg=function(n){var e=n.replace(/\|/g," ").trim();return 0<e.length?e.split(/\s+/):[]},bg=function(n){return Sn(n,function(n){return(on(n)?bg:yg)(n)})},xg=function(t,o){var n=function(t){return function(){return _f(e=t,function(){n.execCommand(e)},{},n=o);var n,e}},e=function(r){return function(){return n=o,t=Ef(e=r),_f(e,function(){n.execCommand(e)},t,n);var n,e,t}},r=function(n,e,t){return function(){return Cf(o,n,e,t)}},i=n("undo"),u=n("redo"),c=e("bold"),a=e("italic"),f=e("underline"),s=n("removeformat"),l=r("unlink","link",function(){o.execCommand("unlink",null,!1)}),d=r("unordered-list","ul",function(){o.execCommand("InsertUnorderedList",null,!1)}),m=r("ordered-list","ol",function(){o.execCommand("InsertOrderedList",null,!1)}),g=pg(o,o.settings),p=function(){return hg(o,g,function(){o.fire("scrollIntoView")})},h=function(n,e){return{isSupported:function(){var e=o.ui.registry.getAll().buttons;return n.forall(function(n){return R(e,n)})},sketch:e}};return{undo:h(O.none(),i),redo:h(O.none(),u),bold:h(O.none(),c),italic:h(O.none(),a),underline:h(O.none(),f),removeformat:h(O.none(),s),link:h(O.none(),function(){return Wd(t,o)}),unlink:h(O.none(),l),image:h(O.none(),function(){return Ql(o)}),bullist:h(O.some("bullist"),d),numlist:h(O.some("numlist"),m),fontsizeselect:h(O.none(),function(){return n={onChange:function(n){Hl(e,n)},getInitialValue:function(){return jl(e)}},Dl(t,"font-size",function(){return Pl(n)},e=o);var e,n}),forecolor:h(O.none(),function(){return n={onChange:function(n,e,t){r.undoManager.transact(function(){r.formatter.apply("forecolor",{value:t}),r.nodeChanged()})},getInitialValue:function(){return-1}},Dl(t,"color-levels",function(){return Il(n)},r=o);var r,n}),styleselect:h(O.none(),function(){return _f("style-formats",function(n){o.fire("toReading"),t.dropup().appear(p,gi.on,n)},Ur([gi.config({toggleClass:bi("toolbar-button-selected"),toggleOnExecute:!1,aria:{mode:"pressed"}}),ti.config({channels:Rt([vi(Mo,gi.off),vi(_o,gi.off)])})]),o)})}},wg=function(n,t){var e,r,o=(r=(e=n).toolbar!==undefined?e.toolbar:vg,(on(r)?bg:yg)(r)),i={};return Sn(o,function(n){var e=!R(i,n)&&R(t,n)&&t[n].isSupported()?[t[n].sketch()]:[];return i[n]=!0,e})},Sg=function(n){var e,t,r,o,i,u,c,a=Te.fromDom(n.target),f=function(){return n.stopPropagation()},s=function(){return n.preventDefault()},l=d(s,f);return e=a,t=n.clientX,r=n.clientY,o=f,i=s,u=l,c=n,{target:y(e),x:y(t),y:y(r),stop:o,prevent:i,kill:u,raw:y(c)}},Og=function(n,e,t,r,o){var i,u,c=(i=t,u=r,function(n){i(n)&&u(Sg(n))});return n.dom().addEventListener(e,c,o),{unbind:S(Tg,n,e,c,o)}},Tg=function(n,e,t,r){n.dom().removeEventListener(e,t,r)},kg=y(!0),Eg=function(n,e,t){return Og(n,e,kg,t,!1)},Cg=function(n,e,t){return Og(n,e,kg,t,!0)},Mg=tinymce.util.Tools.resolve("tinymce.util.Delay"),_g=function(n){var e=n.matchMedia("(orientation: portrait)").matches;return{isPortrait:y(e)}},Dg=function(r,e){var n=Te.fromDom(r),o=null,t=Eg(n,"orientationchange",function(){Mg.clearInterval(o);var n=_g(r);e.onChange(n),i(function(){e.onReady(n)})}),i=function(n){Mg.clearInterval(o);var e=r.innerHeight,t=0;o=Mg.setInterval(function(){e!==r.innerHeight?(Mg.clearInterval(o),n(O.some(r.innerHeight))):20<t&&(Mg.clearInterval(o),n(O.none())),t++},50)};return{onAdjustment:i,destroy:function(){t.unbind()}}};var Ig=function(n){var e=n.raw();return e.touches===undefined||1!==e.touches.length?O.none():O.some(e.touches[0])},Fg=function(t){var u=ho(O.none()),r=ho(!1),o=function n(t,r){var o=null;return{cancel:function(){null!==o&&(v.clearTimeout(o),o=null)},schedule:function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];o=v.setTimeout(function(){t.apply(null,n),o=null},r)}}}(function(n){t.triggerEvent(le(),n),r.set(!0)},400),i=Rt([{key:Gn(),value:function(t){return Ig(t).each(function(n){o.cancel();var e={x:n.clientX,y:n.clientY,target:t.target()};o.schedule(t),r.set(!1),u.set(O.some(e))}),O.none()}},{key:$n(),value:function(n){return o.cancel(),Ig(n).each(function(i){u.get().each(function(n){var e,t,r,o;e=i,t=n,r=Math.abs(e.clientX-t.x),o=Math.abs(e.clientY-t.y),(5<r||5<o)&&u.set(O.none())})}),O.none()}},{key:Un(),value:function(e){o.cancel();return u.get().filter(function(n){return Ae(n.target,e.target())}).map(function(n){return r.get()?(e.prevent(),!1):t.triggerEvent(se(),e)})}}]);return{fireIfReady:function(e,n){return I(i,n).bind(function(n){return n(e)})}}},Rg=function(t){var e=Fg({triggerEvent:function(n,e){t.onTapContent(e)}});return{fireTouchstart:function(n){e.fireIfReady(n,"touchstart")},onTouchend:function(){return Eg(t.body(),"touchend",function(n){e.fireIfReady(n,"touchend")})},onTouchmove:function(){return Eg(t.body(),"touchmove",function(n){e.fireIfReady(n,"touchmove")})}}},Vg=6<=Ln().os.version.major,Bg=function(r,e,t){var o=Rg(r),i=je(e),u=function(n){return!Ae(n.start(),n.finish())||n.soffset()!==n.foffset()},n=function(){var n=r.doc().dom().hasFocus()&&r.getSelection().exists(u);t.getByDom(e).each(!0===(n||Oo(i).filter(function(n){return"input"===ke(n)}).exists(function(n){return n.dom().selectionStart!==n.dom().selectionEnd}))?gi.on:gi.off)},c=[Eg(r.body(),"touchstart",function(n){r.onTouchContent(),o.fireTouchstart(n)}),o.onTouchmove(),o.onTouchend(),Eg(e,"touchstart",function(n){r.onTouchToolstrip()}),r.onToReading(function(){So(r.body())}),r.onToEditing(w),r.onScrollToCursor(function(n){n.preventDefault(),r.getCursorBox().each(function(n){var e=r.win(),t=n.top()>e.innerHeight||n.bottom()>e.innerHeight?n.bottom()-e.innerHeight+50:0;0!=t&&e.scrollTo(e.pageXOffset,e.pageYOffset+t)})})].concat(!0==Vg?[]:[Eg(Te.fromDom(r.win()),"blur",function(){t.getByDom(e).each(gi.off)}),Eg(i,"select",n),Eg(r.doc(),"selectionchange",n)]);return{destroy:function(){pn(c,function(n){n.unbind()})}}},Ag=function(n,e){var t=parseInt(Zr(n,e),10);return isNaN(t)?0:t};var jg,Hg,Ng=function _v(t,r){var e=function(n){return t(n)?O.from(n.dom().nodeValue):O.none()};return{get:function(n){if(!t(n))throw new Error("Can only get "+r+" value of a "+r+" node");return e(n).getOr("")},getOption:e,set:function(n,e){if(!t(n))throw new Error("Can only set raw "+r+" value of a "+r+" node");n.dom().nodeValue=e}}}(Me,"text"),Pg=function(n){return"img"===ke(n)?1:(e=n,Ng.getOption(e).fold(function(){return Ne(n).length},function(n){return n.length}));var e},zg=function(n,e,t,r){return{start:y(n),soffset:y(e),finish:y(t),foffset:y(r)}},Lg=ot([{before:["element"]},{on:["element","offset"]},{after:["element"]}]),Gg={before:Lg.before,on:Lg.on,after:Lg.after,cata:function(n,e,t,r){return n.fold(e,t,r)},getStart:function(n){return n.fold(b,b,b)}},$g=ot([{domRange:["rng"]},{relative:["startSitu","finishSitu"]},{exact:["start","soffset","finish","foffset"]}]),Ug={domRange:$g.domRange,relative:$g.relative,exact:$g.exact,exactFromRange:function(n){return $g.exact(n.start(),n.soffset(),n.finish(),n.foffset())},getWin:function(n){var e,t=n.match({domRange:function(n){return Te.fromDom(n.startContainer)},relative:function(n,e){return Gg.getStart(n)},exact:function(n,e,t,r){return n}});return e=t,Te.fromDom(e.dom().ownerDocument.defaultView)},range:zg},Wg=function(n,e,t){var r,o,i=n.document.createRange();return r=i,e.fold(function(n){r.setStartBefore(n.dom())},function(n,e){r.setStart(n.dom(),e)},function(n){r.setStartAfter(n.dom())}),o=i,t.fold(function(n){o.setEndBefore(n.dom())},function(n,e){o.setEnd(n.dom(),e)},function(n){o.setEndAfter(n.dom())}),i},Xg=function(n,e,t,r,o){var i=n.document.createRange();return i.setStart(e.dom(),t),i.setEnd(r.dom(),o),i},qg=function(n){return{left:y(n.left),top:y(n.top),right:y(n.right),bottom:y(n.bottom),width:y(n.width),height:y(n.height)}},Yg=ot([{ltr:["start","soffset","finish","foffset"]},{rtl:["start","soffset","finish","foffset"]}]),Kg=function(n,e,t){return e(Te.fromDom(t.startContainer),t.startOffset,Te.fromDom(t.endContainer),t.endOffset)},Jg=function(n,e){var o,t,r,i=(o=n,e.match({domRange:function(n){return{ltr:y(n),rtl:O.none}},relative:function(n,e){return{ltr:V(function(){return Wg(o,n,e)}),rtl:V(function(){return O.some(Wg(o,e,n))})}},exact:function(n,e,t,r){return{ltr:V(function(){return Xg(o,n,e,t,r)}),rtl:V(function(){return O.some(Xg(o,t,r,n,e))})}}}));return(r=(t=i).ltr()).collapsed?t.rtl().filter(function(n){return!1===n.collapsed}).map(function(n){return Yg.rtl(Te.fromDom(n.endContainer),n.endOffset,Te.fromDom(n.startContainer),n.startOffset)}).getOrThunk(function(){return Kg(0,Yg.ltr,r)}):Kg(0,Yg.ltr,r)},Qg=(Yg.ltr,Yg.rtl,function(n,e){var t=ke(n);return"input"===t?Gg.after(n):dn(["br","img"],t)?0===e?Gg.before(n):Gg.after(n):Gg.on(n,e)}),Zg=function(n,e,t,r){var o,i,u,c,a,f=(i=e,u=t,c=r,(a=je(o=n).dom().createRange()).setStart(o.dom(),i),a.setEnd(u.dom(),c),a),s=Ae(n,t)&&e===r;return f.collapsed&&!s},np=function(n,e,t,r,o){var i,u,c=Xg(n,e,t,r,o);i=n,u=c,O.from(i.getSelection()).each(function(n){n.removeAllRanges(),n.addRange(u)})},ep=function(l,n){return Jg(l,n).match({ltr:function(n,e,t,r){np(l,n,e,t,r)},rtl:function(n,e,t,r){var o,i,u,c,a,f=l.getSelection();if(f.setBaseAndExtent)f.setBaseAndExtent(n.dom(),e,t.dom(),r);else if(f.extend)try{i=n,u=e,c=t,a=r,(o=f).collapse(i.dom(),u),o.extend(c.dom(),a)}catch(s){np(l,t,r,n,e)}else np(l,t,r,n,e)}})},tp=function(n,e,t,r,o){var i,u,c,a,f=(i=r,u=o,c=Qg(e,t),a=Qg(i,u),Ug.relative(c,a));ep(n,f)},rp=function(n){var e=Te.fromDom(n.anchorNode),t=Te.fromDom(n.focusNode);return Zg(e,n.anchorOffset,t,n.focusOffset)?O.some(zg(e,n.anchorOffset,t,n.focusOffset)):function(n){if(0<n.rangeCount){var e=n.getRangeAt(0),t=n.getRangeAt(n.rangeCount-1);return O.some(zg(Te.fromDom(e.startContainer),e.startOffset,Te.fromDom(t.endContainer),t.endOffset))}return O.none()}(n)},op=function(n){return O.from(n.getSelection()).filter(function(n){return 0<n.rangeCount}).bind(rp)},ip=function(n,e){var i,t,r,o,u=Jg(i=n,e).match({ltr:function(n,e,t,r){var o=i.document.createRange();return o.setStart(n.dom(),e),o.setEnd(t.dom(),r),o},rtl:function(n,e,t,r){var o=i.document.createRange();return o.setStart(t.dom(),r),o.setEnd(n.dom(),e),o}});return r=(t=u).getClientRects(),0<(o=0<r.length?r[0]:t.getBoundingClientRect()).width||0<o.height?O.some(o).map(qg):O.none()},up=function(n){return{left:n.left,top:n.top,right:n.right,bottom:n.bottom,width:y(2),height:n.height}},cp=function(n){return{left:y(n.left),top:y(n.top),right:y(n.right),bottom:y(n.bottom),width:y(n.width),height:y(n.height)}},ap=function(n){var e=n.getSelection();return e!==undefined&&0<e.rangeCount?function(t){if(t.collapsed){var r=Te.fromDom(t.startContainer);return He(r).bind(function(n){var e=Ug.exact(r,t.startOffset,n,Pg(n));return ip(t.startContainer.ownerDocument.defaultView,e).map(up).map(En)}).getOr([])}return gn(t.getClientRects(),cp)}(e.getRangeAt(0)):[]},fp=function(n){n.focus();var e=Te.fromDom(n.document.body);(Oo().exists(function(n){return dn(["input","textarea"],ke(n))})?function(n){Mg.setTimeout(function(){n()},0)}:f)(function(){Oo().each(So),wo(e)})},sp="data-"+bi("last-outer-height"),lp=function(n,e){Jr(n,sp,e)},dp=function(n){return{top:y(n.top()),bottom:y(n.top()+n.height())}},mp=function(n,e){var t=Ag(e,sp),r=n.innerHeight;return r<t?O.some(t-r):O.none()},gp=function(n,u){var e=Te.fromDom(u.document.body),t=Eg(Te.fromDom(n),"resize",function(){mp(n,e).each(function(i){var n;(0<(n=ap(u)).length?O.some(n[0]).map(dp):O.none()).each(function(n){var e,t,r,o=(e=u,r=i,(t=n).top()>e.innerHeight||t.bottom()>e.innerHeight?Math.min(r,t.bottom()-e.innerHeight+50):0);0!==o&&u.scrollTo(u.pageXOffset,u.pageYOffset+o)})}),lp(e,n.innerHeight)});lp(e,n.innerHeight);return{toEditing:function(){fp(u)},destroy:function(){t.unbind()}}},pp=function(n){return O.from(n.dom().contentWindow)},hp=function(n){return pp(n).bind(op)},vp=function(n){return n.getFrame()},yp=function(n,e,t,r){return n[t].getOrThunk(function(){return function(n){return Eg(e,r,n)}})},bp=function(n){return{left:y(n.left),top:y(n.top),right:y(n.right),bottom:y(n.bottom),width:y(n.width),height:y(n.height)}},xp=function(c){var n,a=vp(c);return n=a,O.some(Te.fromDom(n.dom().contentWindow.document.body)).bind(function(u){return n=a,O.some(Te.fromDom(n.dom().contentWindow.document)).bind(function(i){return pp(a).map(function(o){var n=Te.fromDom(i.dom().documentElement),e=c.getCursorBox.getOrThunk(function(){return function(){return op(o).map(function(n){return Ug.exact(n.start(),n.soffset(),n.finish(),n.foffset())}).bind(function(n){return ip(o,n).orThunk(function(){return op(o).filter(function(n){return Ae(n.start(),n.finish())&&n.soffset()===n.foffset()}).bind(function(n){var e=n.start().dom().getBoundingClientRect();return 0<e.width||0<e.height?O.some(e).map(bp):O.none()})})})}}),t=c.setSelection.getOrThunk(function(){return function(n,e,t,r){tp(o,n,e,t,r)}}),r=c.clearSelection.getOrThunk(function(){return function(){o.getSelection().removeAllRanges()}});return{body:y(u),doc:y(i),win:y(o),html:y(n),getSelection:S(hp,a),setSelection:t,clearSelection:r,frame:y(a),onKeyup:yp(c,i,"onKeyup","keyup"),onNodeChanged:yp(c,i,"onNodeChanged","SelectionChange"),onDomChanged:c.onDomChanged,onScrollToCursor:c.onScrollToCursor,onScrollToElement:c.onScrollToElement,onToReading:c.onToReading,onToEditing:c.onToEditing,onToolbarScrollStart:c.onToolbarScrollStart,onTouchContent:c.onTouchContent,onTapContent:c.onTapContent,onTouchToolstrip:c.onTouchToolstrip,getCursorBox:e}})});var n})},wp=(jg="getWin",Hg=pp,function(e){return e[jg].getOrThunk(function(){var n=vp(e);return function(){return Hg(n)}})()}),Sp="data-ephox-mobile-fullscreen-style",Op="position:absolute!important;",Tp="top:0!important;left:0!important;margin:0!important;padding:0!important;width:100%!important;height:100%!important;overflow:visible!important;",kp=Ln().os.isAndroid(),Ep=function(n,e){var t,r,o,i=function(r){return function(n){var e=Zr(n,"style"),t=e===undefined?"no-styles":e.trim();t!==r&&(Jr(n,Sp,t),Jr(n,"style",r))}},u=(t="*",Pi(n,function(n){return Fe(n,t)},r)),c=Sn(u,function(n){return e="*",zi(n,function(n){return Fe(n,e)});var e}),a=(o=Di(e,"background-color"))!==undefined&&""!==o?"background-color:"+o+"!important":"background-color:rgb(255,255,255)!important;";pn(c,i("display:none!important;")),pn(u,i(Op+Tp+a)),i((!0===kp?"":Op)+Tp+a)(n)},Cp=function(){var n=Ve("["+Sp+"]");pn(n,function(n){var e=Zr(n,Sp);"no-styles"!==e?Jr(n,"style",e):to(n,"style"),to(n,Sp)})},Mp=function(){var e=Gi("head").getOrDie(),n=Gi('meta[name="viewport"]').getOrThunk(function(){var n=Te.fromTag("meta");return Jr(n,"name","viewport"),$e(e,n),n}),t=Zr(n,"content");return{maximize:function(){Jr(n,"content","width=device-width, initial-scale=1.0, user-scalable=no, maximum-scale=1.0")},restore:function(){t!==undefined&&null!==t&&0<t.length?Jr(n,"content",t):Jr(n,"content","user-scalable=yes")}}},_p=function(t,r){var o=null;return{cancel:function(){null!==o&&(v.clearTimeout(o),o=null)},throttle:function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];null!==o&&v.clearTimeout(o),o=v.setTimeout(function(){t.apply(null,n),o=null},r)}}},Dp=function(n,e){var t,r,o,i=zl(dd.sketch({dom:Tf('<div aria-hidden="true" class="${prefix}-mask-tap-icon"></div>'),containerBehaviours:Ur([gi.config({toggleClass:bi("mask-tap-icon-selected"),toggleOnExecute:!1})])})),u=(t=n,r=200,o=null,{cancel:function(){null!==o&&(v.clearTimeout(o),o=null)},throttle:function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];null===o&&(o=v.setTimeout(function(){t.apply(null,n),o=null},r))}});return dd.sketch({dom:Tf('<div class="${prefix}-disabled-mask"></div>'),components:[dd.sketch({dom:Tf('<div class="${prefix}-content-container"></div>'),components:[wf.sketch({dom:Tf('<div class="${prefix}-content-tap-section"></div>'),components:[i.asSpec()],action:function(n){u.throttle()},buttonBehaviours:Ur([gi.config({toggleClass:bi("mask-tap-icon-selected")})])})]})]})},Ip=Wt([lr("editor",[fr("getFrame"),dr("getBody"),dr("getDoc"),dr("getWin"),dr("getSelection"),dr("setSelection"),dr("clearSelection"),dr("cursorSaver"),dr("onKeyup"),dr("onNodeChanged"),dr("getCursorBox"),fr("onDomChanged"),hr("onTouchContent",w),hr("onTapContent",w),hr("onTouchToolstrip",w),hr("onScrollToCursor",y({unbind:w})),hr("onScrollToElement",y({unbind:w})),hr("onToEditing",y({unbind:w})),hr("onToReading",y({unbind:w})),hr("onToolbarScrollStart",b)]),fr("socket"),fr("toolstrip"),fr("dropup"),fr("toolbar"),fr("container"),fr("alloy"),yr("win",function(n){return je(n.socket).dom().defaultView}),yr("body",function(n){return Te.fromDom(n.socket.dom().ownerDocument.body)}),hr("translate",b),hr("setReadOnly",w),hr("readOnlyOnInit",y(!0))]),Fp=function(n){var e=or("Getting AndroidWebapp schema",Ip,n);Mi(e.toolstrip,"width","100%");var t=xm(Dp(function(){e.setReadOnly(e.readOnlyOnInit()),f.enter()},e.translate));e.alloy.add(t);var r={show:function(){e.alloy.add(t)},hide:function(){e.alloy.remove(t)}};$e(e.container,t.element());var o,i,u,c,a,f=(o=e,i=r,u=Mp(),c=Ld(),a=Ld(),{enter:function(){i.hide(),co(o.container,bi("fullscreen-maximized")),co(o.container,bi("android-maximized")),u.maximize(),co(o.body,bi("android-scroll-reload")),c.set(gp(o.win,wp(o.editor).getOrDie("no"))),xp(o.editor).each(function(n){Ep(o.container,n.body()),a.set(Bg(n,o.toolstrip,o.alloy))})},exit:function(){u.restore(),i.show(),fo(o.container,bi("fullscreen-maximized")),fo(o.container,bi("android-maximized")),Cp(),fo(o.body,bi("android-scroll-reload")),a.clear(),c.clear()}});return{setReadOnly:e.setReadOnly,refreshStructure:w,enter:f.enter,exit:f.exit,destroy:w}},Rp=y([fr("dom"),hr("shell",!0),la("toolbarBehaviours",[Gm])]),Vp=y([La({name:"groups",overrides:function(){return{behaviours:Ur([Gm.config({})])}}})]),Bp=xf({name:"Toolbar",configFields:Rp(),partFields:Vp(),factory:function(e,n,t,r){var o=function(n){return e.shell?O.some(n):Ka(n,e,"groups")},i=e.shell?{behaviours:[Gm.config({})],components:[]}:{behaviours:[],components:n};return{uid:e.uid,dom:e.dom,components:i.components,behaviours:ma(e.toolbarBehaviours,i.behaviours),apis:{setGroups:function(n,e){o(n).fold(function(){throw v.console.error("Toolbar was defined to not be a shell, but no groups container was specified in components"),new Error("Toolbar was defined to not be a shell, but no groups container was specified in components")},function(n){Gm.set(n,e)})}},domModification:{attributes:{role:"group"}}}},apis:{setGroups:function(n,e,t){n.setGroups(e,t)}}}),Ap=y([fr("items"),lr("markers",gn(["itemSelector"],fr)),la("tgroupBehaviours",[sa])]),jp=y([Ga({name:"items",unit:"item"})]),Hp=xf({name:"ToolbarGroup",configFields:Ap(),partFields:jp(),factory:function(n,e,t,r){return{uid:n.uid,dom:n.dom,components:e,behaviours:ma(n.tgroupBehaviours,[sa.config({mode:"flow",selector:n.markers.itemSelector})]),domModification:{attributes:{role:"toolbar"}}}}}),Np="data-"+bi("horizontal-scroll"),Pp=function(n){Jr(n,Np,"true")},zp=function(n){return"true"===Zr(n,Np)?0<(t=n).dom().scrollLeft||function(n){n.dom().scrollLeft=1;var e=0!==n.dom().scrollLeft;return n.dom().scrollLeft=0,e}(t):0<(e=n).dom().scrollTop||function(n){n.dom().scrollTop=1;var e=0!==n.dom().scrollTop;return n.dom().scrollTop=0,e}(e);var e,t};function Lp(){var e=function(n){var e=!0===n.scrollable?"${prefix}-toolbar-scrollable-group":"";return{dom:Tf('<div aria-label="'+n.label+'" class="${prefix}-toolbar-group '+e+'"></div>'),tgroupBehaviours:Ur([ad("adhoc-scrollable-toolbar",!0===n.scrollable?[Vr(function(n,e){Mi(n.element(),"overflow-x","auto"),Pp(n.element()),ug(n.element())})]:[])]),components:[dd.sketch({components:[Hp.parts().items({})]})],markers:{itemSelector:"."+bi("toolbar-group-item")},items:n.items}},t=xm(Bp.sketch({dom:Tf('<div class="${prefix}-toolbar"></div>'),components:[Bp.parts().groups({})],toolbarBehaviours:Ur([gi.config({toggleClass:bi("context-toolbar"),toggleOnExecute:!1,aria:{mode:"none"}}),sa.config({mode:"cyclic"})]),shell:!0})),n=xm(dd.sketch({dom:{classes:[bi("toolstrip")]},components:[wm(t)],containerBehaviours:Ur([gi.config({toggleClass:bi("android-selection-context-toolbar"),toggleOnExecute:!1})])})),r=function(){Bp.setGroups(t,o.get()),gi.off(t)},o=ho([]);return{wrapper:y(n),toolbar:y(t),createGroups:function(n){return gn(n,d(Hp.sketch,e))},setGroups:function(n){o.set(n),r()},setContextToolbar:function(n){gi.on(t),Bp.setGroups(t,n)},restoreToolbar:function(){gi.isOn(t)&&r()},refresh:function(){},focus:function(){sa.focusIn(t)}}}var Gp=function(n){return xm(wf.sketch({dom:Tf('<div class="${prefix}-mask-edit-icon ${prefix}-icon"></div>'),action:function(){n.run(function(n){n.setReadOnly(!1)})}}))},$p=function(){return xm(dd.sketch({dom:Tf('<div class="${prefix}-editor-socket"></div>'),components:[],containerBehaviours:Ur([Gm.config({})])}))},Up=function(n,e){Gm.append(n,wm(e))},Wp=function(n,e){Gm.remove(n,e)},Xp=function(n,e,t,r){(!0===t?po.toAlpha:po.toOmega)(r),(t?Up:Wp)(n,e)},qp=function(e,n){return n.getAnimationRoot.fold(function(){return e.element()},function(n){return n(e)})},Yp=function(n){return n.dimension.property},Kp=function(n,e){return n.dimension.getDimension(e)},Jp=function(n,e){var t=qp(n,e);sm(t,[e.shrinkingClass,e.growingClass])},Qp=function(n,e){fo(n.element(),e.openClass),co(n.element(),e.closedClass),Mi(n.element(),Yp(e),"0px"),Vi(n.element())},Zp=function(n,e){fo(n.element(),e.closedClass),co(n.element(),e.openClass),Ri(n.element(),Yp(e))},nh=function(n,e,t,r){t.setCollapsed(),Mi(n.element(),Yp(e),Kp(e,n.element())),Vi(n.element()),Jp(n,e),Qp(n,e),e.onStartShrink(n),e.onShrunk(n)},eh=function(n,e,t,r){var o=r.getOrThunk(function(){return Kp(e,n.element())});t.setCollapsed(),Mi(n.element(),Yp(e),o),Vi(n.element());var i=qp(n,e);fo(i,e.growingClass),co(i,e.shrinkingClass),Qp(n,e),e.onStartShrink(n)},th=function(n,e,t){var r=Kp(e,n.element());("0px"===r?nh:eh)(n,e,t,O.some(r))},rh=function(n,e,t){var r=qp(n,e),o=so(r,e.shrinkingClass),i=Kp(e,n.element());Zp(n,e);var u=Kp(e,n.element());(o?function(){Mi(n.element(),Yp(e),i),Vi(n.element())}:function(){Qp(n,e)})(),fo(r,e.shrinkingClass),co(r,e.growingClass),Zp(n,e),Mi(n.element(),Yp(e),u),t.setExpanded(),e.onStartGrow(n)},oh=function(n,e,t){var r=qp(n,e);return!0===so(r,e.growingClass)},ih=function(n,e,t){var r=qp(n,e);return!0===so(r,e.shrinkingClass)},uh=/* */Object.freeze({__proto__:null,refresh:function(n,e,t){if(t.isExpanded()){Ri(n.element(),Yp(e));var r=Kp(e,n.element());Mi(n.element(),Yp(e),r)}},grow:function(n,e,t){t.isExpanded()||rh(n,e,t)},shrink:function(n,e,t){t.isExpanded()&&th(n,e,t)},immediateShrink:function(n,e,t){t.isExpanded()&&nh(n,e,t,O.none())},hasGrown:function(n,e,t){return t.isExpanded()},hasShrunk:function(n,e,t){return t.isCollapsed()},isGrowing:oh,isShrinking:ih,isTransitioning:function(n,e,t){return!0===oh(n,e)||!0===ih(n,e)},toggleGrow:function(n,e,t){(t.isExpanded()?th:rh)(n,e,t)},disableTransitions:Jp}),ch=/* */Object.freeze({__proto__:null,exhibit:function(n,e,t){var r=e.expanded;return Hr(r?{classes:[e.openClass],styles:{}}:{classes:[e.closedClass],styles:Ft(e.dimension.property,"0px")})},events:function(t,r){return Tr([(n=ee(),Mr(n)(function(n,e){e.event().raw().propertyName===t.dimension.property&&(Jp(n,t),r.isExpanded()&&Ri(n.element(),t.dimension.property),(r.isExpanded()?t.onGrown:t.onShrunk)(n))}))]);var n}}),ah=[fr("closedClass"),fr("openClass"),fr("shrinkingClass"),fr("growingClass"),dr("getAnimationRoot"),qo("onShrunk"),qo("onStartShrink"),qo("onGrown"),qo("onStartGrow"),hr("expanded",!1),sr("dimension",ur("property",{width:[Qo("property","width"),Qo("getDimension",function(n){return es(n)+"px"})],height:[Qo("property","height"),Qo("getDimension",function(n){return Ni(n)+"px"})]}))],fh=Xr({fields:ah,name:"sliding",active:ch,apis:uh,state:/* */Object.freeze({__proto__:null,init:function(n){var e=ho(n.expanded);return $r({isExpanded:function(){return!0===e.get()},isCollapsed:function(){return!1===e.get()},setCollapsed:S(e.set,!1),setExpanded:S(e.set,!0),readState:function(){return"expanded: "+e.get()}})}})}),sh=function(e,t){var r=xm(dd.sketch({dom:{tag:"div",classes:[bi("dropup")]},components:[],containerBehaviours:Ur([Gm.config({}),fh.config({closedClass:bi("dropup-closed"),openClass:bi("dropup-open"),shrinkingClass:bi("dropup-shrinking"),growingClass:bi("dropup-growing"),dimension:{property:"height"},onShrunk:function(n){e(),t(),Gm.set(n,[])},onGrown:function(n){e(),t()}}),hi(function(n,e){o(w)})])})),o=function(n){v.window.requestAnimationFrame(function(){n(),fh.shrink(r)})};return{appear:function(n,e,t){!0===fh.hasShrunk(r)&&!1===fh.isTransitioning(r)&&v.window.requestAnimationFrame(function(){e(t),Gm.set(r,[n()]),fh.grow(r)})},disappear:o,component:y(r),element:r.element}},lh=function(n){var e,t;return 8===n.raw().which&&!dn(["input","textarea"],ke(n.target()))&&(e=n.target(),!Wi(e,'[contenteditable="true"]',t).isSome())},dh=function(){return Ln().browser.isFirefox()},mh=Ut([sr("triggerEvent",ar),hr("stopBackspace",!0)]),gh=function(e,n){var t,r,o,i,u=or("Getting GUI events settings",mh,n),c=Fg(u),a=gn(["touchstart","touchmove","touchend","touchcancel","gesturestart","mousedown","mouseup","mouseover","mousemove","mouseout","click"].concat(["selectstart","input","contextmenu","change","transitionend","drag","dragstart","dragend","dragenter","dragleave","dragover","drop","keyup"]),function(n){return Eg(e,n,function(e){c.fireIfReady(e,n).each(function(n){n&&e.kill()}),u.triggerEvent(n,e)&&e.kill()})}),f=ho(O.none()),s=Eg(e,"paste",function(e){c.fireIfReady(e,"paste").each(function(n){n&&e.kill()}),u.triggerEvent("paste",e)&&e.kill(),f.set(O.some(v.setTimeout(function(){u.triggerEvent(ue(),e)},0)))}),l=Eg(e,"keydown",function(n){u.triggerEvent("keydown",n)?n.kill():!0===u.stopBackspace&&lh(n)&&n.prevent()}),d=(t=e,r=function(n){u.triggerEvent("focusin",n)&&n.kill()},dh()?Cg(t,"focus",r):Eg(t,"focusin",r)),m=ho(O.none()),g=(o=e,i=function(n){u.triggerEvent("focusout",n)&&n.kill(),m.set(O.some(v.setTimeout(function(){u.triggerEvent(ie(),n)},0)))},dh()?Cg(o,"blur",i):Eg(o,"focusout",i));return{unbind:function(){pn(a,function(n){n.unbind()}),l.unbind(),d.unbind(),g.unbind(),s.unbind(),f.get().each(v.clearTimeout),m.get().each(v.clearTimeout)}}},ph=function(n,e){var t=I(n,"target").map(function(n){return n()}).getOr(e);return ho(t)},hh=ot([{stopped:[]},{resume:["element"]},{complete:[]}]),vh=function(n,r,e,t,o,i){var u,c,a,f,s=n(r,t),l=(u=e,c=o,a=ho(!1),f=ho(!1),{stop:function(){a.set(!0)},cut:function(){f.set(!0)},isStopped:a.get,isCut:f.get,event:y(u),setSource:c.set,getSource:c.get});return s.fold(function(){return i.logEventNoHandlers(r,t),hh.complete()},function(e){var t=e.descHandler;return tm(t)(l),l.isStopped()?(i.logEventStopped(r,e.element,t.purpose()),hh.stopped()):l.isCut()?(i.logEventCut(r,e.element,t.purpose()),hh.complete()):He(e.element).fold(function(){return i.logNoParent(r,e.element,t.purpose()),hh.complete()},function(n){return i.logEventResponse(r,e.element,t.purpose()),hh.resume(n)})})},yh=function(e,t,r,n,o,i){return vh(e,t,r,n,o,i).fold(function(){return!0},function(n){return yh(e,t,r,n,o,i)},function(){return!1})},bh=function(n,e,t){var r,o,i=(r=e,o=ho(!1),{stop:function(){o.set(!0)},cut:w,isStopped:o.get,isCut:y(!1),event:y(r),setSource:a("Cannot set source of a broadcasted event"),getSource:a("Cannot get source of a broadcasted event")});return pn(n,function(n){var e=n.descHandler();tm(e)(i)}),i.isStopped()},xh=function(n,e,t,r,o){var i=ph(t,r);return yh(n,e,t,r,i,o)},wh=function(n,e){return{element:n,descHandler:e}},Sh=function(n,e){return{id:y(n),descHandler:y(e)}};function Oh(){var i={};return{registerId:function(r,o,n){E(n,function(n,e){var t=i[e]!==undefined?i[e]:{};t[o]=em(n,r),i[e]=t})},unregisterId:function(t){E(i,function(n,e){n.hasOwnProperty(t)&&delete n[t]})},filterByType:function(n){return I(i,n).map(function(n){return D(n,function(n,e){return Sh(e,n)})}).getOr([])},find:function(n,e,t){var o=I(i,e);return xr(t,function(n){return t=o,ff(r=n).fold(function(){return O.none()},function(e){return t.bind(function(n){return I(n,e)}).map(function(n){return wh(r,n)})});var t,r},n)}}}function Th(){var r=Oh(),o={},i=function(r){var n=r.element();return ff(n).fold(function(){return n="uid-",e=r.element(),t=Ea(uf+n),af(e,t),t;var n,e,t},function(n){return n})},u=function(n){ff(n.element()).each(function(n){delete o[n],r.unregisterId(n)})};return{find:function(n,e,t){return r.find(n,e,t)},filter:function(n){return r.filterByType(n)},register:function(n){var e=i(n);R(o,e)&&function(n,e){var t=o[e];if(t!==n)throw new Error('The tagId "'+e+'" is already used by: '+Vo(t.element())+"\nCannot use it for: "+Vo(n.element())+"\nThe conflicting element is"+(_e(t.element())?" ":" not ")+"already in the DOM");u(n)}(n,e);var t=[n];r.registerId(t,e,n.events()),o[e]=n},unregister:u,getById:function(n){return I(o,n)}}}var kh=function(t){var r=function(e){return He(t.element()).fold(function(){return!0},function(n){return Ae(e,n)})},o=Th(),f=function(n,e){return o.find(r,n,e)},n=gh(t.element(),{triggerEvent:function(u,c){return zo(u,c.target(),function(n){return e=f,t=u,o=n,i=(r=c).target(),xh(e,t,r,i,o);var e,t,r,o,i})}}),i={debugInfo:y("real"),triggerEvent:function(e,t,r){zo(e,t,function(n){return xh(f,e,r,t,n)})},triggerFocus:function(c,a){ff(c).fold(function(){wo(c)},function(n){zo(oe(),c,function(n){var e,t,r,o,i,u;return e=f,t=oe(),r={originator:y(a),kill:w,prevent:w,target:y(c)},i=n,u=ph(r,o=c),vh(e,t,r,o,u,i),!1})})},triggerEscape:function(n,e){i.triggerEvent("keydown",n.element(),e.event())},getByUid:function(n){return g(n)},getByDom:function(n){return p(n)},build:xm,addToGui:function(n){c(n)},removeFromGui:function(n){a(n)},addToWorld:function(n){e(n)},removeFromWorld:function(n){u(n)},broadcast:function(n){l(n)},broadcastOn:function(n,e){d(n,e)},broadcastEvent:function(n,e){m(n,e)},isConnected:y(!0)},e=function(n){n.connect(i),Me(n.element())||(o.register(n),pn(n.components(),e),i.triggerEvent(de(),n.element(),{target:y(n.element())}))},u=function(n){Me(n.element())||(pn(n.components(),u),o.unregister(n)),n.disconnect()},c=function(n){Qe(t,n,$e)},a=function(n){Ze(n)},s=function(t){var n=o.filter(ce());pn(n,function(n){var e=n.descHandler();tm(e)(t)})},l=function(n){s({universal:y(!0),data:y(n)})},d=function(n,e){s({universal:y(!1),channels:y(n),data:y(e)})},m=function(n,e){var t=o.filter(n);return bh(t,e)},g=function(n){return o.getById(n).fold(function(){return rt.error(new Error('Could not find component with uid: "'+n+'" in system.'))},rt.value)},p=function(n){var e=ff(n).getOr("not found");return g(e)};return e(t),{root:y(t),element:t.element,destroy:function(){n.unbind(),Xe(t.element())},add:c,remove:a,getByUid:g,getByDom:p,addToWorld:e,removeFromWorld:u,broadcast:l,broadcastOn:d,broadcastEvent:m}},Eh=bi("readonly-mode"),Ch=bi("edit-mode");function Mh(n){var e=xm(dd.sketch({dom:{classes:[bi("outer-container")].concat(n.classes)},containerBehaviours:Ur([po.config({alpha:Eh,omega:Ch})])}));return kh(e)}var _h=function(n,e){var t=Te.fromTag("input");_i(t,{opacity:"0",position:"absolute",top:"-1000px",left:"-1000px"}),$e(n,t),wo(t),e(t),Xe(t)},Dh=function(n){var e=n.getSelection();if(0<e.rangeCount){var t=e.getRangeAt(0),r=n.document.createRange();r.setStart(t.startContainer,t.startOffset),r.setEnd(t.endContainer,t.endOffset),e.removeAllRanges(),e.addRange(r)}},Ih=function(n,e){Oo().each(function(n){Ae(n,e)||So(n)}),n.focus(),wo(Te.fromDom(n.document.body)),Dh(n)},Fh=function(n,e,t,r){var o=function(){Ih(e,r)},i=Eg(t,"keydown",function(n){dn(["input","textarea"],ke(n.target()))||o()});return{toReading:function(){_h(n,So)},toEditing:o,onToolbarTouch:function(){},destroy:function(){i.unbind()}}},Rh=function(t,r,o,i,n){var u=function(){r.run(function(n){n.refreshSelection()})},e=function(n,e){var t=n-i.dom().scrollTop;r.run(function(n){n.scrollIntoView(t,t+e)})},c=function(){r.run(function(n){n.clearSelection()})},a=function(){t.getCursorBox().each(function(n){e(n.top(),n.height())}),r.run(function(n){n.syncHeight()})},f=Rg(t),s=_p(a,300),l=[t.onKeyup(function(){c(),s.throttle()}),t.onNodeChanged(u),t.onDomChanged(s.throttle),t.onDomChanged(u),t.onScrollToCursor(function(n){n.preventDefault(),s.throttle()}),t.onScrollToElement(function(n){n.element(),e(r,i)}),t.onToEditing(function(){r.run(function(n){n.toEditing()})}),t.onToReading(function(){r.run(function(n){n.toReading()})}),Eg(t.doc(),"touchend",function(n){Ae(t.html(),n.target())||Ae(t.body(),n.target())}),Eg(o,"transitionend",function(n){var e;"height"===n.raw().propertyName&&(e=Ni(o),r.run(function(n){n.setViewportOffset(e)}),u(),a())}),Cg(o,"touchstart",function(n){var e;r.run(function(n){n.highlightSelection()}),e=n,r.run(function(n){n.onToolbarTouch(e)}),t.onTouchToolstrip()}),Eg(t.body(),"touchstart",function(n){c(),t.onTouchContent(),f.fireTouchstart(n)}),f.onTouchmove(),f.onTouchend(),Eg(t.body(),"click",function(n){n.kill()}),Eg(o,"touchmove",function(){t.onToolbarScrollStart()})];return{destroy:function(){pn(l,function(n){n.unbind()})}}};var Vh,Bh,Ah,jh,Hh={},Nh={exports:Hh};Vh=undefined,Bh=Hh,Ah=Nh,jh=undefined,function(n){if("object"==typeof Bh&&void 0!==Ah)Ah.exports=n();else if("function"==typeof Vh&&Vh.amd)Vh([],n);else{("undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this).EphoxContactWrapper=n()}}(function(){return function s(i,u,c){function a(e,n){if(!u[e]){if(!i[e]){var t="function"==typeof jh&&jh;if(!n&&t)return t(e,!0);if(f)return f(e,!0);var r=new Error("Cannot find module '"+e+"'");throw r.code="MODULE_NOT_FOUND",r}var o=u[e]={exports:{}};i[e][0].call(o.exports,function(n){return a(i[e][1][n]||n)},o,o.exports,s,i,u,c)}return u[e].exports}for(var f="function"==typeof jh&&jh,n=0;n<c.length;n++)a(c[n]);return a}({1:[function(n,e,t){var r,o,i=e.exports={};function u(){throw new Error("setTimeout has not been defined")}function c(){throw new Error("clearTimeout has not been defined")}function a(n){if(r===setTimeout)return setTimeout(n,0);if((r===u||!r)&&setTimeout)return r=setTimeout,setTimeout(n,0);try{return r(n,0)}catch(e){try{return r.call(null,n,0)}catch(e){return r.call(this,n,0)}}}!function(){try{r="function"==typeof setTimeout?setTimeout:u}catch(n){r=u}try{o="function"==typeof clearTimeout?clearTimeout:c}catch(n){o=c}}();var f,s=[],l=!1,d=-1;function m(){l&&f&&(l=!1,f.length?s=f.concat(s):d=-1,s.length&&g())}function g(){if(!l){var n=a(m);l=!0;for(var e=s.length;e;){for(f=s,s=[];++d<e;)f&&f[d].run();d=-1,e=s.length}f=null,l=!1,function t(n){if(o===clearTimeout)return clearTimeout(n);if((o===c||!o)&&clearTimeout)return o=clearTimeout,clearTimeout(n);try{return o(n)}catch(e){try{return o.call(null,n)}catch(e){return o.call(this,n)}}}(n)}}function p(n,e){this.fun=n,this.array=e}function h(){}i.nextTick=function(n){var e=new Array(arguments.length-1);if(1<arguments.length)for(var t=1;t<arguments.length;t++)e[t-1]=arguments[t];s.push(new p(n,e)),1!==s.length||l||a(g)},p.prototype.run=function(){this.fun.apply(null,this.array)},i.title="browser",i.browser=!0,i.env={},i.argv=[],i.version="",i.versions={},i.on=h,i.addListener=h,i.once=h,i.off=h,i.removeListener=h,i.removeAllListeners=h,i.emit=h,i.prependListener=h,i.prependOnceListener=h,i.listeners=function(n){return[]},i.binding=function(n){throw new Error("process.binding is not supported")},i.cwd=function(){return"/"},i.chdir=function(n){throw new Error("process.chdir is not supported")},i.umask=function(){return 0}},{}],2:[function(n,l,e){(function(e){function r(){}function i(n){if("object"!=typeof this)throw new TypeError("Promises must be constructed via new");if("function"!=typeof n)throw new TypeError("not a function");this._state=0,this._handled=!1,this._value=undefined,this._deferreds=[],s(n,this)}function o(r,o){for(;3===r._state;)r=r._value;0!==r._state?(r._handled=!0,i._immediateFn(function(){var n=1===r._state?o.onFulfilled:o.onRejected;if(null!==n){var e;try{e=n(r._value)}catch(t){return void c(o.promise,t)}u(o.promise,e)}else(1===r._state?u:c)(o.promise,r._value)})):r._deferreds.push(o)}function u(n,e){try{if(e===n)throw new TypeError("A promise cannot be resolved with itself.");if(e&&("object"==typeof e||"function"==typeof e)){var t=e.then;if(e instanceof i)return n._state=3,n._value=e,void a(n);if("function"==typeof t)return void s(function r(n,e){return function(){n.apply(e,arguments)}}(t,e),n)}n._state=1,n._value=e,a(n)}catch(o){c(n,o)}}function c(n,e){n._state=2,n._value=e,a(n)}function a(n){2===n._state&&0===n._deferreds.length&&i._immediateFn(function(){n._handled||i._unhandledRejectionFn(n._value)});for(var e=0,t=n._deferreds.length;e<t;e++)o(n,n._deferreds[e]);n._deferreds=null}function f(n,e,t){this.onFulfilled="function"==typeof n?n:null,this.onRejected="function"==typeof e?e:null,this.promise=t}function s(n,e){var t=!1;try{n(function(n){t||(t=!0,u(e,n))},function(n){t||(t=!0,c(e,n))})}catch(r){if(t)return;t=!0,c(e,r)}}var n,t;n=this,t=setTimeout,i.prototype["catch"]=function(n){return this.then(null,n)},i.prototype.then=function(n,e){var t=new this.constructor(r);return o(this,new f(n,e,t)),t},i.all=function(n){var a=Array.prototype.slice.call(n);return new i(function(o,i){if(0===a.length)return o([]);var u=a.length;function c(e,n){try{if(n&&("object"==typeof n||"function"==typeof n)){var t=n.then;if("function"==typeof t)return void t.call(n,function(n){c(e,n)},i)}a[e]=n,0==--u&&o(a)}catch(r){i(r)}}for(var n=0;n<a.length;n++)c(n,a[n])})},i.resolve=function(e){return e&&"object"==typeof e&&e.constructor===i?e:new i(function(n){n(e)})},i.reject=function(t){return new i(function(n,e){e(t)})},i.race=function(o){return new i(function(n,e){for(var t=0,r=o.length;t<r;t++)o[t].then(n,e)})},i._immediateFn="function"==typeof e?function(n){e(n)}:function(n){t(n,0)},i._unhandledRejectionFn=function(n){"undefined"!=typeof console&&console&&console.warn("Possible Unhandled Promise Rejection:",n)},i._setImmediateFn=function(n){i._immediateFn=n},i._setUnhandledRejectionFn=function(n){i._unhandledRejectionFn=n},void 0!==l&&l.exports?l.exports=i:n.Promise||(n.Promise=i)}).call(this,n("timers").setImmediate)},{timers:3}],3:[function(a,n,f){(function(n,e){var r=a("process/browser.js").nextTick,t=Function.prototype.apply,o=Array.prototype.slice,i={},u=0;function c(n,e){this._id=n,this._clearFn=e}f.setTimeout=function(){return new c(t.call(setTimeout,window,arguments),clearTimeout)},f.setInterval=function(){return new c(t.call(setInterval,window,arguments),clearInterval)},f.clearTimeout=f.clearInterval=function(n){n.close()},c.prototype.unref=c.prototype.ref=function(){},c.prototype.close=function(){this._clearFn.call(window,this._id)},f.enroll=function(n,e){clearTimeout(n._idleTimeoutId),n._idleTimeout=e},f.unenroll=function(n){clearTimeout(n._idleTimeoutId),n._idleTimeout=-1},f._unrefActive=f.active=function(n){clearTimeout(n._idleTimeoutId);var e=n._idleTimeout;0<=e&&(n._idleTimeoutId=setTimeout(function(){n._onTimeout&&n._onTimeout()},e))},f.setImmediate="function"==typeof n?n:function(n){var e=u++,t=!(arguments.length<2)&&o.call(arguments,1);return i[e]=!0,r(function(){i[e]&&(t?n.apply(null,t):n.call(null),f.clearImmediate(e))}),e},f.clearImmediate="function"==typeof e?e:function(n){delete i[n]}}).call(this,a("timers").setImmediate,a("timers").clearImmediate)},{"process/browser.js":1,timers:3}],4:[function(n,e,t){var r=n("promise-polyfill"),o="undefined"!=typeof window?window:Function("return this;")();e.exports={boltExport:o.Promise||r}},{"promise-polyfill":2}]},{},[4])(4)});var Ph,zh=Nh.exports.boltExport,Lh=function(n){var t=O.none(),e=[],r=function(n){o()?u(n):e.push(n)},o=function(){return t.isSome()},i=function(n){pn(n,u)},u=function(e){t.each(function(n){v.setTimeout(function(){e(n)},0)})};return n(function(n){t=O.some(n),i(e),e=[]}),{get:r,map:function(t){return Lh(function(e){r(function(n){e(t(n))})})},isReady:o}},Gh={nu:Lh,pure:function(e){return Lh(function(n){n(e)})}},$h=function(n){v.setTimeout(function(){throw n},0)},Uh=function(t){var n=function(n){t().then(n,$h)};return{map:function(n){return Uh(function(){return t().then(n)})},bind:function(e){return Uh(function(){return t().then(function(n){return e(n).toPromise()})})},anonBind:function(n){return Uh(function(){return t().then(function(){return n.toPromise()})})},toLazy:function(){return Gh.nu(n)},toCached:function(){var n=null;return Uh(function(){return null===n&&(n=t()),n})},toPromise:t,get:n}},Wh=function(n){return Uh(function(){return new zh(n)})},Xh=function(n){return Uh(function(){return zh.resolve(n)})},qh=function(r,o){return Cn([{width:320,height:480,keyboard:{portrait:300,landscape:240}},{width:320,height:568,keyboard:{portrait:300,landscape:240}},{width:375,height:667,keyboard:{portrait:305,landscape:240}},{width:414,height:736,keyboard:{portrait:320,landscape:240}},{width:768,height:1024,keyboard:{portrait:320,landscape:400}},{width:1024,height:1366,keyboard:{portrait:380,landscape:460}}],function(n){return e=r<=n.width&&o<=n.height,t=n.keyboard,e?O.some(t):O.none();var e,t}).getOr({portrait:o/5,landscape:r/4})},Yh=function(n){var e,t=_g(n).isPortrait(),r=qh((e=n).screen.width,e.screen.height),o=t?r.portrait:r.landscape;return(t?n.screen.height:n.screen.width)-n.innerHeight>o?0:o},Kh=function(n,e){var t=je(n).dom().defaultView;return Ni(n)+Ni(e)-Yh(t)},Jh=function(n,e,t){var r=Kh(e,t),o=Ni(e)+Ni(t)-r;Mi(n,"padding-bottom",o+"px")},Qh=ot([{fixed:["element","property","offsetY"]},{scroller:["element","offsetY"]}]),Zh="data-"+bi("position-y-fixed"),nv="data-"+bi("y-property"),ev="data-"+bi("scrolling"),tv="data-"+bi("last-window-height"),rv=function(n){return Ag(n,Zh)},ov=function(n,e){var t=Zr(n,nv);return Qh.fixed(n,t,e)},iv=function(n,e){return Qh.scroller(n,e)},uv=function(n){var e=rv(n);return("true"===Zr(n,ev)?iv:ov)(n,e)},cv=function(n,e,t){var r=je(n).dom().defaultView.innerHeight;return Jr(n,tv,r+"px"),r-e-t},av=function(r,o,i,u){var e=je(r).dom().defaultView,n=function(n){var e=Zr(n,"style");_i(n,{position:"absolute",top:"0px"}),Jr(n,Zh,"0px"),Jr(n,nv,"top");return{restore:function(){Jr(n,"style",e||""),to(n,Zh),to(n,nv)}}}(i),t=Ni(i),c=Ni(u),a=function(n,e,t){var r=Zr(t,"style");ug(t),_i(t,{position:"absolute",height:e+"px",width:"100%",top:n+"px"}),Jr(t,Zh,n+"px"),Jr(t,ev,"true"),Jr(t,nv,"top");return{restore:function(){cg(t),Jr(t,"style",r||""),to(t,Zh),to(t,ev),to(t,nv)}}}(t,cv(r,t,c),r),f=function(n){var e=Zr(n,"style");_i(n,{position:"absolute",bottom:"0px"}),Jr(n,Zh,"0px"),Jr(n,nv,"bottom");return{restore:function(){Jr(n,"style",e||""),to(n,Zh),to(n,nv)}}}(u),s=!0,l=function(){var n=e.innerHeight;return Ag(r,tv)<n},d=function(){if(s){var n=Ni(i),e=Ni(u),t=cv(r,n,e);Jr(r,Zh,n+"px"),Mi(r,"height",t+"px"),Jh(o,r,u)}};return Jh(o,r,u),{setViewportOffset:function(n){Jr(r,Zh,n+"px"),d()},isExpanding:l,isShrinking:m(l),refresh:d,restore:function(){s=!1,n.restore(),a.restore(),f.restore()}}},fv=(Ph=null,{animate:function(o,i,u,c,e,n){var a=!1,f=function(n){a=!0,e(n)};Mg.clearInterval(Ph);var s=function(n){Mg.clearInterval(Ph),f(n)};Ph=Mg.setInterval(function(){var n,e,t,r=o();n=r,e=i,t=u,(Math.abs(n-e)<=t?O.none():n<e?O.some(n+t):O.some(n-t)).fold(function(){Mg.clearInterval(Ph),f(i)},function(n){if(c(n,s),!a){var e=o();(e!==n||Math.abs(e-i)>Math.abs(r-i))&&(Mg.clearInterval(Ph),f(i))}})},n)}}),sv="data-"+bi("last-scroll-top"),lv=function(n){var e=Fi(n,"top").getOr("0");return parseInt(e,10)},dv=function(n){return parseInt(n.dom().scrollTop,10)},mv=function(o,i){return Wh(function(n){var e=S(dv,o);Jr(o,sv,e());var t=Math.abs(i-e()),r=Math.ceil(t/10);fv.animate(e,i,r,function(n,e){Ag(o,sv)!==o.dom().scrollTop?e(o.dom().scrollTop):(o.dom().scrollTop=n,Jr(o,sv,n))},function(){o.dom().scrollTop=i,Jr(o,sv,i),n(i)},10)})},gv=function(n,e){var t=e+rv(n)+"px";Mi(n,"top",t)};var pv=function(u,n){return n(function(r){var o=[],i=0;0===u.length?r([]):pn(u,function(n,e){var t;n.get((t=e,function(n){o[t]=n,++i>=u.length&&r(o)}))})})},hv=function(n,e,t){var r,o,i,u=e+t,c=Fi(n,"top").getOr(t),a=u-parseInt(c,10),f=n.dom().scrollTop+a;return r=n,o=f,i=u,Wh(function(n){var e=S(dv,r);fv.animate(e,o,15,function(n){r.dom().scrollTop=n,Mi(r,"top",lv(r)+15+"px")},function(){r.dom().scrollTop=o,Mi(r,"top",i+"px"),n(o)},10)})},vv=function(n,o){return n.fold(function(n,e,t){return Mi(n,e,o+(r=t)+"px"),Xh(r);var r},function(n,e){return hv(n,o,e)})},yv=function(n,e){var t,r=(t=Li(n,"["+Zh+"]"),gn(t,uv)),o=gn(r,function(n){return vv(n,e)});return pv(o,Wh)},bv=function(i,u,n,t,e,r){var o=function f(t){var r=ho(Gh.pure({}));return{start:function(e){var n=Gh.nu(function(n){return t(e).get(n)});r.set(n)},idle:function(n){r.get().get(function(){n()})}}}(function(n){return t=u,r=n,o=je(e=i).dom().defaultView,Wh(function(n){gv(e,r),gv(t,r),o.scrollTo(0,r),n(r)});var e,t,r,o}),c=_p(function(){o.idle(function(){yv(n,t.pageYOffset).get(function(){var n;(n=ap(r),O.from(n[0]).bind(function(n){var e=n.top()-u.dom().scrollTop;return e>t.innerHeight+5||e<-5?O.some({top:y(e),bottom:y(e+n.height())}):O.none()})).each(function(n){u.dom().scrollTop=u.dom().scrollTop+n.top()}),o.start(0),e.refresh()})})},1e3),a=Eg(Te.fromDom(t),"scroll",function(){t.pageYOffset<0||c.throttle()});return yv(n,t.pageYOffset).get(b),{unbind:a.unbind}},xv=function(n){var a=n.cWin,e=n.ceBody,f=n.socket,t=n.toolstrip,r=n.contentElement,o=n.keyboardType,i=n.outerWindow,s=n.dropup,u=n.outerBody,c=av(f,e,t,s),l=o(u,a,De(),r),d=Dg(i,{onChange:w,onReady:c.refresh});d.onAdjustment(function(){c.refresh()});var m=Eg(Te.fromDom(i),"resize",function(){c.isExpanding()&&c.refresh()}),g=bv(t,f,u,i,c,a),p=function v(t,e){var n=t.document,r=Te.fromTag("div");co(r,bi("unfocused-selections")),$e(Te.fromDom(n.documentElement),r);var o=Eg(r,"touchstart",function(n){n.prevent(),Ih(t,e),u()}),i=function(n){var e=Te.fromTag("span");return fm(e,[bi("layer-editor"),bi("unfocused-selection")]),_i(e,{left:n.left()+"px",top:n.top()+"px",width:n.width()+"px",height:n.height()+"px"}),e},u=function(){We(r)};return{update:function(){u();var n=ap(t),e=gn(n,i);Ue(r,e)},isActive:function(){return 0<Ne(r).length},destroy:function(){o.unbind(),Xe(r)},clear:u}}(a,r),h=function(){p.clear()};return{toEditing:function(){l.toEditing(),h()},toReading:function(){l.toReading()},onToolbarTouch:function(n){l.onToolbarTouch()},refreshSelection:function(){p.isActive()&&p.update()},clearSelection:h,highlightSelection:function(){p.update()},scrollIntoView:function(n,e){var t,r,o,i,u,c;t=a,o=n,i=e,u=Kh(r=f,s),c=S(Dh,t),u<o||u<i?mv(r,r.dom().scrollTop-u+i).get(c):o<0&&mv(r,r.dom().scrollTop+o).get(c)},updateToolbarPadding:w,setViewportOffset:function(n){var i,u;c.setViewportOffset(n),i=f,u=n,Wh(function(n){var e=S(lv,i),t=function(n){Mi(i,"top",n+"px")},r=Math.abs(u-e()),o=Math.ceil(r/10);fv.animate(e,u,o,t,function(){t(u),n(u)},10)}).get(b)},syncHeight:function(){Mi(r,"height",r.dom().contentWindow.document.body.scrollHeight+"px")},refreshStructure:c.refresh,destroy:function(){c.restore(),d.destroy(),g.unbind(),m.unbind(),l.destroy(),p.destroy(),_h(De(),So)}}},wv=function(r,n){var o=Mp(),i=Gd(),u=Gd(),c=Ld(),a=Ld();return{enter:function(){n.hide();var t=Te.fromDom(v.document);xp(r.editor).each(function(n){var e;i.set({socketHeight:Fi(r.socket,"height"),iframeHeight:Fi(n.frame(),"height"),outerScroll:v.document.body.scrollTop}),u.set({exclusives:(e="."+ag,Eg(t,"touchmove",function(n){Wi(n.target(),e).filter(zp).fold(function(){n.raw().preventDefault()},w)}))}),co(r.container,bi("fullscreen-maximized")),Ep(r.container,n.body()),o.maximize(),Mi(r.socket,"overflow","scroll"),Mi(r.socket,"-webkit-overflow-scrolling","touch"),wo(n.body()),c.set(xv({cWin:n.win(),ceBody:n.body(),socket:r.socket,toolstrip:r.toolstrip,dropup:r.dropup.element(),contentElement:n.frame(),outerBody:r.body,outerWindow:r.win,keyboardType:Fh})),c.run(function(n){n.syncHeight()}),a.set(Rh(n,c,r.toolstrip,r.socket,r.dropup))})},refreshStructure:function(){c.run(function(n){n.refreshStructure()})},exit:function(){o.restore(),a.clear(),c.clear(),n.show(),i.on(function(n){n.socketHeight.each(function(n){Mi(r.socket,"height",n)}),n.iframeHeight.each(function(n){Mi(r.editor.getFrame(),"height",n)}),v.document.body.scrollTop=n.scrollTop}),i.clear(),u.on(function(n){n.exclusives.unbind()}),u.clear(),fo(r.container,bi("fullscreen-maximized")),Cp(),cg(r.toolbar),Ri(r.socket,"overflow"),Ri(r.socket,"-webkit-overflow-scrolling"),So(r.editor.getFrame()),xp(r.editor).each(function(n){n.clearSelection()})}}};function Sv(n){var e=Mh({classes:[bi("ios-container")]}),t=Lp(),r=Ld(),o=Gp(r),i=$p(),u=sh(function(){r.run(function(n){n.refreshStructure()})},n);e.add(t.wrapper()),e.add(i),e.add(u.component());return{system:y(e),element:e.element,init:function(n){r.set(function(n){var e=or("Getting IosWebapp schema",Ip,n);Mi(e.toolstrip,"width","100%"),Mi(e.container,"position","relative");var t=xm(Dp(function(){e.setReadOnly(e.readOnlyOnInit()),o.enter()},e.translate));e.alloy.add(t);var r={show:function(){e.alloy.add(t)},hide:function(){e.alloy.remove(t)}},o=wv(e,r);return{setReadOnly:e.setReadOnly,refreshStructure:o.refreshStructure,enter:o.enter,exit:o.exit,destroy:w}}(n))},exit:function(){r.run(function(n){Gm.remove(i,o),n.exit()})},setToolbarGroups:function(n){var e=t.createGroups(n);t.setGroups(e)},setContextToolbar:function(n){var e=t.createGroups(n);t.setContextToolbar(e)},focusToolbar:function(){t.focus()},restoreToolbar:function(){t.restoreToolbar()},updateMode:function(n){Xp(i,o,n,e.root())},socket:y(i),dropup:y(u)}}var Ov=tinymce.util.Tools.resolve("tinymce.EditorManager"),Tv=function(n,e,t){n.system().broadcastOn([Co],{command:e,state:t})},kv=function(e){return function(){var n=function(){e._skinLoaded=!0,e.fire("SkinLoaded")};e.initialized?n():e.on("init",n)}},Ev="toReading",Cv="toEditing",Mv=function(h){return{getNotificationManagerImpl:function(){return{open:y({progressBar:{value:w},close:w,text:w,getEl:y(null),moveTo:w,moveRel:w,settings:{}}),close:w,reposition:w,getArgs:y({})}},renderUI:function(){var n,e=h.getElement(),t={content:(n=I(h.settings,"skin_url").fold(function(){return Ov.baseURL+"/skins/ui/oxide"},function(n){return n}))+"/content.mobile.min.css",ui:n+"/skin.mobile.min.css"};!1==(!1===h.settings.skin)?(h.contentCSS.push(t.content),ko.DOM.styleSheetLoader.load(t.ui,kv(h))):kv(h)();var r,o,i=function(){h.fire("ScrollIntoView")},d=(Ln().os.isAndroid()?function f(n){var e=Mh({classes:[bi("android-container")]}),t=Lp(),r=Ld(),o=Gp(r),i=$p(),u=sh(w,n);return e.add(t.wrapper()),e.add(i),e.add(u.component()),{system:y(e),element:e.element,init:function(n){r.set(Fp(n))},exit:function(){r.run(function(n){n.exit(),Gm.remove(i,o)})},setToolbarGroups:function(n){var e=t.createGroups(n);t.setGroups(e)},setContextToolbar:function(n){var e=t.createGroups(n);t.setContextToolbar(e)},focusToolbar:function(){t.focus()},restoreToolbar:function(){t.restoreToolbar()},updateMode:function(n){Xp(i,o,n,e.root())},socket:y(i),dropup:y(u)}}:Sv)(i),u=Te.fromDom(e);r=u,o=d.system(),nt(r,o,Le);var c=e.ownerDocument.defaultView,m=Dg(c,{onChange:function(){var n,e,t;d.system().broadcastOn([Mo],{width:(n=c,e=Ln().os.isiOS(),t=_g(n).isPortrait(),e&&!t?n.screen.height:n.screen.width)})},onReady:w}),g=function(n,e,t,r){!1===r&&h.selection.collapse();var o=a(n,e,t);d.setToolbarGroups(!0===r?o.readOnly:o.main),h.setMode(!0===r?"readonly":"design"),h.fire(!0===r?Ev:Cv),d.updateMode(r)},a=function(n,e,t){var r=n.get();return{readOnly:r.backToMask.concat(e.get()),main:r.backToMask.concat(t.get())}},p=function(n,e){return h.on(n,e),{unbind:function(){h.off(n)}}};return h.on("init",function(){d.init({editor:{getFrame:function(){return Te.fromDom(h.contentAreaContainer.querySelector("iframe"))},onDomChanged:function(){return{unbind:w}},onToReading:function(n){return p(Ev,n)},onToEditing:function(n){return p(Cv,n)},onScrollToCursor:function(e){h.on("ScrollIntoView",function(n){e(n)});return{unbind:function(){h.off("ScrollIntoView"),m.destroy()}}},onTouchToolstrip:function(){t()},onTouchContent:function(){var n=Te.fromDom(h.editorContainer.querySelector("."+bi("toolbar")));To(n).bind(function(n){return d.system().getByDom(n).toOption()}).each(xe),d.restoreToolbar(),t()},onTapContent:function(n){var e=n.target();if("img"===ke(e))h.selection.select(e.dom()),n.kill();else if("a"===ke(e)){d.system().getByDom(Te.fromDom(h.editorContainer)).each(function(n){po.isAlpha(n)&&function(n){var e=v.document.createElement("a");e.target="_blank",e.href=n.href,e.rel="noreferrer noopener";var t=v.document.createEvent("MouseEvents");t.initMouseEvent("click",!0,!0,v.window,0,0,0,0,0,!1,!1,!1,!1,0,null),v.document.body.appendChild(e),e.dispatchEvent(t),v.document.body.removeChild(e)}(e.dom())})}}},container:Te.fromDom(h.editorContainer),socket:Te.fromDom(h.contentAreaContainer),toolstrip:Te.fromDom(h.editorContainer.querySelector("."+bi("toolstrip"))),toolbar:Te.fromDom(h.editorContainer.querySelector("."+bi("toolbar"))),dropup:d.dropup(),alloy:d.system(),translate:w,setReadOnly:function(n){g(l,s,f,n)},readOnlyOnInit:function(){return!1}});var r,n,e,t=function(){d.dropup().disappear(function(){d.system().broadcastOn([_o],{})})},o={label:"The first group",scrollable:!1,items:[_f("back",function(){h.selection.collapse(),d.exit()},{},h)]},i={label:"Back to read only",scrollable:!1,items:[_f("readonly-back",function(){g(l,s,f,!0)},{},h)]},u=xg(d,h),c=wg(h.settings,u),a={label:"The extra group",scrollable:!1,items:[]},f=ho([{label:"the action group",scrollable:!0,items:c},a]),s=ho([{label:"The read only mode group",scrollable:!0,items:[]},a]),l=ho({backToMask:[o],backToReadOnly:[i]});r=d,e=T((n=h).formatter.get()),pn(e,function(e){n.formatter.formatChanged(e,function(n){Tv(r,e,n)})}),pn(["ul","ol"],function(t){n.selection.selectorChanged(t,function(n,e){Tv(r,t,n)})})}),h.on("remove",function(){d.exit()}),h.on("detach",function(){var e,n;e=d.system(),n=Ne(e.element()),pn(n,function(n){e.getByDom(n).each(qe)}),Xe(e.element()),d.system().destroy()}),{iframeContainer:d.socket().element().dom(),editorContainer:d.element().dom()}}}};!function Dv(){Eo.add("mobile",Mv)}()}(window);