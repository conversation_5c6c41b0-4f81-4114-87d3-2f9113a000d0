USE [master]
GO
/****** Object:  Database [MA_SUP_SITENAME_IRISHUBDB]    Script Date: 01/09/2025 1:24:17 PM ******/
CREATE DATABASE [MA_SUP_SITENAME_IRISHUBDB]
GO
USE [MA_SUP_SITENAME_IRISHUBDB]
GO
/****** Object:  UserDefinedTableType [dbo].[udt700WebConsoleTransactedItems]    Script Date: 01/09/2025 1:24:17 PM ******/
CREATE TYPE [dbo].[udt700WebConsoleTransactedItems] AS TABLE(
	[ItemID] [bigint] NULL,
	[RFID] [varchar](50) NULL,
	[LocationID] [int] NULL,
	[TagType] [varchar](100) NULL
)
GO
/****** Object:  UserDefinedTableType [dbo].[udt701WebConsoleTransactedItemDetails]    Script Date: 01/09/2025 1:24:17 PM ******/
CREATE TYPE [dbo].[udt701WebConsoleTransactedItemDetails] AS TABLE(
	[ItemID] [bigint] NULL,
	[RFID] [varchar](50) NULL,
	[LocationID] [int] NULL,
	[ProductID] [int] NULL,
	[UseCycle] [int] NULL,
	[Qty] [decimal](10, 2) NULL,
	[UOMCode] [varchar](50) NULL,
	[TagType] [varchar](100) NULL,
	[MA_ScheduleID] [varchar](15) NULL
)
GO
/****** Object:  UserDefinedTableType [dbo].[udt702WebConsoleTags]    Script Date: 01/09/2025 1:24:17 PM ******/
CREATE TYPE [dbo].[udt702WebConsoleTags] AS TABLE(
	[RFID] [varchar](50) NULL,
	[LocationID] [varchar](100) NULL
)
GO
/****** Object:  UserDefinedTableType [dbo].[udt703WebConsoleItemsData]    Script Date: 01/09/2025 1:24:17 PM ******/
CREATE TYPE [dbo].[udt703WebConsoleItemsData] AS TABLE(
	[ItemID] [int] NULL,
	[Qty] [int] NULL
)
GO
/****** Object:  UserDefinedTableType [dbo].[udt704WebConsoleScanData]    Script Date: 01/09/2025 1:24:17 PM ******/
CREATE TYPE [dbo].[udt704WebConsoleScanData] AS TABLE(
	[BarcodeNo] [varchar](100) NULL,
	[Qty] [int] NULL
)
GO
/****** Object:  UserDefinedTableType [dbo].[udt705WebConsoleSPActivityLog]    Script Date: 01/09/2025 1:24:17 PM ******/
CREATE TYPE [dbo].[udt705WebConsoleSPActivityLog] AS TABLE(
	[Property] [varchar](100) NULL,
	[Value] [varchar](max) NULL
)
GO
/****** Object:  UserDefinedTableType [dbo].[udt706WebConsoleModules]    Script Date: 01/09/2025 1:24:17 PM ******/
CREATE TYPE [dbo].[udt706WebConsoleModules] AS TABLE(
	[ModuleName] [varchar](50) NULL
)
GO
/****** Object:  UserDefinedTableType [dbo].[udt707WebConsoleUnCheckedModules]    Script Date: 01/09/2025 1:24:17 PM ******/
CREATE TYPE [dbo].[udt707WebConsoleUnCheckedModules] AS TABLE(
	[UnCheckedModuleNames] [varchar](50) NULL
)
GO
/****** Object:  UserDefinedTableType [dbo].[udt708WebConsoleClusters]    Script Date: 01/09/2025 1:24:17 PM ******/
CREATE TYPE [dbo].[udt708WebConsoleClusters] AS TABLE(
	[Cluster] [varchar](30) NULL,
	[Cabinet] [varchar](30) NULL
)
GO
/****** Object:  UserDefinedTableType [dbo].[udt709WebConsoleReports]    Script Date: 01/09/2025 1:24:17 PM ******/
CREATE TYPE [dbo].[udt709WebConsoleReports] AS TABLE(
	[ReportID] [int] NULL,
	[ReportName] [varchar](30) NULL,
	[Checked] [int] NULL
)
GO
/****** Object:  UserDefinedTableType [dbo].[udt710WebConsoleSPParam]    Script Date: 01/09/2025 1:24:17 PM ******/
CREATE TYPE [dbo].[udt710WebConsoleSPParam] AS TABLE(
	[Property] [varchar](50) NULL,
	[Value] [varchar](255) NULL
)
GO
/****** Object:  UserDefinedTableType [dbo].[udtSPParam]    Script Date: 01/09/2025 1:24:17 PM ******/
CREATE TYPE [dbo].[udtSPParam] AS TABLE(
	[Property] [varchar](50) NULL,
	[Value] [varchar](max) NULL
)
GO
/****** Object:  UserDefinedFunction [dbo].[udf700WebConsoleSUPFetchSettings]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE  FUNCTION [dbo].[udf700WebConsoleSUPFetchSettings]
( 
	@DeviceID INT
    , @SettingName VARCHAR(200)
) 
RETURNS VARCHAR(MAX)
WITH EXECUTE AS CALLER
AS
BEGIN
	DECLARE @SettingValue VARCHAR(200)
    IF @SettingValue IS NULL
		SELECT @SettingValue =  [Setting Value] FROM s01tblSettingsCommon  WHERE [Setting Name] = @SettingName --AND ClusterID = @DeviceID
	IF @SettingValue IS NULL
		SELECT @SettingValue =  [Setting Value] FROM s01tblSettingsUnique  WHERE [Setting Name] = @SettingName --AND ClusterID IS NULL
	RETURN @SettingValue
END
GO
CREATE Procedure[dbo].[uspSynonyms]
   @ServerName Varchar(50)= ''
  ,@DBName Varchar(50)=''
AS

BEGIN

	SET NOCOUNT ON;

	DECLARE @SchemaName Varchar(50)=''
	DECLARE @ObjectName Varchar(50)=''
	DECLARE @sObjectName Varchar(50)=''
	DECLARE @ObjectNames Varchar(MAX)=''
	DECLARE @Server# Varchar(5)='0'
	DECLARE @Db# Varchar(5)='0'
	DECLARE @SQL NVarchar(MAX)=''

	-----------------------------------------------------------------------
	-- Synonyms for iRISDB Database
	-----------------------------------------------------------------------
	DECLARE @iRISDB VARCHAR(50)
	
	--Edited by Subramanya to change Default curso from LOCAL to GLOBAL
	DECLARE @Cur_DBName VARCHAR(50)
	SET @Cur_DBName =  DB_NAME()
	EXEC ('ALTER DATABASE '+@Cur_DBName+' SET CURSOR_DEFAULT  GLOBAL WITH NO_WAIT')

	--SET @ServerName=ISNULL(NULLIF(@ServerName,''),@@SERVERNAME)

	SET @SchemaName='dbo'

	IF @Cur_DBName LIKE '%IRISHUBDB%'

	--Subramanya added Script for IRISUPPLYDB (Running Script On , Create Syn object from DB)
		SET @iRISDB=REPLACE(DB_NAME(),'MA_SUP_IRISHUBDB','MA_SUP_IRISHUBDB')		
	ELSE IF DB_NAME() LIKE '%INTERFACE%'
		SET @iRISDB=REPLACE(DB_NAME(),'INTERFACE','IRISDB')

	PRINT 'iRISDB=' + @iRISDB

	SET @DBName=ISNULL(NULLIF(@DBName,''),@iRISDB)
	--SET @DBName=ISNULL(NULLIF(@DBName,''),'MGHEP_IrisDB_01042016')
	
	SET @Server#='0'
	SET @DB#='1'

		
	SET @SQL='
			DECLARE curObject CURSOR FOR
			SELECT name '
			IF LEN(@ServerName)=0
				SET @SQL= @SQL + 'FROM ' + QUOTENAME(@DBName) + '.' + QUOTENAME(@SchemaName) + '.sysobjects '
			ELSE
				SET @SQL= @SQL + 'FROM ' + QUOTENAME(@ServerName) + '.' + QUOTENAME(@DBName) + '.' + QUOTENAME(@SchemaName) + '.sysobjects '


			SET @SQL= @SQL + 'WHERE TYPE in (''V'',''U'',''P'') and name not like ''msMerge%''
							and name not Like ''msdynamic%''
							and name not Like ''msrepl%''
							and name not Like ''sysmerge%''
							and name not Like ''Sysrepl%''
                           ORDER BY name'
							
	PRINT 'SQL = ' + @SQL	 
	 	
	EXEC sp_executesql @SQL;

	OPEN curObject
	FETCH curObject INTO @ObjectName

	WHILE @@fetch_status >= 0
	BEGIN
		SET @SQL=''
		--Subramanya altered script for generating s01 synonyms
		SET @sObjectName = 's' + @Server# + @Db# + @ObjectName
		PRINT 'sObjectName=' + @sObjectName

		IF EXISTS(SELECT name FROM sys.synonyms WHERE name=@sObjectName)
		  BEGIN		  
			PRINT 'Synonym object FOUND = ' + @sObjectName
			SET @SQL= 'DROP SYNONYM s01' +  @ObjectName			
			PRINT 'SQL = ' + @SQL
			
			EXEC sp_executesql @SQL;
		  END
		ELSE
			PRINT 'Synonym object NOT FOUND = ' + @sObjectName
		

		IF LEN(@ServerName)=0		
			SET @SQL= 'CREATE SYNONYM ' + QUOTENAME(@sObjectName) + ' FOR ' + QUOTENAME(@DBName) + '.' + QUOTENAME(@SchemaName) + '.' + QUOTENAME(@ObjectName)
		ELSE
			SET @SQL= 'CREATE SYNONYM ' + QUOTENAME(@sObjectName) + ' FOR ' + QUOTENAME(@ServerName) + '.' + QUOTENAME(@DBName) + '.' + QUOTENAME(@SchemaName) + '.' + QUOTENAME(@ObjectName)
		
		PRINT 'SQL = ' + @SQL
		
		EXEC sp_executesql @SQL;


		PRINT ''
		PRINT '-----------------------------------------------------------------'
		PRINT ''


	FETCH curObject INTO @ObjectName
	END

	CLOSE curObject
	DEALLOCATE curObject

	SELECT * 
	FROM sys.synonyms
	ORDER BY name
END
GO
EXEC uspSynonyms @DBNAME = 'MA_SUP_TJUHORv4_IRISDB'
GO
CREATE SYNONYM [dbo].[s03tblOutboundDFTUsage] FOR [MA_SUP_TEST_INTERFACE].[dbo].[tblOutboundDFTUsage]
GO
CREATE SYNONYM [dbo].[s03tblOutboundDFTMsgs] FOR [MA_SUP_TEST_INTERFACE].[dbo].[tblOutboundDFTMsgs]
GO
/****** Object:  View [dbo].[qryHUBSUPFetchItemUsageList]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE VIEW [dbo].[qryHUBSUPFetchItemUsageList]
AS
SELECT [Description 1] AS Description
	,CASE 
		WHEN I.ItemStatusID = 5
			THEN ISNULL( [CAT NO], 'N/A')+ ' (WASTED)'
		ELSE
			ISNULL( [CAT NO], 'N/A')
	END AS CatNo
	,CONVERT (VARCHAR,U.DateUsed,120) AS DateUsed 
	,U.DateUsed AS REAL, ISNULL(U.Qty,1) AS Qty
	,PT.[Patient Name] AS PatientName
	,U.ItemUsageID, U.UsageTypeID, I.RFID
	,CASE 
		WHEN P.RFID = 'Y' 
			THEN 'I' 
		WHEN P.ProductTypeID = 5
			THEN 'T'
		ELSE '-'
	END AS ProductType
	,[Serial No] AS SerialNo, [Lot No] AS LotNo
	,CASE 
		WHEN D.Processed = 'P' 
			THEN 'Sent to EMR' 
		ELSE 'Not Sent' 
	END AS EpicStatus
	, U.MAScheduleID, U.MAPatientID, SessionID
FROM (SELECT UsageItemID, UsageTypeID, ItemUsageID, DateUsed, Qty, MAScheduleID, MAVisitID, MAPatientID, SessionID 
		FROM dbo.s01tblItemUsage
		WHERE DateUsed > GETDATE() - 60 
		and UsageTypeID = 1) U 

LEFT OUTER JOIN (SELECT MAScheduleID, MAVisitID, MAPatientID, [Patient Name]
					FROM dbo.s01qryPatientSchedules
					WHERE ProcedureDate > GETDATE() -60 ) PT ON U.MAScheduleID = PT.MAScheduleID -- isnull(U.MAScheduleID,U.MAVisitID) = isnull(PT.MAScheduleID,PT.MAScheduleID)
LEFT OUTER JOIN (SELECT ItemID, ProductID, ItemStatusID, [Serial No], [Lot No], RFID
					FROM dbo.s01tblItems) I ON UsageItemID = I.ItemID
INNER JOIN (SELECT [ProductID], [Cat No], [Description 1], RFID, ProductTypeID
				FROM dbo.s01tblProducts) P ON P.ProductID = I.ProductID

LEFT OUTER JOIN (  SELECT ItemUsageID, Processed
		FROM s03tblOutboundDFTMsgs
		WHERE EnteredDate>GETDATE()-60) D ON D.ItemUsageID = U.ItemUsageID --[MA_SUP_TEST_INTERFACE].dbo.tblOutboundDFTMsgs D ON D.ItemUsageID = U.ItemUsageID
UNION ALL
SELECT [Description 1] AS Description
	, CASE 
		WHEN U.OverrideID = 5
			THEN ISNULL( p.[CAT NO], 'N/A')+ ' (WASTED)'
		ELSE
			ISNULL( P.[CAT NO], 'N/A')
	END AS CatNo  
	, CONVERT (VARCHAR,U.DateUsed,120) AS DateUsed, U.DateUsed AS REAL
	, ISNULL(U.Qty,1) AS Qty
	--, @PatientName AS PatientName
	,PT.[Patient Name] AS PatientName
	, U.ItemUsageID, U.UsageTypeID, BR.[Barcode No] RFID
	, Case 
		When ProdUnitID = 4
			THEN 'B'
		ELSE '-'
	END AS ProductType, NULL  SerialNo, NULL LotNo
	, CASE 
		WHEN D.Processed = 'P' 
			THEN 'Sent to EMR' 
			ELSE 'Not Sent' 
	END AS EpicStatus
	, U.MAScheduleID, U.MAPatientID, SessionID
FROM (SELECT ItemUsageID, UsageItemID, UsageTypeID, MAScheduleID, MAVisitID, SessionID
			, MAPatientID, DateUsed, OverrideID,Qty 
		FROM dbo.s01tblItemUsage
		WHERE DateUsed > GETDATE() - 60
		AND UsageTypeID = 2 ) U

LEFT OUTER JOIN (SELECT MAScheduleID, MAVisitID, MAPatientID, [Patient Name]
					FROM dbo.s01qryPatientSchedules
					WHERE ProcedureDate > GETDATE() -60 ) PT ON U.MAScheduleID = PT.MAScheduleID --isnull(U.MAScheduleID,U.MAVisitID) = isnull(PT.MAScheduleID,PT.MAScheduleID)
LEFT OUTER JOIN (SELECT ItemInventoryID, ProductID
					FROM dbo.s01tblItemInventory) I ON U.UsageItemID = I.ItemInventoryID
INNER JOIN  (SELECT ProductID, [Cat No], [Description 1], ProdUnitID
				FROM dbo.s01tblProducts) P ON P.ProductID = I.ProductID 
LEFT OUTER JOIN (SELECT [Barcode No],[Cat No]
					FROM dbo.s01tblBarcodeRuleProduct) BR ON P.[Cat no] = BR.[Cat No]

LEFT OUTER JOIN ( SELECT ItemUsageID, Processed
					FROM s03tblOutboundDFTMsgs
					WHERE EnteredDate>GETDATE()-60) D ON D.ItemUsageID = U.ItemUsageID

WHERE P.[Cat No]<> '-'
GO
/****** Object:  View [dbo].[qryHUBFetchUsageItemList]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE VIEW [dbo].[qryHUBFetchUsageItemList]
AS
SELECT DISTINCT 
    IU.Description, 
    IU.CatNo,
    CONVERT(VARCHAR, CONVERT(DATETIME, IU.DateUsed, 120), 22) AS DateUsed,
    IU.REAL,
    IU.Qty,
    IU.PatientName,
    IU.ItemUsageID,
    IU.UsageTypeID,
    IUE.Capitated,
    IU.RFID,
    IU.ProductType,
    IU.SerialNo,
    IU.LotNo,
    i.ItemStatusID,
    IU.MAScheduleID,
	IU.EpicStatus
FROM 
    qryHUBSUPFetchItemUsageList IU
LEFT JOIN 
    s01tblItemUsageExt IUE ON IU.ItemUsageID = IUE.ItemUsageID
INNER JOIN 
    s01qryItemUsage i ON IU.ItemUsageID = i.ItemUsageID
--INNER JOIN 
--	s01qryItemInventory II ON IU.CatNo = II.[Cat No]
--INNER JOIN 
--s01qryItems QI ON IU.CatNo = II.[Cat No]
-- WHERE IU.MAScheduleID = @MAScheduleID 
-- AND i.ItemStatusID = 4
GO
/****** Object:  View [dbo].[qry700WebConsoleSUPUsersValidate]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE VIEW [dbo].[qry700WebConsoleSUPUsersValidate]
AS
	SELECT U.[UserID], U.[Employee ID], CASE WHEN ([Picture] IS NULL OR Picture = '') THEN [RFID] ELSE Picture END AS RFID,
		U.[PIN], U.[Title], U.[First Name], U.[Middle Name], U.[Last Name], U.[Suffix], 
		U.[Company], U.[Department], U.[Office], U.[Job Title], U.[Office Phone No], U.[Mobile Phone No], U.[Pager No], 
		U.[Fax No], U.[Office Street], U.[Office City], U.[Office State], U.[Office ZIP], U.[Office Country], U.[Username], 
		U.[Password], U.[GroupID], U.[SupplierID], U.[Email], U.[Picture], U.[Home Street], U.[Home City], U.[Home State], 
		U.[Home ZIP], U.[Home Country], U.[Home Phone No], U.[Entered By], U.[Computer Name], U.[Domain], U.[Entered Date], 
		U.[UserStatusID], U.[rowguid], U.[User Group], U.[User Name]
		--,U.TwoFactorEnabled as [2FactorAuthStatus]
		, C.CompartmentID, C.ClusterID
	FROM s01qryUsers U 
	INNER JOIN s01tblCompartmentAccess C ON U.UserID = C.UserID	
	WHERE U.UserStatusID = 1
GO
/****** Object:  View [dbo].[qry701WebConsoleSUPPatientDisplayADT]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE VIEW [dbo].[qry701WebConsoleSUPPatientDisplayADT]
AS
SELECT MaScheduleID, ProcedureCode, ScheduleStatus, VisitNumber
FROM s01qryPatientSchedules 
WHERE UPPER(ISNULL(ScheduleStatus,'ACTIVE'))='ACTIVE'
GO
/****** Object:  View [dbo].[qry703WebConsoleProductCatalog]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO



/******************************************************************************
Name                       : qryProductCatalog
Version                    : 4.0.43
Purpose                    : 
Author                     : 
Date Created               : 
Tables Affected            : s01tblItemInventory,s01tblProducts
Application Affected       : 
-----------------------------------------------------------------------------------------------------
Release Notes         :
********************************************************************************/
CREATE VIEW [dbo].[qry703WebConsoleProductCatalog]
AS
SELECT 	LEFT(s01tblProducts.[Description 1], 45) + '||11' AS Field1, 'Cat No : ' + s01tblProducts.[Cat No] + '|||||0' AS Field3,
	'Qty : ' + CAST(s01tblItemInventory.Qty AS nvarchar) + '|||||0' AS Field4, s01tblProducts.Manufacturer + N'|||||0' AS Field5,
	s01tblItemInventory.UOMCode + '|||||0' AS Field6, s01tblProducts.[Cat No] AS FieldID1, s01tblItemInventory.ItemInventoryID, s01tblProducts.[Cat No],
	s01tblProducts.[Description 1], s01tblProducts.UOMCode, s01tblProducts.[Ref Price], s01tblProducts.ProdUnitID, s01tblItemInventory.Qty,
	s01tblProducts.PCDM,s01tblItemInventory.LocationID,s01tblItemInventory.LocationTypeID,BP.[Barcode No],s01tblProducts.Manufacturer,s01tblProducts.[Brand Name]
FROM 	s01tblItemInventory INNER JOIN
     	s01tblProducts ON s01tblItemInventory.ProductID = s01tblProducts.ProductID
		inner join s01tblbarcoderuleproduct BP on s01tblProducts.[Cat No]=BP.[Cat No]
GO
/****** Object:  View [dbo].[qry704WebConsoleSUPItemUsageList]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE VIEW [dbo].[qry704WebConsoleSUPItemUsageList]

AS

SELECT [Description 1] AS Description

	,CASE 

		WHEN I.ItemStatusID = 5

			THEN ISNULL( [CAT NO], 'N/A')+ ' (WASTED)'

		ELSE

			ISNULL( [CAT NO], 'N/A')

	END AS CatNo

	,CONVERT (VARCHAR,U.DateUsed,120) AS DateUsed 

	,U.DateUsed AS REAL, ISNULL(U.Qty,1) AS Qty

	,PT.[Patient Name] AS PatientName

	,U.ItemUsageID, U.UsageTypeID, I.RFID

	,CASE 

		WHEN P.RFID = 'Y' 

			THEN 'I' 

		WHEN P.ProductTypeID = 5

			THEN 'T'

		ELSE '-'

	END AS ProductType

	,[Serial No] AS SerialNo, [Lot No] AS LotNo

	,CASE 

		WHEN D.Processed = 'P' 

			THEN 'Sent to EMR' 

		ELSE 'Not Sent' 

	END AS EpicStatus

	, U.MAScheduleID, U.MAPatientID, SessionID

FROM (SELECT UsageItemID, UsageTypeID, ItemUsageID, DateUsed, Qty, MAScheduleID, MAVisitID, MAPatientID, SessionID 

		FROM s01tblItemUsage

		WHERE DateUsed > GETDATE() - 60 

		and UsageTypeID = 1) U
 
LEFT OUTER JOIN (SELECT MAScheduleID, MAVisitID, MAPatientID, [Patient Name]

					FROM s01qryPatientSchedules

					WHERE ProcedureDate > GETDATE() -60 ) PT ON U.MAScheduleID = PT.MAScheduleID -- isnull(U.MAScheduleID,U.MAVisitID) = isnull(PT.MAScheduleID,PT.MAScheduleID)

LEFT OUTER JOIN (SELECT ItemID, ProductID, ItemStatusID, [Serial No], [Lot No], RFID

					FROM s01tblItems) I ON UsageItemID = I.ItemID

INNER JOIN (SELECT [ProductID], [Cat No], [Description 1], RFID, ProductTypeID

				FROM s01tblProducts) P ON P.ProductID = I.ProductID
 
LEFT OUTER JOIN (  SELECT ItemUsageID, Processed

		FROM s03tblOutboundDFTMsgs

		WHERE EnteredDate>GETDATE()-60) D ON D.ItemUsageID = U.ItemUsageID --[MA_SUP_TEST_INTERFACE].dbo.tblOutboundDFTMsgs D ON D.ItemUsageID = U.ItemUsageID

UNION ALL

SELECT [Description 1] AS Description

	, CASE 

		WHEN U.OverrideID = 5

			THEN ISNULL( p.[CAT NO], 'N/A')+ ' (WASTED)'

		ELSE

			ISNULL( P.[CAT NO], 'N/A')

	END AS CatNo  

	, CONVERT (VARCHAR,U.DateUsed,120) AS DateUsed, U.DateUsed AS REAL

	, ISNULL(U.Qty,1) AS Qty

	--, @PatientName AS PatientName

	,PT.[Patient Name] AS PatientName

	, U.ItemUsageID, U.UsageTypeID, BR.[Barcode No] RFID

	, Case 

		When ProdUnitID = 4

			THEN 'B'

		ELSE '-'

	END AS ProductType, NULL  SerialNo, NULL LotNo

	, CASE 

		WHEN D.Processed = 'P' 

			THEN 'Sent to EMR' 

			ELSE 'Not Sent' 

	END AS EpicStatus

	, U.MAScheduleID, U.MAPatientID, SessionID

FROM (SELECT ItemUsageID, UsageItemID, UsageTypeID, MAScheduleID, MAVisitID, SessionID

			, MAPatientID, DateUsed, OverrideID,Qty 

		FROM s01tblItemUsage

		WHERE DateUsed > GETDATE() - 60

		AND UsageTypeID = 2 ) U
 
LEFT OUTER JOIN (SELECT MAScheduleID, MAVisitID, MAPatientID, [Patient Name]

					FROM s01qryPatientSchedules

					WHERE ProcedureDate > GETDATE() -60 ) PT ON U.MAScheduleID = PT.MAScheduleID --isnull(U.MAScheduleID,U.MAVisitID) = isnull(PT.MAScheduleID,PT.MAScheduleID)

LEFT OUTER JOIN (SELECT ItemInventoryID, ProductID

					FROM s01tblItemInventory) I ON U.UsageItemID = I.ItemInventoryID

INNER JOIN  (SELECT ProductID, [Cat No], [Description 1], ProdUnitID

				FROM s01tblProducts) P ON P.ProductID = I.ProductID 

LEFT OUTER JOIN (SELECT [Barcode No],[Cat No]

					FROM s01tblBarcodeRuleProduct) BR ON P.[Cat no] = BR.[Cat No]
 
LEFT OUTER JOIN ( SELECT ItemUsageID, Processed

					FROM s03tblOutboundDFTMsgs

					WHERE EnteredDate>GETDATE()-60) D ON D.ItemUsageID = U.ItemUsageID
 
WHERE P.[Cat No]<> '-'
GO
/****** Object:  View [dbo].[qryHUBProductDirectory]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE VIEW [dbo].[qryHUBProductDirectory]
AS
SELECT DISTINCT TOP 100 PERCENT 
		LEFT( [Description 1], 60) + CHAR(10) + 'Manufacturer : ' +  Manufacturer + '||8' AS Field1, 
		'Qty : ' + CONVERT( NVARCHAR, COUNT(*) ) AS Field2, 
		'Cat No : ' + [Cat No] + '|||||0' AS Field3, 
		'Loc : ON SHELF' + '|||||255' AS Field4, 
		--'2-' + 'ON SHELF' + [Cat No] + [Description 1] AS Description, 
		[Description 1] AS Description, 
		COUNT(*) AS Quantity, 
		[Cat No] AS [Catalog No], 
		--'ON SHELF' AS Location, 
		'Out Of Cabinet' AS Location, 
		[Cat No] as FieldID1, 
		Manufacturer
		--,ProductCategory,
		--Size
		,ProductID
	FROM s01qryItems
	WHERE [Description 1] <> 'Unassociated Item' AND ((ItemStatusID = 0) OR (ItemStatusID=1 and CompartmentID is  null))
	GROUP BY [Description 1], [Cat No], Manufacturer, ItemStatusID, CompartmentID,ProductID--, ProductCategory, Size
	--HAVING	[Description 1] <> 'Unassociated Item' AND ((ItemStatusID = 0) OR ([Item Status]=1 and CompartmentID is  null))
 
	UNION ALL
 
	SELECT DISTINCT TOP 100 PERCENT 
		LEFT( [Description 1], 60) + CHAR(10) + 'Manufacturer : ' + Manufacturer + '||8' AS Field1, 
		'Qty : ' + CONVERT( NVARCHAR, COUNT(*) ) AS Field2, 
		'Cat No : ' + [Cat No] + '|||||0' AS Field3, 
		'Loc : ' + [Cluster Name] + ' - ' + [Compartment Name] + '|||||255' AS Field4, 
		--'1-' + [Cluster Name] + ' - ' + [Compartment Name] + [Cat No] + [Description 1] AS Description, 
		[Description 1] AS Description, 
		COUNT(*) AS Quantity, 
		[Cat No] AS [Catalog No], 
		[Cluster Name] + '-' + [Compartment Name] AS Location, 
		[Cat No] as FieldID1, 
		Manufacturer
		--,
		--ProductCategory,
		--Size
		,ProductID
	FROM	s01qryItems
	WHERE	[Description 1] <> 'Unassociated Item' AND ItemStatusID = 1 AND CompartmentID is not null
	GROUP BY [Description 1], [Cat No], [Cluster Name], [Compartment Name], Manufacturer, ItemStatusID, CompartmentID,ProductID
		--,ProductCategory, Size
	--HAVING	[Description 1] <> 'Unassociated Item' AND ItemStatusID = 1 AND CompartmentID is not null
 
	UNION ALL
 
	SELECT DISTINCT TOP 100 PERCENT 
		LEFT( [Description 1], 60) + CHAR(10) + 'Manufacturer : ' +  Manufacturer + '||8' AS Field1, 
		'Qty : ' + '0' AS Field2, 
		'Cat No : ' + [Cat No] + '|||||0' AS Field3, 
		'Out of Stock ' + '' + '|||||255' AS Field4, 
		--'3-OutOfStock-' + [Cat No] + [Description 1] AS Description, 
		[Description 1] AS Description, 
		0 AS Quantity, 
		[Cat No] AS [Catalog No], 
		'' AS Location, 
		[Cat No] as FieldID1, 
		Manufacturer
		--,ProductCategory,
		--Size
		,ProductID
	FROM   s01qryProducts P 
	WHERE ProductID NOT IN ( SELECT DISTINCT ProductID FROM s01tblItems WHERE ItemStatusID < 2 )
		AND [Description 1] <> 'Unassociated Item' AND ProductID IN ( SELECT ProductID FROM s01tblItems )
	GROUP BY ProductID, [Description 1], [Cat No], Manufacturer, ProductID--, ProductCategory, Size

GO
/****** Object:  View [dbo].[qryItemsOneTimeUse]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE VIEW [dbo].[qryItemsOneTimeUse]
AS
SELECT	I.ItemID, I.ItemStatusID, IST.[Item Status], PR.[Cat No], PR.NDC, 
	PR.[Description 1], PR.Manufacturer, I.[Lot No], I.[Expired Date], I.[Serial No], 
	P.PatientID, P.[Patient Name], PS.AppointmentID, PS.PhysicianID, 
	PS.[Physician Name], I.[Use Cycle], S.[Supplier Name], PR.[Ref Price], 
	U2.[User Name] AS [Added By], I.[Date Added], U1.[User Name] AS [Removed By], I.[Date Removed], 
	I.[Act Price], U3.[User Name] AS [Entered By], I.[Entered Date], I.CompartmentID, I.SupplierID, 
	I.AddUserID, I.RmUserID, I.[Domain], I.[Computer Name], I.RFID, I.MAPatientID, 
	I.MAPhysicianID, I.MAScheduleID, PR.ProductID, I.LastActivityDate, I.ICDM, 
	PR.PCDM, PR.MMID, PR.ProductCategoryID, PR.ProductSubCategoryID, 
	PR.ProductGroupID, I.Consignment, I.Misc1, I.Misc2, I.Misc3, C.ClusterID, 
	C.[Cluster Name], C.[Cabinet Name], C.[Cabinet Type], C.[Compartment Name], PS.ProcedureCode, 
	PS.ProcedureDesc, PR.QtyPerPkg, PR.UOMCode, I.Qty, PR.ProdUnitID, 
	C.LocationTypeID, C.[Cluster Location], I.EntUserID, PS.[Account Number], 
	PS.AltAcctNo, PS.MAVisitID, C.LocationID, PR.ProductTypeID, PR.[Notify Interval],
	ProductCategory, Size,LR.department
	--10/23/22 Jerome added fields for tissue verification report
	,PR.[ID 1]
	,S.[Contact Address]
	,S.Number
	,PR.[Brand Name]
FROM s01tblItems I 
		LEFT OUTER JOIN s01qryProducts PR ON I.ProductID = PR.ProductID
		LEFT OUTER JOIN s01tblSuppliers S ON I.SupplierID = S.SupplierID
		LEFT OUTER JOIN	s01qryCabinets C ON IIF(I.CompartmentID IS NULL, IIF(ISNUMERIC(I.Domain)=1, I.Domain, 120), I.CompartmentID) = C.CompartmentID
		LEFT OUTER JOIN s01tblLocationRoom LR ON C.LocationID = LR.RoomID
		LEFT OUTER JOIN s01qryPatientSchedules PS ON I.MAScheduleID = PS.MAScheduleID
		LEFT OUTER JOIN s01qryUsers U3 ON I.EntUserID = U3.UserID
		LEFT OUTER JOIN	s01qryPatients P ON I.MAPatientID = P.MAPatientID
		LEFT OUTER JOIN	s01qryUsers U2 ON I.AddUserID = U2.UserID
		LEFT OUTER JOIN	s01qryUsers U1 ON I.RmUserID = U1.UserID
		LEFT OUTER JOIN	s01tblItemStatus IST ON I.ItemStatusID = IST.ItemStatusID
GO
/****** Object:  View [dbo].[qrySUPUsersValidate]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS OFF
GO
SET QUOTED_IDENTIFIER OFF
GO

CREATE VIEW [dbo].[qrySUPUsersValidate]
AS
	SELECT U.[UserID], U.[Employee ID], CASE WHEN ([Picture] IS NULL OR Picture = '') THEN [RFID] ELSE Picture END AS RFID,
		U.[PIN], U.[Title], U.[First Name], U.[Middle Name], U.[Last Name], U.[Suffix], 
		U.[Company], U.[Department], U.[Office], U.[Job Title], U.[Office Phone No], U.[Mobile Phone No], U.[Pager No], 
		U.[Fax No], U.[Office Street], U.[Office City], U.[Office State], U.[Office ZIP], U.[Office Country], U.[Username], 
		U.[Password], U.[GroupID], U.[SupplierID], U.[Email], U.[Picture], U.[Home Street], U.[Home City], U.[Home State], 
		U.[Home ZIP], U.[Home Country], U.[Home Phone No], U.[Entered By], U.[Computer Name], U.[Domain], U.[Entered Date], 
		U.[UserStatusID], U.[rowguid], U.[User Group], U.[User Name], C.CompartmentID, C.ClusterID
	FROM s01qryUsers U INNER JOIN s01tblCompartmentAccess C ON U.UserID = C.UserID	
	WHERE U.UserStatusID = 1

GO
/****** Object:  View [dbo].[VIEW_FETCH_PRODUCTLIST]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE VIEW [dbo].[VIEW_FETCH_PRODUCTLIST]
	AS
	
SELECT P.ProductID, P.[Cat No], P.NDC, P.[Description 1], P.Manufacturer, P.[Ref Price], P.QtyPerPkg, P.[Entered Date], P.PCDM, P.MMID
	, ProductCategoryID, ProductSubCategoryID, ProductGroupID, EntryTypeID, CAST( [Ref Price] AS VARCHAR(12) ) AS REFERENCEPRICE
	, ReorderTypeID, ISNULL(IT.Department,'N/A') as Department,isnull(IT.RFQty,0) + isnull(IV.BCQty,0) AS ItemCount
	, [Entry Source], UOMCode, ProductTypeID, [ID 1], [ID 2], [SKU No], ProductType
	, isnull(DocumentCount,0) as DocumentCount,[Brand Name]
-- select *
FROM s01qryProducts P 
	Left Outer Join (
		SELECT ProductID, COUNT(*) as DocumentCount 
		FROM s01tblProductDocument 
		Group By ProductID
		) D On P.ProductID = D.ProductID 
	Left Outer Join (
		Select ProductID, department,Count(*) as RFQty 
		from s01qryItems
		where ItemStatusID<2
		Group By  department,ProductID
		) IT on P.ProductID=IT.ProductID
	Left Outer Join (
		Select ProductID, department,Sum(isnull(Qty,0)) as BCQty 
		from s01qryItemInventory
		Group By department,ProductID
		) IV on P.ProductID=IV.ProductID and IT.department=IV.Department

Where P.ProductID>0
GO
/****** Object:  Table [dbo].[TBL_COUNTRIES]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[TBL_COUNTRIES](
	[COUNTRY_ID] [int] IDENTITY(1,1) NOT NULL,
	[COUNTRY_CODE] [varchar](5) NOT NULL,
	[COUNTRY_NAME] [varchar](50) NOT NULL,
PRIMARY KEY CLUSTERED 
(
	[COUNTRY_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[TBL_STATES]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[TBL_STATES](
	[STATE_ID] [bigint] IDENTITY(1,1) NOT NULL,
	[STATE_CODE] [varchar](5) NOT NULL,
	[STATE_NAME] [varchar](50) NOT NULL,
	[COUNTRY_ID] [int] NOT NULL,
PRIMARY KEY CLUSTERED 
(
	[STATE_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[tblAppEventLog]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS OFF
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[tblAppEventLog](
	[AppEventLogID] [int] IDENTITY(1,1) NOT NULL,
	[UserID] [int] NULL,
	[User Name] [varchar](50) NULL,
	[RFID] [varchar](100) NULL,
	[EventID] [smallint] NULL,
	[EntryTypeID] [int] NULL,
	[Cluster Name] [varchar](50) NULL,
	[Cabinet Name] [varchar](50) NULL,
	[Compartment Name] [varchar](50) NULL,
	[PatientID] [varchar](50) NULL,
	[Patient Name] [varchar](50) NULL,
	[PhysicianID] [varchar](50) NULL,
	[Physician Name] [varchar](50) NULL,
	[Data Upload] [bit] NULL,
	[Computer Name] [varchar](50) NULL,
	[Domain] [varchar](50) NULL,
	[Log Date] [datetime] NULL,
	[AlertSent] [bit] NULL,
	[OverrideNotes] [varchar](100) NULL,
	[LogMsg] [varchar](500) NULL,
	[SessionID] [varchar](50) NULL,
	[Door Status] [varchar](255) NULL,
	[Lock Status] [varchar](255) NULL,
	[Switch Status] [varchar](255) NULL,
	[Button Events] [varchar](255) NULL,
PRIMARY KEY CLUSTERED 
(
	[AppEventLogID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[tblAppEvents]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS OFF
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[tblAppEvents](
	[EventID] [int] NOT NULL,
	[Event] [varchar](255) NULL,
	[EventDesc] [varchar](255) NULL,
	[SPName] [varchar](255) NULL,
	[SPDesc] [varchar](255) NULL,
	[LogMsg] [varchar](512) NULL,
	[EnteredDate] [datetime] NULL,
	[AppID] [int] NULL,
PRIMARY KEY CLUSTERED 
(
	[EventID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[tblDeviceRegistration]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[tblDeviceRegistration](
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[DeviceID] [nvarchar](50) NULL,
	[Location ID] [int] NULL,
	[Registration Date] [datetime] NULL,
	[Registered By] [nvarchar](50) NULL,
	[Entered Date] [datetime] NULL,
	[SerialNumber] [nvarchar](50) NULL,
	[DeviceName] [nvarchar](50) NULL,
	[McAddress] [nvarchar](50) NULL,
	[Status] [nvarchar](50) NULL,
	[Device IPAddress] [varchar](50) NULL,
	[Location] [varchar](100) NULL,
 CONSTRAINT [PK_tblDeviceRegistration] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[tblInventoryLog]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS OFF
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[tblInventoryLog](
	[InvHistoryID] [int] IDENTITY(1,1) NOT NULL,
	[ItemHistoryID] [int] NULL,
	[InvntoryID] [int] NULL,
	[ProcessStatus] [char](1) NULL,
	[ClusterID] [int] NULL,
	[LocationID] [int] NULL,
	[UpdatedBy] [int] NULL,
	[EntryTypeID] [int] NULL,
	[EnteredDate] [datetime] NULL,
PRIMARY KEY CLUSTERED 
(
	[InvHistoryID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[tblModules]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[tblModules](
	[MODULE_ID] [int] IDENTITY(1,1) NOT NULL,
	[MODULE_NAME] [nvarchar](255) NOT NULL,
	[DESCRIPTION] [nvarchar](255) NULL,
	[LINK] [nvarchar](500) NOT NULL,
	[PARENT_MODULE_ID] [int] NULL,
	[PROJECT_ID] [int] NULL,
	[ORDER_ID] [int] NULL,
	[MODULE_ICON] [nvarchar](255) NULL,
	[ISENABLED] [bit] NULL,
PRIMARY KEY CLUSTERED 
(
	[MODULE_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[tblOutboundDFTMsgs]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS OFF
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[tblOutboundDFTMsgs](
	[DFTMsgID] [int] IDENTITY(1,1) NOT NULL,
	[ItemUsageID] [int] NULL,
	[MsgType] [varchar](10) NULL,
	[UsageTypeID] [int] NULL,
	[UsageItemID] [int] NULL,
	[EnteredDate] [datetime] NULL,
	[PreProcessed] [varchar](1) NULL,
	[PreProcessedRemarks] [varchar](100) NULL,
	[PatientID] [varchar](25) NULL,
	[AccountNumber] [varchar](25) NULL,
	[AppointmentID] [varchar](25) NULL,
	[LastName] [varchar](25) NULL,
	[FirstName] [varchar](25) NULL,
	[MiddleName] [varchar](25) NULL,
	[DOB] [datetime] NULL,
	[Sex] [varchar](5) NULL,
	[Department] [varchar](25) NULL,
	[MMID] [varchar](25) NULL,
	[IsImplant] [varchar](1) NULL,
	[CatalogNumber] [varchar](50) NULL,
	[Description] [varchar](255) NULL,
	[Manufacturer] [varchar](50) NULL,
	[LotNo] [varchar](50) NULL,
	[SerialNo] [varchar](50) NULL,
	[Qty] [tinyint] NULL,
	[ExpiredDate] [datetime] NULL,
	[UnitPrice] [money] NULL,
	[OverrideID] [int] NULL,
	[OverrideNotes] [varchar](100) NULL,
	[TransactionCode] [varchar](15) NULL,
	[UserName] [varchar](50) NULL,
	[PatientClass] [varchar](5) NULL,
	[AttendingDoc] [varchar](50) NULL,
	[ReferringDoc] [varchar](50) NULL,
	[ConsultingDoc] [varchar](50) NULL,
	[AdmitDate] [datetime] NULL,
	[DischargeDate] [datetime] NULL,
	[ProcedureDate] [datetime] NULL,
	[DateUsed] [datetime] NULL,
	[LastActivityDate] [datetime] NULL,
	[PatientMsg] [varchar](500) NULL,
	[DFTMsg] [varchar](2000) NULL,
	[Processed] [varchar](1) NULL,
	[ProcessedDate] [datetime] NULL,
	[ProcessedRemarks] [varchar](100) NULL,
 CONSTRAINT [PK_tblOutboundDFTMsgs] PRIMARY KEY CLUSTERED 
(
	[DFTMsgID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 100, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[tblReportCategory]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[tblReportCategory](
	[CategoryID] [tinyint] NOT NULL,
	[Category] [varchar](50) NULL,
 CONSTRAINT [PK_tblReportCategory] PRIMARY KEY CLUSTERED 
(
	[CategoryID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[tblReportFields]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[tblReportFields](
	[FieldID] [int] IDENTITY(1,1) NOT NULL,
	[ReportID] [int] NOT NULL,
	[FieldName] [varchar](50) NULL,
	[Title] [varchar](50) NULL,
	[Format] [varchar](50) NULL,
	[Alignment] [varchar](50) NULL,
	[Sorting] [varchar](50) NULL,
	[TotalColumn] [tinyint] NULL,
 CONSTRAINT [PK_tblReportFields] PRIMARY KEY CLUSTERED 
(
	[FieldID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[tblReportFilter]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[tblReportFilter](
	[FilterID] [int] IDENTITY(1,1) NOT NULL,
	[ReportID] [int] NOT NULL,
	[FilterMode] [tinyint] NULL,
	[TopFilter] [tinyint] NULL,
	[NoRecords] [int] NULL,
	[NoCategory] [varchar](50) NULL,
	[FilterScript] [varchar](1000) NULL,
 CONSTRAINT [PK_tblReportFilter] PRIMARY KEY CLUSTERED 
(
	[FilterID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[tblReportLayout]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[tblReportLayout](
	[ReportID] [int] NOT NULL,
	[PaperSize] [varchar](50) NULL,
	[MarginTop] [float] NULL,
	[MarginBottom] [decimal](3, 1) NULL,
	[MarginLeft] [decimal](3, 1) NULL,
	[MarginRight] [decimal](3, 1) NULL,
	[MarginHeader] [decimal](3, 1) NULL,
	[MarginFooter] [decimal](3, 1) NULL,
	[MarginGuide] [tinyint] NULL,
	[TableMarginLeft] [decimal](3, 1) NULL,
	[Orientation] [varchar](50) NULL,
	[BorderPosition] [varchar](50) NULL,
	[BorderStyle] [varchar](50) NULL,
	[BorderWidth] [tinyint] NULL,
	[BorderColor] [varchar](50) NULL,
	[ShowPicture] [tinyint] NULL,
	[PicFile] [varchar](255) NULL,
	[PicScaleX] [decimal](3, 0) NULL,
	[PicScaleY] [decimal](3, 0) NULL,
	[PicX] [smallint] NULL,
	[PicY] [smallint] NULL,
	[ShowRowNo] [tinyint] NULL,
	[ShowBottomSum] [tinyint] NULL,
 CONSTRAINT [PK_tblReportLayout] PRIMARY KEY CLUSTERED 
(
	[ReportID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[tblReportList]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[tblReportList](
	[ReportID] [int] IDENTITY(1,1) NOT NULL,
	[TypeID] [tinyint] NOT NULL,
	[CategoryID] [tinyint] NULL,
	[Title] [varchar](50) NULL,
	[Description] [varchar](255) NULL,
	[Datasource] [varchar](50) NULL,
	[CustomQuery] [tinyint] NULL,
	[SQLQuery] [varchar](2000) NULL,
	[GroupField] [tinyint] NULL,
	[ButtonID] [tinyint] NULL,
	[IconID] [tinyint] NULL,
	[OwnerID] [int] NULL,
	[CreatorID] [int] NULL,
	[ColWidth] [varchar](255) NULL,
	[Shared] [bit] NULL,
	[Shortcut] [bit] NULL,
	[Aggregate] [tinyint] NULL,
	[TotalRow] [tinyint] NULL,
	[AggFunction] [varchar](50) NULL,
	[AggField] [varchar](50) NULL,
	[PivotField] [varchar](50) NULL,
	[Computer Name] [varchar](50) NULL,
	[Domain] [varchar](50) NULL,
	[Entered Date] [datetime] NULL,
 CONSTRAINT [PK_tblReportList] PRIMARY KEY CLUSTERED 
(
	[ReportID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[tblReportRBAC]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[tblReportRBAC](
	[ReportRBACID] [int] IDENTITY(1,1) NOT NULL,
	[ReportID] [int] NULL,
	[UserID] [int] NULL,
	[Status] [bit] NULL,
	[rowguid] [uniqueidentifier] NULL,
PRIMARY KEY CLUSTERED 
(
	[ReportRBACID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[tblReportStatus]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[tblReportStatus](
	[ID] [int] NOT NULL,
	[Status] [varchar](50) NOT NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[tblReportStyle]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[tblReportStyle](
	[StyleID] [int] IDENTITY(1,1) NOT NULL,
	[ReportID] [int] NOT NULL,
	[Section] [varchar](50) NULL,
	[Font] [varchar](50) NULL,
	[BackColor] [varchar](50) NULL,
	[Alignment] [varchar](50) NULL,
	[Height] [tinyint] NULL,
	[Indent] [tinyint] NULL,
	[BeforeSpacing] [tinyint] NULL,
	[TopBorder] [varchar](50) NULL,
	[BottomBorder] [varchar](50) NULL,
	[BorderColor] [varchar](50) NULL,
	[AltColor] [varchar](50) NULL,
	[SideBorder] [tinyint] NULL,
 CONSTRAINT [PK_tblReportStyle] PRIMARY KEY CLUSTERED 
(
	[StyleID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[tblReportText]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[tblReportText](
	[TextID] [int] IDENTITY(1,1) NOT NULL,
	[ReportID] [int] NOT NULL,
	[Section] [varchar](50) NULL,
	[ReportTitle] [varchar](255) NULL,
	[TitleAlignment] [varchar](50) NULL,
	[TextLeft] [varchar](255) NULL,
	[TextCenter] [varchar](255) NULL,
	[TextRight] [varchar](255) NULL,
 CONSTRAINT [PK_tblReportText] PRIMARY KEY CLUSTERED 
(
	[TextID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[tblReportType]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[tblReportType](
	[TypeID] [tinyint] NOT NULL,
	[Report Type] [varchar](50) NULL,
 CONSTRAINT [PK_tblReportType] PRIMARY KEY CLUSTERED 
(
	[TypeID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[tblStandardTimeZone]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[tblStandardTimeZone](
	[StandardTimeZoneID] [int] IDENTITY(1,1) NOT NULL,
	[TimezoneDescription] [varchar](500) NULL,
	[Abbreviation] [varchar](10) NULL,
	[StandardTimeZone] [varchar](500) NULL,
	[UTCStandard] [varchar](100) NULL,
	[Country] [varchar](100) NULL,
	[EnteredDate] [datetime] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[tblSupplyUsersLog]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[tblSupplyUsersLog](
	[ID] [int] IDENTITY(1,1) NOT NULL,
	[UserID] [int] NULL,
	[Login Time] [datetime] NULL,
	[Logout Time] [datetime] NULL,
	[Computer Name] [varchar](50) NULL,
	[Domain] [varchar](50) NULL,
	[ClusterID] [int] NULL,
	[Log Date] [datetime] NULL,
	[SessionID] [varchar](50) NULL,
	[LogoutType] [varchar](30) NULL,
PRIMARY KEY CLUSTERED 
(
	[ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[tblUserBookMarks]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[tblUserBookMarks](
	[BookmarkID] [int] IDENTITY(1,1) NOT NULL,
	[UserID] [int] NULL,
	[Bookmark] [nvarchar](50) NULL,
	[BookmarkURL] [nvarchar](50) NULL,
	[DateAdded] [datetime] NULL,
 CONSTRAINT [PK_tbluserbookmarks] PRIMARY KEY CLUSTERED 
(
	[BookmarkID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[tblUserRBAC]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[tblUserRBAC](
	[UserRBACID] [int] IDENTITY(1,1) NOT NULL,
	[UserGroupID] [int] NULL,
	[ModuleID] [int] NULL,
	[Status] [bit] NULL,
	[rowguid] [uniqueidentifier] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[tblUsersRecentHistory]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[tblUsersRecentHistory](
	[ID] [int] IDENTITY(1,1) NOT NULL,
	[UserID] [int] NULL,
	[ModuleName] [varchar](50) NULL,
	[ModuleLink] [varchar](50) NULL,
	[LastActivityDate] [datetime] NULL,
 CONSTRAINT [PK_tblusersrecenthistory] PRIMARY KEY CLUSTERED 
(
	[ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
ALTER TABLE [dbo].[tblAppEvents] ADD  DEFAULT (getdate()) FOR [EnteredDate]
GO
ALTER TABLE [dbo].[tblDeviceRegistration] ADD  DEFAULT (getutcdate()) FOR [Entered Date]
GO
ALTER TABLE [dbo].[tblReportList] ADD  CONSTRAINT [DF_tblReportList_TypeID]  DEFAULT ((1)) FOR [TypeID]
GO
ALTER TABLE [dbo].[tblReportList] ADD  CONSTRAINT [DF_tblReportList_CategoryID]  DEFAULT ((0)) FOR [CategoryID]
GO
ALTER TABLE [dbo].[tblReportList] ADD  CONSTRAINT [DF_tblReportList_Description]  DEFAULT ('') FOR [Description]
GO
ALTER TABLE [dbo].[tblReportList] ADD  CONSTRAINT [DF_tblReportList_Datasource]  DEFAULT ('') FOR [Datasource]
GO
ALTER TABLE [dbo].[tblReportList] ADD  CONSTRAINT [DF_tblReportList_CustomQuery]  DEFAULT ((0)) FOR [CustomQuery]
GO
ALTER TABLE [dbo].[tblReportList] ADD  CONSTRAINT [DF_tblReportList_SQLQuery]  DEFAULT ('') FOR [SQLQuery]
GO
ALTER TABLE [dbo].[tblReportList] ADD  CONSTRAINT [DF_tblReportList_GroupField]  DEFAULT ((0)) FOR [GroupField]
GO
ALTER TABLE [dbo].[tblReportList] ADD  CONSTRAINT [DF_tblReportList_ButtonID]  DEFAULT ((1)) FOR [ButtonID]
GO
ALTER TABLE [dbo].[tblReportList] ADD  CONSTRAINT [DF_tblReportList_IconID]  DEFAULT ((9)) FOR [IconID]
GO
ALTER TABLE [dbo].[tblReportList] ADD  CONSTRAINT [DF_tblReportList_ColWidth]  DEFAULT ('') FOR [ColWidth]
GO
ALTER TABLE [dbo].[tblReportList] ADD  CONSTRAINT [DF_tblReportList_Shared]  DEFAULT ((1)) FOR [Shared]
GO
ALTER TABLE [dbo].[tblReportList] ADD  CONSTRAINT [DF_tblReportList_MyReport]  DEFAULT ((1)) FOR [Shortcut]
GO
ALTER TABLE [dbo].[tblReportList] ADD  CONSTRAINT [DF_tblReportList_Aggregate]  DEFAULT ((0)) FOR [Aggregate]
GO
ALTER TABLE [dbo].[tblReportList] ADD  CONSTRAINT [DF_tblReportList_Total]  DEFAULT ((0)) FOR [TotalRow]
GO
ALTER TABLE [dbo].[tblReportList] ADD  CONSTRAINT [DF_tblReportList_AggFunction]  DEFAULT ('') FOR [AggFunction]
GO
ALTER TABLE [dbo].[tblReportList] ADD  CONSTRAINT [DF_tblReportList_AggField]  DEFAULT ('') FOR [AggField]
GO
ALTER TABLE [dbo].[tblReportList] ADD  CONSTRAINT [DF_tblReportList_PivotField]  DEFAULT ('') FOR [PivotField]
GO
ALTER TABLE [dbo].[tblReportList] ADD  CONSTRAINT [DF_tblReportList_EnteredDate]  DEFAULT (getdate()) FOR [Entered Date]
GO
ALTER TABLE [dbo].[tblReportRBAC] ADD  DEFAULT (newid()) FOR [rowguid]
GO
ALTER TABLE [dbo].[tblStandardTimeZone] ADD  DEFAULT (getutcdate()) FOR [EnteredDate]
GO
ALTER TABLE [dbo].[tblUserRBAC] ADD  DEFAULT (newid()) FOR [rowguid]
GO
ALTER TABLE [dbo].[TBL_STATES]  WITH NOCHECK ADD FOREIGN KEY([COUNTRY_ID])
REFERENCES [dbo].[TBL_COUNTRIES] ([COUNTRY_ID])
GO
ALTER TABLE [dbo].[TBL_STATES]  WITH NOCHECK ADD FOREIGN KEY([COUNTRY_ID])
REFERENCES [dbo].[TBL_COUNTRIES] ([COUNTRY_ID])
GO
ALTER TABLE [dbo].[TBL_STATES]  WITH NOCHECK ADD FOREIGN KEY([COUNTRY_ID])
REFERENCES [dbo].[TBL_COUNTRIES] ([COUNTRY_ID])
GO
ALTER TABLE [dbo].[TBL_STATES]  WITH NOCHECK ADD FOREIGN KEY([COUNTRY_ID])
REFERENCES [dbo].[TBL_COUNTRIES] ([COUNTRY_ID])
GO
ALTER TABLE [dbo].[TBL_STATES]  WITH NOCHECK ADD FOREIGN KEY([COUNTRY_ID])
REFERENCES [dbo].[TBL_COUNTRIES] ([COUNTRY_ID])
GO
ALTER TABLE [dbo].[TBL_STATES]  WITH NOCHECK ADD FOREIGN KEY([COUNTRY_ID])
REFERENCES [dbo].[TBL_COUNTRIES] ([COUNTRY_ID])
GO
ALTER TABLE [dbo].[TBL_STATES]  WITH NOCHECK ADD FOREIGN KEY([COUNTRY_ID])
REFERENCES [dbo].[TBL_COUNTRIES] ([COUNTRY_ID])
GO
ALTER TABLE [dbo].[TBL_STATES]  WITH NOCHECK ADD FOREIGN KEY([COUNTRY_ID])
REFERENCES [dbo].[TBL_COUNTRIES] ([COUNTRY_ID])
GO
ALTER TABLE [dbo].[TBL_STATES]  WITH NOCHECK ADD FOREIGN KEY([COUNTRY_ID])
REFERENCES [dbo].[TBL_COUNTRIES] ([COUNTRY_ID])
GO
/****** Object:  StoredProcedure [dbo].[usp0010GetClusterId]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE procedure [dbo].[usp0010GetClusterId]
AS
BEGIN
	 DECLARE @CompartmentID INT
     SET @CompartmentID= (SELECT [Setting Value] FROM s01tblSettingsCommon WHERE [Setting Name]='HubConsoleComprtmentID')
     SELECT  ClusterID FROM s01qryCabinets where compartmentid=@CompartmentID;
	
END
GO
/****** Object:  StoredProcedure [dbo].[usp008isDeviceConnected]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE PROCEDURE [dbo].[usp008isDeviceConnected]
    @ClinetIP VARCHAR(MAX)
AS
BEGIN
    
    -- Check if a device with the given IP address and 'Active' status exists
    IF EXISTS (SELECT 1 FROM tblDeviceRegistration WHERE [Device IPAddress] = @ClinetIP AND Status = 'Active')
    BEGIN
        SELECT 'Device is Connected'
    END
    ELSE
    BEGIN
        SELECT 'Device is not Connected'
    END
END
GO
/****** Object:  StoredProcedure [dbo].[usp704WebConsoleProcessUserLogs]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE Procedure [dbo].[usp704WebConsoleProcessUserLogs]
	@Event VARCHAR(255)
	, @udtSystemData udtSPParam READONLY
	, @udtEventData udtSPParam READONLY
	, @UserSessionID VARCHAR (50) = NULL
	--, @UsersID VARCHAR (50)
AS
	SET NOCOUNT ON;

	DECLARE @UserID INT, @CurrentUtcTime DATETIME, @ClusterName VARCHAR(100)
		   ,@DeviceID INT, @ComputerName VARCHAR(100), @Domain VARCHAR(50)
	DECLARE @EventType VARCHAR(30), @PIN VARCHAR(20)

	SET XACT_ABORT ON;
	BEGIN TRY
		BEGIN TRANSACTION

			SELECT @UserID = Value FROM @udtEventData WHERE Property = 'UserID'
			SELECT @DeviceID = Value FROM @udtSystemData WHERE Property = 'DeviceID'
			SELECT @ComputerName = Value FROM @udtSystemData WHERE Property = 'ComputerName'
			SELECT @Domain = Value FROM @udtSystemData WHERE Property = 'Domain'
			SELECT @EventType = Value FROM @udtEventData WHERE Property = 'EventType'
			SELECT @PIN = Value FROM @udtEventData WHERE Property = 'Pwd' 
			SELECT @DeviceID = Value FROM @udtSystemData WHERE Property = 'DeviceID'
			SELECT @ClusterName = [Cluster Name] FROM  dbo.qryCabinets WHERE ClusterID = @DeviceID
			IF @UserID is null
				SELECT @UserID = UserID FROM qry700WebConsoleSUPUsersValidate WHERE RFID = @PIN AND RFID <> ''
			IF @UserID is null
				SELECT @UserID = UserID FROM  qry700WebConsoleSUPUsersValidate WHERE PIN = @PIN AND PIN <> ''
			IF (SELECT [Setting Value] FROM s01tblSettingsCommon WHERE [Setting Name] = 'ENABLE_UTC_TIMEZONE') = 'TRUE'
			BEGIN
				SELECT @CurrentUTCTime = GETUTCDATE()
			END
			ELSE
			BEGIN
				SELECT @CurrentUTCTime = GETDATE()
			END
			IF @Event = 'Login'
			BEGIN
				INSERT INTO s01tblUsersLog( UserID, [Login Time], [Computer Name], Domain, [Log Date], SessionID)
				VALUES( @UserID, @CurrentUtcTime, @ClusterName, @ComputerName, @CurrentUtcTime, @UserSessionID)
				INSERT INTO tblSupplyUsersLog( UserID, [Login Time], [Computer Name], Domain, [Log Date], SessionID, ClusterID )
				VALUES( @UserID, @CurrentUtcTime, @ComputerName, @Domain, @CurrentUtcTime, @UserSessionID, @DeviceID)
			END
			ELSE IF @Event = 'Logout'
			BEGIN
				UPDATE s01tblUsersLog
				SET [Logout Time] = @CurrentUtcTime
				WHERE UserID = @UserID 
					AND SessionID = @UserSessionID 
				UPDATE tblSupplyUsersLog 
				SET [Logout Time] = @CurrentUtcTime
					, LogoutType = @EventType
				WHERE UserID = @UserID 
					AND SessionID = @UserSessionID 
			END
		COMMIT TRANSACTION
		END TRY
		BEGIN CATCH
			IF (XACT_STATE()) <> 0
				ROLLBACK TRANSACTION;
			EXECUTE s01uspLogError;
		END CATCH;
	SET NOCOUNT OFF
GO
/****** Object:  StoredProcedure [dbo].[usp705WebConsoleAppCabEventLogs]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE Procedure [dbo].[usp705WebConsoleAppCabEventLogs]
	@Event VARCHAR(255)
	, @udtSystemData udtSPParam READONLY
	, @udtEventData udtSPParam READONLY
	, @Logs VARCHAR (50) = ''
	, @UserSessionID VARCHAR (50) = NULL
AS
	SET NOCOUNT ON;

		--Edted by subramanya to handle type mismatch exception
		--SET XACT_ABORT ON;
		BEGIN TRY
		--BEGIN TRANSACTION

		DECLARE @UserID INT, @EntryTypeID INT, @EventID INT, @LoginSessionID BIGINT, @SessionID BIGINT, @LogMsg VARCHAR(255) 
		DECLARE @LoginType VARCHAR(100), @Msg VARCHAR(500), @LoginEventID INT, @ClusterName VARCHAR(100)
		DECLARE @EventType VARCHAR(50), @UserName VARCHAR(200), @RFID VARCHAR(50), @DeviceID INT, @ComputerName VARCHAR(100), @Domain VARCHAR(50)
		DECLARE @CurrentUtcTime DATETIME,@OverrideNotes VARCHAR(50),@Reason VARCHAR(50)
		DECLARE @DoorStatus VARCHAR(50), @LockStatus VARCHAR(50), @SwitchStatus VARCHAR(50), @ButtonClick VARCHAR(50), @PIN VARCHAR(20)

		SELECT @DeviceID = Value FROM @udtSystemData WHERE Property = 'DeviceID'
		SELECT @EntryTypeID = Value FROM @udtSystemData WHERE Property = 'EntryTypeID'
		SELECT @ComputerName = Value FROM @udtSystemData WHERE Property = 'ComputerName'
		SELECT @Domain = Value FROM @udtSystemData WHERE Property = 'Domain'
		SELECT @UserID = Value FROM @udtEventData WHERE Property = 'UserID'
		SELECT @SessionID = Value FROM @udtEventData WHERE Property = 'SessionID'
		SELECT @EventType = Value FROM @udtEventData WHERE Property = 'EventType'
		SELECT @OverrideNotes = Value FROM @udtEventData WHERE Property = 'EventInput2'
		SELECT @Reason = Value FROM @udtEventData WHERE Property = 'EventInput' 
		
		--H/w logs
		SELECT @DoorStatus = Value FROM @udtEventData WHERE Property = 'DoorStatus'
		SELECT @LockStatus = Value FROM @udtEventData WHERE Property = 'LockStatus'
		SELECT @SwitchStatus = Value FROM @udtEventData WHERE Property = 'SwitchStatus'
		SELECT @ButtonClick = Value FROM @udtEventData WHERE Property = 'ButtonClick' 

		SELECT @LogMsg = LogMsg, @EventID = EventID FROM tblAppEvents WHERE Event = @Event 
		SELECT @LoginEventID = EventID FROM tblAppEvents WHERE Event = 'LogIn'
		SELECT @ClusterName = [Cluster Name] FROM  dbo.qryCabinets WHERE ClusterID = @DeviceID
		SELECT @PIN = Value FROM @udtEventData WHERE Property = 'Pwd' 	

		SELECT @UserName = [User Name], @RFID = RFID, @UserID = UserID FROM qry700WebConsoleSUPUsersValidate WHERE RFID = @PIN AND RFID <> ''

		IF @UserName IS NULL
			SELECT @UserName = [User Name], @RFID = PIN, @UserID = UserID FROM  qry700WebConsoleSUPUsersValidate WHERE PIN = @PIN AND PIN <> ''

		IF @UserName IS NULL
			SELECT TOP 1 @RFID = RFID, @UserName = [User Name] FROM qry700WebConsoleSUPUsersValidate WHERE ClusterID = @DeviceID AND UserID = @UserID

		IF @RFID IS NULL
				SET @RFID = @PIN

		IF (SELECT [Setting Value] FROM s01tblSettingsCommon WHERE [Setting Name] = 'ENABLE_UTC_TIMEZONE') = 'TRUE'
		BEGIN
			SELECT @CurrentUTCTime = GETUTCDATE()
		END
		ELSE
		BEGIN
			SELECT @CurrentUTCTime = GETDATE()
		END

		IF @Event in ('Login','LogOut')
		BEGIN

			--INSERT INTO dbo.tblCabinetLog( UserID, [User Name], RFID, EventID, [Cluster Name], [Computer Name], [Domain], [Log Date], SessionID  )
			--SELECT  @UserID, @UserName, @RFID, @EventID, @ClusterName, @ComputerName, @Domain, @CurrentUtcTime, @SessionID
					
			INSERT INTO s01tblAppEventLog( UserID, [User Name], RFID, EventID, [Cluster Name], [Computer Name], [Domain], [Log Date], LogMsg, SessionID
									   )
		    SELECT  @UserID, @UserName, @RFID, @EventID, @ClusterName, @ComputerName, @Domain, @CurrentUtcTime, @Logs, @UserSessionID
					
		END

		ELSE IF (@Event NOT IN ('AppStartReason','AppOnLoad','IntializeCluster','LoadCulture', 'Login', 'ButtonClick') 
				   AND @Reason NOT IN ('btnProduct Search')
				   AND @OverrideNotes NOT IN ('ProductInventory') )
		BEGIN

			--INSERT INTO dbo.tblCabinetLog( UserID, [User Name], RFID, EventID, [Cluster Name], [Computer Name], [Domain], [Log Date], SessionID  )
			--SELECT  @UserID, @UserName, @RFID, @EventID, @ClusterName, @ComputerName, @Domain, @CurrentUtcTime, @SessionID
					
			INSERT INTO s01tblAppEventLog( UserID, [User Name], RFID, EventID, [Cluster Name], [Computer Name], [Domain], [Log Date], LogMsg, SessionID
									   )
		    SELECT  @UserID, @UserName, @RFID, @EventID, @ClusterName, @ComputerName, @Domain, @CurrentUtcTime
					, CASE
						WHEN @Event='AppShutdown' AND  @EventType = 'AddReason' THEN  'Added App shut down Reason Notes'
						WHEN @Event='AppShutdown' AND  @EventType = 'SelectReason' THEN  'Selected App shut down Reason and the application exited successfully'
					ELSE 
						@LogMsg
					END , @SessionID
					
		END

		--ELSE IF ( @Event= 'ButtonClick' )
		--BEGIN

			--INSERT INTO tblAppEventLog( UserID, [User Name], RFID, EventID, EntryTypeID, [Cluster Name], [Computer Name], [Domain], [Log Date], LogMsg, SessionID,OverrideNotes
			--						   , [Door Status],[Lock Status],[Switch Status],[Button Events] )
		 --   SELECT  @UserID, @UserName, @RFID, @EventID, @EntryTypeID, @ClusterName, @ComputerName, @Domain, @CurrentUtcTime, @LogMsg, @SessionID
			--		, @OverrideNotes, @DoorStatus, @LockStatus, @SwitchStatus, @ButtonClick

		--END

		ELSE
		BEGIN
			
			--INSERT INTO dbo.tblCabinetLog ([UserID], [User Name], [EventID], [Cluster Name], [Computer Name], [Domain], [Log Date], SessionID)
			--VALUES ( 0, 'System, System', @EventID, @ClusterName, @ComputerName, @Domain, @CurrentUtcTime, @SessionID)

			INSERT INTO s01tblAppEventLog( [UserID], [User Name], EventID,  [Cluster Name], [Computer Name], [Domain], [Log Date], LogMsg, SessionID  )
			VALUES ( 0, 'System, System', @EventID, @ClusterName, @ComputerName, @Domain, @CurrentUtcTime
				, CASE
					WHEN @EventType = 'AddReason' 
						THEN  'Added App Start Reason Notes'
					WHEN @EventType = 'SelectReason'
						THEN 'Selected App start Reason Notes'
					WHEN @EventType = 'CancelReason'
						THEN 'No Reason Added/Selected'
					ELSE 
						@LogMsg
			 	END
				
			, @SessionID  )

		END

		

		--COMMIT TRANSACTION
		END TRY
		BEGIN CATCH
			--IF (XACT_STATE()) <> 0
				--ROLLBACK TRANSACTION;
				SET @Msg = 'Error in inserting Logs';
			EXECUTE s01uspLogError;
		END CATCH;
	SET NOCOUNT OFF
GO
/****** Object:  StoredProcedure [dbo].[usp706WebConsoleGetProductName]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE Procedure [dbo].[usp706WebConsoleGetProductName]
as
begin
    select [Setting Value] from s01tblSettingsCommon where [Setting Name]='SiteName'
end
GO
/****** Object:  StoredProcedure [dbo].[usp708WebConsoleAddADUser]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE Procedure [dbo].[usp708WebConsoleAddADUser]
@username varchar(max),	
@rfid varchar(max),
@firstname varchar(max),
@lastname varchar(max),
@middlename varchar(max),
@designation varchar(max),
@email varchar(max),
@domain varchar(max),
@groupid int,
@computername varchar(max)
AS
BEGIN

IF EXISTS ( SELECT 1 FROM s01tblUsers WHERE  [First Name] = @firstname AND [Last Name] = @lastname AND [Username] = @username)
	begin
		Select 'Status' as status,'User updated' as msg
	end
	else
	begin
		insert into s01tblUsers (Username,[Entered Date],[First Name],[Middle Name],[Last Name],Email,[Computer Name],Domain,[Job Title],GroupID,UserStatusID,RFID)
		values(@username,CURRENT_TIMESTAMP,@firstname,@middlename,@lastname,@email,@computername,@Domain,@designation,3,1,@rfid);

		Select 'Status' as status,'User registered' as msg
	end

END
GO
/****** Object:  StoredProcedure [dbo].[usp709WebConsoleWebEventLog]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE Procedure [dbo].[usp709WebConsoleWebEventLog]
@Event varchar(50)
,@EventData [dbo].[udt705WebConsoleSPActivityLog] readonly
AS
begin
DECLARE @UserID INT,  @EventID INT, @LoginSessionID BIGINT, @SessionID varchar(max), @LogMsg VARCHAR(255) ,@OverrideNotes varchar(max)
        DECLARE @LoginType VARCHAR(100), @Msg VARCHAR(500), @LoginEventID INT, @ClusterName VARCHAR(100), @CompartmentName VARCHAR(100)
        DECLARE @EventType VARCHAR(50), @UserName VARCHAR(200), @RFID VARCHAR(50), @DeviceID INT, @ComputerName VARCHAR(100), @Domain VARCHAR(50),@Location varchar(100),@Room varchar(100)



 

 

        SELECT @DeviceID = Value FROM @EventData WHERE Property = 'DeviceID'

        SELECT @ComputerName = Value FROM @EventData WHERE Property = 'ComputerName'
        SELECT @Domain = Value FROM @EventData WHERE Property = 'Domain'
        SELECT @UserID = Value FROM @EventData WHERE Property = 'UserID'
        SELECT @SessionID = Value FROM @EventData WHERE Property = 'SessionID'
        SELECT @EventType = Value FROM @EventData WHERE Property = 'EventType'
        SELECT @OverrideNotes = Value FROM @EventData WHERE Property = 'OverrideNotes'
        SELECT @Location= Value FROM @EventData WHERE Property = 'Location'
        SELECT @Room = Value FROM @EventData WHERE Property = 'Room'


        SELECT @LogMsg = LogMsg, @EventID = EventID FROM tblAppEvents WHERE Event = @Event 
        SELECT @ClusterName = [Cluster Name] FROM s01qryCabinets WHERE ClusterID = @DeviceID
        select @CompartmentName=CompartmentID FROM s01qryCabinets WHERE ClusterID = @DeviceID

        SELECT @RFID = Value FROM @EventData WHERE Property = 'RFID'
        SELECT @UserName =Value FROM @EventData WHERE Property = 'UserName'
      
        BEGIN
			INSERT INTO tblAppEventLog( UserID, [User Name], RFID, EventID, [Cluster Name],[Compartment Name],[Computer Name], [Domain], [Log Date],LogMsg,SessionID
			--,WorkflowInfo,location,Room 
			)
            SELECT  @UserID, @UserName, @RFID, @EventID, @ClusterName,@CompartmentName, @ComputerName, @Domain, getutcdate(),@LogMsg,@SessionID
                    --, @OverrideNotes,@Location,@Room
        END


end
GO
/****** Object:  StoredProcedure [dbo].[usp710WebConsoleLoadOverrideReasons]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS OFF
GO
SET QUOTED_IDENTIFIER OFF
GO


/****************************************************************************************************
Name				: uspLoadOverrideReasons
Version				: *******
Purpose				: To Load Override reason list
Author				: Rashmita
Date Created 		: 3Oth Nov 2018
Application Affected: IriSupply
-----------------------------------------------------------------------------------------------------
Release Notes		:
------------------------------------------------------------------------------------------------------
--SP CALL
DECLARE @p2 dbo.udtSPParam
DECLARE @p3 dbo.udtSPParam
INSERT INTO @p2 VALUES(N'DeviceID',4)
INSERT INTO @p2 VALUES(N'EntryTypeID',1)
INSERT INTO @p2 values(N'ComputerName',N'TestMachine')
INSERT INTO @p2 values(N'Domain',N'TestDomain')
INSERT INTO @p3 VALUES(N'UserID', 2)
INSERT INTO @p3 VALUES(N'EventInput', 'Return')--Stock Item, Remove Item, Wste Item, Waste
INSERT INTO @p3 VALUES(N'EventOutPut', 'OverrideReasons')
INSERT INTO @p3 VALUES(N'SessionID', N'20180910183204970')

EXEC uspLoadOverrideReasons @Event='LoadOverrideReason',@udtSystemData=@p2, @udtEventData=@p3

--Validate Data-
SELECT TOP 1 * FROM tblCabinetLog ORDER BY CabinetLogID DESC
SELECT TOP 1 * FROM tblAppEventLog ORDER BY AppEventLogID DESC
*****************************************************************************************************/
CREATE Procedure [dbo].[usp710WebConsoleLoadOverrideReasons]
	@Event VARCHAR(255)
	, @udtSystemData udtSPParam READONLY
	, @udtEventData udtSPParam READONLY
AS
	SET NOCOUNT ON;
		DECLARE @TransactionType VARCHAR(100), @DataSetName VARCHAR(100), @Msg VARCHAR(200), @EventType VARCHAR(15)
		DECLARE @OverrideNotes VARCHAR (100), @OverrideID INT, @ExceptionID INT

		--Edited by subramanya to handle type mismatch exception
		SET XACT_ABORT ON;
		BEGIN TRY
		BEGIN TRANSACTION

		SELECT @TransactionType = Value FROM @udtEventData WHERE Property = 'EventInput'
		
		SELECT @EventType = Value FROM @udtEventData WHERE Property = 'EventType'
		SELECT @DataSetName = Value FROM @udtEventData WHERE Property = 'EventOutPut'

		SELECT @OverrideID = OverrideID from s01tblOverride WHERE OverrideDesc = CASE 
																				   WHEN @TransactionType = 'Waste Item' 
																						THEN 'Waste' 
																				   WHEN @TransactionType = 'Return' 
																						THEN 'Return Item'
																				   WHEN @TransactionType = 'Override' 
																						THEN 'Manual Override'
																				   ELSE @TransactionType 
																		      END
		
		SELECT @ExceptionID = ExcID from s01tblException WHERE ExceptionDesc = CASE 
																				WHEN @TransactionType = 'Waste' 
																					THEN 'Waste Item Usage' 
																				WHEN @TransactionType = 'Return' 
																					THEN 'Return RFID item to inventory'
																				WHEN @TransactionType = 'Return' 
																					THEN 'Return RFID item to inventory'
																			    ELSE @TransactionType 
																			END
	
				IF ( @Event = 'LoadOverrideReason' AND @EventType = 'OverrideType' )
				BEGIN
					SELECT @DataSetName AS DatasetName, OverrideID, OverrideNotes 
					FROM s01tblOverrideNotes 
					WHERE OverrideID = @OverrideID
				END
				IF ( @Event = 'LoadOverrideReason' AND @EventType = 'ExceptionType')
				BEGIN
					SELECT @DataSetName AS DatasetName, ExcID, ExcNotes 
					FROM s01qryExceptionNotesChoice 
					WHERE ExcID = @ExceptionID
				END

				EXECUTE  usp705WebConsoleAppCabEventLogs @Event = @Event
						, @udtSystemData = @udtSystemData
						, @udtEventData = @udtEventData

			COMMIT TRANSACTION
		END TRY
		BEGIN CATCH
			IF (XACT_STATE()) <> 0
				ROLLBACK TRANSACTION;
				SET @Msg = 'Error in Loading in Override Reasons';
			EXECUTE s01uspLogError;
		END CATCH;
	SET NOCOUNT OFF
GO
/****** Object:  StoredProcedure [dbo].[usp711WebConsoleLogUserRecentVisits]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE Procedure [dbo].[usp711WebConsoleLogUserRecentVisits]
@UserID int,
@ModuleName varchar(50)
as 
begin
Declare @Module_URL varchar(50)
    if exists (select Module_Name from tblModules where module_Name=@ModuleName)
	begin
	   set @Module_URL=(select Link from tblModules where module_Name=@ModuleName)
	   delete from tblusersrecenthistory where userid=@UserID and ModuleName=@ModuleName;
	   insert into tblusersrecenthistory values(@UserID,@ModuleName,@Module_URL,GETUTCDATE())
	end
	else
	begin
	   select 'Failed to add'
	end
end
GO
/****** Object:  StoredProcedure [dbo].[usp712WebConsoleAddPatient]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE Procedure [dbo].[usp712WebConsoleAddPatient]
	@Event VARCHAR(255)
	, @udtSystemData udtSPParam READONLY
	, @udtEventData udtSPParam READONLY
AS
	SET NOCOUNT ON;
	DECLARE @LastName VARCHAR(50), @FirstName VARCHAR(50), @MiddleName VARCHAR(50), @PatientID VARCHAR(20)
	DECLARE @AccountNo VARCHAR(50), @AdmitDate VARCHAR(50), @DischargeDate VARCHAR(50), @ProcedureDate VARCHAR(50), @CurrentUTCTime DATETIME
	DECLARE @ProcedureStartTime VARCHAR(50), @ProcedureEndTime VARCHAR(50), @ProcedureID INT , @PhysicianName VARCHAR(255), @DOB VARCHAR(50)
	DECLARE @EnteredUserID INT, @UserID INT, @EntryTypeID INT, @MAVisitID BIGINT, @MAPatientID BIGINT, @SessionID BIGINT, @PhysicianID VARCHAR(15)
	DECLARE @MAPhysicianID INT,  @MAScheduleID INT, @AppointmentID VARCHAR(20), @Msg VARCHAR(200), @DeviceID INT
	DECLARE @ClientTimeZone VARCHAR(500), @UTCStandard VARCHAR(100), @SecondDiff INT, @DataSetName VARCHAR(50)
	
	--Edited by subramanya to handle type mismatch exception
	SET XACT_ABORT ON;
	BEGIN TRY
	BEGIN TRANSACTION;

	SELECT @DeviceID = Value FROM @udtSystemData WHERE Property = 'DeviceID'
	SELECT @EntryTypeID = Value FROM @udtSystemData WHERE Property = 'EntryTypeID'
	SELECT @SessionID = Value FROM @udtEventData WHERE Property = 'SessionID'
	SELECT @UserID = Value FROM @udtEventData WHERE Property = 'UserID'
	SELECT @LastName = Value FROM @udtEventData WHERE Property = 'LastName'
	SELECT @MiddleName = Value FROM @udtEventData WHERE Property = 'MiddleName'
	SELECT @FirstName = Value FROM @udtEventData WHERE Property = 'FirstName'
	SELECT @PatientID = Value FROM @udtEventData WHERE Property = 'PatientID'
	SELECT @DOB = Value FROM @udtEventData WHERE Property = 'DOB' 
	SELECT @AccountNo = Value FROM @udtEventData WHERE Property = 'AccountNo'
	SELECT @AppointmentID = Value FROM @udtEventData WHERE Property = 'AppointmentID'
	SELECT @AdmitDate = Value FROM @udtEventData WHERE Property = 'AdmitDate'
	SELECT @DischargeDate = Value FROM @udtEventData WHERE Property = 'DischargeDate'
	SELECT @ProcedureDate = Value FROM @udtEventData WHERE Property = 'ProcedureDate'
	SELECT @ProcedureStartTime = Value FROM @udtEventData WHERE Property = 'ProcedureStartTime'
	SELECT @ProcedureEndTime = Value FROM @udtEventData WHERE Property = 'ProcedureEndTime'
	SELECT @ProcedureID = Value FROM @udtEventData WHERE Property = 'ProcedureID'
	SELECT @PhysicianID = Value FROM @udtEventData WHERE Property = 'PhysicianID'
	SELECT @DataSetName = Value FROM @udtEventData WHERE Property = 'EventOutPut'

	SET @DOB = FORMAT( CAST( @DOB AS DATETIME ), 'yyyy\-MM\-dd hh\:mm\:ss' )
	SET @AdmitDate = FORMAT( CAST( @AdmitDate AS DATETIME ), 'yyyy\-MM\-dd hh\:mm\:ss' )
	SET @DischargeDate = FORMAT( CAST( @DischargeDate AS DATETIME ), 'yyyy\-MM\-dd hh\:mm\:ss' )
	SET @ProcedureDate = FORMAT( CAST( @ProcedureDate AS DATETIME ), 'yyyy\-MM\-dd hh\:mm\:ss' )
	SET @ProcedureStartTime = FORMAT( CAST( @ProcedureStartTime AS DATETIME ), 'yyyy\-MM\-dd hh\:mm\:ss' )
	SET @ProcedureEndTime = FORMAT( CAST( @ProcedureEndTime AS DATETIME ), 'yyyy\-MM\-dd hh\:mm\:ss' )

	IF @ProcedureID IS NULL
		SELECT @ProcedureID = ProcedureID FROM s01tblProcedures WHERE ProcedureDesc = 'Unknown'
		SELECT @PhysicianName = [Physician Name], @MAPhysicianID = MAPhysicianID FROM s01qryPhysicians WHERE PhysicianID = @PhysicianID 
	
	SELECT @ClientTimeZone = [dbo].[udf700WebConsoleSUPFetchSettings] ( @DeviceId,'CLIENT_TIMEZONE' )
	SELECT @UTCStandard = UTCStandard FROM tblStandardTimeZone WHERE StandardTimeZone = @ClientTimeZone
	SELECT @SecondDiff =  REPLACE(@UTCStandard, 'UTC', '')
	--IF (SELECT [Setting Value] FROM s01tblSupplySettings WHERE [Setting Name] = 'ENABLE_UTC_TIMEZONE') = 'TRUE'
	--BEGIN
	--	SELECT @CurrentUTCTime = GETUTCDATE()
	--END
	--ELSE
	--BEGIN
	--	SELECT @CurrentUTCTime = GETDATE()
	--END
	SELECT @CurrentUTCTime = GETDATE()

		SELECT @MAPatientID = MAPatientID 
		FROM s01tblPatients 
		WHERE PatientID = @PatientID

		IF (@MAPatientID IS NULL)
		BEGIN

  			INSERT INTO s01tblPatients( PatientID, [Account Number], [First Name], [Middle Name], [Last Name], DOB, EntryTypeID, [Entered By]
						, [Entered Date], LastActivityDate )
			VALUES( @PatientID, @AccountNo, @FirstName, @MiddleName, @LastName, @DOB, @EntryTypeID, @UserID 
						, @CurrentUTCTime, @CurrentUTCTime )
            select  'Patinet Added Successfully' as Msg
			SET @MAPatientID = SCOPE_IDENTITY()
		END

		ELSE
		BEGIN
			UPDATE s01tblPatients 
			SET DOB = @DOB, [First Name] = @FirstName, [Middle Name] = @MiddleName
					, [Last Name] = @LastName, LastActivityDate = @CurrentUTCTime 
			WHERE @MAPatientID = MAPatientID

			SELECT @MAVisitID = MAVisitID 
			FROM s01tblPatientVisits 
			WHERE MAPatientID = @MAPatientID
			--AND [AltAcctNo] = @AccountNo
		END

		IF EXISTS ( SELECT  MAVisitID FROM s01tblPatientVisits WHERE MAPatientID = @MAPatientID )
		BEGIN
			PRINT 'VIST UPDATE'
			UPDATE s01tblPatientVisits 
			SET MAPatientID = @MAPatientID, AdmitDate = @AdmitDate, AltAcctNo = @AccountNo
					, DischargeDate = @DischargeDate, LastActivityDate = @CurrentUTCTime, UpdatedBy = @UserID				
			WHERE @MAVisitID = MAVisitID
		END
		ELSE

		BEGIN
			PRINT 'VIST INSERT'
			INSERT INTO s01tblPatientVisits( MAPatientID, AltAcctNo, UpdatedBy, EntryTypeID, AdmitDate, DischargeDate, LastActivityDate )
  			VALUES( @MAPatientID, @AccountNo, @UserID, @EntryTypeID, @AdmitDate, @DischargeDate, @CurrentUTCTime )
			SET @MAVisitID = SCOPE_IDENTITY()
		END

		SELECT  @MAScheduleID = MAScheduleID
		FROM s01tblPatientSchedules 
		WHERE MAVisitID = @MAVisitID
			AND AppointmentID = @AppointmentID
			
		IF EXISTS ( SELECT MAScheduleID FROM s01tblPatientSchedules WHERE MAVisitID = @MAVisitID AND AppointmentID = @AppointmentID )
		BEGIN
			PRINT 'Schedule UPDATE'
			UPDATE s01tblPatientSchedules 
			SET MAPatientID = @MAPatientID, AppointmentID = @AppointmentID, MAPhysicianID = @MAPhysicianID
							, MAVisitID = @MAVisitID, ProcedureID = @ProcedureID, ProcedureDate = @ProcedureDate, ProcedureStartTime = @ProcedureStartTime
							, ProcedureEndTime = @ProcedureEndTime, LastActivityDate = @CurrentUTCTime				
			WHERE @MAScheduleID = @MAScheduleID 
				AND AppointmentID = @AppointmentID
		END


		ELSE IF NOT EXISTS ( SELECT MAScheduleID FROM s01tblPatientSchedules WHERE MAVisitID = @MAVisitID AND AppointmentID = @AppointmentID )
		BEGIN
			PRINT 'Schedule INSERT'		
			INSERT INTO s01tblPatientSchedules ( MAPatientID, AppointmentID, MAPhysicianID, EntryTypeID, [Entered By], [Entered Date]
							, MAVisitID, ProcedureID, ProcedureDate, ProcedureStartTime, ProcedureEndTime, LastActivityDate )
			VALUES ( @MAPatientID, @AppointmentID, @MAPhysicianID, @EntryTypeID,  @UserID, @CurrentUTCTime, @MAVisitID, @ProcedureID
							, @ProcedureDate, @ProcedureStartTime, @ProcedureEndTime, @CurrentUTCTime )
			SET @MAScheduleID = SCOPE_IDENTITY()		
		END

		COMMIT TRANSACTION;
		SET @Msg = 'Successfully Added the Patinet'

		EXECUTE  usp705WebConsoleAppCabEventLogs @Event = @Event
						, @udtSystemData = @udtSystemData
						, @udtEventData = @udtEventData

		SELECT @DataSetName AS DataSetName, 'CSN: ' + ISNULL(SPD.VisitNumber,'N/A') AS CSN, PD.[Patient Name] AS PatientName
			, 'MRN: ' + ISNULL(PD.PatientID,'N/A') AS MRN, PD.[Physician Name] AS PhysicianName
			, CASE 
					WHEN DATEADD( SECOND, @SecondDiff, PD.ProcedureDate ) IS NULL 
						THEN PD.LastActivityDate 
					ELSE DATEADD( SECOND, @SecondDiff, PD.LastActivityDate ) 
			  END AS ProcedureDate
		    , 'CASEID: ' + ISNULL(PD.AppointmentID,'N/A') AS CASEID,PD.MAPatientID, PD.MAScheduleID, PD.MAVisitID, SPD.ProcedureCode 
		FROM s01qryPatientDisplayADT PD
							LEFT OUTER JOIN qry701WebConsoleSUPPatientDisplayADT SPD ON SPD.MaScheduleID = PD.MaScheduleID
		WHERE PD.MAScheduleID  = @MAScheduleID

	END TRY
	BEGIN CATCH
		IF (XACT_STATE()) <> 0
			ROLLBACK TRANSACTION;
			SET @Msg = 'Unable to Add Patient Data'
		EXECUTE s01uspLogError;
	END CATCH;
	SET NOCOUNT OFF
GO
/****** Object:  StoredProcedure [dbo].[usp712WebConsoleCheckUserModuleAccess]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE Procedure [dbo].[usp712WebConsoleCheckUserModuleAccess]
@UserGroupID int,
@ModuleName varchar(50)
as
begin
Declare @ModuleID int;
set @ModuleID= (select Module_ID from tblModules where Replace( Module_Name,' ','')=@ModuleName)
       if(@UserGroupID = 0)
	   begin 
	        if exists (select * from tblModules where Replace( Module_Name,' ','') =@ModuleName and ISENABLED =1)
			begin
			select 'true' as result;
			end
	   end
      else if exists (select * from tblUserRBAC where UserGroupID =@UserGroupID and ModuleID =@ModuleID and status=1 )
	   begin
	        select 'true' as result
	   end
	   else
	   begin
	        select 'false' as result
	   end
end
GO
/****** Object:  StoredProcedure [dbo].[usp714WebConsoleGetPatientUsage ]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE Procedure [dbo].[usp714WebConsoleGetPatientUsage ]
@MAScheduleID nvarchar(50)
as
begin
					--SELECT qry704.[Description], qry704.CatNo,CONVERT(varchar, CONVERT(datetime,qry704.DateUsed,120),22)as DateUsed, qry704.[REAL], qry704.Qty, qry704.PatientName, qry704.ItemUsageID, qry704.UsageTypeID,Ext.Capitated
					--	, RFID, ProductType,SerialNo, LotNo--, EpicStatus
					--FROM qry704WebConsoleSUPItemUsageList qry704
					--left JOIN tblItemUsageExt Ext ON qry704.ItemUsageID=Ext.ItemUsageID
					--WHERE MAScheduleID = @MAScheduleID
					--ORDER BY DateUsed DESC

					--updated by viresh -- 11/11/2024
					
					--SELECT qry704.[Description], qry704.CatNo,CONVERT(varchar, CONVERT(datetime,qry704.DateUsed,120),22)as DateUsed, qry704.[REAL], qry704.Qty, qry704.PatientName, qry704.ItemUsageID, qry704.UsageTypeID,Ext.Capitated
					--	, qry704.RFID, ProductType,SerialNo, LotNo,I.ItemStatusID
					--FROM qry704WebConsoleSUPItemUsageList qry704
					--left JOIN tblItemUsageExt Ext ON qry704.ItemUsageID=Ext.ItemUsageID
					--INNER JOIN qryItemUsage I ON qry704.ItemUsageID = I.ItemUsageID
					--WHERE qry704.MAScheduleID = @MAScheduleID --AND I.ItemStatusID = 4
					--ORDER BY DateUsed DESC
					SELECT * 
					FROM qryHUBFetchUsageItemList
					WHERE MAScheduleID = @MAScheduleID --AND I.ItemStatusID = 4
					ORDER BY DateUsed DESC
end
GO
/****** Object:  StoredProcedure [dbo].[usp717WebConsoleGetUserGroups]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE Procedure [dbo].[usp717WebConsoleGetUserGroups]
@GroupID int
AS
BEGIN
	if(@GroupID =1)
	begin
	     select groupid GROUP_ID,[User Group] USER_GROUP from s01tblUserGroup where GroupID not in (0);
	end	
	else if(@GroupID=10)
	begin
	     select groupid Group_ID,[User Group] User_Group from s01tblUserGroup where GroupID in (9,10)
	end
	else
	begin
	     select groupid GROUP_ID,[User Group] USER_GROUP from s01tblUserGroup where GroupID not in (0);
	end
END

GO
/****** Object:  StoredProcedure [dbo].[usp719WebConsoleGetUsers]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE Procedure [dbo].[usp719WebConsoleGetUsers]
@UserGroupID int
AS
BEGIN
if(@UserGroupID =10)
begin
    select UserID, CONCAT(t.[First Name],' ',t.[Last Name]) as Name,
    t.Username, 
    t.[Job Title] as Designation,
    t.[Employee ID],
    t.Department,
    t.[Entered Date] DateAdded,
    t.Domain,
    t.UserStatusID as Status,g.[User Group] as UserGroup
    from s01tblUsers as t
    inner join s01tblUserGroup as g on t.GroupID=g.GroupID
    where  t.GroupID not in (9,10)
    order by CONCAT(t.[First Name],' ',t.[Last Name])
end
else if(@UserGroupID =1)
   begin
    select UserID, CONCAT(t.[First Name],' ',t.[Last Name]) as Name,
    t.Username, 
    t.[Job Title] as Designation,
    t.[Employee ID],
    t.Department,
    t.[Entered Date] DateAdded,
    t.Domain,
    t.UserStatusID as Status,g.[User Group] as UserGroup
    from s01tblUsers as t
    inner join s01tblUserGroup as g on t.GroupID=g.GroupID
    where  t.GroupID not in (0)
    order by CONCAT(t.[First Name],' ',t.[Last Name])
end  
else
   begin
    select UserID, CONCAT(t.[First Name],' ',t.[Last Name]) as Name,
    t.Username, 
    t.[Job Title] as Designation,
    t.[Employee ID],
    t.Department,
    t.[Entered Date] DateAdded,
    t.Domain,
    t.UserStatusID as Status,g.[User Group] as UserGroup
    from s01tblUsers as t
    inner join s01tblUserGroup as g on t.GroupID=g.GroupID
    
    order by CONCAT(t.[First Name],' ',t.[Last Name])
end  
END
GO
/****** Object:  StoredProcedure [dbo].[usp720WebConsoleUpdateUserGroupAccess]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE Procedure [dbo].[usp720WebConsoleUpdateUserGroupAccess]
@UserGroupID int,	 
@ModuleNames dbo.udt706WebConsoleModules  READONLY,
@UnCheckedModuleNames dbo.udt707WebConsoleUnCheckedModules READONLY
AS
BEGIN

		SET NOCOUNT ON;

		--Module Enable disable 
		DROP TABLE IF EXISTS #tblModulesf

		CREATE TABLE #tblModules
		(
			Id INT IDENTITY(1, 1) PRIMARY KEY NOT NULL, 
			ModuleName VARCHAR(50)
			
		)
		INSERT INTO #tblModules (ModuleName)
		select [ModuleName] from @ModuleNames

		DROP TABLE IF EXISTS #tblUnCheckedModules

		CREATE TABLE #tblUnCheckedModules
		(
			Id INT IDENTITY(1, 1) PRIMARY KEY NOT NULL, 
			ModuleName VARCHAR(50)
			
		)

		INSERT INTO #tblUnCheckedModules (ModuleName)
		select [UnCheckedModuleNames] from @UnCheckedModuleNames

		DECLARE @Counter INT , @MaxId INT, @ModuleName NVARCHAR(100),@ModuleID int;
		DECLARE @Counter1 INT , @MaxId1 INT, @ModuleName1 NVARCHAR(100),@ModuleID1 int;
	

		SELECT @Counter = min(Id) , @MaxId = max(Id)
		FROM #tblModules;

		SELECT @Counter1 = min(Id) , @MaxId1 = max(Id)
		FROM #tblUnCheckedModules;



		
		--update tblUserRBAC set Status=0 where UserGroupID=@UserGroupID and Moduleid !=0;
			--Inactive 
		WHILE(@Counter1 IS NOT NULL AND @Counter1 <= @MaxId1)
		BEGIN
			SELECT @ModuleName1 = ModuleName
			
			FROM #tblUnCheckedModules WHERE Id = @Counter1
			Declare @Mid1 int;
			set @Mid1 =(select Module_ID from tblModules where MODULE_NAME=@ModuleName1)
			--PRINT CONVERT(VARCHAR,@Counter) + '. Cluster name is ' + @ClusterName
			if Exists (select top 1 * from tblUserRBAC where ModuleID=@Mid1 and UserGroupID =@UserGroupID)
			begin
				update tblUserRBAC set status= 0 where ModuleID=@Mid1 and UserGroupID =@UserGroupID;
			
			end
			else 
			begin
				insert into tblUserRBAC (UserGroupID,ModuleID,Status)
			        values (@UserGroupID,@Mid1,0)
			end
			
			 --Print @ClusterID
			SET @Counter1 = @Counter1 + 1
		END	
		--Active 
		WHILE(@Counter IS NOT NULL AND @Counter <= @MaxId)
		BEGIN
			SELECT @ModuleName = ModuleName
			
			FROM #tblModules WHERE Id = @Counter
		Declare  @Mid int;
			set @Mid =(select Module_ID from tblModules where MODULE_NAME=@ModuleName)
			--PRINT CONVERT(VARCHAR,@Counter) + '. Cluster name is ' + @ClusterName
			if Exists (select top 1 * from tblUserRBAC where ModuleID=@Mid and UserGroupID =@UserGroupID)
			begin
				update tblUserRBAC set status= 1 where ModuleID=@Mid and UserGroupID =@UserGroupID;
				Declare @ParentModuleID int;
				set @ParentModuleID= (select PARENT_MODULE_ID from tblModules where MODULE_ID=@Mid)
			
			if(@ParentModuleID >0)
			begin
				update tblUserRBAC set Status=1 where ModuleID=@ParentModuleID and UserGroupID=@UserGroupID;
			end
			end
			else 
			begin
				insert into tblUserRBAC (UserGroupID,ModuleID,Status)
			        values (@UserGroupID,@Mid,1)
			end
			
			 --Print @ClusterID
			SET @Counter = @Counter + 1
		END	
		
	
		
    
	
	
END
GO
/****** Object:  StoredProcedure [dbo].[usp721WebConsoleManageUsers]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE Procedure [dbo].[usp721WebConsoleManageUsers]
@Event varchar(50)
,@EventData udtSPParam readonly
as
Declare @EmployeeID varchar(100),@Username varchar(100),@FirstName varchar(100),@MiddleName varchar(100),@LastName varchar(100),@Department varchar(100),@JobTitle varchar(100),@Suffix varchar(100),
@UserGroupID varchar(100),@RFID varchar(100),@PIN varchar(100),@Title varchar(100),@Company varchar(100),
@Office varchar(100),@OfficeStreetAddress varchar(500),@OfficeCity varchar(100),@OfficeState varchar(100),
@OfficeZip varchar(100),@OfficeCountry varchar(100),@HomeStreetAddress varchar(100),@HomeCity varchar(100),
@HomeState varchar(100),@HomeZip varchar(100),@HomeCountry varchar(100),@HomePhoneNumber varchar(100),
@OfficePhoneNumber varchar(100),@MobilePhoneNumber varchar(100),@FaxNumber varchar(100),@PagerNumber varchar(100),@Email varchar(100),
@Password varchar(100),@Domain varchar(100),@EnteredBy varchar(100),@ComputerName varchar(100),@UserStatus int,@UserID int,@Room varchar(100)

set @Domain =(select [Value] from @EventData where [Property] = 'Domain')
set @EnteredBy =(select [Value] from @EventData where [Property] = 'EnteredBy')
set @ComputerName =(select [Value] from @EventData where [Property] = 'ComputerName')
set @Password =(select [Value] from @EventData where [Property] = 'Password')
set @Email =(select [Value] from @EventData where [Property] = 'Email')
set @EmployeeID =(select [Value] from @EventData where [Property] = 'EmployeeID')
set @Username =(select [Value] from @EventData where [Property] = 'Username')
set @FirstName =(select [Value] from @EventData where [Property] = 'FirstName ')
set @MiddleName =(select [Value] from @EventData where [Property] = 'MiddleName')
set @LastName =(select [Value] from @EventData where [Property] = 'LastName')
set @Department =(select [Value] from @EventData where [Property] = 'Department')
set @JobTitle =(select [Value] from @EventData where [Property] = 'JobTitle')
set @Suffix =(select [Value] from @EventData where [Property] = 'Suffix')
set @UserGroupID =(select [Value] from @EventData where [Property] = 'UserGroupID')
set @RFID =(select [Value] from @EventData where [Property] = 'RFID')
set @PIN =(select [Value] from @EventData where [Property] = 'PIN')
set @Title =(select [Value] from @EventData where [Property] = 'Title')
set @Company =(select [Value] from @EventData where [Property] = 'Company')
set @Office =(select [Value] from @EventData where [Property] = 'Office')
set @OfficeStreetAddress =(select [Value] from @EventData where [Property] = 'OfficeStreetAddress')
set @OfficeCity =(select [Value] from @EventData where [Property] = 'OfficeCity')
set @OfficeState =(select [Value] from @EventData where [Property] = 'OfficeState')
set @OfficeZip =(select [Value] from @EventData where [Property] = 'OfficeZip')
set @OfficeCountry =(select [Value] from @EventData where [Property] = 'OfficeCountry')
set @HomeStreetAddress =(select [Value] from @EventData where [Property] = 'HomeStreetAddress')
set @HomeCity =(select [Value] from @EventData where [Property] = 'HomeCity')
set @HomeState =(select [Value] from @EventData where [Property] = 'HomeState')
set @HomeZip =(select [Value] from @EventData where [Property] = 'HomeZip')
set @HomeCountry =(select [Value] from @EventData where [Property] = 'HomeCountry')
set @HomePhoneNumber =(select [Value] from @EventData where [Property] = 'HomePhoneNumber')
set @OfficePhoneNumber =(select [Value] from @EventData where [Property] = 'OfficePhoneNumber')
set @MobilePhoneNumber =(select [Value] from @EventData where [Property] = 'MobilePhoneNumber')
set @FaxNumber =(select [Value] from @EventData where [Property] = 'FaxNumber')
set @PagerNumber =(select [Value] from @EventData where [Property] = 'PagerNumber')
set @UserStatus =(select ID from tblReportStatus where Status=(select [Value] from @EventData where [Property] = 'UserStatus'))
set @UserID = (select [Value] from @EventData where [Property] = 'UserID')
set @Room= (select [Value] from @EventData where [Property] = 'Room')

if @Event ='GetUserdetails' 
Begin
	exec [dbo].[usp737WebConsoleGetUserDetails] @UserID

end

if @Event ='UpdateUserdetails' 
Begin
	exec [usp738WebConsoleUpdateUserDetails]
		@Domain,
		@EnteredBy,
		@ComputerName,
		@Password,
		@Email,
		@UserID,
		@EmployeeID ,
		@Username ,
		@FirstName ,
		@MiddleName,
		@LastName,
		@Department,
		@JobTitle,
		@Suffix,
		@UserGroupID,
		@RFID,
		@PIN,
		@Title,
		@Company,
		@Office,
		@OfficeStreetAddress,
		@OfficeCity,
		@OfficeState,
		@OfficeZip,
		@OfficeCountry,
		@HomeStreetAddress,
		@HomeCity,
		@HomeState,
		@HomeZip,
		@HomeCountry,
		@HomePhoneNumber,
		@OfficePhoneNumber,
		@MobilePhoneNumber,
		@FaxNumber,
		@PagerNumber,
		@UserStatus,
		@Room
		
		
end

if @Event ='AddNewUser' 
Begin
	exec [usp739WebConsoleAddNewUser]
		@Domain,
		@EnteredBy,
		@ComputerName,
		@Password,
		@Email,
		@EmployeeID ,
		@Username ,
		@FirstName ,
		@MiddleName,
		@LastName,
		@Department,
		@JobTitle,
		@Suffix,
		@UserGroupID,
		@RFID,
		@PIN,
		@Title,
		@Company,
		@Office,
		@OfficeStreetAddress,
		@OfficeCity,
		@OfficeState,
		@OfficeZip,
		@OfficeCountry,
		@HomeStreetAddress,
		@HomeCity,
		@HomeState,
		@HomeZip,
		@HomeCountry,
		@HomePhoneNumber,
		@OfficePhoneNumber,
		@MobilePhoneNumber,
		@FaxNumber,
		@PagerNumber,
		@UserStatus,
		@Room
end
GO
/****** Object:  StoredProcedure [dbo].[usp722WebConsoleGetStatesList]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE Procedure [dbo].[usp722WebConsoleGetStatesList]
AS
BEGIN
	select STATE_ID StateID,STATE_NAME StateName from TBL_STATES;
END

/****** Object:  StoredProcedure [dbo].[uspGetUserGroups]    Script Date: 05/17/2021 7.59.10 PM ******/
SET ANSI_NULLS ON
GO
/****** Object:  StoredProcedure [dbo].[usp723WebConsoleGetCountryList]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE Procedure [dbo].[usp723WebConsoleGetCountryList]
AS
BEGIN
	select Country_ID CountryID,Country_NAME CountryName from TBL_COUNTRIES;
END
GO
/****** Object:  StoredProcedure [dbo].[usp728WebConsoleGetStatistics]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE Procedure [dbo].[usp728WebConsoleGetStatistics]
AS
BEGIN


Declare @Days int
select @Days=[Setting Value] from s01tblSettingsCommon
where description like '%Default lead time for Lead Expiration Alerts%'
and [Setting Name] = 'ExpLeadTime'

Declare @Style1 nvarchar(255)='linear-gradient(45deg, #2c6b6e, #f20febf7)|fa fa-calendar-week';
Declare @Style2 nvarchar(255)='linear-gradient(45deg, #0000ff5e, #1035ea)|fa fa-calendar-minus';
Declare @Style3 nvarchar(255)='linear-gradient(45deg, #353b35c4, #1aff91f5)|fa fa-calendar-plus';
Declare @Style4 nvarchar(255)='linear-gradient(45deg, black, #2dcf11f5)|fas fa-calendar-check';
Declare @Style5 nvarchar(255)='linear-gradient(45deg, #6e6363, #ffdb02)|fas fa-exclamation-triangle';
Declare @Style6 nvarchar(255)='linear-gradient(45deg, #b29da6, #e9192d)|fas fa-trash-restore-alt';
Declare @Style7 nvarchar(255)='linear-gradient(45deg, #ef0e08d9, #c4ef0d)|fa fa-clock';
Declare @Style8 nvarchar(255)='linear-gradient(45deg, #403940d9, #7bbbff)|fa fa-user-plus';



SELECT '# Items Removed Against Patient' As [Name] ,'usp730WebConsoleRemovedItemsReport' as SPName,'Items Removed Against Patient' as 'LinkToReport', @Style1 as Style
	  ,Count(*) AS [Count]  from s01qryitemhistory I 
	  
		where itemstatusid=2 and MApatientID is not null and
		cast(lastActivitydate as date)=cast (getdate() as date)

SELECT '# Items Removed Without Patient selection' As [Name] ,'usp731WebConsoleUnknownPatientsRemovedItemsReport' as SPName,'Items Removed Without Patient selection' as 'LinkToReport', @Style2 as Style
	  ,Count(*) AS [Count]  from s01qryitemhistory I 
		where itemstatusid=2 and MApatientID is null and
		cast(lastActivitydate as date)=cast (getdate() as date)


SELECT '# Items Return' As [Name] ,'usp732WebConsoleReturnedItemsReport' as SPName,'Items Return' as 'LinkToReport', @Style3 as Style
	  ,Count(*) AS [Count]  from s01qryitemhistory I 
		where itemeventid=3 and cast(lastActivitydate as date)=cast (getdate() as date)


SELECT  '# Items In Cabinet' AS [Name],'usp733WebConsoleItemsInCabinetReport' as SPName,'Items In Cabinet' as 'LinkToReport', @Style4 as Style
		,format(COunt(*),'N0') AS [Count]
		from s01tblItems where ItemStatusID =1 








SELECT '# Items Near to Expire' AS [Name],'usp734WebConsoleItemsNearExpirationReport' as SPName,'Items Near to Expire' as 'LinkToReport', @Style5 as Style
	,Count(*) AS [Count]  from s01tblItems I 
	  WHERE I.[Expired Date] <= Dateadd(d,@Days,GETDATE()) 
	  and I.[Expired Date] >=GETDATE()
	  and I.ItemStatusID =1 


	  
SELECT 'Wasted Items' AS [Name],'usp735WebConsoleWastedItemsReport' as SPName,'Wasted Items' as 'LinkToReport', @Style6 as Style
	  ,Count(*) AS [Count]  from s01tblItems I     
	  WHERE I.[ItemStatusID]=5



SELECT 'Expired Items' AS [Name],'usp736WebConsoleExpiredItemsReport' as SPName,'Expired Items' as 'LinkToReport', @Style7 as Style
	, format(COunt(*),'N0') AS [Count]  from s01tblItems I 
	  WHERE I.[Expired Date] <= GETDATE()
	  AND I.ItemStatusID <= 1 



SELECT '# Patients Added' AS [Name],'usp737WebConsoleNewPatientsReport' as SPName,'Patients Added' as 'LinkToReport', @Style8 as Style
		,COunt(*) AS [Count]
		from s01tblpatients where CAST ([Entered Date] AS DATE)>= CAST (Dateadd(d,-30,GETDATE()) AS DATE)


END
GO
/****** Object:  StoredProcedure [dbo].[usp729WebConsoleUpdateUserDetails]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE Procedure [dbo].[usp729WebConsoleUpdateUserDetails]
@UserID int,
@EmployeeID varchar(100),
@Username varchar(100),
@FirstName varchar(100),
@MiddleName varchar(100),
@LastName varchar(100),
@Department varchar(100),
@JobTitle varchar(100),
@Suffix varchar(100),
--@UserGroupID varchar(100),
@RFID varchar(100),
--@PIN varchar(100),
@Title varchar(100),
@Company varchar(100),
@Office varchar(100),
@OfficeStreetAddress varchar(500),
@OfficeCity varchar(100),
@OfficeState varchar(100),
@OfficeZip varchar(100),
@OfficeCountry varchar(100),
@HomeStreetAddress varchar(100),
@HomeCity varchar(100),
@HomeState varchar(100),
@HomeZip varchar(100),
@HomeCountry varchar(100),
@HomePhoneNumber varchar(100),
@OfficePhoneNumber varchar(100),
@MobilePhoneNumber varchar(100),
@FaxNumber varchar(100),
@PagerNumber varchar(100),
@Email varchar(100),
@SessionID varchar(100),
@ComputerName varchar(100)
AS
BEGIN
  --   Declare @OldPin varchar(50),@OldBadge varchar(50),@Remarks varchar(max)='Self update';
     
	 --set @OldBadge=(select RFID from tblUsers where UserID=@UserID)
	

 
    update s01tblusers set
    [First Name]=@FirstName,
    [Last Name]=@LastName,
    Username =@Username,
    [Job Title] = @JobTitle,
    Department =@Department,
    [Entered Date] =GETDATE(),
    Suffix = @Suffix,
    [Middle Name] = @MiddleName,
    --GroupID=@UserGroupID,
    RFID=@RFID,
    --PIN=@PIN,
    Title=@Title,
    Company=@Company,
    Office=@Office,
    [Office Street]=@OfficeStreetAddress,
    [Office City]=@OfficeCity,
    [Office State]=@OfficeState,
    [Office ZIP]=@OfficeZip,
    [Office Country]=@OfficeCountry,
    [Home Street]=@HomeStreetAddress,
    [Home City]=@HomeCity,
    [Home State]=@HomeState,
    [Home ZIP]=@HomeZip,
    [Home Country]=@HomeCountry,
    [Home Phone No]=@HomePhoneNumber,
    [Office Phone No]=@OfficePhoneNumber,
    [Mobile Phone No]=@MobilePhoneNumber,
    [Fax No]=@FaxNumber,
    [Pager No]=@PagerNumber,
    Email = @Email,
    [Employee ID] =@EmployeeID
    where 
    UserID = @UserID
	
	
	--if( @OldBadge!=@RFID)
	--begin
	        
	--	insert into s01tblUsersBadgeHistory (UserID,[Updated UserID],[First Name],[Last Name],[User Name],[EntryTypeID],[Previous Badge],[New Badge],
	--		[Override Notes],[SessionID],[UpdatedBy],[Entered Date],[Computer Name],[Domain])
	--	values(@UserID,@UserID,@FirstName,@LastName,@Username,5,@OldBadge,@RFID,'Self Update',@SessionID,@Username,GETUTCDATE(),@ComputerName,'iRISecureBloodWeb')
	--end
END
GO
/****** Object:  StoredProcedure [dbo].[usp737WebConsoleGetUserDetails]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

create PROCEDURE [dbo].[usp737WebConsoleGetUserDetails]
@UserID int
AS
BEGIN

	select UserID, CONCAT([First Name],' ',[Last Name]) as Name,
    isnull([Employee ID],'') [Employee ID],
    RFID,
    PIN,
    [First Name],
    [Middle Name],
    [Last Name],
    Username,
    [Job Title] as Designation,
    Department,
    [Entered Date] DateAdded,
	Suffix,
	Title,
	Company,
    GroupID as [User Group],
    UserStatusID as Status,
	Office,
	[Office Street] OfficeStreetAddress,
	[Office City] OfficeCity,
	[Office State] OfficeState,
	[Office ZIP] OfficeZip,
	[Office Country] OfficeCountry,
	[Home Street] HomeStreetAddress,
	[Home City] HomeCity,
	[Home State] HomeState,
	[Home ZIP] HomeZip,
	[Home Country] HomeCountry,
	[Home Phone No] HomePhoneNumber,
	[Office Phone No] OfficePhoneNumber,
	[Mobile Phone No] MobilePhoneNumber,
    [Fax No] FaxNumber,
	[Pager No] PagerNumber,
	Email,
	Domain,
	--Room,
	
	Password,
	Password ConfirmPassword,
	GroupID
	--case when StatusType = 'Not Active' then 'Inactive'
	--else StatusType end [UserStatus]
    from s01tblUsers
    where 
	UserID =@UserID
END
GO
/****** Object:  StoredProcedure [dbo].[usp738WebConsoleUpdateUserDetails]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

Create PROCEDURE [dbo].[usp738WebConsoleUpdateUserDetails]
@Domain varchar(100),
@EnteredBy varchar(100),
@ComputerName varchar(100),
@Password varchar(100),
@Email varchar(100),
@UserID int,
@EmployeeID varchar(100),
@Username varchar(100),
@FirstName varchar(100),
@MiddleName varchar(100),
@LastName varchar(100),
@Department varchar(100),
@JobTitle varchar(100),
@Suffix varchar(100),
@UserGroupID varchar(100),
@RFID varchar(100),
@PIN varchar(100),
@Title varchar(100),
@Company varchar(100),
@Office varchar(100),
@OfficeStreetAddress varchar(500),
@OfficeCity varchar(100),
@OfficeState varchar(100),
@OfficeZip varchar(100),
@OfficeCountry varchar(100),
@HomeStreetAddress varchar(100),
@HomeCity varchar(100),
@HomeState varchar(100),
@HomeZip varchar(100),
@HomeCountry varchar(100),
@HomePhoneNumber varchar(100),
@OfficePhoneNumber varchar(100),
@MobilePhoneNumber varchar(100),
@FaxNumber varchar(100),
@PagerNumber varchar(100),
@UserStatus int,
@Room varchar(100)
AS
BEGIN

    if(@PIN='Null')
	begin
	update s01tblUsers set
	[Employee ID] =@EmployeeID,
	[First Name]=@FirstName,
	[Last Name]=@LastName,
	Username =@Username,
	[Job Title] = @JobTitle,
	Department =@Department,
	[Entered Date] =GETDATE(),
	Suffix = @Suffix,
	[Middle Name] = @MiddleName,
	GroupID=@UserGroupID,
	RFID=@RFID,
	--PIN=@PIN,
	Title=@Title,
	Company=@Company,
	Office=@Office,
	[Office Street]=@OfficeStreetAddress,
	[Office City]=@OfficeCity,
	[Office State]=@OfficeState,
	[Office ZIP]=@OfficeZip,
	[Office Country]=@OfficeCountry,
	[Home Street]=@HomeStreetAddress,
	[Home City]=@HomeCity,
	[Home State]=@HomeState,
	[Home ZIP]=@HomeZip,
	[Home Country]=@HomeCountry,
	[Home Phone No]=@HomePhoneNumber,
	[Office Phone No]=@OfficePhoneNumber,
	[Mobile Phone No]=@MobilePhoneNumber,
	[Fax No]=@FaxNumber,
	[Pager No]=@PagerNumber,
	Domain = @Domain,
	[Entered By] = @EnteredBy,
	[Computer Name] = @ComputerName,
	UserStatusID = @UserStatus,
	Email = @Email
	--,Room=@Room
	where UserID =@UserID;
	end
	else
	begin
	update s01tblUsers set
	[Employee ID] =@EmployeeID,
	[First Name]=@FirstName,
	[Last Name]=@LastName,
	Username =@Username,
	[Job Title] = @JobTitle,
	Department =@Department,
	[Entered Date] =GETDATE(),
	Suffix = @Suffix,
	[Middle Name] = @MiddleName,
	GroupID=@UserGroupID,
	RFID=@RFID,
	PIN=@PIN,
	Title=@Title,
	Company=@Company,
	Office=@Office,
	[Office Street]=@OfficeStreetAddress,
	[Office City]=@OfficeCity,
	[Office State]=@OfficeState,
	[Office ZIP]=@OfficeZip,
	[Office Country]=@OfficeCountry,
	[Home Street]=@HomeStreetAddress,
	[Home City]=@HomeCity,
	[Home State]=@HomeState,
	[Home ZIP]=@HomeZip,
	[Home Country]=@HomeCountry,
	[Home Phone No]=@HomePhoneNumber,
	[Office Phone No]=@OfficePhoneNumber,
	[Mobile Phone No]=@MobilePhoneNumber,
	[Fax No]=@FaxNumber,
	[Pager No]=@PagerNumber,
	Domain = @Domain,
	[Entered By] = @EnteredBy,
	[Computer Name] = @ComputerName,
	UserStatusID = @UserStatus,
	Email = @Email
	--,Room=@Room
	where UserID =@UserID;
	end
END
GO
/****** Object:  StoredProcedure [dbo].[usp739WebConsoleAddNewUser]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE PROCEDURE [dbo].[usp739WebConsoleAddNewUser]
@Domain varchar(100),
@EnteredBy varchar(100),
@ComputerName varchar(100),
@Password varchar(100),
@Email varchar(100),
@EmployeeID varchar(100),
@Username varchar(100),
@FirstName varchar(100),
@MiddleName varchar(100),
@LastName varchar(100),
@Department varchar(100),
@JobTitle varchar(100),
@Suffix varchar(100),
@UserGroupID varchar(100),
@RFID varchar(100),
@PIN varchar(100),
@Title varchar(100),
@Company varchar(100),
@Office varchar(100),
@OfficeStreetAddress varchar(500),
@OfficeCity varchar(100),
@OfficeState varchar(100),
@OfficeZip varchar(100),
@OfficeCountry varchar(100),
@HomeStreetAddress varchar(100),
@HomeCity varchar(100),
@HomeState varchar(100),
@HomeZip varchar(100),
@HomeCountry varchar(100),
@HomePhoneNumber varchar(100),
@OfficePhoneNumber varchar(100),
@MobilePhoneNumber varchar(100),
@FaxNumber varchar(100),
@PagerNumber varchar(100),
@UserStatus int,
@Room varchar(100)
AS
BEGIN
 
	insert into s01tblUsers 
	(
		[Employee ID],[First Name],[Last Name],Username ,[Job Title],Department,[Entered Date],Suffix ,[Middle Name],GroupID,
		RFID,PIN,Title,Company,Office,[Office Street],[Office City],[Office State],[Office ZIP],[Office Country],[Home Street],[Home City],
		[Home State],[Home ZIP],[Home Country],[Home Phone No],[Office Phone No],[Mobile Phone No],[Fax No],[Pager No],Email,Password,Domain
		,[Entered By],[Computer Name],UserStatusID--,Room
		,rowguid
	)
	values
	(
		@EmployeeID,@FirstName,@LastName,@Username,@JobTitle,@Department,GETDATE(),@Suffix,@MiddleName,@UserGroupID,
		@RFID,@PIN,@Title,@Company,@Office,@OfficeStreetAddress,@OfficeCity,@OfficeState,@OfficeZip,@OfficeCountry,@HomeStreetAddress,@HomeCity,
		@HomeState,@HomeZip,@HomeCountry,@HomePhoneNumber,@OfficePhoneNumber,@MobilePhoneNumber,@FaxNumber,@PagerNumber,@Email,@Password,@Domain
		,@EnteredBy,@ComputerName,@UserStatus--,@Room
		,newid()
	)
	select SCOPE_IDENTITY ()
END
GO
/****** Object:  StoredProcedure [dbo].[usp742WebConsoleProcessProcessRFIDItems]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE PROCEDURE [dbo].[usp742WebConsoleProcessProcessRFIDItems]
	@Event VARCHAR(200)
	, @udtSystemData udtSPParam READONLY
	, @udtEventData udtSPParam READONLY
	--, @udtTransactedItems udtTransactedItems READONLY
	--, @udtTagsData udtTags READONLY
AS
	SET NOCOUNT ON;

	DECLARE @UserID INT, @EntryTypeID INT, @SessionID BIGINT, @DeviceID INT, @BillingStatusID INT, @OverrideID INT, @OverrideNotesID INT, @OverrideNotes VARCHAR(255)
	DECLARE @ItemEventID INT, @UsageTypeID INT, @ItemStatusID INT, @MAPatientID VARCHAR(50), @MAScheduleID INT, @MAVisitID INT, @AlertMessage VARCHAR(512)
	DECLARE @CurrentUtcTime DATETIME, @OverrideDescription VARCHAR(50), @OverrideDescNotes VARCHAR(50), @UageSummarySettings VARCHAR(250), @AlertID INT, @DBID INT
	DECLARE @ClientTimeZone VARCHAR(500), @UTCStandard VARCHAR(100), @SecondDiff INT, @ShowUnAssociateItems VARCHAR(50), @PhysicianID VARCHAR(50)
	DECLARE @MAPhysicianID INT, @EnableEpicInterface VARCHAR(50), @SpuriousLogAlertEnabled BIT, @EXPDATE_CHECK VARCHAR(10), @CLOSEEXP_CHECK VARCHAR(10)
	DECLARE @MAX_RMV_PAT_ITEMS INT, @MAX_RMV_PAT_ITEMS_AlertEnabled BIT, @AntenaStatus VARCHAR(500), @AntennaFailure_AlertEnabled INT
	DECLARE @RFID VARCHAR(50),@ItemID INT, @CompartmentID INT,@Domain VARCHAR(100),@ItemUsageID INT
	DECLARE @udt701WebConsoleTransactedItemDetails udt701WebConsoleTransactedItemDetails 

		--SET XACT_ABORT ON;
		BEGIN TRY
		--BEGIN TRANSACTION

				
			
		SELECT @DeviceID = Value FROM @udtSystemData WHERE Property = 'DeviceID'
		
		SELECT @EntryTypeID = Value FROM @udtSystemData WHERE Property = 'EntryTypeID'
		
		SELECT @SessionID = Value FROM @udtEventData WHERE Property = 'SessionID'
		
		SELECT @UserID = Value FROM @udtEventData WHERE Property = 'UserID'
		SELECT @MAPatientID = Value FROM @udtEventData WHERE Property = 'MAPatientID'
		SELECT @OverrideDescription = Value FROM @udtEventData WHERE Property = 'OverrideDesc'
		SELECT @Domain = Value FROM @udtEventData WHERE Property = 'Domain'

		
		--SELECT @OverrideDescNotes = Value FROM @udtEventData WHERE Property = 'EventInput2'
		SELECT @RFID = Value FROM @udtEventData WHERE Property = 'RFID'
		SELECT @ItemID =(select ItemID from s01tblItems where RFID=@RFID)
		set @CompartmentID = (select CompartmentID from s01tblItems where ItemID=@ItemID)
			
		SELECT @UsageTypeID = UsageTypeID 
		FROM s01tblUsageType 
		WHERE UsageType = 'RF ITEM'

		--SET @EnableEpicInterface =  [dbo].[udf700WebConsoleSUPFetchSettings] (@DeviceId,'ENABLE_EPIC_INTERFACE') 
		
		SELECT @OverrideID = OverrideID 
		FROM s01tblOverride 
		WHERE OverrideDesc = CASE 
								WHEN @OverrideDescription = 'Waste Item' THEN 'Waste'   
								WHEN @OverrideDescription = 'Override' THEN 'Manual Override'
								WHEN @MAPatientID IS NOT NULL THEN 'Default'
								WHEN @Event = 'InitialScan' THEN 'Initial Scan'
								WHEN @Event = 'KeyAccessScan' THEN 'Key Access Scan'
								WHEN @OverrideDescription IN ('Take','Waste','Return') THEN 'Default' 
								ELSE @OverrideDescription 
							 END		
	
		SELECT @UageSummarySettings = [dbo].[udf700WebConsoleSUPFetchSettings] ( @DeviceId,'SHOW_USAGE_SUMMARY' ) 

		SELECT @ClientTimeZone = [dbo].[udf700WebConsoleSUPFetchSettings] ( @DeviceId,'CLIENT_TIMEZONE' )
		
		SELECT @UTCStandard = UTCStandard 
		FROM tblStandardTimeZone 
		WHERE StandardTimeZone = @ClientTimeZone

		

		SELECT @SecondDiff =  REPLACE(@UTCStandard, 'UTC', '')
		 
		IF (SELECT [Setting Value] FROM s01tblSettingsCommon WHERE [Setting Name] = 'ENABLE_UTC_TIMEZONE') = 'TRUE'
		BEGIN
			SELECT @CurrentUTCTime = GETUTCDATE()
		END
		ELSE
		BEGIN
			SELECT @CurrentUTCTime = GETDATE()
		END
						
		--IF ( SELECT COUNT(*) FROM @udtTransactedItems WHERE TagType = 'FoundTag' ) > 0
		--BEGIN
			
		if (@Event='ReturnItem')
		begin
	IF EXISTS(Select TOP 1 * from s01tblitems where  RFID=@RFID and ItemStatusID=1 ) 
            BEGIN

            Select 'Error' as datasetname,'Item already stocked in cabinet.'  as msg
            END
			else if exists( Select TOP 1 * from s01tblitems where  RFID=@RFID and ItemStatusID=2)
			begin
			Select 'Error' as datasetname,'Item already returnred in cabinet.'  as msg
			end
			else if not exists( Select TOP 1 * from s01tblitems where  RFID=@RFID)
			begin
			Select 'Error' as datasetname,'RFID not associated to any item.'  as msg
			end
			else
			begin
			SELECT @ItemStatusID = ItemStatusID 
			FROM s01tblItemStatus 
			WHERE [Item Status] = 'In Cabinet'

			SELECT @ItemEventID = CASE 
									  WHEN @Event = 'ReturnItem' 
										THEN 3
                                      ELSE 1
								  END

			--DELETE FROM s01tblItemUsage where UsageItemID=@ItemID; --UPDATED BY VIRESH ON 11/11/2024
			--WHERE UsageTypeID = 1 AND UsageItemID IN ( SELECT I.ItemID 
			--											FROM s01tblItems I INNER JOIN @udt701WebConsoleTransactedItemDetails TI ON TI.RFID = I.RFID 
			--											WHERE TagType = 'FoundTag' )
			DELETE FROM s01tblItemUsage
			WHERE UsageItemID = @ItemID
			AND LastActivityDate = (
				SELECT MAX(LastActivityDate)
				FROM s01tblItemUsage
				WHERE UsageItemID = @ItemID
				); -- Deleting Records from s01tblItemUsage, becouse we are returning Items from HUB ,
				    --means not actully returning , only removing patient records from ItemUsage.
					--not Updating ItemUsage Record, instead deleting old usage and Inserting new.

			SELECT @ItemStatusID = ItemStatusID 
			FROM s01tblItemStatus 
			WHERE [Item Status] = 'Removed'
			
			UPDATE s01tblItems 
			SET ItemStatusID = @ItemStatusID,  [AddUserID] = @UserID -- changed from ItemStatusID = @ItemStatusID to ItemStatusID = 2 by viresh on 11/11/2024
				, [Date Added] = @CurrentUtcTime, EntryTypeID = @EntryTypeID, SupplierID = 0, LastActivityDate = @CurrentUtcTime
				, MAPatientID = NULL, MAScheduleID = NULL, MAPhysicianID = NULL 
				, Qty = 1 
			FROM s01tblItems I where RFID=@RFID;
			--	INNER JOIN @udt701WebConsoleTransactedItemDetails TI ON TI.RFID = I.RFID
			--WHERE TagType = 'FoundTag'

			--UPDATED BY VIRESH ON 11/11/2024 
			--BEGIN
			INSERT INTO s01tblItemUsage (UsedBy,DateUsed,Qty,UnitPrice,BillingStatusID,OverrideID,OverrideNotes,UpdatedBy,LastActivityDate,UsageTypeID,UsageItemID,SessionID,UCDM,EntryTypeID,rowguid)
			SELECT @UserID,@CurrentUtcTime,ISNULL(I.Qty,1),I.[Act Price],@BillingStatusID,@OverrideID, @OverrideNotes,@UserID,@CurrentUtcTime,@UsageTypeID,I.ItemID,@SessionID,I.ICDM,@EntryTypeID,NEWID()
			FROM s01qryItems I where RFID=@RFID;

			--END
			SELECT TOP 1 @ItemUsageID = ItemUsageID
			FROM s01tblItemUsage
			ORDER BY LastActivityDate DESC;
			--Performing Return and Remove Work folow , when item is removed from a HUB Application 
			--Updated By viresh on 11/18/2024
			INSERT INTO s01tblItemHistory ( ItemUsageID,ItemStatusID, UsageTypeID, UsageItemID, MAPatientID, MAScheduleID, MAVisitID, BillingStatusID, Qty, UnitPrice --UOMCode
							,  UpdatedBy,UsedBy, UCDM, LastActivityDate, EntryTypeID, OverrideID, OverrideNotes, SessionID, ItemEventID )
			SELECT @ItemUsageID,1, @UsageTypeID, I.ItemID, I.MAPatientID INT, I.MAScheduleID, I.MAVisitID, @BillingStatusID, ISNULL(I.Qty,1), I.[Act Price]-- TI.UOMCode
							, @UserID,@UserID, I.ICDM, @CurrentUtcTime, @EntryTypeID, @OverrideID, @OverrideNotes, @SessionID, @ItemEventID 
			FROM s01qryItems I where RFID=@RFID;

			INSERT INTO s01tblItemHistory ( ItemUsageID,ItemStatusID, UsageTypeID, UsageItemID, MAPatientID, MAScheduleID, MAVisitID, BillingStatusID, Qty, UnitPrice --UOMCode
							,  UpdatedBy,UsedBy, UCDM, LastActivityDate, EntryTypeID, OverrideID, OverrideNotes, SessionID, ItemEventID )
			SELECT @ItemUsageID,2, @UsageTypeID, I.ItemID, I.MAPatientID INT, I.MAScheduleID, I.MAVisitID, @BillingStatusID, ISNULL(I.Qty,1), I.[Act Price]-- TI.UOMCode
							, @UserID,@UserID, I.ICDM, @CurrentUtcTime, @EntryTypeID, @OverrideID, @OverrideNotes, @SessionID, @ItemEventID 
			FROM s01qryItems I where RFID=@RFID;
			--INNER JOIN @udt701WebConsoleTransactedItemDetails TI ON TI.RFID = I.RFID AND TagType = 'FoundTag'

			INSERT INTO s01tblitemhistoryextn (ItemHistoryID, LocationID)
			SELECT ItemHistoryID, @CompartmentID 
			--FROM @udt701WebConsoleTransactedItemDetails T
			from s01tblItemHistory H  --H.UsageItemID = T.ItemID 
			where H.LastActivityDate = @CurrentUtcTime
			AND H.UsageTypeID=1
			--INSERT INTO s01tblitemhistoryextn (ItemHistoryID, LocationID)
			--SELECT ItemHistoryID, T.LocationID 
			--FROM @udt701WebConsoleTransactedItemDetails T
			--	INNER JOIN [IrisDB].s01tblItemHistory H ON H.UsageItemID = T.ItemID 
			--	AND H.LastActivityDate = @CurrentUtcTime 
			--	AND H.UsageTypeID=1
			--	AND T.TagType = 'FoundTag'
				--EXECUTE  usp812WebConsoleLoadUsageList @Event = @Event
				--	, @udtSystemData = @udtSystemData
				--	, @udtEventData = @udtEventData
				select [Serial No],[Lot No],P.[Cat No],p.[Description 1],I.RFID from s01tblItems I
				inner join s01tblProducts P on P.ProductID=I.ProductID
				where I.RFID=@RFID
			
			--IF(@EnableEpicInterface = 'TRUE') added by viresh on 11/11/2024
			IF (1=1) --setting value
			BEGIN
				IF ( @RFID IS NOT NULL AND @RFID <> '' ) 
				BEGIN
					--Epic Transaction
					EXECUTE  uspSWCSendUsageToEpic @Event = @Event
							, @udtSystemData = @udtSystemData
							, @udtEventData = @udtEventData
							, @ItemEventID = @ItemEventID
							, @ItemStatusID = @ItemStatusID
							, @UsageTypeID = @UsageTypeID
							, @ItemID = @ItemID
				END				
			END
		end
		end
		else
		begin
			
		SELECT @MAVisitID = Value FROM @udtEventData WHERE Property = 'MAVisitID' 
		SELECT @MAScheduleID = Value FROM @udtEventData WHERE Property = 'MAScheduleID' 
		SELECT @PhysicianID = PhysicianID FROM s01qryPatientDisplayADT WHERE MAPatientID = @MAPatientID 
		SELECT @MAPhysicianID = MAPhysicianID FROM s01tblPhysicians WHERE PhysicianID = @PhysicianID

			SELECT @ItemStatusID = ItemStatusID 
			FROM s01tblItemStatus 
			WHERE [Item Status] = CASE 
									WHEN @OverrideDescription IN ( 'Waste Item','Waste') THEN 'Waste' 
									WHEN @OverrideDescription = 'Transfer Item' THEN 'Transfered'
									WHEN @OverrideDescription = 'Swap Item' THEN 'Swap'
									WHEN @OverrideDescription = 'Render To External Hospital' THEN 'Render to External Hospital'   
									WHEN @OverrideDescription = 'Remove Item' THEN 'Used'
									ELSE 'Removed' 
									END

			SELECT @ItemEventID = ItemEventID 
			FROM s01tblItemEvent 
			WHERE EventDescription = CASE 
											WHEN @OverrideDescription IN ('Waste Item','Waste') THEN 'Item has been wasted' 
											WHEN @OverrideDescription = 'Transfer Item' THEN 'Item has been transfered'
											ELSE 'Item taken out of the cabinet' 
										END
			
			--IF EXISTS(Select TOP 1 * from tblitems where  RFID=@RFID and ItemStatusID=2 )  viresh commented these code
			IF EXISTS(Select TOP 1 * from s01tblitems where  RFID=@RFID) 
            BEGIN
				EXECUTE usp823WebConsoleProcessMaxRmvPatItems @Event = @Event
						, @udtSystemData = @udtSystemData
						, @udtEventData = @udtEventData
						--, @udt701WebConsoleTransactedItemDetails = @udt701WebConsoleTransactedItemDetails
						, @ItemStatusID = @ItemStatusID
						, @ItemEventID = @ItemEventID
						, @CurrentUtcTime = @CurrentUtcTime
						, @UsageTypeID = @UsageTypeID
					
							EXECUTE  usp812WebConsoleLoadUsageList @Event = @Event
							, @udtSystemData = @udtSystemData
							, @udtEventData = @udtEventData
            END
			else if not exists( Select TOP 1 * from s01tblitems where  RFID=@RFID)
			begin
			Select 'Error' as datasetname,'RFID not associated to any item.'  as msg
			end
			else 
            


            begin


				EXECUTE usp823WebConsoleProcessMaxRmvPatItems @Event = @Event
						, @udtSystemData = @udtSystemData
						, @udtEventData = @udtEventData
						--, @udt701WebConsoleTransactedItemDetails = @udt701WebConsoleTransactedItemDetails
						, @ItemStatusID = @ItemStatusID
						, @ItemEventID = @ItemEventID
						, @CurrentUtcTime = @CurrentUtcTime
						, @UsageTypeID = @UsageTypeID
					
							EXECUTE  usp812WebConsoleLoadUsageList @Event = @Event
							, @udtSystemData = @udtSystemData
							, @udtEventData = @udtEventData
			end
			
			
			end
	END TRY
		BEGIN CATCH
			--IF (XACT_STATE()) <> 0
				--ROLLBACK TRANSACTION;
			EXECUTE s01uspLogError;
		END CATCH;
	SET NOCOUNT OFF




GO
/****** Object:  StoredProcedure [dbo].[usp750WebConsoleGetUserFavorites]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE Procedure [dbo].[usp750WebConsoleGetUserFavorites]
@UserID int
as
begin
     select Bookmark as BookMark, BookmarkURL as Link from tbluserbookmarks where UserID=@UserID;
end
GO
/****** Object:  StoredProcedure [dbo].[usp751WebConsoleGetSessionTimeOut]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE Procedure [dbo].[usp751WebConsoleGetSessionTimeOut]
@UserGroupId int 
as
begin
    if(@UserGroupId in (0,1))
	
	select [Setting Value] from s01tblSettingsCommon where [Setting Name]='TIME_OUT_ADM';

	else 
	select [Setting Value] from s01tblSettingsCommon where [Setting Name]='TIME_OUT';
end
GO
/****** Object:  StoredProcedure [dbo].[usp752WebConsoleUpdateFavorites]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE Procedure [dbo].[usp752WebConsoleUpdateFavorites] 
@UserID int,
@Bookmark varchar(50),
@Status varchar(50)
as 
begin
declare @BookmarkLink varchar(50);
   if exists (select Module_Name from tblModules where Module_name=@Bookmark)
   begin
   set @BookmarkLink=(select Link from tblmodules where Module_name=@Bookmark)
       if exists(select Bookmark from tbluserbookmarks where UserID=@UserID and Bookmark=@Bookmark)
	   begin
	         if(@Status='1')
			 begin
				 delete from tbluserbookmarks where UserID=@UserID and Bookmark=@Bookmark;
				 select 'Bookmark removed'
			 end
			 else
			 select'Failed to add bookmark'
	   end
	   else
	   begin
			if(@Status='0')
			 begin
				 insert into tbluserbookmarks values(@UserID,@Bookmark,@BookmarkLink,GETUTCDATE());
				 select'Bookmark added'
			 end
			 else 
			 begin
			     select 'Failed to remove bookmark'
			 end
	   end
   end
   else
      select'Failed to add bookmark'
end
GO
/****** Object:  StoredProcedure [dbo].[usp753WebConsoleGetUnpairedDevices]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

create procedure [dbo].[usp753WebConsoleGetUnpairedDevices]
as
begin
select count(*) from tblDeviceRegistration where Status='Inactive' or status is null;
end
GO
/****** Object:  StoredProcedure [dbo].[usp754WebConsoleGetmenuitems]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [dbo].[usp754WebConsoleGetmenuitems]
@UserGroupID int,
@UserID int
AS
BEGIN

IF (@UserGroupID <> 0) 
Begin
	select MODULE_ID,MODULE_NAME,DESCRIPTION,LINK,PARENT_MODULE_ID,MODULE_ICON
	,CASE WHEN bm.Bookmark IS NULL or bm.bookmark='' THEN 0 ELSE 1 END AS bookmarkstatus
	from 
	tblModules tMod
	left join tblUserRBAC u on tmod.MODULE_ID = u.ModuleID
	left join tbluserbookmarks bm on tMod.MODULE_NAME = bm.Bookmark and bm.UserID=@UserID
	where IsENABLED=1 and u.UserGroupID = @UserGroupID and u.Status = 1
	order by PARENT_MODULE_ID, ORDER_ID
	
END
Else
Begin
	select MODULE_ID,MODULE_NAME,DESCRIPTION,LINK,PARENT_MODULE_ID,MODULE_ICON
	,CASE WHEN bm.Bookmark IS NULL or bm.bookmark='' THEN 0 ELSE 1 END AS bookmarkstatus
	from 
	tblModules tMod
	left join tbluserbookmarks bm on tMod.MODULE_NAME = bm.Bookmark and bm.UserID=@UserID
	where IsENABLED=1
	order by PARENT_MODULE_ID, ORDER_ID
end
END
GO
/****** Object:  StoredProcedure [dbo].[usp755WebConsoleGetUserRecentVisitedHistory]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE procedure [dbo].[usp755WebConsoleGetUserRecentVisitedHistory]
@UserID int
as
begin
     select distinct top(3) ModuleName,ModuleLink,LastActivityDate 
	 from tblusersrecenthistory
	 where UserID=@UserID
	 Group by ModuleName,ModuleLink,LastActivityDate
	  order by LastActivityDate desc    
end
GO
/****** Object:  StoredProcedure [dbo].[usp800WebConsoleUserLogin]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [dbo].[usp800WebConsoleUserLogin]
    @Event VARCHAR(255)
    , @udtSystemData AS udtSPParam READONLY
    , @udtEventData AS udtSPParam READONLY

AS 
    --SET NOCOUNT ON;
        DECLARE @User VARCHAR(50), @Pwd VARCHAR(50), @UserID INT, @UserName VARCHAR(100), @SessionID BIGINT, @SPName VARCHAR(100), @UserStatus INT, @Msg VARCHAR(255)
        DECLARE @EventType VARCHAR(50), @IsCardAssociated BIT, @EntryTypeID INT, @DeviceID INT, @ComputerName VARCHAR(100), @ClusterName VARCHAR(100), @DataSetName VARCHAR(50)
        DECLARE @PIN VARCHAR(50), @RFID VARCHAR(100), @Password VARCHAR(500), @ClusterID INT, @Domain VARCHAR(50), @GroupID INT
        DECLARE @CurrentUtcTime DATETIME, @DataSetName1 VARCHAR(50), @Golivedate DATETIME, @CheckCompartmentID INT

        --SET XACT_ABORT ON;
        BEGIN --TRY
        --BEGIN TRANSACTION

        SELECT @DeviceID = Value FROM @udtSystemData WHERE Property = 'DeviceID'
        SELECT @EntryTypeID = Value FROM @udtSystemData WHERE Property = 'EntryTypeID'
        SELECT @ComputerName = Value FROM @udtSystemData WHERE Property = 'ComputerName'
        SELECT @Domain = Value FROM @udtSystemData WHERE Property = 'Domain'
        SELECT @EventType = Value FROM @udtEventData WHERE Property = 'EventType'
        --SELECT @DataSetName = Value FROM @udtEventData WHERE Property = 'EventOutPut'
        --SELECT @DataSetName1 = Value FROM @udtEventData WHERE Property = 'EventOutPut1'
        SELECT @User = Value FROM @udtEventData WHERE Property = 'User'
        SELECT @Pwd = Value FROM @udtEventData WHERE Property = 'Pwd'


        IF (SELECT [Setting Value] FROM s01tblSettingsCommon WHERE [Setting Name] = 'ENABLE_UTC_TIMEZONE') = 'TRUE'
        BEGIN
            SELECT @CurrentUTCTime = GETUTCDATE()
            SELECT @SessionID = REPLACE( REPLACE( REPLACE( REPLACE( 
                                CONVERT( VARCHAR(30), @CurrentUTCTime, 121 ), ':', '' ), '-', '' ), ' ', '' ) , '.', '' )
        END
        ELSE
        BEGIN
            SELECT @CurrentUTCTime = GETDATE()
            SELECT @SessionID = REPLACE( REPLACE( REPLACE( REPLACE( 
                                CONVERT( VARCHAR(30), @CurrentUTCTime, 121 ), ':', '' ), '-', '' ), ' ', '' ) , '.', '' )
        END
        
        --SELECT @Golivedate = MA_SUP_SITENAME_IRISUPPLYDB..[udfSUPFetchSettings] ( @DeviceId,'GoLiveDate' ) -- VIRESH

        --IF( @EventType = 'PIN' AND @Pwd IS NOT NULL AND ISNULL( @DeviceID, 0 ) <> 0 )
        --BEGIN
        --  --Checking for Authorization
        --  IF EXISTS (SELECT 1 FROM s01qrySUPUsersValidate WHERE PIN = @Pwd AND ClusterID = @DeviceID)
        --  BEGIN
        --      SELECT TOP 1 @DataSetName AS DataSetName, UserID, CONCAT([First Name], ' ', [Last Name]) AS [User Name], [User Group], 1 AS CabinetAccess, @SessionID AS SessionID 
        --      FROM s01qrySUPUsersValidate 
        --      WHERE PIN = @Pwd 
        --          AND ClusterID = @DeviceID

        --      --EXECUTE uspUserAppAccessRBAC @Event = @Event 
        --      --      , @udtSystemData = @udtSystemData
        --      --      , @udtEventData = @udtEventData

        --      --EXECUTE  uspLoadExpireItems @Event = @Event
        --      --  , @udtSystemData = @udtSystemData
        --      --  , @udtEventData = @udtEventData
        --  END
        --  --SR added condition for Console application having single comp id = Null (Login for Authenticated user)
        --  ELSE IF EXISTS (SELECT 1 FROM dbo.s01tblUsers WHERE PIN = @Pwd AND UserStatusID = 1 AND @CheckCompartmentID IS NULL )
        --  BEGIN
        --       SELECT TOP 1 @DataSetName AS DataSetName, UserID, CONCAT([First Name], ' ', [Last Name]) AS [User Name], G.[User Group], 1 AS CabinetAccess, @SessionID AS SessionID 
        --       FROM dbo.s01tblUsers U 
        --          LEFT OUTER JOIN dbo.s01tblUserGroup G ON U.GroupID = G.GroupID
        --       WHERE PIN = @Pwd AND UserStatusID = 1 

        --      EXECUTE uspUserAppAccessRBAC @Event = @Event 
        --              , @udtSystemData = @udtSystemData
        --              , @udtEventData = @udtEventData

        --      EXECUTE  uspLoadExpireItems @Event = @Event
        --          , @udtSystemData = @udtSystemData
        --          , @udtEventData = @udtEventData
        --  END
        --  ELSE
        --      SELECT 'Alert' AS DatasetName, 'Invalid PIN' AS MSG

        --END

        IF( @EventType = 'HID' AND @Pwd IS NOT NULL --AND ISNULL( @DeviceID, 0 ) <> 0 
		)
        BEGIN
            IF EXISTS (SELECT 1 FROM qrySUPUsersValidate WHERE RFID = @Pwd AND ClusterID = @DeviceID)
            BEGIN
                SELECT TOP 1  UserID, CONCAT([First Name], ' ', [Last Name]) AS [Name],Username,[Employee ID],Department, GroupID as UserGroupID, 1 AS Status,
                @SessionID AS SessionID 
                FROM qry700WebConsoleSUPUsersValidate 
                WHERE RFID = @Pwd 
                   -- AND ClusterID = @DeviceID

                --EXECUTE uspUserAppAccessRBAC @Event = @Event 
                --  , @udtSystemData = @udtSystemData
                --  , @udtEventData = @udtEventData

                --EXECUTE  uspLoadExpireItems @Event = @Event
                --  , @udtSystemData = @udtSystemData
                --  , @udtEventData = @udtEventData
                  
            END
            --SR added condition for Console application having single comp id = Null (Login for Authenticated user)
            ELSE IF EXISTS (SELECT 1 FROM s01tblUsers WHERE RFID = @Pwd AND UserStatusID = 1 AND @CheckCompartmentID IS NULL )
            BEGIN
                 SELECT TOP 1 @DataSetName AS DataSetName, UserID, CONCAT([First Name], ' ', [Last Name]) AS [Name],Username,[Employee ID],Department, U.[GroupID] as UserGroupID, 1 AS Status,
                @SessionID AS SessionID
                 FROM s01tblUsers U 
                    LEFT OUTER JOIN s01tblUserGroup G ON U.GroupID = G.GroupID
                 WHERE RFID = @Pwd AND UserStatusID = 1 

                --EXECUTE uspUserAppAccessRBAC @Event = @Event 
                --        , @udtSystemData = @udtSystemData
                --        , @udtEventData = @udtEventData

                --EXECUTE  uspLoadExpireItems @Event = @Event
                --    , @udtSystemData = @udtSystemData
                --    , @udtEventData = @udtEventData
            END
            ELSE IF EXISTS ( SELECT 1 FROM s01qryUsers U INNER JOIN s01tblCompartmentAccess C ON U.UserID = C.UserID  
                        WHERE RFID =  @Pwd AND ClusterID = @DeviceID AND UserStatusID = 0 AND [Entered By] = 'Self Registered' AND getdate() < @Golivedate)

                SELECT  'AlertForGoLive' AS DatasetName, 'Login is not allowed till Go Live Date for self registered users' AS MSG
                    
            ELSE
                SELECT 'Alert' AS DatasetName, 'Invalid Badge' AS MSG
        END

        IF( @EventType = 'User' AND @User IS NOT NULL AND @Pwd IS NOT NULL --AND ISNULL( @DeviceID, 0 ) <> 0 
		)
        BEGIN
            SELECT TOP 1  UserID, CONCAT([First Name], ' ', [Last Name]) AS [Name],Username,[Employee ID],Department, GroupID as UserGroupID, 1 AS Status
                ,@SessionID AS SessionID
				--,1 AS [TwoFactorAuthStatus]
            FROM qry700WebConsoleSUPUsersValidate 
            WHERE [UserName] = @User 
                AND Password = @Pwd 
               -- AND ClusterID = @DeviceID

            --EXECUTE uspUserAppAccessRBAC @Event = @Event 
            --      , @udtSystemData = @udtSystemData
            --      , @udtEventData = @udtEventData

            --EXECUTE  uspLoadExpireItems @Event = @Event
            --      , @udtSystemData = @udtSystemData
            --      , @udtEventData = @udtEventData
        END
    
        IF ( ISNULL( @DeviceID, 0 ) = 0 )
        BEGIN
            IF ( @EventType = 'PIN' AND @Pwd IS NOT NULL )
            BEGIN
                SELECT @UserID = UserID, @PIN = PIN, @UserName = [User Name], @RFID = PIN 
                FROM s01qryUsers 
                WHERE PIN = @Pwd 
                    AND UserStatusID = 1 
                    AND GroupID IN ( 0, 1 )

                IF EXISTS ( SELECT 1 FROM s01qryUsers WHERE PIN = @Pwd AND UserStatusID = 1 AND GroupID IN ( 0, 1 ) )
                BEGIN
                        
                    --EXECUTE  uspAppCabEventLogs @Event = @Event
                    --              , @udtSystemData = @udtSystemData
                    --              , @udtEventData = @udtEventData
                    --              , @Logs =  'UtilityApp - Using PIN - Login Successfull'

                    SELECT TOP 1 @DataSetName AS DataSetName, UserID, CONCAT([First Name], ' ', [Last Name]) AS [User Name], [User Group], 1 AS CabinetAccess, @SessionID AS SessionID 
                    FROM s01qryUsers 
                    WHERE PIN = @Pwd 
                        AND UserStatusID = 1 
                        AND GroupID IN ( 0, 1 ) 
                END
                --ELSE IF NOT EXISTS ( SELECT 1 FROM dbo.s01qryUsers WHERE PIN = @Pwd AND UserStatusID = 1 AND GroupID IN ( 0, 1 ) )
                --BEGIN

                --  --EXECUTE  uspAppCabEventLogs @Event = @Event
                --  --              , @udtSystemData = @udtSystemData
                --  --              , @udtEventData = @udtEventData
                --  --              , @Logs =  'UtilityApp - Using PIN - Invalid PIN'

                --END
            END
        END 

        IF ( @EventType = 'User' AND  @User IS NOT NULL AND @Pwd IS NOT NULL AND ISNULL( @DeviceID, 0 ) <> 0)
        BEGIN
            SET @UserID = NULL

            SELECT @UserID = UserID, @PIN = PIN, @RFID = RFID , @UserName = [User Name], @Password = Password--, @ClusterID = [ClusterID] 
            FROM qry700WebConsoleSUPUsersValidate  
            WHERE [UserName] = @User 
                AND Password = @Pwd 
                --AND ClusterID = @DeviceID

            SELECT  @ClusterName = [Cluster Name] 
            FROM  s01qryCabinets 
            WHERE ClusterID = @DeviceID

            --IF EXISTS ( SELECT 1 FROM s01qrySUPUsersValidate WHERE [User Name] = @UserName AND Password = @Pwd ) 
          --  BEGIN

          --      EXECUTE  uspAppCabEventLogs @Event = @Event
          --                      , @udtSystemData = @udtSystemData
          --                      , @udtEventData = @udtEventData
          --                      , @Logs =  'Using Name - Login Successfull'
								  --, @UserSessionID =  @SessionID

          --  END 

            IF ( (@UserName IS NULL OR @UserName = '') AND ( @Password IS NULL OR @PassWord = '') AND ISNULL( @DeviceID, 0 ) <> 0 )
            BEGIN

                EXECUTE  usp705WebConsoleAppCabEventLogs @Event = @Event
                            , @udtSystemData = @udtSystemData
                            , @udtEventData = @udtEventData
                            , @Logs =  'Using Name - Invalid User/Password'

            END
                    
        END

        IF ( @EventType = 'HID' AND ISNULL( @DeviceID, 0 ) <> 0 )
        BEGIN
            SELECT @UserID = UserID, @RFID = RFID , @UserName = [User Name], @Password = Password--, @ClusterID = [ClusterID] 
            FROM qry700WebConsoleSUPUsersValidate  
            WHERE RFID = @Pwd 
               -- AND ClusterID = @DeviceID

            SELECT  @ClusterName = [Cluster Name] 
            FROM  s01qryCabinets 
            WHERE ClusterID = @DeviceID

            IF EXISTS ( SELECT 1 FROM qry700WebConsoleSUPUsersValidate WHERE RFID = @Pwd )
            BEGIN

                EXECUTE  usp704WebConsoleProcessUserLogs @Event = @Event
                                    , @udtSystemData = @udtSystemData
                                    , @udtEventData = @udtEventData
                                    , @UserSessionID =  @SessionID

        --        EXECUTE  uspAppCabEventLogs @Event = @Event
        --                    , @udtSystemData = @udtSystemData
        --                    , @udtEventData = @udtEventData
        --                    , @Logs =  'Using HID - Login Successfull'
							 --, @UserSessionID =  @SessionID

            END 

            IF NOT EXISTS ( SELECT 1 FROM qry700WebConsoleSUPUsersValidate WHERE RFID = @Pwd )
            BEGIN

                EXECUTE  usp705WebConsoleAppCabEventLogs @Event = @Event
                        , @udtSystemData = @udtSystemData
                        , @udtEventData = @udtEventData
                        , @Logs =  'Using HID - Invalid HID Card'

            END                 
        END

		 IF( @EventType = 'AD' AND @User IS NOT NULL )
        BEGIN
            SELECT TOP 1  UserID, CONCAT([First Name], ' ', [Last Name]) AS [Name],Username,[Employee ID],Department, GroupID as UserGroupID, 1 AS Status,--2FactorAuthStatus,
                @SessionID AS SessionID
           FROM s01tblUsers     
            WHERE [UserName] = @User 
			--SELECT TOP 1  UserID, CONCAT([First Name], ' ', [Last Name]) AS [Name],Username,[Employee ID],Department, GroupID as UserGroupID, 1 AS Status,--'1' as [TwoFactorAuthStatus],
   --             @SessionID AS SessionID
   --        FROM qry700WebConsoleSUPUsersValidate         
            --WHERE [UserName] = @User 
                --AND Password = @Pwd 
               -- AND ClusterID = @DeviceID

            --EXECUTE uspUserAppAccessRBAC @Event = @Event 
            --      , @udtSystemData = @udtSystemData
            --      , @udtEventData = @udtEventData

            --EXECUTE  uspLoadExpireItems @Event = @Event
            --      , @udtSystemData = @udtSystemData
            --      , @udtEventData = @udtEventData
        END

        --COMMIT TRANSACTION;

        END--TRY
        --BEGIN CATCH
        --    --IF (XACT_STATE()) <> 0
        --        --ROLLBACK TRANSACTION;
        --        SET @Msg = 'ERROR VALIDATING LOGIN';
        --    --EXECUTE uspLogError;
        --END CATCH;
    --SET NOCOUNT OFF
GO
/****** Object:  StoredProcedure [dbo].[usp801WebConsoleUserLogout]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE Procedure [dbo].[usp801WebConsoleUserLogout]
	@Event VARCHAR(255)
	, @udtSystemData udtSPParam READONLY
	, @udtEventData udtSPParam READONLY
AS
	SET NOCOUNT ON;
		DECLARE @UserID INT,@LoginSessionID BIGINT, @SessionID BIGINT
		DECLARE @Msg VARCHAR(500)
		DECLARE @EventType VARCHAR(50), @DeviceID INT
		DECLARE @CurrentUtcTime DATETIME,  @Message VARCHAR(50)
		--Edted by subramanya to handle type mismatch exception
		--SET XACT_ABORT ON;
		BEGIN TRY
		--BEGIN TRANSACTION
		SELECT @DeviceID = Value FROM @udtSystemData WHERE Property = 'DeviceID'
		SELECT @UserID = Value FROM @udtEventData WHERE Property = 'UserID'
		SELECT @SessionID = Value FROM @udtEventData WHERE Property = 'SessionID'
		SELECT @EventType = Value FROM @udtEventData WHERE Property = 'EventType'
		SELECT @Message = VALUE FROM @udtEventData WHERE Property = 'Message'
		IF (SELECT [Setting Value] FROM s01tblSettingsCommon WHERE [Setting Name] = 'ENABLE_UTC_TIMEZONE') = 'TRUE'
		BEGIN
			SELECT @CurrentUTCTime = GETUTCDATE()
		END
		ELSE
		BEGIN
			SELECT @CurrentUTCTime = GETDATE()
		END
				IF @EventType = 'SessionTimedout'
				BEGIN	 
					EXECUTE  usp704WebConsoleProcessUserLogs @Event = @Event
						, @udtSystemData = @udtSystemData
						, @udtEventData = @udtEventData
						, @UserSessionID =  @SessionID
					EXECUTE  usp705WebConsoleAppCabEventLogs @Event = @Event
						, @udtSystemData = @udtSystemData
						, @udtEventData = @udtEventData
						   , @Logs =  'Logout Successfull'
						, @UserSessionID =  @SessionID
				END
				IF @EventType = 'UserLogOut'
				BEGIN	
					EXECUTE  usp704WebConsoleProcessUserLogs @Event = @Event
						, @udtSystemData = @udtSystemData
						, @udtEventData = @udtEventData
						, @UserSessionID =  @SessionID
					--EXECUTE  usp705WebConsoleAppCabEventLogs @Event = @Event
					--	, @udtSystemData = @udtSystemData
					--	, @udtEventData = @udtEventData
					--	   , @Logs =  'Logout Successfull'
					--	, @UserSessionID =  @SessionID
				END
				IF @EventType = 'ExitAutoLogin'
				BEGIN	
					EXECUTE  usp704WebConsoleProcessUserLogs @Event = @Event
						, @udtSystemData = @udtSystemData
						, @udtEventData = @udtEventData
						, @UserSessionID =  @SessionID
					EXECUTE  usp705WebConsoleAppCabEventLogs @Event = @Event
						, @udtSystemData = @udtSystemData
						, @udtEventData = @udtEventData
						   , @Logs =  'Logout Successfull'
						, @UserSessionID =  @SessionID
				END
				
		END TRY
		BEGIN CATCH
			--IF (XACT_STATE()) <> 0
				--ROLLBACK TRANSACTION;
				SET @Msg = 'Error in Logout Application';
			EXECUTE s01uspLogError;
		END CATCH;
	SET NOCOUNT OFF
GO
/****** Object:  StoredProcedure [dbo].[usp802WebConsoleLoginValidation]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE Procedure [dbo].[usp802WebConsoleLoginValidation]
@UserName varchar(100),
@Password varchar(100)
AS
BEGIN

Declare @LatestPassword varchar(100),@UserPassword varchar(100),@Department nvarchar(50)

If  exists(select top 1 * from s01tblUsers where Username=@UserName and Password=@Password and UserStatusID=1)
Begin
     select 'Login Success'
	 --set @Department=(select Department from tblUsers where Username=@UserName);
	 --select RoomNumber  from tbllocationroom where Department=@Department;
	
end
else
      select 'Invalid login'
		--Select 'Error' as datasetname,'Invalid Username' as msg;
END
GO
/****** Object:  StoredProcedure [dbo].[usp810WebConsoleLoadPatientList]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE Procedure [dbo].[usp810WebConsoleLoadPatientList]
	@Event VARCHAR(255)
	, @udtSystemData udtSPParam READONLY
	, @udtEventData udtSPParam READONLY
AS
	SET NOCOUNT ON;
		DECLARE @SettingValue VARCHAR(512), @StrSQL VARCHAR(MAX), @Msg VARCHAR(200), @PatSysType VARCHAR (20), @DeviceID INT, @OrderBY VARCHAR(MAX)
		--SET XACT_ABORT ON;
		BEGIN TRY
		--BEGIN TRANSACTION
		SELECT @DeviceID = Value FROM @udtSystemData WHERE Property = 'DeviceID'
		SET @SettingValue=  [dbo].[udf700WebConsoleSUPFetchSettings] (@DeviceId,'HUB_FILTER_PAT_DEFAULT') 
		SET @PatSysType=  [dbo].[udf700WebConsoleSUPFetchSettings] (@DeviceId,'PAT_SYS_TYPE') 
		SET @OrderBY = ' ORDER BY PatientName ASC, ProcedureDate DESC, CSN ASC'
		--IF @Event <> 'LoadLastPatientList'
		--	EXECUTE  uspLoadSettingsList @Event = 'FetchAllSettings'
		--			, @udtSystemData = @udtSystemData
		--			, @udtEventData = @udtEventData
		SET @SettingValue = REPLACE( @SettingValue, 'Filter List|', '' )
		SET @StrSQL=   'DECLARE @ClientTimeZone VARCHAR(500), @UTCStandard VARCHAR(100), @SecondDiff INT
						SELECT @ClientTimeZone = [Setting Value] 
						FROM s01tblSettingsCommon 
						WHERE [Setting Name] = ''CLIENT_TIMEZONE'' 
						SELECT @UTCStandard = UTCStandard 
						FROM tblStandardTimeZone 
						WHERE StandardTimeZone = @ClientTimeZone
						SELECT @SecondDiff =  REPLACE(@UTCStandard, ''UTC'', '''')
						SELECT ''DefaultPatientList'' AS DataSetName, ''CSN: '' + LTRIM(RTRIM(ISNULL(SPD.VisitNumber,''N/A''))) AS CSN, LTRIM(RTRIM(PD.[Patient Name])) AS PatientName, 
									''MRN: '' + LTRIM(RTRIM(ISNULL(PD.PatientID,''N/A''))) AS MRN, LTRIM(RTRIM(PD.[Physician Name])) AS PhysicianName 
									, CASE WHEN DATEADD( SECOND, @SecondDiff, ProcedureDate ) IS NULL 
												THEN DATEADD( SECOND, @SecondDiff, LTRIM(RTRIM(PD.LastActivityDate))) 
											ELSE DATEADD( SECOND, @SecondDiff, LTRIM(RTRIM(ProcedureDate )))
										END AS ProcedureDate
									, ''CASEID: '' + LTRIM(RTRIM(ISNULL(PD.AppointmentID,''N/A''))) AS CASEID, PD.MAPatientID, PD.MAScheduleID, PD.MAVisitID,PD.ProcedureDesc--, LTRIM(RTRIM(SPD.ProcedureCode))
						FROM s01qryPatientDisplay'+@PatSysType+' PD
							LEFT OUTER JOIN qry701WebConsoleSUPPatientDisplayADT SPD ON SPD.MaScheduleID = PD.MaScheduleID 
							'
		SET @StrSQL = @StrSQL + ' ' + @SettingValue + @OrderBY
		PRINT( @StrSQL )
		EXECUTE( @StrSQL )
		--EXECUTE  usp705WebConsoleAppCabEventLogs @Event = @Event
		--				, @udtSystemData = @udtSystemData
		--				, @udtEventData = @udtEventData
		--COMMIT TRANSACTION		
		END TRY
		BEGIN CATCH
		--IF XACT_STATE() <> 0
			--ROLLBACK TRANSACTION
			SET @Msg = 'ERROR in Loading Patient';
		EXECUTE s01uspLogError
	END CATCH
	SET NOCOUNT OFF
GO
/****** Object:  StoredProcedure [dbo].[usp812WebConsoleLoadUsageList]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE Procedure [dbo].[usp812WebConsoleLoadUsageList]

	@Event VARCHAR(255)

	, @udtSystemData udtSPParam READONLY

	, @udtEventData udtSPParam READONLY

AS

	SET NOCOUNT ON;

	DECLARE @Msg VARCHAR(200), @DataSetName VARCHAR(50),  @SessionID VARCHAR(50), @PatientName VARCHAR(167)

	DECLARE @MAPatientID BIGINT, @SecondDiff INT, @ClientTimeZone VARCHAR(500), @UTCStandard VARCHAR(100), @ScanType VARCHAR(25)

	DECLARE  @MAScheduleID INT, @MAVisitID INT, @DeviceId INT 	

	SELECT @DeviceID = Value FROM @udtSystemData WHERE Property = 'DeviceID'

	SELECT @MAPatientID = Value FROM @udtEventData WHERE Property = 'MAPatientID'

	SELECT @SessionID = Value FROM @udtEventData WHERE Property = 'SessionID'

	--SELECT @DataSetName = Value FROM @udtEventData WHERE Property = 'EventOutPut'

	SELECT @MAVisitID = Value FROM @udtEventData WHERE Property = 'MAVisitID' 

	SELECT @MAScheduleID = Value FROM @udtEventData WHERE Property = 'MAScheduleID' 

	IF @Event IN ( 'StockNewItem', 'RemoveItem', 'ReturnItem', 'WasteItem', 'TransferItem', 'SwapItem', 'OverrideItem', 'RenderToExternalHospital')

		SET @ScanType = 'CabinetScan'

	ELSE

		SELECT @ScanType = Value FROM @udtEventData WHERE Property = 'EventInPut'

	SELECT @PatientName = [Patient Name] 

	FROM s01qryPatients 

	WHERE MAPatientID = @MAPatientID

	SELECT @ClientTimeZone = [dbo].[udf700WebConsoleSUPFetchSettings] ( @DeviceId,'CLIENT_TIMEZONE' )

	SELECT @UTCStandard = UTCStandard 

	FROM tblStandardTimeZone 

	WHERE StandardTimeZone = @ClientTimeZone

	SELECT @SecondDiff =  REPLACE(@UTCStandard, 'UTC', '')

	-- patient removal usage

	IF ( 

			@MAPatientID IS NOT NULL 

			AND @MAScheduleID IS NOT NULL 

		)

	BEGIN			

					SELECT top 1 [Description], CatNo,CONVERT(varchar, CONVERT(datetime,DateUsed,120),22)as DateUsed, [REAL], Qty, PatientName, ItemUsageID, UsageTypeID

						, RFID, ProductType,SerialNo, LotNo, EpicStatus

					FROM qry704WebConsoleSUPItemUsageList

					WHERE MAScheduleID = @MAScheduleID

					and usagetypeid=1

					ORDER BY DateUsed DESC

	END

	--Non patient removal usage

	IF ( @MAPatientID IS NULL ) 

	BEGIN

			SELECT top 1 [Description], CatNo, DateUsed, [REAL], Qty, PatientName, ItemUsageID

			, UsageTypeID, RFID, ProductType, SerialNo, LotNo, EpicStatus

			FROM qry704WebConsoleSUPItemUsageList

			WHERE SessionID = @SessionID

			AND MAPatientID is null

			and usagetypeid=1

			ORDER BY  DateUsed DESC

	END

		--EXECUTE  usp705WebConsoleAppCabEventLogs @Event = 'LoadUsageList'

		--		, @udtSystemData = @udtSystemData

		--		, @udtEventData = @udtEventData

	SET NOCOUNT OFF


GO
/****** Object:  StoredProcedure [dbo].[usp821WebConsoleExecuteAppEvent]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE Procedure [dbo].[usp821WebConsoleExecuteAppEvent]
	@Event VARCHAR(255),
	@udtSystemData udtSPParam READONLY,
	@udtEventData udtSPParam READONLY
AS


	SET NOCOUNT ON
	DECLARE @SPName VARCHAR(100), @AppID INT, @DeviceID INT, @Msg VARCHAR(200)
	SET XACT_ABORT ON;
	BEGIN TRY
	BEGIN TRANSACTION;
	SELECT @AppID = Value FROM @udtSystemData WHERE Property = 'EntryTypeID'
	SELECT @SPName = SPName
	FROM tblAppEvents
	WHERE [Event] = @Event AND AppID = @AppID
	IF NOT @SPName IS NULL
	BEGIN

		    IF @Event IN ( 'StockNewItem','ReturnItem','RemoveItem',
							'WasteItem','TransferItem','UpdateFDAItemDetails')
				EXEC @SPName @Event, @udtSystemData, @udtEventData
			ELSE IF @Event IN ( 'FetchBillOnlyItemDetails','UpdateOTUItemDetails','FetchOneTimeUseItemDetails','FetchItemDetailsByCatNo')
				EXEC @SPName @Event, @udtSystemData, @udtEventData
			ELSE
				EXEC usp825WebConsoleScanBarcodeInventory @Event, @udtSystemData, @udtEventData 
	END
	ELSE
		SELECT 'No SP Found' AS ErrorDataSet
	COMMIT TRANSACTION;
		END TRY
		BEGIN CATCH
			IF (XACT_STATE()) <> 0
				ROLLBACK TRANSACTION;
				SET @Msg = 'ERROR in tblAppEvents for calling SP';
			EXECUTE s01uspLogError;
		END CATCH;
	SET NOCOUNT OFF

GO
/****** Object:  StoredProcedure [dbo].[usp822WebConsoleProcessItems]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE Procedure [dbo].[usp822WebConsoleProcessItems]
	@Event VARCHAR(50)
	, @udtSystemData udtSPParam READONLY
	, @udtEventData udtSPParam READONLY
	--, @udt702WebConsoleTagsData udt702WebConsoleTags READONLY
AS

	SET NOCOUNT ON;
	DECLARE @udt700WebConsoleTransactedItems udt700WebConsoleTransactedItems, @ScannedLocation INT, @TransactionTime DATETIME, @ItemStatusID INT, @SacnType VARCHAR(20)
	BEGIN TRY	
	
	IF @Event IN ( 'StockNewItem', 'RemoveItem', 'ReturnItem', 'WasteItem'
					, 'TransferItem', 'SwapItem', 'OverrideItem', 'InitialScan', 'KeyAccessScan', 'RenderToExternalHospital' )
	BEGIN			
		--IF (@SacnType <> 'OutsideScan')
		--BEGIN
		
			EXECUTE usp742WebConsoleProcessProcessRFIDItems @Event = @Event
				, @udtSystemData = @udtSystemData
				, @udtEventData = @udtEventData
				--, @udt700WebConsoleTransactedItems = @udt700WebConsoleTransactedItems
				--, @udt702WebConsoleTagsData = @udt702WebConsoleTagsData
		--END 
	END
	END TRY
	BEGIN CATCH			
		EXECUTE s01uspLogError;
	END CATCH;
	SET NOCOUNT OFF
GO
/****** Object:  StoredProcedure [dbo].[usp823WebConsoleProcessMaxRmvPatItems]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE Procedure [dbo].[usp823WebConsoleProcessMaxRmvPatItems]
	@Event VARCHAR(200)
	, @udtSystemData udtSPParam READONLY
	, @udtEventData udtSPParam READONLY
	, @ItemStatusID INT
	, @ItemEventID INT
	, @CurrentUtcTime DateTime
	, @UsageTypeID INT
AS
	SET NOCOUNT ON;
	DECLARE @UserID INT, @EntryTypeID INT, @SessionID BIGINT, @DeviceID INT
	DECLARE @ComputerName VARCHAR(50), @Domain VARCHAR(50), @UOMCode VARCHAR(50), @BillingStatusID INT, @ItemUsageID INT, @Msg VARCHAR(500), @MAX_RMV_PAT_ITEMS VARCHAR(10)
	DECLARE @MAPatientID VARCHAR(50), @MAScheduleID INT, @MAVisitID INT, @MAPhysicianID INT, @DBID INT, @AlertID INT, @AlertMessage VARCHAR(512), @AlertEnabled BIT
	DECLARE @RFID VARCHAR(50)
	DECLARE @ItemID INT
	DECLARE @CompartmentID INT
	DECLARE @OverrideNotes VARCHAR(50)
	DECLARE @OverrideID INT
	DECLARE @isAlreadyRemoved BIT;
	SET XACT_ABORT ON;
	BEGIN TRY
	BEGIN TRANSACTION	
	
	SELECT @DeviceID = Value FROM @udtSystemData WHERE Property = 'DeviceID'
	SELECT @EntryTypeID = Value FROM @udtSystemData WHERE Property = 'EntryTypeID'
	SELECT @Domain = Value FROM @udtSystemData WHERE Property = 'Domain'
	SELECT @ComputerName = Value FROM @udtSystemData WHERE Property = 'ComputerName'
	SELECT @SessionID = Value FROM @udtEventData WHERE Property = 'SessionID'
	SELECT @UserID = Value FROM @udtEventData WHERE Property = 'UserID'
	SELECT @RFID = Value FROM @udtEventData WHERE Property = 'RFID'
	SELECT @MAPatientID = Value FROM @udtEventData WHERE Property = 'MAPatientID'
	SELECT @MAVisitID = Value FROM @udtEventData WHERE Property = 'MAVisitID'
	SELECT @MAScheduleID = Value FROM @udtEventData WHERE Property = 'MAScheduleID'
	set @ItemID = (select itemid from s01tblItems where RFID=@RFID)
	set @CompartmentID = (select CompartmentID from s01tblItems where ItemID=@ItemID)
	set @MAPhysicianID= (select MAPhysicianID from s01tblPatientSchedules where MAScheduleID=@MAScheduleID)
	SELECT @OverrideNotes = Value FROM @udtEventData WHERE Property = 'OverrideNotes'
	if (@MAPatientID is null)
	begin
		set @OverrideID=7;
	end


	-- Check the item is already removed and sent to epic. and again trying to remove for dif patient
	IF EXISTS (SELECT 1 FROM s03tblOutboundDFTUsage WHERE MsgType = 'CHARGE' AND EnteredDate = (
				SELECT MAX(EnteredDate)
				FROM s03tblOutboundDFTUsage
				WHERE UsageItemID = @ItemID AND MsgType = 'CHARGE'			
				))
	BEGIN
		SET @isAlreadyRemoved = 1;  -- TRUE

		DELETE FROM s01tblItemUsage
		WHERE UsageItemID = @ItemID
		AND LastActivityDate = (
			SELECT MAX(LastActivityDate)
			FROM s01tblItemUsage
			WHERE UsageItemID = @ItemID
							   ); --deleting recent Usage for Same Item

		--Updating ItemStatusID to 4 (Used)
		UPDATE dbo.s01tblItems 
		SET ItemStatusID = @ItemStatusID, RMUserID = @UserID, [Date Removed] = @CurrentUtcTime
			, EntryTypeID = @EntryTypeID, MAScheduleID = @MAScheduleID, MAPatientID = @MAPatientID, LastActivityDate = @CurrentUtcTime
			, MAPhysicianID = @MAPhysicianID 
		FROM dbo.s01tblItems I where  I.RFID=@RFID;

		INSERT INTO dbo.s01tblItemUsage ( MAPatientID, MAScheduleID, MAVisitID, UsedBy, DateUsed, Qty, UnitPrice, UOMCode, BillingStatusID, OverrideID, OverrideNotes, UpdatedBy
					, LastActivityDate , UsageTypeID, UsageItemID, SessionID, UCDM, EntryTypeID )
		SELECT @MAPatientID, @MAScheduleID, @MAVisitID, @UserID, @CurrentUtcTime, 1, I.[Act Price], null, @BillingStatusID, 0,@OverrideNotes , @UserID, @CurrentUtcTime
							, @UsageTypeID, I.ItemID, @SessionID, I.ICDM, @EntryTypeID 
		FROM dbo.s01tblItems I where  I.RFID=@RFID; 

		--performing return work flow, becouse the item is already removed but not used and again trying to remove
		INSERT INTO dbo.s01tblItemHistory( ItemStatusID, ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAScheduleID, MAVisitID, BillingStatusID, UsedBy, Qty
						, UnitPrice, UOMCode, UpdatedBy, UCDM, LastActivityDate, EntryTypeID, DateUsed, OverrideID, OverrideNotes, SessionID, ItemEventID )
		SELECT '1' AS ItemStatusID, ItemUsageID, @UsageTypeID, U.UsageItemID, MAPatientID, MAScheduleID, MAVisitID, BillingStatusID, @UserID, U.Qty, U.UnitPrice
							, null, @UserID, U.UCDM, @CurrentUtcTime, @EntryTypeID, U.DateUsed, 0, U.OverrideNotes, @SessionID, '3' AS ItemEventID
		FROM dbo.s01tblItemUsage U 
		WHERE SessionID = @SessionID  AND U.LastActivityDate = @CurrentUtcTime 
		and U.UsageItemID=@ItemID;

		INSERT INTO dbo.s01tblitemhistoryextn (ItemHistoryID, LocationID)
		SELECT ItemHistoryID, @CompartmentID 
		from dbo.s01tblItemHistory H  
		where H.LastActivityDate = @CurrentUtcTime
		AND H.UsageTypeID=1

		EXECUTE  uspSWCSendUsageToEpic @Event = @Event
					, @udtSystemData = @udtSystemData
					, @udtEventData = @udtEventData
					, @ItemEventID = 13 -- Credit , we can use 3 also here
					, @ItemStatusID = @ItemStatusID
					, @UsageTypeID = @UsageTypeID
					, @ItemID = @ItemID

		--perofrming remove work flow, becouse they scanned again
		INSERT INTO dbo.s01tblItemHistory( ItemStatusID, ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAScheduleID, MAVisitID, BillingStatusID, UsedBy, Qty
						, UnitPrice, UOMCode, UpdatedBy, UCDM, LastActivityDate, EntryTypeID, DateUsed, OverrideID, OverrideNotes, SessionID, ItemEventID )
		SELECT @ItemStatusID, ItemUsageID, @UsageTypeID, U.UsageItemID, MAPatientID, MAScheduleID, MAVisitID, BillingStatusID, @UserID, U.Qty, U.UnitPrice
							, null, @UserID, U.UCDM, @CurrentUtcTime, @EntryTypeID, U.DateUsed, 0, U.OverrideNotes, @SessionID, @ItemEventID  
							--select *
		FROM dbo.s01tblItemUsage U 
		WHERE SessionID = @SessionID  AND U.LastActivityDate = @CurrentUtcTime 
		and U.UsageItemID=@ItemID;

		INSERT INTO dbo.s01tblitemhistoryextn (ItemHistoryID, LocationID)
		SELECT ItemHistoryID, @CompartmentID 
		from dbo.s01tblItemHistory H  
		where H.LastActivityDate = @CurrentUtcTime
		AND H.UsageTypeID=1

		EXECUTE  uspSWCSendUsageToEpic @Event = @Event
					, @udtSystemData = @udtSystemData
					, @udtEventData = @udtEventData
					, @ItemEventID = @ItemEventID
					, @ItemStatusID = @ItemStatusID
					, @UsageTypeID = @UsageTypeID
					, @ItemID = @ItemID
	END
	ELSE
	BEGIN
		SET @isAlreadyRemoved = 0;  -- FALSE

		DELETE FROM s01tblItemUsage
		WHERE UsageItemID = @ItemID
		AND LastActivityDate = (
			SELECT MAX(LastActivityDate)
			FROM s01tblItemUsage
			WHERE UsageItemID = @ItemID
							   ); --deleting recent Usage for Same Item

		--TODO --auto return hestrory delete need to add

		--Updating ItemStatusID to 4 (Used)
		UPDATE dbo.s01tblItems 
		SET ItemStatusID = @ItemStatusID, RMUserID = @UserID, [Date Removed] = @CurrentUtcTime
			, EntryTypeID = @EntryTypeID, MAScheduleID = @MAScheduleID, MAPatientID = @MAPatientID, LastActivityDate = @CurrentUtcTime
			, MAPhysicianID = @MAPhysicianID 
		FROM dbo.s01tblItems I where  I.RFID=@RFID;

		INSERT INTO dbo.s01tblItemUsage ( MAPatientID, MAScheduleID, MAVisitID, UsedBy, DateUsed, Qty, UnitPrice, UOMCode, BillingStatusID, OverrideID, OverrideNotes, UpdatedBy
					, LastActivityDate , UsageTypeID, UsageItemID, SessionID, UCDM, EntryTypeID )
		SELECT @MAPatientID, @MAScheduleID, @MAVisitID, @UserID, @CurrentUtcTime, 1, I.[Act Price], null, @BillingStatusID, 0,@OverrideNotes , @UserID, @CurrentUtcTime
							, @UsageTypeID, I.ItemID, @SessionID, I.ICDM, @EntryTypeID 
		FROM dbo.s01tblItems I where  I.RFID=@RFID; 

		--for Normal Remove Workflow , we are performing Return and Remove for No Patient work flow
		INSERT INTO dbo.s01tblItemHistory( ItemStatusID, ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAScheduleID, MAVisitID, BillingStatusID, UsedBy, Qty
						, UnitPrice, UOMCode, UpdatedBy, UCDM, LastActivityDate, EntryTypeID, DateUsed, OverrideID, OverrideNotes, SessionID, ItemEventID )
		--SELECT '1' AS ItemStatusID, ItemUsageID, @UsageTypeID, U.UsageItemID, MAPatientID, MAScheduleID, MAVisitID, BillingStatusID, @UserID, U.Qty, U.UnitPrice --COMMENTED BECOUSE Performing No Patient Return work flow
		SELECT '1' AS ItemStatusID, ItemUsageID, @UsageTypeID, U.UsageItemID, NULL, NULL, NULL, BillingStatusID, @UserID, U.Qty, U.UnitPrice
							, null, @UserID, U.UCDM, @CurrentUtcTime, @EntryTypeID, U.DateUsed, 0, U.OverrideNotes, @SessionID, '3' AS ItemEventID
		FROM dbo.s01tblItemUsage U 
		WHERE SessionID = @SessionID  AND U.LastActivityDate = @CurrentUtcTime 
		and U.UsageItemID=@ItemID;

		INSERT INTO dbo.s01tblItemHistory( ItemStatusID, ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAScheduleID, MAVisitID, BillingStatusID, UsedBy, Qty
						, UnitPrice, UOMCode, UpdatedBy, UCDM, LastActivityDate, EntryTypeID, DateUsed, OverrideID, OverrideNotes, SessionID, ItemEventID )
		SELECT @ItemStatusID, ItemUsageID, @UsageTypeID, U.UsageItemID, MAPatientID, MAScheduleID, MAVisitID, BillingStatusID, @UserID, U.Qty, U.UnitPrice
							, null, @UserID, U.UCDM, @CurrentUtcTime, @EntryTypeID, U.DateUsed, 0, U.OverrideNotes, @SessionID, @ItemEventID  
		FROM dbo.s01tblItemUsage U 
		WHERE SessionID = @SessionID  AND U.LastActivityDate = @CurrentUtcTime 
		and U.UsageItemID=@ItemID;

		INSERT INTO dbo.s01tblitemhistoryextn (ItemHistoryID, LocationID)
		SELECT ItemHistoryID, @CompartmentID 
		from dbo.s01tblItemHistory H  
		where H.LastActivityDate = @CurrentUtcTime
		AND H.UsageTypeID=1

		EXECUTE  uspSWCSendUsageToEpic @Event = @Event
					, @udtSystemData = @udtSystemData
					, @udtEventData = @udtEventData
					, @ItemEventID = @ItemEventID
					, @ItemStatusID = @ItemStatusID
					, @UsageTypeID = @UsageTypeID
					, @ItemID = @ItemID
	END




	--UPDATE dbo.s01tblItems 
	--SET ItemStatusID = @ItemStatusID, RMUserID = @UserID, [Date Removed] = @CurrentUtcTime
	--	, EntryTypeID = @EntryTypeID, MAScheduleID = @MAScheduleID, MAPatientID = @MAPatientID, LastActivityDate = @CurrentUtcTime
	--	, MAPhysicianID = @MAPhysicianID 
	--FROM dbo.s01tblItems I where  I.RFID=@RFID;

	--DELETE FROM s01tblItemUsage
	--	   WHERE UsageItemID = @ItemID
	--	   AND LastActivityDate = (
	--			SELECT MAX(LastActivityDate)
	--			FROM s01tblItemUsage
	--			WHERE UsageItemID = @ItemID
	--			);

	--INSERT INTO dbo.s01tblItemUsage ( MAPatientID, MAScheduleID, MAVisitID, UsedBy, DateUsed, Qty, UnitPrice, UOMCode, BillingStatusID, OverrideID, OverrideNotes, UpdatedBy
	--					, LastActivityDate , UsageTypeID, UsageItemID, SessionID, UCDM, EntryTypeID )
	--SELECT @MAPatientID, @MAScheduleID, @MAVisitID, @UserID, @CurrentUtcTime, 1, I.[Act Price], null, @BillingStatusID, 0,@OverrideNotes , @UserID, @CurrentUtcTime
	--					, @UsageTypeID, I.ItemID, @SessionID, I.ICDM, @EntryTypeID 
	--FROM dbo.s01tblItems I where  I.RFID=@RFID; 
	
	--INSERT INTO dbo.s01tblItemHistory( ItemStatusID, ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAScheduleID, MAVisitID, BillingStatusID, UsedBy, Qty
	--					, UnitPrice, UOMCode, UpdatedBy, UCDM, LastActivityDate, EntryTypeID, DateUsed, OverrideID, OverrideNotes, SessionID, ItemEventID )
	--SELECT @ItemStatusID, ItemUsageID, @UsageTypeID, U.UsageItemID, MAPatientID, MAScheduleID, MAVisitID, BillingStatusID, @UserID, U.Qty, U.UnitPrice
	--					, null, @UserID, U.UCDM, @CurrentUtcTime, @EntryTypeID, U.DateUsed, 0, U.OverrideNotes, @SessionID, @ItemEventID  
	--FROM dbo.s01tblItemUsage U 
	--WHERE SessionID = @SessionID  AND U.LastActivityDate = @CurrentUtcTime 
	--and U.UsageItemID=@ItemID;

	--INSERT INTO dbo.s01tblitemhistoryextn (ItemHistoryID, LocationID)
	--SELECT ItemHistoryID, @CompartmentID 
	--	from dbo.s01tblItemHistory H  
	--	where H.LastActivityDate = @CurrentUtcTime
	--	AND H.UsageTypeID=1

	--IF (1=1) --setting value
	--BEGIN
	--	IF ( @RFID IS NOT NULL AND @RFID <> '' ) 
	--	BEGIN
	--		--Epic Transaction
	--		EXECUTE  uspSWCSendUsageToEpic @Event = @Event
	--				, @udtSystemData = @udtSystemData
	--				, @udtEventData = @udtEventData
	--				, @ItemEventID = @ItemEventID
	--				, @ItemStatusID = @ItemStatusID
	--				, @UsageTypeID = @UsageTypeID
	--				, @ItemID = @ItemID
	--	END					
	--END
COMMIT TRANSACTION;
END TRY
		BEGIN CATCH
			IF (XACT_STATE()) <> 0
				ROLLBACK TRANSACTION;
				SET @Msg = 'ERROR in Item processing during cabinet scan';
			EXECUTE s01uspLogError;
		END CATCH;
	SET NOCOUNT OFF
GO
/****** Object:  StoredProcedure [dbo].[usp824WebConsoleProcessRFIDItems]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE Procedure [dbo].[usp824WebConsoleProcessRFIDItems]
	@Event VARCHAR(200)
	, @udtSystemData udtSPParam READONLY
	, @udtEventData udtSPParam READONLY
	--, @udt700WebConsoleTransactedItems udt700WebConsoleTransactedItems READONLY
	--, @udt702WebConsoleTagsData udt702WebConsoleTags READONLY
AS
	SET NOCOUNT ON;
	DECLARE @UserID INT, @EntryTypeID INT, @SessionID BIGINT, @DeviceID INT, @BillingStatusID INT, @OverrideID INT, @OverrideNotesID INT, @OverrideNotes VARCHAR(255)
	DECLARE @ItemEventID INT, @UsageTypeID INT, @ItemStatusID INT,@ItemStatus INT, @MAPatientID VARCHAR(50), @MAScheduleID INT, @MAVisitID INT, @AlertMessage VARCHAR(512)
	DECLARE @CurrentUtcTime DATETIME, @OverrideDescription VARCHAR(50), @OverrideDescNotes VARCHAR(50), @UageSummarySettings VARCHAR(250), @AlertID INT, @DBID INT
	DECLARE @ClientTimeZone VARCHAR(500), @UTCStandard VARCHAR(100), @SecondDiff INT, @ShowUnAssociateItems VARCHAR(50), @PhysicianID VARCHAR(50)
	DECLARE @MAPhysicianID INT, @EnableEpicInterface VARCHAR(50), @SpuriousLogAlertEnabled BIT, @EXPDATE_CHECK VARCHAR(10), @CLOSEEXP_CHECK VARCHAR(10)
	DECLARE @MAX_RMV_PAT_ITEMS INT, @MAX_RMV_PAT_ITEMS_AlertEnabled BIT, @AntenaStatus VARCHAR(500), @AntennaFailure_AlertEnabled INT
	DECLARE @RFID VARCHAR(50),@ItemID INT, @CompartmentID INT
	DECLARE @udt701WebConsoleTransactedItemDetails udt701WebConsoleTransactedItemDetails 
		--SET XACT_ABORT ON;
		BEGIN TRY
		--BEGIN TRANSACTION
		SELECT @DeviceID = Value FROM @udtSystemData WHERE Property = 'DeviceID'
		SELECT @EntryTypeID = Value FROM @udtSystemData WHERE Property = 'EntryTypeID'
		SELECT @SessionID = Value FROM @udtEventData WHERE Property = 'SessionID'
		SELECT @UserID = Value FROM @udtEventData WHERE Property = 'UserID'
		SELECT @MAPatientID = Value FROM @udtEventData WHERE Property = 'MAPatientID'
		SELECT @OverrideDescription = Value FROM @udtEventData WHERE Property = 'OverrideDesc'
		--SELECT @OverrideDescNotes = Value FROM @udtEventData WHERE Property = 'EventInput2'
		SELECT @RFID = Value FROM @udtEventData WHERE Property = 'RFID'
			SELECT @ItemStatus = Value FROM @udtEventData WHERE Property = 'ItemStatus' 
		SELECT @ItemID =(select ItemID from s01tblItems where RFID=@RFID)
		set @CompartmentID = (select CompartmentID from s01tblItems where ItemID=@ItemID)
		SELECT @MAX_RMV_PAT_ITEMS = [dbo].[udf700WebConsoleSUPFetchSettings] ( @MAX_RMV_PAT_ITEMS,'MAX_RMV_PAT_ITEMS' )
		SELECT @UsageTypeID = UsageTypeID 
		FROM dbo.s01tblUsageType 
		WHERE UsageType = 'RF ITEM'
		--SET @EnableEpicInterface =  [dbo].[udf700WebConsoleSUPFetchSettings] (@DeviceId,'ENABLE_EPIC_INTERFACE') 
		SELECT @OverrideID = OverrideID 
		FROM dbo.s01tblOverride 
		WHERE OverrideDesc = CASE 
								WHEN @OverrideDescription = 'Waste Item' THEN 'Waste'   
								WHEN @OverrideDescription = 'Override' THEN 'Manual Override'
								WHEN @MAPatientID IS NOT NULL THEN 'Default'
								WHEN @Event = 'InitialScan' THEN 'Initial Scan'
								WHEN @Event = 'KeyAccessScan' THEN 'Key Access Scan'
								WHEN @OverrideDescription IN ('Take','Waste','Return') THEN 'Default' 
								ELSE @OverrideDescription 
							 END
		SELECT @UageSummarySettings = [dbo].[udf700WebConsoleSUPFetchSettings] ( @DeviceId,'SHOW_USAGE_SUMMARY' ) 
		SELECT @ClientTimeZone = [dbo].[udf700WebConsoleSUPFetchSettings] ( @DeviceId,'CLIENT_TIMEZONE' )
		SELECT @UTCStandard = UTCStandard 
		FROM s01tblStandardTimeZone 
		WHERE StandardTimeZone = @ClientTimeZone
		SELECT @SecondDiff =  REPLACE(@UTCStandard, 'UTC', '')
		IF (SELECT [Setting Value] FROM dbo.s01tblSettingsCommon WHERE [Setting Name] = 'ENABLE_UTC_TIMEZONE') = 'TRUE'
		BEGIN
			SELECT @CurrentUTCTime = GETUTCDATE()
		END
		ELSE
		BEGIN
			SELECT @CurrentUTCTime = GETDATE()
		END
		--IF ( SELECT COUNT(*) FROM @udt700WebConsoleTransactedItems WHERE TagType = 'FoundTag' ) > 0
		--BEGIN
		if (@Event='ReturnItem')
		begin
	IF EXISTS(Select TOP 1 * from s01tblitems where  RFID=@RFID and ItemStatusID=1 ) 
            BEGIN
            Select 'Error' as datasetname,'Item already stocked in cabinet.'  as msg
            END
			else if not exists( Select TOP 1 * from s01tblitems where  RFID=@RFID)
			begin
			Select 'Error' as datasetname,'RFID not associated to any item.'  as msg
			end
			else
			begin
			SELECT @ItemStatusID = ItemStatusID 
			FROM dbo.s01tblItemStatus 
			WHERE [Item Status] = 'In Cabinet'
			SELECT @ItemEventID = CASE 
									  WHEN @Event = 'ReturnItem' 
										THEN 3
                                      ELSE 1
								  END
			DELETE FROM dbo.s01tblItemUsage where UsageItemID=@ItemID;
			--WHERE UsageTypeID = 1 AND UsageItemID IN ( SELECT I.ItemID 
			--											FROM dbo.s01tblItems I INNER JOIN @udt701WebConsoleTransactedItemDetails TI ON TI.RFID = I.RFID 
			--											WHERE TagType = 'FoundTag' )
			UPDATE dbo.s01tblItems 
			SET ItemStatusID = @ItemStatusID,  [AddUserID] = @UserID
				, [Date Added] = @CurrentUtcTime, EntryTypeID = @EntryTypeID, SupplierID = 0, LastActivityDate = @CurrentUtcTime
				, MAPatientID = NULL, MAScheduleID = NULL, MAPhysicianID = NULL 
				, Qty = 1 
			FROM dbo.s01tblItems I where RFID=@RFID;
			--	INNER JOIN @udt701WebConsoleTransactedItemDetails TI ON TI.RFID = I.RFID
			--WHERE TagType = 'FoundTag'
			INSERT INTO dbo.s01tblItemHistory ( ItemStatusID, UsageTypeID, UsageItemID, MAPatientID, MAScheduleID, MAVisitID, BillingStatusID, Qty, UnitPrice --UOMCode
							,  UpdatedBy, UCDM, LastActivityDate, EntryTypeID, OverrideID, OverrideNotes, SessionID, ItemEventID )
			SELECT @ItemStatusID, @UsageTypeID, I.ItemID, I.MAPatientID INT, I.MAScheduleID, I.MAVisitID, @BillingStatusID, ISNULL(I.Qty,1), I.[Act Price]-- TI.UOMCode
							, @UserID, I.ICDM, @CurrentUtcTime, @EntryTypeID, @OverrideID, @OverrideNotes, @SessionID, @ItemEventID 
			FROM dbo.s01qryItems I where RFID=@RFID;
			--INNER JOIN @udt701WebConsoleTransactedItemDetails TI ON TI.RFID = I.RFID AND TagType = 'FoundTag'
			INSERT INTO dbo.s01tblitemhistoryextn (ItemHistoryID, LocationID)
	SELECT ItemHistoryID, @CompartmentID 
	--FROM @udt701WebConsoleTransactedItemDetails T
		from dbo.s01tblItemHistory H  --H.UsageItemID = T.ItemID 
		where H.LastActivityDate = @CurrentUtcTime
		AND H.UsageTypeID=1
				select [Serial No],[Lot No],P.[Cat No],p.[Description 1],I.RFID from s01tblItems I
				inner join s01tblProducts P on P.ProductID=I.ProductID
				where I.RFID=@RFID
		end
		end
		else
		begin
		SELECT @MAVisitID = Value FROM @udtEventData WHERE Property = 'MAVisitID' 
		SELECT @MAScheduleID = Value FROM @udtEventData WHERE Property = 'MAScheduleID' 
		SELECT @PhysicianID = PhysicianID FROM dbo.s01qryPatientDisplayADT WHERE MAPatientID = @MAPatientID 
		SELECT @MAPhysicianID = MAPhysicianID FROM dbo.s01tblPhysicians WHERE PhysicianID = @PhysicianID
			SELECT @ItemStatusID = ItemStatusID 
			FROM dbo.s01tblItemStatus 
			WHERE [Item Status] = CASE 
									WHEN @OverrideDescription IN ( 'Waste Item','Waste') THEN 'Waste' 
									WHEN @OverrideDescription = 'Transfer Item' THEN 'Transfered'
									WHEN @OverrideDescription = 'Swap Item' THEN 'Swap'
									WHEN @OverrideDescription = 'Render To External Hospital' THEN 'Render to External Hospital'   
									ELSE 'Removed' 
									END
			SELECT @ItemEventID = ItemEventID 
			FROM dbo.s01tblItemEvent 
			WHERE EventDescription = CASE 
											WHEN @OverrideDescription IN ('Waste Item','Waste') THEN 'Item has been wasted' 
											WHEN @OverrideDescription = 'Transfer Item' THEN 'Item has been transfered'
											ELSE 'Item taken out of the cabinet' 
										END
			IF EXISTS(Select TOP 1 * from s01tblitems where  RFID=@RFID and ItemStatusID=2 ) 
            BEGIN
            Select 'Error' as datasetname,'Item already removed against patient.'  as msg
            END
			else if not exists( Select TOP 1 * from s01tblitems where  RFID=@RFID)
			begin
			Select 'Error' as datasetname,'RFID not associated to any item.'  as msg
			end
			else 
            begin
				EXECUTE usp823WebConsoleProcessMaxRmvPatItems @Event = @Event
						, @udtSystemData = @udtSystemData
						, @udtEventData = @udtEventData
						--, @udt701WebConsoleTransactedItemDetails = @udt701WebConsoleTransactedItemDetails
						, @ItemStatusID = @ItemStatus
						, @ItemEventID = @ItemEventID
						, @CurrentUtcTime = @CurrentUtcTime
						, @UsageTypeID = @UsageTypeID
							EXECUTE  usp812WebConsoleLoadUsageList @Event = @Event
							, @udtSystemData = @udtSystemData
							, @udtEventData = @udtEventData
			end
			end
	END TRY
		BEGIN CATCH
			EXECUTE s01uspLogError;
		END CATCH;
	SET NOCOUNT OFF
GO
/****** Object:  StoredProcedure [dbo].[usp825WebConsoleScanBarcodeInventory]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE Procedure [dbo].[usp825WebConsoleScanBarcodeInventory]
	@Event VARCHAR(255)
	, @udtSystemData udtSPParam READONLY
	, @udtEventData udtSPParam READONLY
AS
	SET NOCOUNT ON;
		DECLARE @DeviceID INT, @EntryTypeID INT, @Msg VARCHAR(200), @DataSetName VARCHAR(50), @SessionID varchar(50), @UserID INT, @ItemUsageID INT, @FDACatNo VARCHAR(50)
		DECLARE @CurrentPatientID INT, @CurrentUtcTime DATETIME, @ScannedBarcodeValue VARCHAR(100), @EventType VARCHAR(50), @CatNo VARCHAR(50), @ProductID INT, @LocationID INT, @LocationTypeID INT, @BarcodeSettings VARCHAR(10)
		DECLARE @UpdateQty VARCHAR(10), @UOMCode VARCHAR(10), @Qty INT,@inputQty INT, @ItemInventoryID INT, @EventInput3 VARCHAR(50), @EventInPut4 VARCHAR(50),  @OverrideID INT, @ItemEventID INT, @InventoryID INT
		DECLARE @ReasonWindowSetting VARCHAR(10), @OverrideNotes VARCHAR(50), @PatientName VARCHAR(200), @MAPatientID INT, @MAScheduleID INT, @MAVisitID INT, @ItemHistoryID INT, @UsageItemID INT, @IsFDA VARCHAR(50), @IsUsageExists VARCHAR(50),@implantStatus VARCHAR(50),@isImplant VARCHAR(10)
		
		--Edted by subramanya to handle type mismatch exception
		--SET XACT_ABORT ON;
		BEGIN TRY
		--BEGIN TRANSACTION

		SELECT @DeviceID = Value FROM @udtSystemData WHERE Property = 'DeviceID'
		SELECT @EntryTypeID = Value FROM @udtSystemData WHERE Property = 'EntryTypeID'
		SELECT @UserID = Value FROM @udtEventData WHERE Property = 'UserID'
		SELECT @MAPatientID = Value FROM @udtEventData WHERE Property = 'MAPatientID'
		SELECT @ScannedBarcodeValue = Value FROM @udtEventData WHERE Property = 'EventInPut1'
		SELECT @UpdateQty = Value FROM @udtEventData WHERE Property = 'EventInPut2'
		SELECT @EventInPut3 = Value FROM @udtEventData WHERE Property = 'EventInPut3'
		SELECT @EventInPut4 = Value FROM @udtEventData WHERE Property = 'EventInPut4'
		SELECT @EventType = Value FROM @udtEventData WHERE Property = 'EventType'
		SELECT @SessionID = Value FROM @udtEventData WHERE Property = 'SessionID'
		SELECT @DataSetName = Value FROM @udtEventData WHERE Property = 'EventOutPut'
		SELECT @InventoryID = Value FROM @udtEventData WHERE Property = 'ItemInventoryID'
		
		SELECT @MAVisitID = Value FROM @udtEventData WHERE Property = 'MAVisitID' 
		SELECT @MAScheduleID = Value FROM @udtEventData WHERE Property = 'MAScheduleID' 
		SELECT @FDACatNo = Value FROM @udtEventData WHERE Property = 'CatNo' 
		SELECT @IsFDA = Value FROM @udtEventData WHERE Property = 'IsFDA' 
		SELECT @inputQty = Value FROM @udtEventData WHERE Property = 'Qty' 
		SELECT @implantStatus = Value FROM @udtEventData WHERE Property = 'ImplantStatus' 
		--select @IsFDA;
	

		SELECT @LocationTypeID = [dbo].[udf700WebConsoleSUPFetchSettings] ( @DeviceId, 'BarcodeInventory_LocationType' )

		SELECT @LocationID = [dbo].[udf700WebConsoleSUPFetchSettings] ( @DeviceId, 'BarcodeInventory_Location' )

		IF(@implantStatus IS NOT NULL)
		BEGIN
			IF(@implantStatus = 'Implant')
			BEGIN
				SET @isImplant = 'Y';
			END
			ELSE
			BEGIN
				SET @isImplant = 'N';
			END
		END
		
		IF (SELECT [Setting Value] FROM s01tblSettingsCommon WHERE [Setting Name] = 'ENABLE_UTC_TIMEZONE') = 'TRUE'
		BEGIN
			SELECT @CurrentUTCTime = GETUTCDATE()
		END
		ELSE
		BEGIN
			SELECT @CurrentUTCTime = GETDATE()
		END
		

		SELECT @CatNo = [Cat No]
		FROM s01tblBarcodeRuleProduct 
		WHERE [Barcode No] = @ScannedBarcodeValue
			AND @ScannedBarcodeValue IS NOT NULL
			AND @ScannedBarcodeValue <> ''

		SELECT @ProductID = ProductID, @UOMCode = UOMCode 
		FROM s01tblProducts 
		WHERE [Cat No] = @CatNo
		
		--SR added > FDA/Master
		if (@IsFDA='true')
		begin
		
		--IF NOT EXISTS ( SELECT 1 FROM s01tblProducts 	
		--				WHERE REPLACE(REPLACE([Cat No],'-',''),'.','') = REPLACE(REPLACE(@CatNo,'-',''),'.','')
		--					OR @CatNo IS NULL OR @CatNo = ''
		--		      )
		--BEGIN
			
			EXECUTE usp826WebConsoleSaveBarcodeMasterFDAProducts 
					  @Event = @Event
					, @udtSystemData = @udtSystemData
					, @udtEventData = @udtEventData
					, @ProductID = @ProductID -- viresh added on 12/02/2024
			
			SELECT @CatNo = [Cat No]
			FROM s01tblBarcodeRuleProduct 
			WHERE [Barcode No] = @ScannedBarcodeValue

			SELECT @ProductID = ProductID, @UOMCode = UOMCode 
			FROM s01tblProducts 
			WHERE [Cat No] = @CatNo

			IF @ProductID IS NULL
			BEGIN
				SELECT @FDACatNo = [Cat No]
				FROM s01tblBarcodeRuleProduct 
				WHERE [Barcode No] = @ScannedBarcodeValue

				SELECT @ProductID = ProductID, @UOMCode = UOMCode 
				FROM s01tblProducts 
				WHERE [Cat No] = @FDACatNo
			END

			IF @ProductID IS NOT NULL
			BEGIN
				INSERT INTO s01tblItemInventory(ProductID,Qty,UpdatedBy,LastActivityDate,LocationID,LocationTypeID)
				VALUES (@ProductId, @inputQty, @UserID, @CurrentUTCTime, @LocationID, @LocationTypeID )
			
			IF 	@FDACatNo IS NOT NULL AND @FDACatNo <> ''
				SET @CatNo = @FDACatNo
			END
			
		--END
		end
		IF @ProductID IS NULL
		BEGIN
			SELECT 'Fail' AS DataSetName, 'Product is not registered' AS MSG
			RETURN
		END
		
		SELECT @PatientName = [Patient Name] 
		FROM s01qryPatients 
		WHERE MAPatientID = @MAPatientID

		SELECT @UsageItemID = ItemInventoryID 
		FROM s01tblItemInventory 
		WHERE ProductID = @ProductID 
			AND LocationID = @LocationID 
			AND LocationTypeID = @LocationTypeID

		SELECT @CurrentPatientID = MAPatientID 
		FROM s01tblItemUsage 
		WHERE MAPatientID = @MAPatientID 
			AND UsageItemID = @UsageItemID 
			AND UsageTypeID = 2
			
		SELECT @BarcodeSettings = [dbo].[udf700WebConsoleSUPFetchSettings] ( @DeviceId,'ENABLE_ITEMIZE_BARCODE' )
		
		SELECT @Qty = Qty, @ItemInventoryID = ItemInventoryID 
		FROM s01tblItemInventory 
		WHERE ProductID = @ProductID 
			AND LocationID = @LocationID 
			AND LocationTypeID = @LocationTypeID

		SELECT @OverrideID = OverrideID 
		FROM s01tblOverride 
		WHERE OverrideDesc = CASE 
								WHEN @EventInput3 = 'Waste Item' 
									THEN 'Waste'
							    WHEN @EventInput3 = 'Override' 
									THEN 'Manual Override'
								WHEN @MAPatientID IS NOT NULL AND @EventInput3 <> 'Waste' 
									THEN 'Default'
								ELSE @EventInput3 
							 END
		
		SELECT @ReasonWindowSetting = [dbo].[udf700WebConsoleSUPFetchSettings] ( @DeviceId,'ENABLE_OVERRIDEHANDLER' )

		IF ( @EventInput4 IS NULL )
		BEGIN
			SELECT @OverrideNotes = 'DEFAULT'
		END
		ELSE IF ( @EventInput4 IS NOT NULL )
		BEGIN
			SELECT @OverrideNotes = OverrideNotes 
			FROM s01tblOverrideNotes 
			WHERE OverrideNotes = @EventInput4 
				AND OverrideID = @OverrideID
		END

		SELECT @ItemEventID = ItemEventID 
		FROM s01tblItemEvent 
		WHERE EventDescription = CASE 
									 WHEN @EventInput3 = 'Waste Item' 
										THEN 'Barcode Item Wasted'
								     WHEN @EventInput3 = 'Transfer Item' 
										THEN 'Barcode Item Transferred'
									 WHEN @EventInput3 IN ('Return Item','Remove Item', 'Swap Item', 'Override', 'Waste Item') 
										THEN 'Item Used'
									 WHEN @EventType = 'StockBarcodeInventory' 
										THEN 'Stock quantity for barcode item has been updated'
									 WHEN @EventType = 'ReconcileBarcodeInventory' 
										THEN 'Quantity for barcode item has been updated'
									 WHEN @EventType = 'DeleteBarcodeInventory' 
										THEN 'Barcode Item Deleted'
									 ELSE 'Item returned to inventory' 
								 END
					
				IF ( @EventType = 'RemoveBarcodeInventory' )
				BEGIN	
					SELECT @DataSetName AS DataSetName, ItemInventoryID, [Ref Price], UOMCode, [Cat No], [Description 1], Qty, ProdUnitID, PCDM, LocationID ,[Barcode No]
					FROM qry703WebConsoleProductCatalog 
					WHERE [Cat No] = @CatNo 
						AND LocationTypeID = @LocationTypeID 
						AND LocationID = @LocationID

					UPDATE s01tblProducts
					SET RFID = @isImplant
					WHERE [Cat No] = @CatNo; --Updating implant status from Detail Screen

					DECLARE @Counter INT = 1;
					DECLARE @BarCodeQty INT = 1;
					WHILE @Counter <= @inputQty
					BEGIN
						IF ( @BarcodeSettings = 'TRUE')
						BEGIN
						--REPLACE( REPLACE( REPLACE ([Cat No], '-', ''), '.', ''), ' ', '' )
							IF ( SELECT 1 FROM qry703WebConsoleProductCatalog WHERE [Cat No] = @CatNo And LocationTypeID = @LocationTypeID And LocationID = @LocationID ) = 1 
							BEGIN
					
								INSERT INTO s01tblItemUsage ( MAPatientID, MAVisitID, MAScheduleID, UsedBy, DateUsed, Qty, UnitPrice, UOMCode, BillingStatusID, OverrideID, OverrideNotes
														, UpdatedBy, LastActivityDate, UsageTypeID, UsageItemID, SessionID, EntryTypeID )
								SELECT @MAPatientID, @MAVisitID, @MAScheduleID, @UserID, @CurrentUtcTime, @inputQty, [Ref Price], UOMCode, 1, @OverrideID, @OverrideNotes, @UserID
														, @CurrentUTCTime, 2, @ItemInventoryID, @SessionID, @EntryTypeID 
								FROM s01tblProducts 
								WHERE ProductID = @ProductID

								SELECT @ItemUsageID = SCOPE_IDENTITY()
							
								SELECT @UsageItemID = UsageItemID FROM s01tblItemUsage 
								WHERE ItemUsageID = @ItemUsageID

								UPDATE s01tblItemInventory 
								SET Qty = Qty - @inputQty, UpdatedBy = @UserID, QtyLastUpdated = @CurrentUTCTime 
								WHERE ItemInventoryID = @UsageItemID

								INSERT INTO s01tblUpdateBarcodeItemInventoryLog( ItemInventoryID, QtyInHand, QtyLastUpdated, QtyUsed 
														, QtyReturned, QtyDeleted, QtyStocked, QtyWasted, CutOffTime, UpdatedQtyInHand )
								SELECT ItemInventoryID, Qty + @inputQty, QtyLastUpdated, @inputQty, 0, 0, 0, 0, @CurrentUTCTime, Qty 
								FROM s01tblItemInventory
								WHERE ItemInventoryID = @UsageItemID

								INSERT INTO s01tblItemHistory ( ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAVisitID, MAScheduleID, UsedBy, DateUsed, UnitPrice, UOMCode
													, OverrideID, OverrideNotes, SessionID, UCDM, BillingStatusID, ItemStatusID, UpdatedBy, LastActivityDate, ItemEventID, Qty, EntryTypeID ) 
								SELECT ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAVisitID, MAScheduleID, UsedBy, DateUsed, UnitPrice, UOMCode, @OverrideID, @OverrideNotes, @SessionID 
													, UCDM, BillingStatusID, NULL, @UserID, @CurrentUTCTime, @ItemEventID, @inputQty, @EntryTypeID 
								FROM s01tblItemUsage 
								WHERE ItemUsageID = @ItemUsageID

								SELECT @ItemHistoryID = SCOPE_IDENTITY()
								INSERT INTO s01tblItemHistoryExtn (ItemHistoryID, LocationID ) 
								SELECT ItemHistoryID, @LocationID
								FROM s01tblItemHistory 
								WHERE ItemHistoryID = @ItemHistoryID

								INSERT INTO tblInventoryLog ( ItemHistoryID, InvntoryID, ProcessStatus, ClusterID, LocationID, UpdatedBy, EntryTypeID, EnteredDate )
								SELECT ItemHistoryID, UsageItemID, 'P', @DeviceID, @LocationID, @UserID, @EntryTypeID, @CurrentUTCTime 
								FROM s01tblItemHistory 
								WHERE ItemHistoryID = @ItemHistoryID	
							END
						END
						IF ( @BarcodeSettings = 'FALSE')
						BEGIN
							IF ( @CurrentPatientID = @MAPatientID ) AND ( @MAPatientID IS NOT NULL )
							BEGIN
						
								IF EXISTS ( SELECT 1 
											FROM s01tblItemInventory I 
												INNER JOIN s01tblProducts P ON I.ProductID = P.ProductID 
												INNER JOIN s01tblItemUsage U ON I.ItemInventoryID = U.UsageItemID
											WHERE I.ProductID = @ProductID 
												AND I.LocationID = @LocationID 
												AND U.SessionID = @SessionID 
												AND U.MAPatientID = @MAPatientID)
								BEGIN
							
									SELECT @QTY = U.Qty+@BarCodeQty, @ItemInventoryID = I.ItemInventoryID  
									FROM s01tblItemUsage U 
										INNER JOIN s01tblItemInventory I ON I.ItemInventoryID = U.UsageItemID 
									WHERE U.SessionID = @SessionID 
										AND I.ProductID = @ProductID 
										AND MAPatientID = @MAPatientID
								
									UPDATE s01tblItemUsage 
									SET Qty = @QTY, UpdatedBy = @UserID, LastActivityDate = @CurrentUTCTime, OverrideID = @OverrideID, OverrideNotes = @OverrideNotes
									WHERE UsageItemID = @ItemInventoryID 
										AND SessionID = @SessionID	
										AND MAPatientID = @MAPatientID 
										AND UsageTypeID = 2
							
									UPDATE s01tblItemInventory 
									SET Qty = Qty - @BarCodeQty, UpdatedBy = @UserID, QtyLastUpdated = @CurrentUTCTime 
									WHERE ItemInventoryID = @ItemInventoryID 

									INSERT INTO s01tblUpdateBarcodeItemInventoryLog( ItemInventoryID, QtyInHand, QtyLastUpdated, QtyUsed 
															, QtyReturned, QtyDeleted, QtyStocked, QtyWasted, CutOffTime, UpdatedQtyInHand )
									SELECT ItemInventoryID, Qty + @BarCodeQty, QtyLastUpdated, @BarCodeQty, 0, 0, 0, 0, @CurrentUTCTime, Qty 
									FROM s01tblItemInventory
									WHERE ItemInventoryID = @ItemInventoryID

									SELECT @ItemUsageID = ItemUsageID 
									FROM s01tblItemUsage 
									WHERE UsageItemID = @ItemInventoryID 
										AND SessionID = @SessionID

									INSERT INTO s01tblItemHistory ( ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAVisitID, MAScheduleID, UsedBy, DateUsed, UnitPrice, UOMCode
														, OverrideID, OverrideNotes, SessionID, UCDM, BillingStatusID, ItemStatusID, UpdatedBy, LastActivityDate, ItemEventID, Qty, EntryTypeID ) 
									SELECT ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAVisitID, MAScheduleID, UsedBy, DateUsed, UnitPrice, UOMCode, @OverrideID, @OverrideNotes, @SessionID 
														, UCDM, BillingStatusID, NULL, @UserID, @CurrentUTCTime, @ItemEventID, @BarCodeQty, @EntryTypeID 
									FROM s01tblItemUsage 
									WHERE ItemUsageID = @ItemUsageID
					
									SELECT @ItemHistoryID = SCOPE_IDENTITY()
									INSERT INTO s01tblItemHistoryExtn (ItemHistoryID, LocationID ) 
									SELECT ItemHistoryID, @LocationID 
									FROM s01tblItemHistory 
									WHERE ItemHistoryID = @ItemHistoryID

									INSERT INTO tblInventoryLog ( ItemHistoryID, InvntoryID, ProcessStatus, ClusterID, LocationID, UpdatedBy, EntryTypeID, EnteredDate )
									SELECT ItemHistoryID, UsageItemID, 'P', @DeviceID, @LocationID, @UserID, @EntryTypeID, @CurrentUTCTime 
									FROM s01tblItemHistory 
									WHERE ItemHistoryID = @ItemHistoryID	
								END
								IF NOT EXISTS ( SELECT 1 
												FROM s01tblItemInventory I 
													INNER JOIN s01tblProducts P ON I.ProductID = P.ProductID 
													INNER JOIN s01tblItemUsage U ON I.ItemInventoryID = U.UsageItemID
												WHERE I.ProductID = @ProductID 
													AND I.LocationID = @LocationID 
													AND U.SessionID = @SessionID 
													AND U.MAPatientid = @MAPatientID ) 				
								BEGIN

									IF  EXISTS ( 
													 SELECT 1 
													 FROM s01tblItemInventory I 
														INNER JOIN s01tblProducts P ON I.ProductID = P.ProductID 
														INNER JOIN s01tblItemUsage U ON I.ItemInventoryID = U.UsageItemID
													 WHERE I.ProductID = @ProductID 
														AND I.LocationID = @LocationID 
														AND U.MAPatientID = @MAPatientID
														AND U.MAScheduleID = @MAScheduleID
												 )
									BEGIN

										SELECT @QTY = U.Qty+@BarCodeQty, @ItemInventoryID = I.ItemInventoryID  
										FROM s01tblItemUsage U 
											INNER JOIN s01tblItemInventory I ON I.ItemInventoryID = U.UsageItemID 
										WHERE I.ProductID = @ProductID 
											AND MAPatientID = @MAPatientID
											AND U.MAScheduleID = @MAScheduleID

										UPDATE s01tblItemUsage 
										SET Qty = @Qty, UpdatedBy = @UserID, LastActivityDate = @CurrentUTCTime, OverrideID = @OverrideID, OverrideNotes = @OverrideNotes
										WHERE UsageItemID = @ItemInventoryID 
											AND MAPatientID = @MAPatientID 
											AND UsageTypeID = 2

											

									--SELECT @ItemUsageID = SCOPE_IDENTITY()
									SELECT @ItemUsageID = ItemUsageID
									FROM s01tblItemUsage 
									WHERE  UsageItemID = @ItemInventoryID 
											AND MAPatientID = @MAPatientID 
											AND UsageTypeID = 2
									SELECT @UsageItemID = @ItemInventoryID
									--FROM s01tblItemUsage 
									--WHERE ItemUsageID = @ItemUsageID

									UPDATE s01tblItemInventory 
									SET Qty = Qty - @BarCodeQty, UpdatedBy = @UserID, QtyLastUpdated = @CurrentUTCTime 
									WHERE ItemInventoryID = @UsageItemID 
								
									INSERT INTO s01tblUpdateBarcodeItemInventoryLog( ItemInventoryID, QtyInHand, QtyLastUpdated, QtyUsed 
															, QtyReturned, QtyDeleted, QtyStocked, QtyWasted, CutOffTime, UpdatedQtyInHand )
									SELECT ItemInventoryID, Qty + @BarCodeQty, QtyLastUpdated, @BarCodeQty, 0, 0, 0, 0, @CurrentUTCTime, Qty 
									FROM s01tblItemInventory
									WHERE ItemInventoryID = @UsageItemID

									INSERT INTO s01tblItemHistory ( ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAVisitID, MAScheduleID, UsedBy, DateUsed, UnitPrice, UOMCode
														, OverrideID, OverrideNotes, SessionID, UCDM, BillingStatusID, ItemStatusID, UpdatedBy, LastActivityDate, ItemEventID, Qty, EntryTypeID ) 
									SELECT ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAVisitID, MAScheduleID, UsedBy, DateUsed, UnitPrice, UOMCode, @OverrideID, @OverrideNotes, @SessionID 
														, UCDM, BillingStatusID, NULL, @UserID, @CurrentUTCTime, @ItemEventID, @BarCodeQty, @EntryTypeID 
									FROM s01tblItemUsage 
									WHERE ItemUsageID = @ItemUsageID

									SELECT @ItemHistoryID = SCOPE_IDENTITY()
									INSERT INTO s01tblItemHistoryExtn (ItemHistoryID, LocationID ) 
									SELECT ItemHistoryID, @LocationID 
									FROM s01tblItemHistory 
									WHERE ItemHistoryID = @ItemHistoryID

									INSERT INTO tblInventoryLog ( ItemHistoryID, InvntoryID, ProcessStatus, ClusterID, LocationID, UpdatedBy, EntryTypeID, EnteredDate )
									SELECT ItemHistoryID, UsageItemID, 'P', @DeviceID, @LocationID, @UserID, @EntryTypeID, @CurrentUTCTime 
									FROM s01tblItemHistory 
									WHERE ItemHistoryID = @ItemHistoryID

									END
									ELSE
									BEGIN
										INSERT INTO s01tblItemUsage ( MAPatientID, MAVisitID, MAScheduleID, UsedBy, DateUsed, Qty, UnitPrice, UOMCode, BillingStatusID, OverrideID, OverrideNotes
																, UpdatedBy, LastActivityDate, UsageTypeID, UsageItemID, SessionID, EntryTypeID )
										SELECT @MAPatientID, @MAVisitID, @MAScheduleID, @UserID, @CurrentUtcTime, @BarCodeQty, [Ref Price], UOMCode, 1, @OverrideID, @OverrideNotes, @UserID
																, @CurrentUTCTime, 2, @ItemInventoryID, @SessionID, @EntryTypeID 
										FROM s01tblProducts 
										WHERE ProductID = @ProductID

								

									SELECT @ItemUsageID = SCOPE_IDENTITY()
									SELECT @UsageItemID = UsageItemID 
									FROM s01tblItemUsage 
									WHERE ItemUsageID = @ItemUsageID

									UPDATE s01tblItemInventory 
									SET Qty = Qty - @BarCodeQty, UpdatedBy = @UserID, QtyLastUpdated = @CurrentUTCTime 
									WHERE ItemInventoryID = @UsageItemID 
								
									INSERT INTO s01tblUpdateBarcodeItemInventoryLog( ItemInventoryID, QtyInHand, QtyLastUpdated, QtyUsed 
															, QtyReturned, QtyDeleted, QtyStocked, QtyWasted, CutOffTime, UpdatedQtyInHand )
									SELECT ItemInventoryID, Qty + @BarCodeQty, QtyLastUpdated, @BarCodeQty, 0, 0, 0, 0, @CurrentUTCTime, Qty 
									FROM s01tblItemInventory
									WHERE ItemInventoryID = @UsageItemID

									INSERT INTO s01tblItemHistory ( ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAVisitID, MAScheduleID, UsedBy, DateUsed, UnitPrice, UOMCode
														, OverrideID, OverrideNotes, SessionID, UCDM, BillingStatusID, ItemStatusID, UpdatedBy, LastActivityDate, ItemEventID, Qty, EntryTypeID ) 
									SELECT ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAVisitID, MAScheduleID, UsedBy, DateUsed, UnitPrice, UOMCode, @OverrideID, @OverrideNotes, @SessionID 
														, UCDM, BillingStatusID, NULL, @UserID, @CurrentUTCTime, @ItemEventID, @BarCodeQty, @EntryTypeID 
									FROM s01tblItemUsage 
									WHERE ItemUsageID = @ItemUsageID

									SELECT @ItemHistoryID = SCOPE_IDENTITY()
									INSERT INTO s01tblItemHistoryExtn (ItemHistoryID, LocationID ) 
									SELECT ItemHistoryID, @LocationID 
									FROM s01tblItemHistory 
									WHERE ItemHistoryID = @ItemHistoryID

									INSERT INTO tblInventoryLog ( ItemHistoryID, InvntoryID, ProcessStatus, ClusterID, LocationID, UpdatedBy, EntryTypeID, EnteredDate )
									SELECT ItemHistoryID, UsageItemID, 'P', @DeviceID, @LocationID, @UserID, @EntryTypeID, @CurrentUTCTime 
									FROM s01tblItemHistory 
									WHERE ItemHistoryID = @ItemHistoryID
									END
								END
							END
							IF ( @CurrentPatientID IS NULL AND @MAPatientID IS NOT NULL )
							BEGIN

									INSERT INTO s01tblItemUsage ( MAPatientID, MAVisitID, MAScheduleID, UsedBy, DateUsed, Qty, UnitPrice, UOMCode, BillingStatusID, OverrideID, OverrideNotes
															, UpdatedBy, LastActivityDate, UsageTypeID, UsageItemID, SessionID, EntryTypeID )
									SELECT @MAPatientID, @MAVisitID, @MAScheduleID, @UserID, @CurrentUtcTime, @BarCodeQty, [Ref Price], UOMCode, 1, @OverrideID, @OverrideNotes, @UserID
															, @CurrentUTCTime, 2, @ItemInventoryID, @SessionID, @EntryTypeID 
									FROM s01tblProducts 
									WHERE ProductID = @ProductID

									SELECT @ItemUsageID = SCOPE_IDENTITY()
									SELECT @UsageItemID = UsageItemID 
									FROM s01tblItemUsage 
									WHERE ItemUsageID = @ItemUsageID
							
									UPDATE s01tblItemInventory 
									SET Qty = Qty - @BarCodeQty, UpdatedBy = @UserID, QtyLastUpdated = @CurrentUTCTime 
									WHERE ItemInventoryID = @UsageItemID 

									INSERT INTO s01tblUpdateBarcodeItemInventoryLog( ItemInventoryID, QtyInHand, QtyLastUpdated, QtyUsed 
															, QtyReturned, QtyDeleted, QtyStocked, QtyWasted, CutOffTime, UpdatedQtyInHand )
									SELECT ItemInventoryID, Qty + @BarCodeQty, QtyLastUpdated, @BarCodeQty, 0, 0, 0, 0, @CurrentUTCTime, Qty 
									FROM s01tblItemInventory
									WHERE ItemInventoryID = @UsageItemID

									INSERT INTO s01tblItemHistory ( ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAVisitID, MAScheduleID, UsedBy, DateUsed, UnitPrice, UOMCode
														, OverrideID, OverrideNotes, SessionID, UCDM, BillingStatusID, ItemStatusID, UpdatedBy, LastActivityDate, ItemEventID, Qty, EntryTypeID ) 
									SELECT ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAVisitID, MAScheduleID, UsedBy, DateUsed, UnitPrice, UOMCode, @OverrideID, @OverrideNotes, @SessionID 
														, UCDM, BillingStatusID, NULL, @UserID, @CurrentUTCTime, @ItemEventID, @BarCodeQty, @EntryTypeID 
									FROM s01tblItemUsage 
									WHERE ItemUsageID = @ItemUsageID

									SELECT @ItemHistoryID = SCOPE_IDENTITY()
									INSERT INTO s01tblItemHistoryExtn (ItemHistoryID, LocationID ) 
									SELECT ItemHistoryID, @LocationID 
									FROM s01tblItemHistory 
									WHERE ItemHistoryID = @ItemHistoryID

									INSERT INTO tblInventoryLog ( ItemHistoryID, InvntoryID, ProcessStatus, ClusterID, LocationID, UpdatedBy, EntryTypeID, EnteredDate )
									SELECT ItemHistoryID, UsageItemID, 'P', @DeviceID, @LocationID, @UserID, @EntryTypeID, @CurrentUTCTime 
									FROM s01tblItemHistory 
									WHERE ItemHistoryID = @ItemHistoryID	
								--END
							END

							IF ( @MAPatientID IS NULL )
							BEGIN
								PRINT 'FALSE'
								IF EXISTS ( SELECT 1 
											FROM s01tblItemInventory I 
												INNER JOIN s01tblProducts P ON I.ProductID = P.ProductID 
												INNER JOIN s01tblItemUsage U ON I.ItemInventoryID = U.UsageItemID
											WHERE I.ProductID = @ProductID 
												AND I.LocationID = @LocationID 
												AND U.SessionID = @SessionID 
												AND MAPatientID IS NULL )
								BEGIN
									SELECT @QTY = U.Qty+1, @ItemInventoryID = I.ItemInventoryID  
									FROM s01tblItemUsage U 
										INNER JOIN s01tblItemInventory I ON I.ItemInventoryID = U.UsageItemID 
									WHERE U.SessionID = @SessionID 
										AND I.ProductID = @ProductID

									UPDATE s01tblItemUsage 
									SET Qty = @Qty, UpdatedBy = @UserID, LastActivityDate = @CurrentUTCTime, OverrideID = @OverrideID, OverrideNotes = @OverrideNotes
									WHERE UsageItemID = @ItemInventoryID AND SessionID = @SessionID	AND MAPatientID IS NULL 
							
									UPDATE s01tblItemInventory 
									SET Qty = Qty - @BarCodeQty, UpdatedBy = @UserID, QtyLastUpdated = @CurrentUTCTime 
									WHERE ItemInventoryID = @ItemInventoryID 

									INSERT INTO s01tblUpdateBarcodeItemInventoryLog( ItemInventoryID, QtyInHand, QtyLastUpdated, QtyUsed 
															, QtyReturned, QtyDeleted, QtyStocked, QtyWasted, CutOffTime, UpdatedQtyInHand )
									SELECT ItemInventoryID, Qty + @BarCodeQty, QtyLastUpdated, @BarCodeQty, 0, 0, 0, 0, @CurrentUTCTime, Qty 
									FROM s01tblItemInventory
									WHERE ItemInventoryID = @ItemInventoryID

									SELECT @ItemUsageID = ItemUsageID 
									FROM s01tblItemUsage 
									WHERE UsageItemID = @ItemInventoryID AND SessionID = @SessionID

									INSERT INTO s01tblItemHistory ( ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAVisitID, MAScheduleID, UsedBy, DateUsed, UnitPrice, UOMCode
														, OverrideID, OverrideNotes, SessionID, UCDM, BillingStatusID, ItemStatusID, UpdatedBy, LastActivityDate, ItemEventID, Qty, EntryTypeID ) 
									SELECT ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAVisitID, MAScheduleID, UsedBy, DateUsed, UnitPrice, UOMCode, @OverrideID, @OverrideNotes, @SessionID 
														, UCDM, BillingStatusID, NULL, @UserID, @CurrentUTCTime, @ItemEventID, @BarCodeQty, @EntryTypeID 
									FROM s01tblItemUsage 
									WHERE ItemUsageID = @ItemUsageID
					
									SELECT @ItemHistoryID = SCOPE_IDENTITY()
									INSERT INTO s01tblItemHistoryExtn (ItemHistoryID, LocationID ) 
									SELECT ItemHistoryID, @LocationID 
									FROM s01tblItemHistory 
									WHERE ItemHistoryID = @ItemHistoryID

									INSERT INTO tblInventoryLog ( ItemHistoryID, InvntoryID, ProcessStatus, ClusterID, LocationID, UpdatedBy, EntryTypeID, EnteredDate )
									SELECT ItemHistoryID, UsageItemID, 'P', @DeviceID, @LocationID, @UserID, @EntryTypeID, @CurrentUTCTime 
									FROM s01tblItemHistory 
									WHERE ItemHistoryID = @ItemHistoryID	
								END
								IF NOT EXISTS ( SELECT 1 
												FROM s01tblItemInventory I 
													INNER JOIN s01tblProducts P ON I.ProductID = P.ProductID 
													INNER JOIN s01tblItemUsage U ON I.ItemInventoryID = U.UsageItemID
												WHERE I.ProductID = @ProductID 
													AND I.LocationID = @LocationID 
													AND U.SessionID = @SessionID 
													AND MAPatientID IS NULL ) 				
								BEGIN
									INSERT INTO s01tblItemUsage ( MAPatientID, MAVisitID, MAScheduleID, UsedBy, DateUsed, Qty, UnitPrice, UOMCode, BillingStatusID, OverrideID, OverrideNotes
															, UpdatedBy, LastActivityDate, UsageTypeID, UsageItemID, SessionID, EntryTypeID )
									SELECT @MAPatientID, @MAVisitID, @MAScheduleID, @UserID, @CurrentUtcTime, 1, [Ref Price], UOMCode, 1, @OverrideID, @OverrideNotes, @UserID
															, @CurrentUTCTime, 2, @ItemInventoryID, @SessionID, @EntryTypeID 
									FROM s01tblProducts 
									WHERE ProductID = @ProductID

									SELECT @ItemUsageID = SCOPE_IDENTITY()

									SELECT @UsageItemID = UsageItemID 
									FROM s01tblItemUsage 
									WHERE ItemUsageID = @ItemUsageID

									UPDATE s01tblItemInventory 
									SET Qty = Qty - 1, UpdatedBy = @UserID, QtyLastUpdated = @CurrentUTCTime 
									WHERE ItemInventoryID = @UsageItemID 

									INSERT INTO s01tblUpdateBarcodeItemInventoryLog( ItemInventoryID, QtyInHand, QtyLastUpdated, QtyUsed 
															, QtyReturned, QtyDeleted, QtyStocked, QtyWasted, CutOffTime, UpdatedQtyInHand )
									SELECT ItemInventoryID, Qty + 1, QtyLastUpdated, 1, 0, 0, 0, 0, @CurrentUTCTime, Qty 
									FROM s01tblItemInventory
									WHERE ItemInventoryID = @UsageItemID

									INSERT INTO s01tblItemHistory ( ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAVisitID, MAScheduleID, UsedBy, DateUsed, UnitPrice, UOMCode
														, OverrideID, OverrideNotes, SessionID, UCDM, BillingStatusID, ItemStatusID, UpdatedBy, LastActivityDate, ItemEventID, Qty, EntryTypeID ) 
									SELECT ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAVisitID, MAScheduleID, UsedBy, DateUsed, UnitPrice, UOMCode, @OverrideID, @OverrideNotes, @SessionID 
														, UCDM, BillingStatusID, NULL, @UserID, @CurrentUTCTime, @ItemEventID, 1, @EntryTypeID 
									FROM s01tblItemUsage 
									WHERE ItemUsageID = @ItemUsageID

									SELECT @ItemHistoryID = SCOPE_IDENTITY()
									INSERT INTO s01tblItemHistoryExtn (ItemHistoryID, LocationID ) 
									SELECT ItemHistoryID, @LocationID 
									FROM s01tblItemHistory 
									WHERE ItemHistoryID = @ItemHistoryID

									INSERT INTO tblInventoryLog ( ItemHistoryID, InvntoryID, ProcessStatus, ClusterID, LocationID, UpdatedBy, EntryTypeID, EnteredDate )
									SELECT ItemHistoryID, UsageItemID, 'P', @DeviceID, @LocationID, @UserID, @EntryTypeID, @CurrentUTCTime 
									FROM s01tblItemHistory 
									WHERE ItemHistoryID = @ItemHistoryID	
								END
							END
						END
						PRINT 'Executing task iteration ' + CAST(@Counter AS VARCHAR(10));

						-- Increment the counter
						SET @Counter = @Counter + 1;
					END
				END

				IF ( @EventType = 'ReturnBarcodeInventory' )
				BEGIN

					--PRINT 'Barcode Return'

					if exists (select top 1 * from s01tblitemusage where usageitemid=@UsageItemID)
					begin
						set @IsUsageExists='Available';
					end
	
					IF @IsUsageExists IS NULL
					BEGIN
						SELECT 'Fail' AS DataSetName, 'Item already stocked in cabinet.' AS MSG
					RETURN
					END

					EXECUTE  usp827WebConsoleReturnBarcodeItems @Event = @Event
							, @udtSystemData = @udtSystemData
							, @udtEventData = @udtEventData
							, @ProductID = @ProductID
							, @ItemEventID = @ItemEventID
							, @OverrideID = @OverrideID
		
				END
				

			--COMMIT TRANSACTION	
	END TRY
	BEGIN CATCH
		--IF XACT_STATE() <> 0
			--ROLLBACK TRANSACTION
			SET @Msg = 'Error in updating Barcode Inventory';
		EXECUTE s01uspLogError
	END CATCH
	SET NOCOUNT OFF
GO
/****** Object:  StoredProcedure [dbo].[usp826WebConsoleSaveBarcodeMasterFDAProducts]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO



 

/************************************************************************************************************************
Name				: usp826WebConsoleSaveBarcodeMasterFDAProducts
Version				: ********
Purpose				: To save the product details from MASTER/FDA to Localhost for Barcode clusters
Author				: Subramanya Rao
Date Created 		: 24th march 2021
Application Affected: IriSupply 6.0
---------------------------------------------------------------------------------------------------------------------------
Release Notes		:

 

----------------------------------------------------------------------------------------------------------------------------
-- SP CALL
declare @p2 dbo.udtSPParam
insert into @p2 values(N'EntryTypeID',N'1')
insert into @p2 values(N'DeviceID',N'3')
insert into @p2 values(N'ComputerName',N'MAILPTP52')
insert into @p2 values(N'Domain',N'maspects.com')

 

declare @p3 dbo.udtSPParam
insert into @p3 values(N'UserID',N'2')
insert into @p3 values(N'SessionID',N'20210325120110970')
insert into @p3 values(N'EventType',N'RemoveBarcodeInventory')
insert into @p3 values(N'EventInPut1',N'bbaasss240ENSP35015UX') -- Scanned Full barcode
insert into @p3 values(N'EventInput3',N'Remove Item')
insert into @p3 values(N'EventInput4',N'TTT')

 

--If DataSetName = Fail and MSG is	Product is not registered then send below 4 parameters
--insert into @p3 values(N'CatNo',N'sscbazabcabababxxabc')
--insert into @p3 values(N'Description',N'test')
--insert into @p3 values(N'Manufacturer',N'test')
--insert into @p3 values(N'BrandName',N'test')

 

insert into @p3 values(N'EventOutPut',N'FetchBarcodeInventoryList')

 

exec usp821WebConsoleExecuteAppEvent @Event='UpdateBarcodeInventory',@udtSystemData=@p2,@udtEventData=@p3

 

 

--------------------------------------------------------------------------------------------------------*/
CREATE Procedure [dbo].[usp826WebConsoleSaveBarcodeMasterFDAProducts]
	@Event VARCHAR(255)
	, @udtSystemData udtSPParam READONLY
	, @udtEventData udtSPParam READONLY
	, @ProductID INT
AS

 

SET NOCOUNT ON;

 

DECLARE @Msg VARCHAR(200), @ProcessedBarcondeNo VARCHAR(250),  @FullBarcodeNo VARCHAR(250), @ProcessedCatNo VARCHAR (500), @SessionID BIGINT, @ItemStatusID INT, @ItemEventID INT
DECLARE @CatNo VARCHAR (200),  @ItemID INT, @RemovedUserName VARCHAR(100), @RFID VARCHAR (500), @UserID INT, @CurrentUtcTime DATETIME, @ItemUsageID INT, @ItemHistoryID INT
DECLARE @MAPatientID VARCHAR(50), @MAScheduleID INT, @MAVisitID INT, @OverrideId INT, @OverrideNotes VARCHAR(255), @PhysicianID VARCHAR(50), @BillingStatusID INT, @EntryTypeID INT
		, @InsertFDAProduct INT, @InsertBracodeFDA INT,  @Description VARCHAR(50), @Manufacturer VARCHAR(50), @BrandName VARCHAR(50), @MastProductId INT
		, @ScannedBarcode VARCHAR(70), @MCatNo VARCHAR(50)

 

SET XACT_ABORT ON;
BEGIN TRY
BEGIN TRANSACTION

 

	--SELECT @ProcessedBarcodeNo =Value FROM  @udtEventData WHERE Property = 'ProcessedBarcodeNo'
	--SELECT @FullBarcodeNo =Value FROM  @udtEventData WHERE Property = 'FullBarcodeNo'
	--SELECT @ProcessedCatNo =Value FROM  @udtEventData WHERE Property = 'ProcessedCatNo'

	SELECT @ScannedBarcode =Value FROM  @udtEventData WHERE Property = 'EventInPut1'

 

	--SELECT @CatNo = REPLACE( REPLACE( REPLACE ([Cat No], '-', ''), '.', ''), ' ', '' ) 
	--FROM [$(IrisDB)].dbo.tblBarcodeRuleProduct 
	--WHERE [Barcode No] = @ScannedBarcode

	SELECT @CatNo = [Cat No] ,@Manufacturer = Manufacturer , @Description =[Description 1], @BrandName = [Brand Name]
	FROM s01tblProducts 
	WHERE ProductID = @ProductID

	--SELECT @CatNo =Value FROM  @udtEventData WHERE Property = 'CatNo'
	--SELECT @Description =  Value FROM  @udtEventData WHERE Property = 'Description'	
	--SELECT @Manufacturer =  Value FROM  @udtEventData WHERE Property = 'Manufacturer'	
	--SELECT @BrandName =  Value FROM  @udtEventData WHERE Property = 'BrandName'

	IF @CatNo IS NULL 
	BEGIN
		IF EXISTS ( SELECT 1 FROM s01tblBarcodeRuleProductMaster WHERE [Barcode No] = @ScannedBarcode)
		BEGIN

 

				SELECT @MCatNo = [Cat No]
				FROM s01tblBarcodeRuleProductMaster
				WHERE [Barcode No] = @ScannedBarcode

 

				INSERT INTO s01tblBarcodeRuleProduct ([Barcode No], [Cat No])
				SELECT [Barcode No], [Cat No]
				FROM s01tblBarcodeRuleProductMaster
				WHERE [Barcode No] = @ScannedBarcode

 

				PRINT 'Inserted into [IrisDB].dbo.tblBarcodeRuleProduct from MASTER'

 

		END

 

		IF EXISTS ( SELECT 1 FROM s01tblProductsMaster WHERE replace(replace([Cat No],'-',''),'.','') = replace(replace(@MCatNo,'-',''),'.',''))
		BEGIN

 

				INSERT INTO s01tblProducts 
							( [Cat No], NDC, [Brand Name], [Other Name], [Description 1], [Description 2], [Description 3], Size, Material, Manufacturer, [Ref Price], Picture, QtyPerPkg
							, RFID, [SKU No],[ID 1], [ID 2], [ID 3], [ID 4], [ID 5], [Entered By], [Computer Name], Domain, [Entered Date], ReorderTypeID, [Notify Interval]
							, [Notify Time], [Notify Sent], ProductTypeID, PCDM, MMID, ProductCategoryID, ProductSubCategoryID, ProductGroupID, EntryTypeID
							, [Min Stock], [Max Stock])

 

				SELECT        [Cat No], NDC, [Brand Name], [Other Name], [Description 1], [Description 2], [Description 3], Size, Material, Manufacturer, [Ref Price], Picture, QtyPerPkg
							, RFID, [SKU No],[ID 1], [ID 2], [ID 3], [ID 4], [ID 5], [Entered By], [Computer Name], Domain, [Entered Date], ReorderTypeID, [Notify Interval]
							, [Notify Time], [Notify Sent], ProductTypeID, PCDM, MMID, ProductCategoryID, ProductSubCategoryID, ProductGroupID, EntryTypeID
							, [Min Stock], [Max Stock]
				FROM s01tblProductsMaster
				WHERE [Cat No] = @MCatNo

 

				PRINT 'Inserted into [IrisDB].dbo.tblProducts from MASTER'

 

		END
	END
	
	IF ( @CatNo IS NOT NULL)
	BEGIN
			SET IDENTITY_INSERT s01tblProductsMaster ON		
		    INSERT INTO s01tblProductsMaster  
							( ProductID, [Cat No], [Brand Name], Manufacturer, [Description 1] ) 
		    SELECT MAX(ProductID)+1, @CatNo, @BrandName, @Manufacturer, @Description
		    FROM s01tblProductsMaster
			SET IDENTITY_INSERT s01tblProductsMaster OFF
			
 

			SELECT @MastProductId = ProductId 
			FROM s01tblProductsMaster 
			WHERE replace(replace([Cat No],'-',''),'.','') = @CatNo


 

			INSERT INTO s01tblProducts
						(  [Cat No], [Brand Name], Manufacturer, [Description 1] ) 
			SELECT  @CatNo, @BrandName, @Manufacturer, @Description

 

			
		--	INSERT INTO [iRISProductMaster].dbo.tblProductsMaster  
		--					( [Cat No], NDC, [Brand Name], [Other Name], [Description 1], [Description 2], [Description 3], Size, Material, Manufacturer, [Ref Price], Picture, QtyPerPkg
		--					, RFID, [SKU No],[ID 1], [ID 2], [ID 3], [ID 4], [ID 5], [Entered By], [Computer Name], Domain, [Entered Date], ReorderTypeID, [Notify Interval]
		--					, [Notify Time], [Notify Sent], ProductTypeID, PCDM, MMID, ProductCategoryID, ProductSubCategoryID, ProductGroupID, EntryTypeID
		--					, [Min Stock], [Max Stock], IsFromFDA)
		--	SELECT          , 1 ) --last col 1 for FDA

 

		--	--Select missing
		--	INSERT INTO [$(IrisDB)].dbo.tblProducts 
		--					( [Cat No], NDC, [Brand Name], [Other Name], [Description 1], [Description 2], [Description 3], Size, Material, Manufacturer, [Ref Price], Picture, QtyPerPkg
		--					, RFID, [SKU No],[ID 1], [ID 2], [ID 3], [ID 4], [ID 5], [Entered By], [Computer Name], Domain, [Entered Date], ReorderTypeID, [Notify Interval]
		--					, [Notify Time], [Notify Sent], ProductTypeID, PCDM, MMID, ProductCategoryID, ProductSubCategoryID, ProductGroupID, EntryTypeID
		--					, [Min Stock], [Max Stock])

 

			PRINT 'Inserted into [IrisDB].dbo.tblProducts and MASTER from FDA'

 

	END

 

	IF (  @CatNo IS NOT NULL )
	BEGIN
		--INSERT INTO [iRISProductMaster].dbo.s01tblBarcodeRuleProductMaster 
		--	   (RuleID, [Barcode No], [Cat No], IsFromFDA, ProductID)
		--SELECT MAX(RuleID)+1, @ScannedBarcode, @CatNo, 1, @MastProductId
		--FROM [iRISProductMaster].dbo.s01tblBarcodeRuleProductMaster

 

		INSERT INTO s01tblBarcodeRuleProduct 
			   ([Barcode No], [Cat No])
		SELECT  @ScannedBarcode, @CatNo

 

		PRINT 'Inserted into [IrisDB].dbo.tblBarcodeRuleProduct and tblBarcodeRuleProductMaster from FDA'
	END

 

	
COMMIT TRANSACTION		
END TRY

 

BEGIN CATCH
	IF XACT_STATE() <> 0
		ROLLBACK TRANSACTION
		SET @Msg = 'Error in Searching Product details';
	EXECUTE s01uspLogError
END CATCH
SET NOCOUNT OFF

GO
/****** Object:  StoredProcedure [dbo].[usp827WebConsoleReturnBarcodeItems]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE Procedure [dbo].[usp827WebConsoleReturnBarcodeItems]
	@Event VARCHAR(255)
	, @udtSystemData udtSPParam READONLY
	, @udtEventData udtSPParam READONLY
	, @ProductID INT = ''
	, @ItemEventID INT = ''
	, @OverrideID INT = ''
AS
	SET NOCOUNT ON;
		
		SET XACT_ABORT ON;
		BEGIN TRY
		BEGIN TRANSACTION

		DECLARE   @DeviceID INT, @ItemInventoryID INT, @LocationID INT, @LocationTypeID INT, @UserID INT, @CurrentUtcTime DATETIME, @EntryTypeID INT
		DECLARE   @MAScheduleID INT,  @Qty INT,@inputQty INT, @UsageItemID INT, @MAVisitID INT,  @MAPatientID INT, @ItemUsageID INT,  @SessionID varchar(50),  @ItemHistoryID  INT
		

		SELECT @DeviceID = Value FROM @udtSystemData WHERE Property = 'DeviceID'
		SELECT @UserID = Value FROM @udtEventData WHERE Property = 'UserID'
		SELECT @SessionID = Value FROM @udtEventData WHERE Property = 'SessionID'
		SELECT @EntryTypeID = Value FROM @udtSystemData WHERE Property = 'EntryTypeID'

		SELECT @LocationTypeID = [dbo].[udf700WebConsoleSUPFetchSettings] ( @DeviceId, 'BarcodeInventory_LocationType' )
		SELECT @LocationID = [dbo].[udf700WebConsoleSUPFetchSettings] ( @DeviceId, 'BarcodeInventory_Location' )

		SELECT @MAVisitID = Value FROM @udtEventData WHERE Property = 'MAVisitID' 
		SELECT @MAScheduleID = Value FROM @udtEventData WHERE Property = 'MAScheduleID' 
		SELECT @MAPatientID = Value FROM @udtEventData WHERE Property = 'MAPatientID'
		SELECT @inputQty = Value FROM @udtEventData WHERE Property = 'Qty'
	
		IF (SELECT [Setting Value] FROM s01tblSettingsCommon WHERE [Setting Name] = 'ENABLE_UTC_TIMEZONE') = 'TRUE'
		BEGIN
			SELECT @CurrentUTCTime = GETUTCDATE()
		END
		ELSE
		BEGIN
			SELECT @CurrentUTCTime = GETDATE()
		END


		SELECT  @ItemInventoryID = ItemInventoryID  
		FROM s01tblItemInventory 
		WHERE ProductID = @ProductID
			AND LocationID = @LocationID
			AND LocationTypeID = @LocationTypeID
		
		IF (@MAScheduleID IS NOT NULL)
		BEGIN
			
			SELECT TOP 1 @Qty = Qty, @UsageItemID = UsageItemID 
			FROM s01tblItemUsage 
			WHERE UsageItemId IN (
									SELECT ItemInventoryID 
									FROM s01tblItemInventory
									WHERE ItemInventoryID = @ItemInventoryID
									)
				AND MAScheduleID = @MAScheduleID
				AND MAVisitID = @MAVisitID
				AND MAPatientID = @MAPatientID
			ORDER BY LastActivityDate DESC

			SELECT @ItemUsageID = ItemUsageID 
			FROM s01tblItemUsage 
			WHERE UsageItemID = @ItemInventoryID 
				AND MAScheduleID = @MAScheduleID
				AND MAVisitID = @MAVisitID
				AND MAPatientID = @MAPatientID
		END

		ELSE 
		BEGIN
		
		if exists (select sessionid from s01tblItemUsage where SessionID = @SessionID and UsageItemId=@ItemInventoryID)
		begin
			SELECT @Qty = Qty, @UsageItemID = UsageItemID 
			FROM s01tblItemUsage 
			WHERE UsageItemId IN (
									SELECT ItemInventoryID 
									FROM s01tblItemInventory
									WHERE ItemInventoryID = @ItemInventoryID

									)
				AND SessionID = @SessionID
	
			SELECT @ItemUsageID = ItemUsageID 
			FROM s01tblItemUsage 
			WHERE UsageItemID = @ItemInventoryID 
				AND SessionID = @SessionID

				
		end
		else
		begin
			if(@MAScheduleID IS not NULL)
			begin
				SELECT top 1 @Qty = Qty, @UsageItemID = UsageItemID 
				FROM s01tblItemUsage 
				WHERE UsageItemId IN (
										SELECT ItemInventoryID 
										FROM s01tblItemInventory
										WHERE ItemInventoryID = @ItemInventoryID

										)
				and MAPatientID is not null
				order by DateUsed desc
					
	
				SELECT top 1 @ItemUsageID = ItemUsageID 
				FROM s01tblItemUsage 
				WHERE UsageItemID = @ItemInventoryID and MAPatientID is not null
				order by DateUsed desc
			end
			else
			begin
				SELECT top 1 @Qty = Qty, @UsageItemID = UsageItemID 
				FROM s01tblItemUsage 
				WHERE UsageItemId IN (
										SELECT ItemInventoryID 
										FROM s01tblItemInventory
										WHERE ItemInventoryID = @ItemInventoryID

										)
				and MAPatientID is null
				order by DateUsed desc
				if(@Qty is null)
				begin
						SELECT top 1 @Qty = Qty, @UsageItemID = UsageItemID 
						FROM s01tblItemUsage 
						WHERE UsageItemId IN (
											SELECT ItemInventoryID 
											FROM s01tblItemInventory
											WHERE ItemInventoryID = @ItemInventoryID

											)
						and MAPatientID is not null
						order by DateUsed desc
				end
				
				SELECT top 1 @ItemUsageID = ItemUsageID 
				FROM s01tblItemUsage 
				WHERE UsageItemID = @ItemInventoryID and MAPatientID is null
				order by DateUsed desc

				if(@ItemUsageID is null)
				begin
					SELECT top 1 @ItemUsageID = ItemUsageID 
				FROM s01tblItemUsage 
				WHERE UsageItemID = @ItemInventoryID and MAPatientID is not null
				order by DateUsed desc
				end
			end
				
		end
		END
								 
		IF ( @Qty = 1 )
		BEGIN
			
			INSERT INTO s01tblItemHistory ( ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAVisitID, MAScheduleID, BillingStatusID, UsedBy,  UnitPrice, UOMCode
									, OverrideID, OverrideNotes, DateUsed, SessionID, UpdatedBy, LastActivityDate, ItemEventID, Qty, EntryTypeID ) 
			SELECT ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAVisitID, MAScheduleID, BillingStatusID, UsedBy, UnitPrice, UOMCode, @OverrideID, OverrideNotes 
									, DateUsed, @SessionID , @UserID, @CurrentUTCTime, @ItemEventID, 1 , @EntryTypeID 
			FROM s01tblItemUsage 
			WHERE ItemUsageID = @ItemUsageID
					
			SELECT @ItemHistoryID = SCOPE_IDENTITY()
			INSERT INTO s01tblItemHistoryExtn (ItemHistoryID, LocationID ) 
			SELECT ItemHistoryID, @LocationID 
			FROM s01tblItemHistory 
			WHERE ItemHistoryID = @ItemHistoryID

			INSERT INTO tblInventoryLog ( ItemHistoryID, InvntoryID, ProcessStatus, ClusterID, LocationID, UpdatedBy, EntryTypeID, EnteredDate )
			SELECT ItemHistoryID, UsageItemID, 'P', @DeviceID, @LocationID, @UserID, @EntryTypeID, @CurrentUTCTime 
			FROM s01tblItemHistory 
			WHERE ItemHistoryID = @ItemHistoryID	
					
			UPDATE s01tblItemInventory 
			SET Qty = Qty + 1, UpdatedBy = @UserID, QtyLastUpdated = @CurrentUTCTime 
			WHERE ItemInventoryID = @ItemInventoryID
				AND LocationID = @LocationID
				AND LocationTypeID = @LocationTypeID

			INSERT INTO s01tblUpdateBarcodeItemInventoryLog( ItemInventoryID, QtyInHand, QtyLastUpdated, QtyUsed 
									, QtyReturned, QtyDeleted, QtyStocked, QtyWasted, CutOffTime, UpdatedQtyInHand )
			SELECT ItemInventoryID, Qty - 1, QtyLastUpdated, 0, 1, 0, 0, 0, @CurrentUTCTime, Qty 
			FROM s01tblItemInventory
			WHERE ItemInventoryID = @ItemInventoryID
			
			DELETE FROM s01tblItemUsage 
			WHERE ItemUsageID = @ItemUsageID		

			SELECT  ItemInventoryID, [Ref Price], UOMCode, [Cat No], [Description 1], Qty, ProdUnitID, PCDM, LocationID ,[Barcode No]
					FROM qry703WebConsoleProductCatalog 
					WHERE ItemInventoryID = @ItemInventoryID
		
			--SELECT 'Success' DataSetName, 'Item Returned to the Inventory' AS MSG
		END

		ELSE
		BEGIN
		
			IF (@MAScheduleID IS NOT NULL)
			BEGIN
				UPDATE s01tblItemUsage 
				SET Qty = @Qty - @inputQty, UpdatedBy = @UserID, LastActivityDate = @CurrentUTCTime, OverrideID = @OverrideID
				WHERE ItemUsageID = @ItemUsageID
					AND MAScheduleID = @MAScheduleID
					AND MAVisitID = @MAVisitID
					AND MAPatientID = @MAPatientID
			END
			ELSE
			BEGIN
			
				UPDATE s01tblItemUsage 
				SET Qty = @Qty - @inputQty, UpdatedBy = @UserID, LastActivityDate = @CurrentUTCTime, OverrideID = @OverrideID
				WHERE ItemUsageID = @ItemUsageID
					--AND SessionID = @SessionID
			END
						
			UPDATE s01tblItemInventory 
			SET Qty = Qty + @inputQty, UpdatedBy = @UserID, QtyLastUpdated = @CurrentUTCTime 
			WHERE ItemInventoryID = @ItemInventoryID
				AND LocationID = @LocationID
				AND LocationTypeID = @LocationTypeID

			INSERT INTO s01tblUpdateBarcodeItemInventoryLog( ItemInventoryID, QtyInHand, QtyLastUpdated, QtyUsed 
									, QtyReturned, QtyDeleted, QtyStocked, QtyWasted, CutOffTime, UpdatedQtyInHand )
			SELECT ItemInventoryID, Qty - @inputQty, QtyLastUpdated, 0, @inputQty, 0, 0, 0, @CurrentUTCTime, Qty 
			FROM s01tblItemInventory
			WHERE ItemInventoryID = @ItemInventoryID
				
			INSERT INTO s01tblItemHistory ( ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAVisitID, MAScheduleID, BillingStatusID, UsedBy,  UnitPrice, UOMCode
										, OverrideID, OverrideNotes, DateUsed, SessionID, UpdatedBy, LastActivityDate, ItemEventID, Qty, EntryTypeID ) 
			SELECT ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAVisitID, MAScheduleID, BillingStatusID, UsedBy, UnitPrice, UOMCode, @OverrideID, OverrideNotes 
										, DateUsed, @SessionID , @UserID, @CurrentUTCTime, @ItemEventID, @inputQty, @EntryTypeID 
			FROM s01tblItemUsage 
			WHERE ItemUsageID = @ItemUsageID
		
			SELECT @ItemHistoryID = SCOPE_IDENTITY()
			INSERT INTO s01tblItemHistoryExtn (ItemHistoryID, LocationID ) 
			SELECT ItemHistoryID, @LocationID 
			FROM s01tblItemHistory 
			WHERE ItemHistoryID = @ItemHistoryID

			INSERT INTO tblInventoryLog ( ItemHistoryID, InvntoryID, ProcessStatus, ClusterID, LocationID, UpdatedBy, EntryTypeID, EnteredDate )
			SELECT ItemHistoryID, UsageItemID, 'P', @DeviceID, @LocationID, @UserID, @EntryTypeID, @CurrentUTCTime 
			FROM s01tblItemHistory 
			WHERE ItemHistoryID = @ItemHistoryID	

			SELECT  ItemInventoryID, [Ref Price], UOMCode, [Cat No], [Description 1], Qty, ProdUnitID, PCDM, LocationID ,[Barcode No]
					FROM qry703WebConsoleProductCatalog 
					WHERE ItemInventoryID = @ItemInventoryID

		--	SELECT 'Success' DataSetName, 'Item Returned to the Inventory' AS MSG

			
		END					

	COMMIT TRANSACTION	
	END TRY
	BEGIN CATCH
		IF XACT_STATE() <> 0
			ROLLBACK TRANSACTION
		EXECUTE s01uspLogError
	END CATCH
	SET NOCOUNT OFF
GO
/****** Object:  StoredProcedure [dbo].[usp850WebConsoleGetUserReports]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE Procedure [dbo].[usp850WebConsoleGetUserReports]
@UserID int
as
begin 

Declare @UserGroupID int

set @UserGroupID = (select GroupID from s01tblUsers where UserID =@UserID)


IF (@UserGroupID <> 0) and (@UserGroupID <> 1) 
Begin
	select REPORT_ID as ReportID,REPORT_NAME as ReportName,DESCRIPTION,REPORT_PATH as ReportPath, isENABLED as Status 
	from s01tblReports r
	left join tblReportRBAC u on r.REPORT_ID=u.ReportID
	where u.UserID =@UserID
	order by REPORT_NAME
End
else
	exec [uspGetReports]
end
GO
/****** Object:  StoredProcedure [dbo].[usp851WebConsoleAddNewReport]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE Procedure [dbo].[usp851WebConsoleAddNewReport]
@ReportName varchar(100),
@Description varchar(100),
@ReportPath varchar(100),
@IsEnabled bit
as

insert into s01tblReports(REPORT_NAME,DESCRIPTION,REPORT_PATH,isENABLED,SUB_MODULE_ID)
select @ReportName,@Description,@ReportPath,@IsEnabled,69 
GO
/****** Object:  StoredProcedure [dbo].[usp852WebConsoleGetReportDetails]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE Procedure [dbo].[usp852WebConsoleGetReportDetails]
@ReportID int
as
begin 

select REPORT_ID as ReportID,REPORT_NAME as ReportName,DESCRIPTION,REPORT_PATH as ReportPath, isENABLED as Status 
from s01tblReports 
where REPORT_ID =@ReportID

end
GO
/****** Object:  StoredProcedure [dbo].[usp853WebConsoleProcessReport]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE Procedure [dbo].[usp853WebConsoleProcessReport]
@ReportID int,
@ReportName varchar(100),
@Description varchar(100),
@ReportPath varchar(150),
@IsEnabled bit
as

update s01tblReports set REPORT_NAME=@ReportName,DESCRIPTION=@Description,
REPORT_PATH=@ReportPath,isENABLED=@IsEnabled
where REPORT_ID=@ReportID
GO
/****** Object:  StoredProcedure [dbo].[usp854WebConsoleActivityLogReport]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE Procedure [dbo].[usp854WebConsoleActivityLogReport]

--@SessionID varchar(200)

as

Begin


    select 

         AppEventLogID,

          Sessionid,

           [User Name] as UserName,

           --[Log Date] as[Date],

           [Log Date] as [Date],

           [Log Date] as [DateTime],

           [Computer Name] as ComputerName,

           --Location as Department,
		   
		   --Room,

           --WorkflowInfo as Overridenotes,


           LogMsg as Message,


           [Cluster Name] as ClusterName,


           RFID

    from tblAppEventLog WEL

    where 

        EventID  in(94,95,96,97,98,99,100,101)

        and WEL.[User Name] !='System, System'

        --and WEL.SessionID = @SessionID

    order by [Log Date]

End

GO
/****** Object:  StoredProcedure [dbo].[usp901WebConsoleGetDeviceDetails]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE Procedure [dbo].[usp901WebConsoleGetDeviceDetails]
@DeviceID int
as
begin
    select d.[Registration Date],d.[Registered By],d.[Entered Date],d.SerialNumber,D.DeviceName,
	d.McAddress,d.Status,l.Department,l.RoomNumber as Room,d.[Device IPAddress] as ipAddress
	from tblDeviceRegistration as d 
	left join s01tblLocationRoom as l on d.[Location ID]=l.RoomID
	where Id=@DeviceID;

	select Department as Departments from s01tblLocationRoom where RoomDesc <>'iRISupplyWebConsole'
end
GO
/****** Object:  StoredProcedure [dbo].[usp903WebConsoleUpdateDeviceStatus]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE Procedure [dbo].[usp903WebConsoleUpdateDeviceStatus]
@McAddress varchar(50),@Status varchar(50)
as
begin
    if exists (select * from tblDeviceRegistration where McAddress=@McAddress)
	begin
	    update tblDeviceRegistration set Status= @Status where McAddress=@McAddress
		select 'Updated Successfully'
	end
	else
	begin
	    select 'Failed to update'
	end
end

GO
/****** Object:  StoredProcedure [dbo].[usp904WebConsoleTimeOfImplantCheck]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE PROCEDURE [dbo].[usp904WebConsoleTimeOfImplantCheck]
@RFID VARCHAR(100)
AS
BEGIN
    DECLARE @ItemID INT

    IF EXISTS (SELECT * FROM s01tblitems WHERE RFID = @RFID)
    BEGIN
        SET @ItemID = (SELECT itemid FROM s01tblitems WHERE RFID = @RFID)

        SELECT I.[Expired Date] AS [Expired Date],
              I.[Entered Date] AS [Entered Date],
               D.[Description 1] AS Description,
			   I.ItemStatusID,D.RFID AS isImplant
        FROM s01tblitems AS I
        INNER JOIN s01tblproducts AS D ON I.ProductID = D.ProductID
        WHERE I.RFID = @RFID 
    END
	
END
GO
/****** Object:  StoredProcedure [dbo].[usp905WebConsoleUpdateCapitateItem]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE PROCEDURE [dbo].[usp905WebConsoleUpdateCapitateItem]
@ItemUsageID INT,
@CapitateValue INT
AS
BEGIN
  IF EXISTS (SELECT * FROM s01tblItemUsageExt WHERE ItemUsageID = @ItemUsageID)
  BEGIN
    UPDATE s01tblItemUsageExt 
    SET Capitated = @CapitateValue 
    WHERE ItemUsageID = @ItemUsageID

    IF @CapitateValue = 0
      SELECT 'ITEM UNCAPITATED SUCCESSFULLY'
    ELSE
      SELECT 'ITEM CAPITATED SUCCESSFULLY'
  END
  ELSE 
  BEGIN 
    INSERT INTO s01tblItemUsageExt (ItemUsageID, ManualScan, Capitated, BillOnly, PostedToDoc, EnteredDate, LastActivityDate)
    VALUES (@ItemUsageID, 0, @CapitateValue, 0, 0, GETDATE(), GETDATE())
    
    SELECT 'ITEM CAPITATED SUCCESSFULLY'
  END
END




GO
/****** Object:  StoredProcedure [dbo].[uspFetchBillOnlyDetails]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS OFF
GO
SET QUOTED_IDENTIFIER OFF
GO

/************************************************************************************************************************
Name				: [uspFetchBillOnlyItems]
Version				: ********
Purpose				: To fetch the product details through processed barcode
Author				: Subramanya Rao
Date Created 		: 6 Mar 2020
Application Affected: IriSupply 6.0
---------------------------------------------------------------------------------------------------------------------------
Release Notes		:
******** [Subramanya, 6 Apr 2021] - Bill only feature
----------------------------------------------------------------------------------------------------------------------------
--SP CALL
-- @Event='FetchTagData'
DECLARE @p2 dbo.udtSPParam
DECLARE @p3 dbo.udtSPParam
INSERT INTO @p2 VALUES(N'DeviceID',4)
INSERT INTO @p2 VALUES(N'EntryTypeID',1)
INSERT INTO @p2 values(N'ComputerName',N'TestMachine')

insert into @p3 values(N'ProcessedBarcodeNo',N'+H739PG3980BPS3X')
insert into @p3 values(N'ProcessedCatNo',N'PG3980BPS') -- 00026201100 Prod Master
insert into @p3 values(N'FullBarcodeNo',N'+H739PGeeeeeeeeeeeee3960BPS3V')

EXECUTE [uspFetchBillOnlyDetails] @Event='BillOnly',@udtSystemData=@p2, @udtEventData=@p3

--------------------------------------------------------------------------------------------------------*/
CREATE PROCEDURE [dbo].[uspFetchBillOnlyDetails]
	  @Event VARCHAR(255)
	, @udtSystemData udtSPParam READONLY
	, @udtEventData udtSPParam READONLY
AS

SET NOCOUNT ON;

DECLARE @Msg VARCHAR(200), @ProcessedBarcodeNo VARCHAR(250), @BarcodeCatNo VARCHAR(250), @FullBarcodeNo VARCHAR(250), @ProcessedCatNo VARCHAR (500)
DECLARE @RFID VARCHAR(500),@ProductID VARCHAR(500),@ItemID VARCHAR(200)

SET XACT_ABORT ON;
BEGIN TRY
BEGIN TRANSACTION

	--SELECT @ProcessedBarcodeNo =Value FROM  @udtEventData WHERE Property = 'ProcessedBarcodeNo'
	--SELECT @FullBarcodeNo =Value FROM  @udtEventData WHERE Property = 'FullBarcodeNo'
	--SELECT @ProcessedCatNo =Value FROM  @udtEventData WHERE Property = 'ProcessedCatNo'
	SELECT @RFID =Value FROM  @udtEventData WHERE Property = 'RFID'

	IF @BarcodeCatNo IS NULL
	BEGIN
		IF @RFID LIKE 'E007%'
		BEGIN
			SELECT RFID,[Cat No],[Description 1],[Serial No],[Lot No],[Expired Date],[Brand Name] as BrandName,Manufacturer,[Expired Date],Qty,'Success' as Message
			FROM [qryItemsOneTimeUse]
			WHERE RFID = @RFID
			SET @BarcodeCatNo = @RFID
		END
	END

	IF @BarcodeCatNo IS NULL
	BEGIN
		SELECT @BarcodeCatNo = [Cat No] 
		FROM s01tblBarcodeRuleProduct 
		WHERE [Barcode No] = @RFID AND @RFID <> ''

		--SELECT @ProductID = ProductID
		--FROM s01tblProducts
		--WHERE [Cat No] = @BarcodeCatNo

		--SELECT @ItemID = ItemID
		--FROM s01tblItems
		--WHERE ProductID = @ProductID

		SELECT RFID,[Cat No],[Description 1],[Serial No],[Lot No],[Expired Date],[Brand Name] as BrandName,Manufacturer,[Expired Date],Qty,'Success' as Message
		FROM [qryItemsOneTimeUse]
		WHERE [Cat No] = @BarcodeCatNo

		SET @BarcodeCatNo = @RFID

		PRINT 'tblBarcodeRuleProduct'	
	END

	IF @BarcodeCatNo IS NULL
	BEGIN
		SELECT @BarcodeCatNo = [Cat No] 
		FROM s01tblBarcodeRuleProduct 
		WHERE [Barcode No] = @RFID AND @RFID <> ''

		SELECT 'ProductDetails' AS DataSetName, [Cat No], [Description 1], Manufacturer, [Brand Name] AS BrandName,'Success' as Message
		FROM s01tblProducts 
		WHERE [Cat No] = @BarcodeCatNo 
		AND @BarcodeCatNo <> ''

		PRINT 'tblBarcodeRuleProduct'	
	END

	IF @BarcodeCatNo IS NULL 
	BEGIN
		SELECT @BarcodeCatNo = [Cat No]  
		FROM [iRISProductMaster].dbo.tblBarcodeRuleProductMaster
		WHERE [Barcode No] = @RFID AND @RFID <> ''

		SELECT 'BarcodeDetails'  AS DataSetName, [Cat No], [Description 1], Manufacturer, [Brand Name] AS BrandName,'Success' as Message
		FROM [iRISProductMaster].dbo.tblProductsMaster
		WHERE [Cat No] = @BarcodeCatNo	
		AND  @BarcodeCatNo <> ''

		Print 'tblBarcodeRuleProductMaster'	
	END	

	ELSE IF @BarcodeCatNo IS NULL OR  @BarcodeCatNo = ''
	BEGIN
		SELECT 'No Data Found' AS DataSetName
	END
		
	--IF @BarcodeCatNo IS NULL 
	--BEGIN
	--	SELECT @BarcodeCatNo = [Cat No] 
	--	FROM s01tblProducts
	--	WHERE replace(replace([Cat No],'-',''),'.','') = @ProcessedCatNo AND @ProcessedCatNo <> ''
			
	--	SELECT 'ProductDetails' AS DataSetName, [Cat No] AS CatNo, [Description 1] AS Description, Manufacturer, [Brand Name] AS BrandName
	--	FROM s01tblProducts 
	--	WHERE [Cat No] = @BarcodeCatNo	
	--		AND  @BarcodeCatNo <> ''

	--	PRINT 'tblProducts'																					
	--END

	--Product Master
	--IF @BarcodeCatNo IS NULL 
	--BEGIN
	--	SELECT @BarcodeCatNo = [Cat No]  
	--	FROM [iRISProductMaster].dbo.tblBarcodeRuleProductMaster
	--	WHERE [Barcode No] = @ProcessedBarcodeNo AND @ProcessedBarcodeNo <> ''
	--		OR replace(replace([Cat No],'-',''),'.','')= @ProcessedCatNo AND @ProcessedCatNo <> ''
	--		OR [Barcode No] = @FullBarcodeNo AND @FullBarcodeNo <> ''

	--	SELECT 'ProductDetails' AS DataSetName, [Cat No] AS CatNo, [Description 1] AS Description, Manufacturer, [Brand Name] AS BrandName
	--	FROM [iRISProductMaster].dbo.tblProductsMaster
	--	WHERE [Cat No] = @BarcodeCatNo	AND  @BarcodeCatNo <> ''

	--	Print 'tblBarcodeRuleProductMaster'	
	--END	

	--IF @BarcodeCatNo IS NULL 
	--BEGIN
	--	SELECT @BarcodeCatNo = [Cat No] 
	--	FROM [iRISProductMaster].dbo.tblProductsMaster 
	--	WHERE replace(replace([Cat No],'-',''),'.','') = @ProcessedCatNo AND @ProcessedCatNo <> ''
		
	--	SELECT 'ProductDetails' AS DataSetName, [Cat No] AS CatNo, [Description 1] AS Description, Manufacturer, [Brand Name] AS BrandName
	--	FROM [iRISProductMaster].dbo.tblProductsMaster 
	--	WHERE [Cat No] = @BarcodeCatNo	AND  @BarcodeCatNo <> ''
		
	--	Print 'tblProductsMaster'	
	--END


	--IF @BarcodeCatNo IS NULL OR  @BarcodeCatNo = ''
	--BEGIN
	--	SELECT 'No Data Found' AS DataSetName
	--END

	--EXECUTE  uspAppCabEventLogs @Event = @Event
	--			, @udtSystemData = @udtSystemData
	--			, @udtEventData = @udtEventData
		
COMMIT TRANSACTION		
END TRY

BEGIN CATCH
	IF XACT_STATE() <> 0
		ROLLBACK TRANSACTION
		SET @Msg = 'Error in Searching Product details';
	EXECUTE s01uspLogError
END CATCH
SET NOCOUNT OFF
GO
/****** Object:  StoredProcedure [dbo].[uspFetchBillOnlyItemDetails]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE Procedure [dbo].[uspFetchBillOnlyItemDetails]
	@Event VARCHAR(200)
	, @udtSystemData udtSPParam READONLY
	, @udtEventData udtSPParam READONLY
	--, @udt700WebConsoleTransactedItems udt700WebConsoleTransactedItems READONLY
	--, @udt702WebConsoleTagsData udt702WebConsoleTags READONLY
AS
	DECLARE @RFID VARCHAR(200), @DataSetName VARCHAR(50)
	SELECT @RFID = Value FROM @udtEventData WHERE Property = 'RFID' 
	SELECT @DataSetName = Value FROM @udtEventData WHERE Property = 'EventOutPut'

	SELECT @DataSetName AS DataSetName, RFID,[Cat No],[Description 1],Manufacturer,[Serial No],[Lot No],Qty,[Expired Date],'iRISupply' AS BrandName,'Success' AS Message
	FROM s01qryItems
	WHERE RFID = @RFID
GO
/****** Object:  StoredProcedure [dbo].[uspFetchItemDetailsByCatNo]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE PROCEDURE [dbo].[uspFetchItemDetailsByCatNo]
    @Event VARCHAR(200),
    @udtSystemData udtSPParam READONLY,
    @udtEventData udtSPParam READONLY
AS
BEGIN
    SET NOCOUNT ON;
    DECLARE
        @ScannedBarcode VARCHAR(500),
		@ScannedRFID VARCHAR(500),
		@CatNo VARCHAR(25),
		@EventOutPut VARCHAR(25),
		@Qty INT,
        @UserID INT = 0,
		@SessionID INT,
		@DeviceID INT,
		@EntryTypeID INT,
        @ComputerName VARCHAR(25), 
		@ProductUnitID INT,
		@SettingValue VARCHAR(150),
        @CurrentUtcTime DATETIME;
 
 
    BEGIN TRY
        SELECT @DeviceID = Value FROM @udtSystemData WHERE Property = 'DeviceID';
        SELECT @ComputerName = Value FROM @udtSystemData WHERE Property = 'ComputerName';
        SELECT @EntryTypeID = Value FROM @udtSystemData WHERE Property = 'EntryTypeID';
        SELECT @EventOutPut = Value FROM @udtEventData WHERE Property = 'EventOutPut';
		SELECT @CatNo = Value FROM @udtEventData WHERE Property = 'CatalogNumber';
		--SET @SettingValue = dbo.udfSUPFetchSettings(@DeviceID, 'USAGE_CONFIRM_MODE');
 
		IF EXISTS (SELECT 1 FROM s01qryProducts WHERE [Cat No] LIKE '%' + @CatNo + '%')
		BEGIN
			SELECT 
						PC.[Cat No] AS CatNo, 
						NULL AS Location, 
						PC.Qty, 
						NULL AS Manufacturer,
						PC.[Description 1] AS Description, 
						NULL AS BrandName, 
						BP.[Barcode No] AS BarCodeNo, 
						NULL AS ImplantStatus
					FROM 
						s01qryProductCatalog PC 
					LEFT OUTER JOIN
						s01tblBarcodeRuleProduct BP 
						ON PC.[Cat No] = BP.[Cat No]
					WHERE PC.[Cat No] LIKE '%' + @CatNo + '%'
		END
        ELSE
		BEGIN
			SELECT 'NO DATA FOUND' AS msg
		END
 
END TRY
 
BEGIN CATCH
        IF (XACT_STATE()) <> 0
 
        EXECUTE s01uspLogError;
END CATCH;
    SET NOCOUNT OFF;
END
GO
/****** Object:  StoredProcedure [dbo].[uspFetchOneTimeUseDetails]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [dbo].[uspFetchOneTimeUseDetails]
    @Event VARCHAR(200),
    @udtSystemData udtSPParam READONLY,
    @udtEventData udtSPParam READONLY
AS
BEGIN
    SET NOCOUNT ON;
    DECLARE
	
        @ScannedBarcode VARCHAR(500),
		@ScannedRFID VARCHAR(500),
		@CatNo VARCHAR(25),
		@Qty INT,
        @UserID INT = 0,
		@SessionID INT,
		@DeviceID INT,
		@EntryTypeID INT,
        @ComputerName VARCHAR(25), 
		@ProductUnitID INT,
		@SettingValue VARCHAR(150),
        @CurrentUtcTime DATETIME;


    BEGIN TRY
        SELECT @DeviceID = Value FROM @udtSystemData WHERE Property = 'DeviceID';
        SELECT @ComputerName = Value FROM @udtSystemData WHERE Property = 'ComputerName';
        SELECT @EntryTypeID = Value FROM @udtSystemData WHERE Property = 'EntryTypeID';
        SELECT @ScannedBarcode = Value FROM @udtEventData WHERE Property = 'RFID';
		SELECT @ScannedRFID = Value FROM @udtEventData WHERE Property = 'RFID';
		SET @SettingValue = dbo.udf700WebConsoleSUPFetchSettings(@DeviceID, 'USAGE_CONFIRM_MODE');

       SELECT @CatNo = [Cat No] 
	   FROM s01tblBarcodeRuleProduct 
	   WHERE [Barcode No] = @ScannedBarcode	
	   
	   IF @CatNo IS NULL
	   BEGIN
		   SELECT @CatNo = [Cat No] 
		   FROM s01qryitems 
		   WHERE [RFID] = @ScannedRFID
	   END

	   IF(@CatNo IS NOT NULL)
	   BEGIN
	   SELECT @ProductUnitID = ProdUnitID
	   FROM s01tblProducts
	   WHERE [Cat No] = @CatNo
	   END

	   IF(@SettingValue = 3 )
	   BEGIN
			IF (@ScannedRFID LIKE 'E00%')
			BEGIN
				SELECT 'TaggedDetails' as DataSetName, RFID,[Cat No],[Description 1],[Serial No],[Lot No],[Expired Date]
			   ,[Brand Name] as BrandName,Manufacturer,[Expired Date],Qty,'Success' as Message,'RFID item found' AS MSG
			   from [s01qryItemsOneTimeUse] Where RFID= @ScannedRFID
			END
			ELSE
			BEGIN
				IF EXISTS(SELECT 1 FROM s01tblProducts where replace(replace(replace([cat no],'-',''),'.',''),' ','')= @CatNo OR [Cat No] = @CatNo)
				BEGIN
					SELECT 'BarcodeDetails' as DataSetName, [Cat No],[Description 1] ,Manufacturer
				   , [Brand Name] as BrandName, 'Success' as Message,'Product Barcode found' AS MSG
				   ,Null as [Serial No],Null as [Lot No],Null as [Expired Date]
				   from s01tblProducts where replace(replace(replace([cat no],'-',''),'.',''),' ','')= @CatNo OR [Cat No] = @CatNo
				END
				ELSE
				BEGIN
					SELECT 'NO DATA FOUND' AS DatasetName,'NO DATA FOUND' AS MSG
				END
			END
	   END
	   ELSE IF(@SettingValue = 1 )
	   BEGIN
		   IF EXISTS ( SELECT 1 FROM [s01qryItemsOneTimeUse] WHERE [Cat No] = @CatNo AND RFID = @ScannedRFID )
		   BEGIN
			   SELECT 'TaggedDetails' as DataSetName, RFID,[Cat No],[Description 1],[Serial No],[Lot No],[Expired Date]
			   ,[Brand Name] as BrandName,Manufacturer,[Expired Date],Qty,'Success' as Message,'RFID item found' AS MSG
			   from [s01qryItemsOneTimeUse] Where RFID= @ScannedRFID
		   END
		   ELSE
		   BEGIN
			 SELECT 'WARNING' AS DataSetName,'NO DATA FOUND' AS MSG
		   END

	   END

	   ELSE IF(@SettingValue = 2)
	   BEGIN		   	
		   IF EXISTS ( SELECT 1 FROM s01tblProducts WHERE [Cat No] = @CatNo)
		   BEGIN
				--SELECT 'BarcodeDetails' as DataSetName, [Cat No],[Description 1] ,Manufacturer
			 --  , [Brand Name] as BrandName, 'Success' as Message,'Product Barcode found' AS MSG
			 --  ,Null as [Serial No],Null as [Lot No],Null as [Expired Date]
			 --  from s01tblProducts where replace(replace(replace([cat no],'-',''),'.',''),' ','')= @CatNo

						SELECT 
						'BarcodeDetails' as DataSetName,
						PC.[Cat No] AS [Cat No], 
						NULL as BrandName,
						PC.[Description 1],
						NULL AS Manufacturer,
						--NULL AS Location, 
						'Success' as Message,
						'Product Barcode found' AS MSG,
						--PC.Qty, 
						Null as [Serial No],
						Null as [Lot No],
						Null as [Expired Date]
					FROM 
						s01qryProductCatalog PC 
					LEFT OUTER JOIN
						s01tblBarcodeRuleProduct BP 
						ON PC.[Cat No] = BP.[Cat No]
						--WHERE PC.[Cat No] = '204.885'
					where PC.[Cat No]= @CatNo
		   END
		   ELSE
		   BEGIN
			 SELECT 'WARNING' AS DataSetName,'The scanned barcode not found in Inventory' AS MSG
		   END
	   END
	   
	   ELSE IF @CatNo IS NULL OR @ProductUnitID IS NULL
	   BEGIN
		    SELECT 'NO DATA FOUND' AS DatasetName,'NO DATA FOUND' AS MSG
	   END


END TRY

BEGIN CATCH
        IF (XACT_STATE()) <> 0

        EXECUTE s01uspLogError;
END CATCH;
    SET NOCOUNT OFF;
END
GO
/****** Object:  StoredProcedure [dbo].[uspGetReports]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
Create procedure [dbo].[uspGetReports]
as
begin
	select REPORT_ID as ReportID,REPORT_NAME as ReportName,DESCRIPTION,REPORT_PATH as ReportPath, isENABLED as Status 
	from s01tblReports 
	order by REPORT_NAME
end
GO
/****** Object:  StoredProcedure [dbo].[uspHUBSaveBarcodeMasterFDAProducts]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS OFF
GO
SET QUOTED_IDENTIFIER OFF
GO

/************************************************************************************************************************
Name				: uspSaveBarcodeMasterFDAProducts
Version				: ********
Purpose				: To save the product details from MASTER/FDA to Localhost for Barcode clusters
Author				: Subramanya Rao
Date Created 		: 24th march 2021
Application Affected: IriSupply 6.0
---------------------------------------------------------------------------------------------------------------------------
Release Notes		:

----------------------------------------------------------------------------------------------------------------------------
-- SP CALL
declare @p2 dbo.udtSPParam
insert into @p2 values(N'EntryTypeID',N'1')
insert into @p2 values(N'DeviceID',N'3')
insert into @p2 values(N'ComputerName',N'MAILPTP52')
insert into @p2 values(N'Domain',N'maspects.com')

declare @p3 dbo.udtSPParam
insert into @p3 values(N'UserID',N'2')
insert into @p3 values(N'SessionID',N'20210325120110970')
insert into @p3 values(N'EventType',N'RemoveBarcodeInventory')
insert into @p3 values(N'EventInPut1',N'bbaasss240ENSP35015UX') -- Scanned Full barcode
insert into @p3 values(N'EventInput3',N'Remove Item')
insert into @p3 values(N'EventInput4',N'TTT')

--If DataSetName = Fail and MSG is	Product is not registered then send below 4 parameters
--insert into @p3 values(N'CatNo',N'sscbazabcabababxxabc')
--insert into @p3 values(N'Description',N'test')
--insert into @p3 values(N'Manufacturer',N'test')
--insert into @p3 values(N'BrandName',N'test')

insert into @p3 values(N'EventOutPut',N'FetchBarcodeInventoryList')

exec uspExecuteAppEvent @Event='UpdateBarcodeInventory',@udtSystemData=@p2,@udtEventData=@p3


--------------------------------------------------------------------------------------------------------*/
CREATE PROCEDURE [dbo].[uspHUBSaveBarcodeMasterFDAProducts]
	@Event VARCHAR(255)
	, @udtSystemData udtSPParam READONLY
	, @udtEventData udtSPParam READONLY
AS

SET NOCOUNT ON;

DECLARE @Msg VARCHAR(200), @ProcessedBarcondeNo VARCHAR(250),  @FullBarcodeNo VARCHAR(250), @ProcessedCatNo VARCHAR (500), @SessionID BIGINT, @ItemStatusID INT, @ItemEventID INT
DECLARE @CatNo VARCHAR (200), @ProductId INT, @ItemID INT, @RemovedUserName VARCHAR(100), @RFID VARCHAR (500), @UserID INT, @CurrentUtcTime DATETIME, @ItemUsageID INT, @ItemHistoryID INT
DECLARE @MAPatientID VARCHAR(50), @MAScheduleID INT, @MAVisitID INT, @OverrideId INT, @OverrideNotes VARCHAR(255), @PhysicianID VARCHAR(50), @BillingStatusID INT, @EntryTypeID INT
		, @InsertFDAProduct INT, @InsertBracodeFDA INT,  @Description VARCHAR(50), @Manufacturer VARCHAR(50), @BrandName VARCHAR(50), @MastProductId INT
		, @ScannedBarcode VARCHAR(70), @MCatNo VARCHAR(50)

SET XACT_ABORT ON;
BEGIN TRY
BEGIN TRANSACTION

	--SELECT @ProcessedBarcodeNo =Value FROM  @udtEventData WHERE Property = 'ProcessedBarcodeNo'
	--SELECT @FullBarcodeNo =Value FROM  @udtEventData WHERE Property = 'FullBarcodeNo'
	--SELECT @ProcessedCatNo =Value FROM  @udtEventData WHERE Property = 'ProcessedCatNo'

	SELECT @ScannedBarcode =Value FROM  @udtEventData WHERE Property = 'RFID'

	--SELECT @CatNo = REPLACE( REPLACE( REPLACE ([Cat No], '-', ''), '.', ''), ' ', '' ) 
	--FROM [$(IrisDB)].dbo.tblBarcodeRuleProduct 
	--WHERE [Barcode No] = @ScannedBarcode

	SELECT @CatNo =Value FROM  @udtEventData WHERE Property = 'CatalogNumber'
	SELECT @Description =  Value FROM  @udtEventData WHERE Property = 'Description'
	SELECT @Manufacturer =  Value FROM  @udtEventData WHERE Property = 'Manufacturer'	
	SELECT @BrandName =  Value FROM  @udtEventData WHERE Property = 'BrandName'

	IF @CatNo IS NULL 
	BEGIN
		IF EXISTS ( SELECT 1 FROM s01tblBarcodeRuleProductMaster WHERE [Barcode No] = @ScannedBarcode)
		BEGIN

				SELECT @MCatNo = [Cat No]
				FROM s01tblBarcodeRuleProductMaster
				WHERE [Barcode No] = @ScannedBarcode

				INSERT INTO s01tblBarcodeRuleProduct ([Barcode No], [Cat No])
				SELECT [Barcode No], [Cat No]
				FROM s01tblBarcodeRuleProductMaster
				WHERE [Barcode No] = @ScannedBarcode

				PRINT 'Inserted into s01tblBarcodeRuleProduct from MASTER'

		END

		IF EXISTS ( SELECT 1 FROM s01tblProductsMaster WHERE replace(replace([Cat No],'-',''),'.','') = replace(replace(@MCatNo,'-',''),'.',''))
		BEGIN

				INSERT INTO s01tblProducts 
							( [Cat No], NDC, [Brand Name], [Other Name], [Description 1], [Description 2], [Description 3], Size, Material, Manufacturer, [Ref Price], Picture, QtyPerPkg
							, RFID, [SKU No],[ID 1], [ID 2], [ID 3], [ID 4], [ID 5], [Entered By], [Computer Name], Domain, [Entered Date], ReorderTypeID, [Notify Interval]
							, [Notify Time], [Notify Sent], ProductTypeID, PCDM, MMID, ProductCategoryID, ProductSubCategoryID, ProductGroupID, EntryTypeID
							, [Min Stock], [Max Stock])

				SELECT        [Cat No], NDC, [Brand Name], [Other Name], [Description 1], [Description 2], [Description 3], Size, Material, Manufacturer, [Ref Price], Picture, QtyPerPkg
							, RFID, [SKU No],[ID 1], [ID 2], [ID 3], [ID 4], [ID 5], [Entered By], [Computer Name], Domain, [Entered Date], ReorderTypeID, [Notify Interval]
							, [Notify Time], [Notify Sent], ProductTypeID, PCDM, MMID, ProductCategoryID, ProductSubCategoryID, ProductGroupID, EntryTypeID
							, [Min Stock], [Max Stock]
				FROM s01tblProductsMaster
				WHERE [Cat No] = @MCatNo

				PRINT 'Inserted into s01tblProducts from MASTER'

		END
	END

	IF ( @CatNo IS NOT NULL)
	BEGIN
			--SET IDENTITY_INSERT s01tblProductsMaster ON
		 --   INSERT INTO s01tblProductsMaster  
			--				( ProductID, [Cat No], [Brand Name], Manufacturer, [Description 1], IsFromFDA ) 
		 --   SELECT MAX(ProductID)+1, @CatNo, @BrandName, @Manufacturer, @Description, 1
		 --   FROM s01tblProductsMaster 
			--SET IDENTITY_INSERT s01tblProductsMaster OFF

			--SELECT @MastProductId = ProductId 
			--FROM s01tblProductsMaster 
			--WHERE [Cat No] = @CatNo
		 
			SET IDENTITY_INSERT s01tblProducts ON
			INSERT INTO s01tblProducts
						( [ProductID],[Cat No], [Brand Name], Manufacturer, [Description 1] ) 
			SELECT (SELECT MAX(ProductID) AS ProductID
			FROM s01tblProducts)+1,@CatNo, @BrandName, @Manufacturer, @Description
			SET IDENTITY_INSERT s01tblProducts OFF


			PRINT 'Inserted into s01tblProducts and MASTER from FDA'

	END

	IF (  @CatNo IS NOT NULL )
	BEGIN
		--SET IDENTITY_INSERT s01tblBarcodeRuleProductMaster ON
		--INSERT INTO s01tblBarcodeRuleProductMaster 
		--	   (RuleID, [Barcode No], [Cat No], IsFromFDA, ProductID)
		--SELECT (SELECT MAX(RuleID) AS MaxMasterRuleID
		--FROM s01tblBarcodeRuleProductMaster)+1,@ScannedBarcode, @CatNo, 1, @MastProductId
		--FROM s01tblBarcodeRuleProductMaster 
		--SET IDENTITY_INSERT s01tblBarcodeRuleProductMaster OFF

		SET IDENTITY_INSERT s01tblBarcodeRuleProduct  ON
		INSERT INTO s01tblBarcodeRuleProduct (RuleID,[Barcode No], [Cat No])
		SELECT (SELECT MAX(RuleID) AS MaxRuleID
		FROM s01tblBarcodeRuleProduct)+1, @ScannedBarcode, @CatNo
		SET IDENTITY_INSERT s01tblBarcodeRuleProduct  OFF

		PRINT 'Inserted into s01tblBarcodeRuleProduct and tblBarcodeRuleProductMaster from FDA'
	END

	
COMMIT TRANSACTION		
END TRY

BEGIN CATCH
	IF XACT_STATE() <> 0
		ROLLBACK TRANSACTION
		SET @Msg = 'Error in Searching Product details';
	EXECUTE s01uspLogError
END CATCH
SET NOCOUNT OFF
GO
/****** Object:  StoredProcedure [dbo].[uspSaveBillOnlyMasterFDAProducts]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [dbo].[uspSaveBillOnlyMasterFDAProducts]
	@Event VARCHAR(255)
	, @udtSystemData udtSPParam READONLY
	, @udtEventData udtSPParam READONLY
AS

SET NOCOUNT ON;

DECLARE @Msg VARCHAR(200), @ProcessedBarcodeNo VARCHAR(250),  @FullBarcodeNo VARCHAR(250), @ProcessedCatNo VARCHAR (500), @SessionID BIGINT, @ItemStatusID INT, @ItemEventID INT
DECLARE @CatNo VARCHAR (200), @ProductId INT, @ItemID INT, @RemovedUserName VARCHAR(100), @RFID VARCHAR (500), @UserID INT, @CurrentUtcTime DATETIME, @ItemUsageID INT, @ItemHistoryID INT
DECLARE @MAPatientID VARCHAR(50), @MAScheduleID INT, @MAVisitID INT, @OverrideId INT, @OverrideNotes VARCHAR(255), @PhysicianID VARCHAR(50), @BillingStatusID INT, @EntryTypeID INT
		, @InsertFDAProduct INT, @InsertBracodeFDA INT,  @Description VARCHAR(50), @Manufacturer VARCHAR(50), @BrandName VARCHAR(50), @MastProductId INT,@IsImplant VARCHAR(50)

SET XACT_ABORT ON;
BEGIN TRY
BEGIN TRANSACTION

	SELECT @FullBarcodeNo =Value FROM  @udtEventData WHERE Property = 'RFID'
	SELECT @ProcessedCatNo =Value FROM  @udtEventData WHERE Property = 'CatalogNumber'
	SELECT @Description =  Value FROM  @udtEventData WHERE Property = 'Description'	
	SELECT @Manufacturer =  Value FROM  @udtEventData WHERE Property = 'Manufacturer'	
	SELECT @BrandName =  Value FROM  @udtEventData WHERE Property = 'BrandName'
	 SELECT @IsImplant = Value FROM @udtEventData WHERE Property = 'IsImplant';

	IF EXISTS ( SELECT 1 FROM [iRISProductMaster].dbo.tblBarcodeRuleProductMaster WHERE [Cat No] =  @ProcessedCatNo)
	BEGIN
		IF NOT EXISTS (SELECT 1 FROM s01tblBarcodeRuleProduct WHERE [Cat No] =  @ProcessedCatNo)
		BEGIN
			INSERT INTO s01tblBarcodeRuleProduct ([Barcode No], [Cat No])
			SELECT [Barcode No], [Cat No]
			FROM [iRISProductMaster].dbo.tblBarcodeRuleProductMaster
			WHERE [Cat No] = @ProcessedCatNo

			SET @InsertBracodeFDA = 1 

			PRINT 'Inserted into s01tblBarcodeRuleProduct from MASTER'
		END
	END

	IF EXISTS ( SELECT 1 FROM [iRISProductMaster].dbo.tblProductsMaster WHERE [Cat No] =  @ProcessedCatNo)
	BEGIN
		IF NOT EXISTS (SELECT 1 FROM s01tblProducts WHERE [Cat No] =  @ProcessedCatNo)
		BEGIN
			INSERT INTO s01tblProducts 
						( [Cat No], NDC, [Brand Name], [Other Name], [Description 1], [Description 2], [Description 3], Size, Material, Manufacturer, [Ref Price], Picture, QtyPerPkg
						, RFID, [SKU No],[ID 1], [ID 2], [ID 3], [ID 4], [ID 5], [Entered By], [Computer Name], Domain, [Entered Date], ReorderTypeID, [Notify Interval]
						, [Notify Time], [Notify Sent], ProductTypeID, PCDM, MMID, ProductCategoryID, ProductSubCategoryID, ProductGroupID, EntryTypeID
						, [Min Stock], [Max Stock])

			SELECT        [Cat No], NDC, [Brand Name], [Other Name], [Description 1], [Description 2], [Description 3], Size, Material, Manufacturer, [Ref Price], Picture, QtyPerPkg
						, @IsImplant, [SKU No],[ID 1], [ID 2], [ID 3], [ID 4], [ID 5], [Entered By], [Computer Name], Domain, [Entered Date], ReorderTypeID, [Notify Interval]
						, [Notify Time], [Notify Sent], ProductTypeID, PCDM, MMID, ProductCategoryID, ProductSubCategoryID, ProductGroupID, EntryTypeID
						, [Min Stock], [Max Stock]
			FROM [iRISProductMaster].dbo.tblProductsMaster
			WHERE [Cat No] = @ProcessedCatNo

			PRINT 'Inserted into s01tblProducts from MASTER'

		END
		SET @InsertFDAProduct = 1
	END

	IF ( @InsertFDAProduct IS NULL)
	BEGIN
			INSERT INTO [iRISProductMaster].dbo.tblProductsMaster  
							( ProductID, [Cat No], [Brand Name], Manufacturer, [Description 1], IsFromFDA,RFID ) 
			SELECT MAX(ProductID)+1, @ProcessedCatNo, @BrandName, @Manufacturer, @Description, 1,@IsImplant
			FROM [iRISProductMaster].dbo.tblProductsMaster 

			SELECT @MastProductId = ProductId 
			FROM [iRISProductMaster].dbo.tblProductsMaster 
			WHERE [Cat No] =  @ProcessedCatNo
		 
			INSERT INTO s01tblProducts
						([Cat No], [Brand Name], Manufacturer, [Description 1],RFID ) 
			SELECT  @ProcessedCatNo, @BrandName, @Manufacturer, @Description,@IsImplant

	PRINT 'Inserted into s01tblProducts and MASTER from FDA'

	END

	IF ( @InsertBracodeFDA IS NULL )
	BEGIN
		INSERT INTO [iRISProductMaster].dbo.tblBarcodeRuleProductMaster 
			   (RuleID, [Barcode No], [Cat No], IsFromFDA, ProductID)
		SELECT MAX(RuleID)+1, @FullBarcodeNo, @ProcessedCatNo, 1, @MastProductId
		FROM [iRISProductMaster].dbo.tblBarcodeRuleProductMaster 

		INSERT INTO s01tblBarcodeRuleProduct 
			   ([Barcode No], [Cat No])
		SELECT  @FullBarcodeNo, @ProcessedCatNo

		PRINT 'Inserted into s01tblBarcodeRuleProduct and tblBarcodeRuleProductMaster from FDA'
	END

	
COMMIT TRANSACTION		
END TRY

BEGIN CATCH
	IF XACT_STATE() <> 0
		ROLLBACK TRANSACTION
		SET @Msg = 'Error in Searching Product details';
	EXECUTE s01uspLogError
END CATCH
SET NOCOUNT OFF
GO
/****** Object:  StoredProcedure [dbo].[uspSaveOneTimeUseDetails]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [dbo].[uspSaveOneTimeUseDetails]
    @Event VARCHAR(255),
    @udtSystemData udtSPParam READONLY,
    @udtEventData udtSPParam READONLY
AS
BEGIN
    SET NOCOUNT ON;
    SET XACT_ABORT ON;

    DECLARE @Msg VARCHAR(200), 
            @ProcessedBarcodeNo VARCHAR(250),  
            @FullBarcodeNo VARCHAR(250), 
            @ProcessedCatNo VARCHAR(500), 
            @SessionID BIGINT, 
            @ItemStatusID INT, 
            @ItemEventID INT, 
            @Acknowledge VARCHAR(50),
            @CatNo VARCHAR(200), 
            @ProductId INT, 
            @ItemID INT, 
            @RFID VARCHAR(50), 
            @UserID INT, 
            @CurrentUtcTime DATETIME, 
            @ItemUsageID INT, 
            @ItemHistoryID INT,
            @MAPatientID VARCHAR(50), 
            @MAScheduleID INT, 
            @MAVisitID INT, 
            @OverrideId INT, 
            @OverrideNotes VARCHAR(255), 
            @PhysicianID VARCHAR(50), 
            @BillingStatusID INT, 
            @EntryTypeID INT,
            @SerialNo VARCHAR(50), 
            @MAPhysicianID INT, 
            @ComputerName VARCHAR(50), 
            @LotNo VARCHAR(50), 
            @ExpDate VARCHAR(50), 
            @Description VARCHAR(50), 
            @Manufacturer VARCHAR(50), 
            @BrandName VARCHAR(50),
            @BOID INT, 
            @DeviceID INT,  
            @LocationID INT, 
            @LocationTypeID INT, 
            @LocationID1 INT,  
            @LocationTypeID1 INT,
			@IsImplant VARCHAR(50),
			@IsLotNoReq VARCHAR(50),
			@IsSerialNoReq VARCHAR(50),
			@IsDiscontinue VARCHAR(50),
			@IsDiscontinueDate VARCHAR(50),
			@IsExpirationDateReq VARCHAR(50),
			@ModelNumber VARCHAR(200),
			@CompartmentID INT,
			@Qty INT,
			@ItemInventoryID INT,
			@UsageItemID INT,
			@isFDA VARCHAR(50);

    BEGIN TRY
        BEGIN TRANSACTION;

        SELECT @ProcessedBarcodeNo = Value FROM @udtEventData WHERE Property = 'ProcessedBarcodeNo';
        SELECT @FullBarcodeNo = Value FROM @udtEventData WHERE Property = 'FullBarcodeNo';
        SELECT @ProcessedCatNo = Value FROM @udtEventData WHERE Property = 'ProcessedCatNo';
        SELECT @UserID = Value FROM @udtEventData WHERE Property = 'UserID';
        SELECT @DeviceID = Value FROM @udtSystemData WHERE Property = 'DeviceID';
        SELECT @ComputerName = Value FROM @udtSystemData WHERE Property = 'ComputerName';
        SELECT @MAPatientID = Value FROM @udtEventData WHERE Property = 'MAPatientID';
        SELECT @MAVisitID = Value FROM @udtEventData WHERE Property = 'MAVisitID'; 
        SELECT @MAScheduleID = Value FROM @udtEventData WHERE Property = 'MAScheduleID'; 
        SELECT @ExpDate = Value FROM @udtEventData WHERE Property = 'ExpiryDate';
        SELECT @LotNo = Value FROM @udtEventData WHERE Property = 'LotNumber';
        SELECT @SerialNo = Value FROM @udtEventData WHERE Property = 'SerialNumber';
        SELECT @CatNo = Value FROM @udtEventData WHERE Property = 'CatalogNumber';    
        SELECT @Description = Value FROM @udtEventData WHERE Property = 'Description';    
        SELECT @Manufacturer = Value FROM @udtEventData WHERE Property = 'Manufacturer';    
        SELECT @BrandName = Value FROM @udtEventData WHERE Property = 'BrandName';
        SELECT @IsImplant = Value FROM @udtEventData WHERE Property = 'IsImplant';
		SELECT @IsLotNoReq = Value FROM @udtEventData WHERE Property = 'IsLotNoReq';
		SELECT @IsSerialNoReq = Value FROM @udtEventData WHERE Property = 'IsSerialNoReq';
		SELECT @IsDiscontinue = Value FROM @udtEventData WHERE Property = 'IsDiscontinue';
		SELECT @IsDiscontinueDate = Value FROM @udtEventData WHERE Property = 'IsDiscontinueDate';
		SELECT @IsExpirationDateReq = Value FROM @udtEventData WHERE Property = 'IsExpirationDateReq';
		SELECT @ModelNumber = Value FROM @udtEventData WHERE Property = 'ModelNumber';
		SELECT @isFDA = Value FROM @udtEventData WHERE Property = 'isFDA';
		SELECT @MAPatientID = Value FROM @udtEventData WHERE Property = 'MAPatientID'
		SELECT @MAVisitID = Value FROM @udtEventData WHERE Property = 'MAVisitID'
		SELECT @MAScheduleID = Value FROM @udtEventData WHERE Property = 'MAScheduleID'
		SELECT @Qty = Value FROM @udtEventData WHERE Property = 'Qty'
		set @ItemID = (select itemid from s01tblItems where RFID=@RFID)
		set @CompartmentID = (select CompartmentID from s01tblItems where ItemID=@ItemID)
		set @MAPhysicianID= (select MAPhysicianID from s01tblPatientSchedules where MAScheduleID=@MAScheduleID)
		SELECT @OverrideNotes = Value FROM @udtEventData WHERE Property = 'OverrideNotes'

        SELECT @PhysicianID = PhysicianID FROM s01qryPatientDisplayADT WHERE MAPatientID = @MAPatientID;
        SELECT @MAPhysicianID = MAPhysicianID FROM s01tblPhysicians WHERE PhysicianID = @PhysicianID;
        SELECT @SessionID = Value FROM @udtEventData WHERE Property = 'SessionID';
        SELECT @EntryTypeID = Value FROM @udtSystemData WHERE Property = 'EntryTypeID';
        SELECT @PhysicianID = PhysicianID FROM s01qryPatientDisplayADT WHERE MAPatientID = @MAPatientID;
        SELECT @BillingStatusID = BillingStatusID FROM s01tblbillingstatus WHERE BillingStatus = 'Pending Bill Approval';
        SELECT @OverrideId = OverrideID, @OverrideNotes = OverrideDesc FROM s01tblOverride WHERE OverrideDesc = 'One Time Use';

        IF (SELECT [Setting Value] FROM s01tblSettingsCommon WHERE [Setting Name] = 'ENABLE_UTC_TIMEZONE') = 'TRUE'
        BEGIN
            SELECT @CurrentUTCTime = GETUTCDATE();
        END
        ELSE
        BEGIN
            SELECT @CurrentUTCTime = GETDATE();
        END

        SELECT @ItemStatusID = ItemStatusID FROM s01tblItemStatus WHERE [Item Status] = 'Used';
        SELECT @ItemEventID = ItemEventID FROM s01tblItemEvent WHERE EventDescription = 'Item used';
        SELECT @LocationTypeID = [Setting Value] FROM s01tblSettingscommon WHERE [Setting Name] = 'BarcodeInventory_LocationType' --AND ClusterID = @DeviceID;
        SELECT @LocationID = [Setting Value] FROM s01tblSettingscommon WHERE [Setting Name] = 'BarcodeInventory_Location' --AND ClusterID = @DeviceID;

		IF NOT EXISTS (SELECT 1 FROM s01tblProducts WHERE [Cat No] = @CatNo)
        BEGIN
            EXECUTE uspSaveBillOnlyMasterFDAProducts 
                      @Event = @Event,
                      @udtSystemData = @udtSystemData,
                      @udtEventData = @udtEventData;
        END

        SELECT @ProductId = ProductID FROM s01tblProducts WHERE  [Cat No] = @CatNo;

		IF(@isFDA = 'TRUE')
		BEGIN
			IF(@IsImplant = 'Y' AND (@SerialNo IS NOT NULL OR @LotNo IS NOT NULL OR @ExpDate IS NOT NULL))
			BEGIN
				IF NOT EXISTS(SELECT 1 FROM s01tblItems WHERE [Serial No] = @SerialNo) 
				BEGIN
					INSERT INTO s01tblItems (
					ProductID, [Serial No], RFID, [Lot No], [Expired Date], [ItemStatusID], [Use Cycle], [AddUserID], [Date Added],
					[RmUserID], [Date Removed], [MAScheduleID], [MAPatientID], [MAPhysicianID], [Computer Name], [LastActivityDate], [Qty], [EntryTypeID]
				)
				SELECT @ProductId, @SerialNo, 'MA' + REPLACE(CAST(RAND() AS VARCHAR(50)), '.', '') + REPLACE(CONVERT(VARCHAR(10), @CurrentUtcTime, 120), '-', ''), @LotNo, @ExpDate, 4, 1, @UserID, @CurrentUtcTime, @UserID, @CurrentUtcTime,
					   @MAScheduleID, @MAPatientID, @MAPhysicianID, @ComputerName, @CurrentUtcTime, 1, @EntryTypeID;

				SELECT @ItemID = SCOPE_IDENTITY();

				INSERT INTO s01tblItemUsage (
					MAPatientID, MAScheduleID, MAVisitID, UsedBy, DateUsed, Qty, UnitPrice, BillingStatusID, OverrideID, OverrideNotes, UpdatedBy,
					LastActivityDate, UsageTypeID, UsageItemID, SessionID, UCDM, EntryTypeID
				)
				SELECT @MAPatientID, @MAScheduleID, @MAVisitID, @UserID, @CurrentUtcTime, 1, I.[Act Price], @BillingStatusID, @OverrideID, @OverrideNotes, @UserID, @CurrentUtcTime,
					   1, I.ItemID, @SessionID, I.ICDM, @EntryTypeID
				FROM s01tblItems I WHERE ItemID = @ItemID;

				SELECT @ItemUsageID = SCOPE_IDENTITY();

				INSERT INTO s01tblItemHistory(
					ItemStatusID, ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAScheduleID, MAVisitID, BillingStatusID, UsedBy, Qty,
					UnitPrice, UpdatedBy, UCDM, LastActivityDate, EntryTypeID, DateUsed, OverrideID, OverrideNotes, SessionID, ItemEventID
				)
				SELECT 1, @ItemUsageID, 1, U.UsageItemID, MAPatientID, MAScheduleID, MAVisitID, BillingStatusID, @UserID, U.Qty, U.UnitPrice,
					   @UserID, U.UCDM, @CurrentUtcTime, @EntryTypeID, U.DateUsed, 4, U.OverrideNotes, @SessionID, @ItemEventID  
				FROM s01tblItemUsage U WHERE SessionID = @SessionID AND U.LastActivityDate = @CurrentUtcTime;

				SELECT @ItemHistoryID = SCOPE_IDENTITY();

				INSERT INTO s01tblitemhistoryextn (ItemHistoryID, LocationID)
				SELECT @ItemHistoryID, @LocationID;

				INSERT INTO s01tblItemHistory(
					ItemStatusID, ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAScheduleID, MAVisitID, BillingStatusID, UsedBy, Qty,
					UnitPrice, UpdatedBy, UCDM, LastActivityDate, EntryTypeID, DateUsed, OverrideID, OverrideNotes, SessionID, ItemEventID
				)
				SELECT 1, @ItemUsageID, 1, U.UsageItemID, MAPatientID, MAScheduleID, MAVisitID, BillingStatusID, @UserID, U.Qty, U.UnitPrice,
					   @UserID, U.UCDM, @CurrentUtcTime, @EntryTypeID, U.DateUsed, 7, U.OverrideNotes, @SessionID, @ItemEventID  
				FROM s01tblItemUsage U WHERE SessionID = @SessionID AND U.LastActivityDate = @CurrentUtcTime;

				INSERT INTO s01tblitemhistoryextn (ItemHistoryID, LocationID)
				SELECT @ItemHistoryID, @LocationID;

				EXECUTE  uspSWCSendUsageToEpic @Event = @Event
					, @udtSystemData = @udtSystemData
					, @udtEventData = @udtEventData
					, @ItemEventID = 13 -- Credit , we can use 3 also here
					, @ItemStatusID = @ItemStatusID
					, @UsageTypeID = 1
					, @ItemID = @ItemID

				INSERT INTO s01tblProductsExtn
						   ([ProductID],[ModelNumber], [isSerialNoReq], [isLotNoReq], [isImplant], [isExpirationDateReq], 
							[isDiscontiued], [isDiscontiueDate], [LastActivityDate], [CreatedDate])
					VALUES
							   (@ProductId,@ModelNumber, @IsSerialNoReq, @IsLotNoReq, @IsImplant, @IsExpirationDateReq, @IsDiscontinue, @IsDiscontinueDate, @CurrentUtcTime, @CurrentUtcTime);


				SELECT 'SUCCESS' as DataSetName, 'Item has been removed' AS MSG;
				END
				ELSE
				BEGIN
					SELECT 'WARNING' AS DataSetName,'Serial number already exists' AS MSG
				END
                        
			END
			--ELSE IF(@IsImplant = 'N' AND (@IsLotNoReq = 'TRUE' OR @IsSerialNoReq = 'TRUE' OR @IsExpirationDateReq = 'TRUE'))
			ELSE IF(@IsImplant = 'N' AND (@SerialNo IS NOT NULL OR @LotNo IS NOT NULL OR @ExpDate IS NOT NULL))
			BEGIN
			   IF NOT EXISTS(SELECT 1 FROM s01tblItems WHERE [Serial No] = @SerialNo) 
			   BEGIN
					INSERT INTO s01tblItems (
						ProductID, [Serial No], RFID, [Lot No], [Expired Date], [ItemStatusID], [Use Cycle], [AddUserID], [Date Added],
						[RmUserID], [Date Removed], [MAScheduleID], [MAPatientID], [MAPhysicianID], [Computer Name], [LastActivityDate], [Qty], [EntryTypeID]
					)
					SELECT @ProductId, @SerialNo, 'MA' + REPLACE(CAST(RAND() AS VARCHAR(50)), '.', '') + REPLACE(CONVERT(VARCHAR(10), @CurrentUtcTime, 120), '-', ''), @LotNo, @ExpDate, 4, 1, @UserID, @CurrentUtcTime, @UserID, @CurrentUtcTime,
						   @MAScheduleID, @MAPatientID, @MAPhysicianID, @ComputerName, @CurrentUtcTime, 1, @EntryTypeID;

					SELECT @ItemID = SCOPE_IDENTITY();

					INSERT INTO s01tblItemUsage (
						MAPatientID, MAScheduleID, MAVisitID, UsedBy, DateUsed, Qty, UnitPrice, BillingStatusID, OverrideID, OverrideNotes, UpdatedBy,
						LastActivityDate, UsageTypeID, UsageItemID, SessionID, UCDM, EntryTypeID
					)
					SELECT @MAPatientID, @MAScheduleID, @MAVisitID, @UserID, @CurrentUtcTime, 1, I.[Act Price], @BillingStatusID, @OverrideID, @OverrideNotes, @UserID, @CurrentUtcTime,
						   1, I.ItemID, @SessionID, I.ICDM, @EntryTypeID
					FROM s01tblItems I WHERE ItemID = @ItemID;

					SELECT @ItemUsageID = SCOPE_IDENTITY();

					INSERT INTO s01tblItemHistory(
						ItemStatusID, ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAScheduleID, MAVisitID, BillingStatusID, UsedBy, Qty,
						UnitPrice, UpdatedBy, UCDM, LastActivityDate, EntryTypeID, DateUsed, OverrideID, OverrideNotes, SessionID, ItemEventID
					)
					SELECT 1, @ItemUsageID, 1, U.UsageItemID, MAPatientID, MAScheduleID, MAVisitID, BillingStatusID, @UserID, U.Qty, U.UnitPrice,
						   @UserID, U.UCDM, @CurrentUtcTime, @EntryTypeID, U.DateUsed, 4, U.OverrideNotes, @SessionID, @ItemEventID  
					FROM s01tblItemUsage U WHERE SessionID = @SessionID AND U.LastActivityDate = @CurrentUtcTime;

					SELECT @ItemHistoryID = SCOPE_IDENTITY();

					INSERT INTO s01tblitemhistoryextn (ItemHistoryID, LocationID)
					SELECT @ItemHistoryID, @LocationID;

					INSERT INTO s01tblItemHistory(
					ItemStatusID, ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAScheduleID, MAVisitID, BillingStatusID, UsedBy, Qty,
					UnitPrice, UpdatedBy, UCDM, LastActivityDate, EntryTypeID, DateUsed, OverrideID, OverrideNotes, SessionID, ItemEventID
					)
					SELECT @ItemStatusID, @ItemUsageID, 1, U.UsageItemID, MAPatientID, MAScheduleID, MAVisitID, BillingStatusID, @UserID, U.Qty, U.UnitPrice,
						   @UserID, U.UCDM, @CurrentUtcTime, @EntryTypeID, U.DateUsed, 7, U.OverrideNotes, @SessionID, @ItemEventID  
					FROM s01tblItemUsage U WHERE SessionID = @SessionID AND U.LastActivityDate = @CurrentUtcTime;

					INSERT INTO s01tblitemhistoryextn (ItemHistoryID, LocationID)
					SELECT @ItemHistoryID, @LocationID;


					EXECUTE  uspSWCSendUsageToEpic @Event = @Event
					, @udtSystemData = @udtSystemData
					, @udtEventData = @udtEventData
					, @ItemEventID = 13 -- Credit , we can use 3 also here
					, @ItemStatusID = @ItemStatusID
					, @UsageTypeID = 1
					, @ItemID = @ItemID

					INSERT INTO s01tblProductsExtn
						   ([ProductID],[ModelNumber], [isSerialNoReq], [isLotNoReq], [isImplant], [isExpirationDateReq], 
							[isDiscontiued], [isDiscontiueDate], [LastActivityDate], [CreatedDate])
					VALUES
							   (@ProductId,@ModelNumber, @IsSerialNoReq, @IsLotNoReq, @IsImplant, @IsExpirationDateReq, @IsDiscontinue, @IsDiscontinueDate, @CurrentUtcTime, @CurrentUtcTime);

					SELECT 'SUCCESS' as DataSetName, 'Item has been removed' AS MSG;
			   END
			   ELSE
			   BEGIN
					SELECT 'WARNING' AS DataSetName,'Serial number already exists' AS MSG
			   END
			END
			ELSE IF(@IsImplant = 'N' AND (@SerialNo IS NULL OR @LotNo IS NULL OR @ExpDate IS NULL)) 
			BEGIN
				INSERT INTO s01tblItemInventory(ProductID,Qty,UpdatedBy,LastActivityDate,LocationID,LocationTypeID)
				VALUES (@ProductId, @Qty, @UserID, @CurrentUTCTime, @LocationID, @LocationTypeID )

				SELECT @ItemInventoryID = SCOPE_IDENTITY()

				INSERT INTO s01tblItemUsage ( MAPatientID, MAVisitID, MAScheduleID, UsedBy, DateUsed, Qty, UnitPrice, UOMCode, BillingStatusID, OverrideID, OverrideNotes
															, UpdatedBy, LastActivityDate, UsageTypeID, UsageItemID, SessionID, EntryTypeID )
									SELECT @MAPatientID, @MAVisitID, @MAScheduleID, @UserID, @CurrentUtcTime, 1, [Ref Price], UOMCode, 1, @OverrideID, @OverrideNotes, @UserID
															, @CurrentUTCTime, 2, @ItemInventoryID, @SessionID, @EntryTypeID 
									FROM s01tblProducts 
									WHERE ProductID = @ProductID

									SELECT @ItemUsageID = SCOPE_IDENTITY()
									SELECT @UsageItemID = UsageItemID 
									FROM s01tblItemUsage 
									WHERE ItemUsageID = @ItemUsageID
							
									--UPDATE s01tblItemInventory 
									--SET Qty = Qty - 1, UpdatedBy = @UserID, QtyLastUpdated = @CurrentUTCTime 
									--WHERE ItemInventoryID = @UsageItemID 

									INSERT INTO s01tblUpdateBarcodeItemInventoryLog( ItemInventoryID, QtyInHand, QtyLastUpdated, QtyUsed 
															, QtyReturned, QtyDeleted, QtyStocked, QtyWasted, CutOffTime, UpdatedQtyInHand )
									SELECT ItemInventoryID, Qty + 1, QtyLastUpdated, 1, 0, 0, 0, 0, @CurrentUTCTime, Qty 
									FROM s01tblItemInventory
									WHERE ItemInventoryID = @UsageItemID

									INSERT INTO s01tblItemHistory ( ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAVisitID, MAScheduleID, UsedBy, DateUsed, UnitPrice, UOMCode
														, OverrideID, OverrideNotes, SessionID, UCDM, BillingStatusID, ItemStatusID, UpdatedBy, LastActivityDate, ItemEventID, Qty, EntryTypeID ) 
									SELECT ItemUsageID, UsageTypeID, UsageItemID, MAPatientID, MAVisitID, MAScheduleID, UsedBy, DateUsed, UnitPrice, UOMCode, @OverrideID, @OverrideNotes, @SessionID 
														, UCDM, BillingStatusID, NULL, @UserID, @CurrentUTCTime, @ItemEventID, 1, @EntryTypeID 
									FROM s01tblItemUsage 
									WHERE ItemUsageID = @ItemUsageID

									SELECT @ItemHistoryID = SCOPE_IDENTITY()
									INSERT INTO s01tblItemHistoryExtn (ItemHistoryID, LocationID ) 
									SELECT ItemHistoryID, @LocationID 
									FROM s01tblItemHistory 
									WHERE ItemHistoryID = @ItemHistoryID

									INSERT INTO tblInventoryLog ( ItemHistoryID, InvntoryID, ProcessStatus, ClusterID, LocationID, UpdatedBy, EntryTypeID, EnteredDate )
									SELECT ItemHistoryID, UsageItemID, 'P', @DeviceID, @LocationID, @UserID, @EntryTypeID, @CurrentUTCTime 
									FROM s01tblItemHistory 
									WHERE ItemHistoryID = @ItemHistoryID	
			END
		END
		ELSE IF(@isFDA = 'FALSE')
		BEGIN
			--TODO UPDATE RFID ITEM DETAILS WHICH ARE EXIST IN DATABASE
			EXEC uspUpdateOneTimeUseDetails @Event = @Event
					, @udtSystemData = @udtSystemData
					, @udtEventData = @udtEventData
		END

    COMMIT TRANSACTION;
    END TRY

    BEGIN CATCH
	IF XACT_STATE() <> 0
		SET @Msg = 'Error in Searching Product details';
	EXECUTE s01uspLogError
END CATCH
SET NOCOUNT OFF
END;

GO
/****** Object:  StoredProcedure [dbo].[uspSaveOneTimeUseMasterProductDetails]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS OFF
GO
SET QUOTED_IDENTIFIER OFF
GO

/************************************************************************************************************************
Name				: uspSaveBillOnlyMasterFDAProducts
Version				: ********
Purpose				: To save the product details from MASTER/FDA to Localhost
Author				: Subramanya Rao
Date Created 		: 18 FEB 2021
Application Affected: IriSupply 6.0
---------------------------------------------------------------------------------------------------------------------------
Release Notes		:

----------------------------------------------------------------------------------------------------------------------------
-- DB CALL

--------------------------------------------------------------------------------------------------------*/
CREATE PROCEDURE [dbo].[uspSaveOneTimeUseMasterProductDetails]
	@Event VARCHAR(255)
	, @udtSystemData udtSPParam READONLY
	, @udtEventData udtSPParam READONLY
AS

SET NOCOUNT ON;

DECLARE @Msg VARCHAR(200), @ProcessedBarcodeNo VARCHAR(250),  @FullBarcodeNo VARCHAR(250), @ProcessedCatNo VARCHAR (500), @SessionID BIGINT, @ItemStatusID INT, @ItemEventID INT
DECLARE @CatNo VARCHAR (200), @ProductId INT, @ItemID INT, @RemovedUserName VARCHAR(100), @RFID VARCHAR (500), @UserID INT, @CurrentUtcTime DATETIME, @ItemUsageID INT, @ItemHistoryID INT
DECLARE @MAPatientID VARCHAR(50), @MAScheduleID INT, @MAVisitID INT, @OverrideId INT, @OverrideNotes VARCHAR(255), @PhysicianID VARCHAR(50), @BillingStatusID INT, @EntryTypeID INT
		, @InsertFDAProduct INT, @InsertBracodeFDA INT,  @Description VARCHAR(50), @Manufacturer VARCHAR(50), @BrandName VARCHAR(50), @MastProductId INT

SET XACT_ABORT ON;
BEGIN TRY
BEGIN TRANSACTION

	--SELECT @ProcessedBarcodeNo =Value FROM  @udtEventData WHERE Property = 'ProcessedBarcodeNo'
	SELECT @FullBarcodeNo =Value FROM  @udtEventData WHERE Property = 'FullBarcodeNo'
	--SELECT @ProcessedCatNo =Value FROM  @udtEventData WHERE Property = 'ProcessedCatNo'

	SELECT @CatNo = Value FROM  @udtEventData WHERE Property = 'CatNo'	
	SELECT @Description =  Value FROM  @udtEventData WHERE Property = 'Description'	
	SELECT @Manufacturer =  Value FROM  @udtEventData WHERE Property = 'Manufacturer'	
	SELECT @BrandName =  Value FROM  @udtEventData WHERE Property = 'BrandName'

	IF EXISTS ( SELECT 1 FROM [iRISProductMaster].dbo.tblBarcodeRuleProductMaster WHERE replace(replace([Cat No],'-',''),'.','') = @CatNo)
	BEGIN
		IF NOT EXISTS (SELECT 1 FROM s01tblBarcodeRuleProduct WHERE replace(replace([Cat No],'-',''),'.','') = @CatNo)
		BEGIN
			INSERT INTO s01tblBarcodeRuleProduct ([Barcode No], [Cat No])
			SELECT [Barcode No], [Cat No]
			FROM [iRISProductMaster].dbo.tblBarcodeRuleProductMaster
			WHERE [Cat No] = @CatNo

			SET @InsertBracodeFDA = 1

			PRINT 'Inserted into s01tblBarcodeRuleProduct from MASTER'
		END
	END

	IF EXISTS ( SELECT 1 FROM [iRISProductMaster].dbo.tblProductsMaster WHERE replace(replace([Cat No],'-',''),'.','') = @CatNo)
	BEGIN
		IF NOT EXISTS (SELECT 1 FROM s01tblProducts WHERE replace(replace([Cat No],'-',''),'.','') = @CatNo)
		BEGIN
			INSERT INTO s01tblProducts 
						( [Cat No], NDC, [Brand Name], [Other Name], [Description 1], [Description 2], [Description 3], Size, Material, Manufacturer, [Ref Price], Picture, QtyPerPkg
						, RFID, [SKU No],[ID 1], [ID 2], [ID 3], [ID 4], [ID 5], [Entered By], [Computer Name], Domain, [Entered Date], ReorderTypeID, [Notify Interval]
						, [Notify Time], [Notify Sent], ProductTypeID, PCDM, MMID, ProductCategoryID, ProductSubCategoryID, ProductGroupID, EntryTypeID
						, [Min Stock], [Max Stock])

			SELECT        [Cat No], NDC, [Brand Name], [Other Name], [Description 1], [Description 2], [Description 3], Size, Material, Manufacturer, [Ref Price], Picture, QtyPerPkg
						, RFID, [SKU No],[ID 1], [ID 2], [ID 3], [ID 4], [ID 5], [Entered By], [Computer Name], Domain, [Entered Date], ReorderTypeID, [Notify Interval]
						, [Notify Time], [Notify Sent], ProductTypeID, PCDM, MMID, ProductCategoryID, ProductSubCategoryID, ProductGroupID, EntryTypeID
						, [Min Stock], [Max Stock]
			FROM [iRISProductMaster].dbo.tblProductsMaster
			WHERE [Cat No] = @CatNo

			PRINT 'Inserted into s01tblProducts from MASTER'

		END
		SET @InsertFDAProduct = 1
	END

	IF ( @InsertFDAProduct IS NULL)
	BEGIN
			INSERT INTO [iRISProductMaster].dbo.tblProductsMaster  
							( ProductID, [Cat No], [Brand Name], Manufacturer, [Description 1], IsFromFDA ) 
			SELECT MAX(ProductID)+1, @CatNo, @BrandName, @Manufacturer, @Description, 1
			FROM [iRISProductMaster].dbo.tblProductsMaster 

			SELECT @MastProductId = ProductId 
			FROM [iRISProductMaster].dbo.tblProductsMaster 
			WHERE replace(replace([Cat No],'-',''),'.','') = @CatNo
		 
			INSERT INTO s01tblProducts
						([Cat No], [Brand Name], Manufacturer, [Description 1] ) 
			SELECT  @CatNo, @BrandName, @Manufacturer, @Description

	PRINT 'Inserted into s01tblProducts and MASTER from FDA'

	END

	IF ( @InsertBracodeFDA IS NULL )
	BEGIN
		INSERT INTO [iRISProductMaster].dbo.tblBarcodeRuleProductMaster 
			   (RuleID, [Barcode No], [Cat No], IsFromFDA, ProductID)
		SELECT MAX(RuleID)+1, @FullBarcodeNo, @CatNo, 1, @MastProductId
		FROM [iRISProductMaster].dbo.tblBarcodeRuleProductMaster 

		INSERT INTO s01tblBarcodeRuleProduct 
			   ([Barcode No], [Cat No])
		SELECT  @FullBarcodeNo, @CatNo

		PRINT 'Inserted into s01tblBarcodeRuleProduct and tblBarcodeRuleProductMaster from FDA'
	END

	
COMMIT TRANSACTION		
END TRY

BEGIN CATCH
	IF XACT_STATE() <> 0
		ROLLBACK TRANSACTION
		SET @Msg = 'Error in Searching Product details';
	EXECUTE s01uspLogError
END CATCH
SET NOCOUNT OFF
GO
/****** Object:  StoredProcedure [dbo].[uspSWCSendUsageToEpic]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE PROCEDURE [dbo].[uspSWCSendUsageToEpic]

	  @Event VARCHAR(200)

	, @udtSystemData udtSPParam READONLY

	, @udtEventData udtSPParam READONLY

	, @ItemEventID INT

	, @ItemStatusID INT

	, @UsageTypeID INT

	, @ItemID INT

AS
 
SET NOCOUNT ON;
 
	DECLARE @UserID INT, @SessionID BIGINT, @Msg VARCHAR(500), @Transaction VARCHAR(10), @DeviceID INT

			, @ComputerName VARCHAR(50), @UsageItemID INT, @ItemUsageID INT
 
	SET XACT_ABORT ON;

		BEGIN TRY

		BEGIN TRANSACTION
 
		SELECT @SessionID = Value FROM @udtEventData WHERE Property = 'SessionID'

		SELECT @UserID = Value FROM @udtEventData WHERE Property = 'UserID'

		SELECT @DeviceID = Value FROM @udtSystemData WHERE Property = 'DeviceID'

		SELECT @ComputerName = Value FROM @udtSystemData WHERE Property = 'ComputerName'

		SELECT @ItemUsageID = ItemUsageID 

		from s01tblItemHistory where UsageItemID = @ItemID

 
	

				EXECUTE s01usp_LogItemTransaction

					 @ItemStatusID=@ItemStatusID

					 , @UsageTypeID=@UsageTypeID

					 , @UsageItemID=@ItemID

					 , @ItemEventID = @ItemEventID

					 , @ItemUsageID = @ItemUsageID

					 , @UserID = @UserID

					 , @ClusterID = @DeviceID

					 , @ComputerName = @ComputerName


	COMMIT TRANSACTION;

	END TRY

		BEGIN CATCH

			IF (XACT_STATE()) <> 0

				ROLLBACK TRANSACTION;

				SET @Msg = 'ERROR in Item sending usage to EPIC';

			EXECUTE s01uspLogError;

		END CATCH;

	SET NOCOUNT OFF

 
GO
/****** Object:  StoredProcedure [dbo].[uspSynonyms]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
/****** Object:  StoredProcedure [dbo].[uspUpdateFDAItemDetails]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE Procedure [dbo].[uspUpdateFDAItemDetails]
	@Event VARCHAR(255)
	, @udtSystemData udtSPParam READONLY
	, @udtEventData udtSPParam READONLY
AS

SET NOCOUNT ON;
DECLARE @RFID VARCHAR (500),@SerialNumber VARCHAR(200),@LotNumber VARCHAR(200),@ExpiryDate DATETIME,@CurrentDateTime DATETIME,@UserID INT
DECLARE @CatNo VARCHAR (200),@MasterProductId INT,@Manufacturer VARCHAR(50),@BrandName VARCHAR(50), @Description VARCHAR(50),@ScannedBarcode VARCHAR(70)
DECLARE @LocationID INT, @LocationTypeID INT, @DeviceID INT,@InputQty INT,@CurrentUtcTime DATETIME, @Msg VARCHAR(200),@ProductID VARCHAR(200),@ScannedBarcodeValue VARCHAR(100), @IsFDA VARCHAR(50)
DECLARE  @UOMCode VARCHAR(10), @FDACatNo VARCHAR(50)
BEGIN TRY

	
	SELECT @RFID =Value FROM  @udtEventData WHERE Property = 'RFID' 
	SELECT @CatNo =Value FROM  @udtEventData WHERE Property = 'CatalogNumber'
	SELECT @Manufacturer =Value FROM  @udtEventData WHERE Property = 'Manufacturer'
	SELECT @SerialNumber =Value FROM  @udtEventData WHERE Property = 'SerialNumber'
	SELECT @LotNumber =Value FROM  @udtEventData WHERE Property = 'LotNumber'
	SELECT @BrandName =Value FROM  @udtEventData WHERE Property = 'BrandName'
	SELECT @Description =Value FROM  @udtEventData WHERE Property = 'Description'
	SELECT @ScannedBarcode =Value FROM  @udtEventData WHERE Property = 'RFID' 
	SELECT @ExpiryDate =Value FROM  @udtEventData WHERE Property = 'ExpiryDate'
	SELECT @InputQty = Value FROM @udtEventData WHERE Property = 'Qty'
	SELECT @UserID =Value FROM  @udtEventData WHERE Property = 'UserID'
	SELECT @ScannedBarcodeValue =Value FROM  @udtEventData WHERE Property = 'RFID'  
	SELECT @IsFDA = Value FROM @udtEventData WHERE Property = 'IsFDA' 
	SELECT @CurrentDateTime = GETDATE();

	SELECT @LocationTypeID = [dbo].[udf700WebConsoleSUPFetchSettings] ( @DeviceId, 'BarcodeInventory_LocationType' )

	SELECT @LocationID = [dbo].[udf700WebConsoleSUPFetchSettings] ( @DeviceId, 'BarcodeInventory_Location' )
	IF (SELECT [Setting Value] FROM s01tblSettingsCommon WHERE [Setting Name] = 'ENABLE_UTC_TIMEZONE') = 'TRUE'
		BEGIN
			SELECT @CurrentUTCTime = GETUTCDATE()
		END
		ELSE
		BEGIN
			SELECT @CurrentUTCTime = GETDATE()
		END

		if (@IsFDA='true')
		begin
		
		--IF NOT EXISTS ( SELECT 1 FROM s01tblProducts 	
		--				WHERE REPLACE(REPLACE([Cat No],'-',''),'.','') = REPLACE(REPLACE(@CatNo,'-',''),'.','')
		--					OR @CatNo IS NULL OR @CatNo = ''
		--		      )
		--BEGIN
			
			EXECUTE uspHUBSaveBarcodeMasterFDAProducts 
					  @Event = @Event
					, @udtSystemData = @udtSystemData
					, @udtEventData = @udtEventData
			
			SELECT @CatNo = [Cat No]
			FROM s01tblBarcodeRuleProduct 
			WHERE [Barcode No] = @ScannedBarcodeValue

			SELECT @ProductID = ProductID, @UOMCode = UOMCode 
			FROM s01tblProducts 
			WHERE [Cat No] = @CatNo

			IF @ProductID IS NULL
			BEGIN
				SELECT @FDACatNo = [Cat No]
				FROM s01tblBarcodeRuleProduct 
				WHERE [Barcode No] = @ScannedBarcodeValue

				SELECT @ProductID = ProductID, @UOMCode = UOMCode 
				FROM s01tblProducts 
				WHERE [Cat No] = @FDACatNo
			END

			IF @ProductID IS NOT NULL
			BEGIN
				INSERT INTO s01tblItemInventory(ProductID,Qty,UpdatedBy,LastActivityDate,LocationID,LocationTypeID)
				VALUES (@ProductId, @inputQty, @UserID, @CurrentUTCTime, @LocationID, @LocationTypeID )
			
			IF 	@FDACatNo IS NOT NULL AND @FDACatNo <> ''
				SET @CatNo = @FDACatNo
			END
			
		--END
		end

	IF(@SerialNumber IS NOT NULL AND @LotNumber IS NOT NULL AND @ExpiryDate IS NOT NULL)
	BEGIN
		INSERT INTO s01tblItems(ProductID,RFID,[Serial No],[Lot No],[Expired Date],[Entered Date],LastActivityDate)
		VALUES (@ProductID,@RFID,@SerialNumber,@LotNumber,@ExpiryDate,@CurrentDateTime,@CurrentDateTime)
	END
	--ELSE
	--BEGIN
	--	IF (  @CatNo IS NOT NULL )
	--	BEGIN
	--		SET IDENTITY_INSERT s01tblProductsMaster ON		
	--	    INSERT INTO s01tblProductsMaster  
	--						( ProductID, [Cat No], [Brand Name], Manufacturer, [Description 1], IsFromFDA ) 
	--	    SELECT MAX(ProductID)+1, @CatNo, @BrandName, @Manufacturer, @Description, 1
	--	    FROM s01tblProductsMaster
	--		SET IDENTITY_INSERT s01tblProductsMaster OFF
			

	--		SELECT @MasterProductId = ProductID 
	--		FROM s01tblProductsMaster 
	--		WHERE replace(replace([Cat No],'-',''),'.','') = @CatNo

	--		INSERT INTO s01tblProducts
	--		([Cat No], [Brand Name], Manufacturer, [Description 1] ) 
	--		SELECT  @CatNo, @BrandName, @Manufacturer, @Description
	--		INSERT INTO s01tblBarcodeRuleProductMaster 
	--			   (RuleID, [Barcode No], [Cat No], IsFromFDA, ProductID)
	--		SELECT MAX(RuleID)+1, @ScannedBarcode, @CatNo, 1, @MasterProductId
	--		FROM s01tblBarcodeRuleProductMaster

	--		INSERT INTO s01tblBarcodeRuleProduct 
	--			   ([Barcode No], [Cat No])
	--		SELECT  @ScannedBarcode, @CatNo

	--		-------------------------------------------------------
	--		SELECT @CatNo = [Cat No]
	--		FROM s01tblBarcodeRuleProduct 
	--		WHERE [Barcode No] = @ScannedBarcode

	--		SELECT @ProductID = ProductID 
	--		FROM s01tblProducts 
	--		WHERE [Cat No] = @CatNo

	--		IF @ProductID IS NULL
	--		BEGIN				
	--			SELECT @ProductID = ProductID
	--			FROM s01tblProducts 
	--			WHERE [Cat No] = @CatNo
	--		END

	--		IF @ProductID IS NOT NULL
	--		BEGIN
	--			INSERT INTO s01tblItemInventory(ProductID,Qty,UpdatedBy,LastActivityDate,LocationID,LocationTypeID)
	--			VALUES (@ProductId, @InputQty, @UserID, @CurrentUTCTime, @LocationID, @LocationTypeID )

	--	PRINT 'Inserted into [IrisDB].dbo.tblBarcodeRuleProduct and tblBarcodeRuleProductMaster from FDA'
	--END
--END
--END
COMMIT TRANSACTION		
	END TRY
	BEGIN CATCH
		--IF XACT_STATE() <> 0
			--ROLLBACK TRANSACTION
			SET @Msg = 'Error in updating Barcode Inventory';
		EXECUTE s01uspLogError
	END CATCH
	SET NOCOUNT OFF
GO
/****** Object:  StoredProcedure [dbo].[uspUpdateOneTimeUseDetails]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [dbo].[uspUpdateOneTimeUseDetails]
    @Event VARCHAR(255),
    @udtSystemData udtSPParam READONLY,
    @udtEventData udtSPParam READONLY
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @ExpDate VARCHAR(50),
            @LotNo VARCHAR(50),
            @SerialNo VARCHAR(50),
            @ScannedRFID VARCHAR(50),
			@CatNo VARCHAR (200),
			@ProductID INT,
			@Qty INT, 
            @Msg VARCHAR(200);

    BEGIN TRY
        -- Start a transaction
        BEGIN TRANSACTION;

        -- Retrieve values from the event data
        SELECT @ExpDate = Value FROM @udtEventData WHERE Property = 'ExpiryDate';
        SELECT @LotNo = Value FROM @udtEventData WHERE Property = 'LotNumber';
        SELECT @SerialNo = Value FROM @udtEventData WHERE Property = 'SerialNumber';
        SELECT @ScannedRFID = Value FROM @udtEventData WHERE Property = 'RFID';
		SELECT @CatNo = Value FROM  @udtEventData WHERE Property = 'CatNo'	
		SELECT @Qty = Value FROM  @udtEventData WHERE Property = 'Qty'

        -- Update statement for the s01tblItems table
        IF NOT EXISTS (SELECT 1 FROM s01tblItems WHERE [Serial No] = @SerialNo AND [Lot No] = @LotNo)
        BEGIN
            -- Update the existing record if Serial No and Lot No match
            UPDATE s01tblItems
            SET [Serial No] = @SerialNo, [Lot No] = @LotNo, [Expired Date] = @ExpDate
            WHERE RFID = @ScannedRFID;

            SELECT 'Tagged Details' AS DataSetName, 'Successfully updated' AS MSG;
        END

		SELECT @CatNo = [Cat No] 
		FROM s01tblBarcodeRuleProduct 
		WHERE [Barcode No] = @ScannedRFID	

		SELECT @ProductID = ProductID
		FROM s01tblProducts
		WHERE [Cat No] = @CatNo		

		--IF EXISTS (SELECT 1 from s01tblBarcodeRuleProduct WHERE [Barcode No] = @ScannedRFID )
		--BEGIN
		--	UPDATE s01tblItemInventory
		--	Set Qty = Qty + @Qty
		--	where ProductID = @ProductID;
		--END

        -- Commit the transaction if no error occurs
        COMMIT TRANSACTION;
    END TRY
    BEGIN CATCH
        -- Handle errors
        IF XACT_STATE() <> 0
        BEGIN
            -- Rollback the transaction if an error occurs
            ROLLBACK TRANSACTION;

            -- Log the error using the error logging procedure
            SET @Msg = ERROR_MESSAGE();
            EXECUTE s01uspLogError @ErrorMessage = @Msg;
        END
    END CATCH

    SET NOCOUNT OFF;
END;
GO
/****** Object:  StoredProcedure [dbo].[uspWebFetchSettingValue]    Script Date: 01/09/2025 1:24:20 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
--USE [MA_SUP_TJUHOR_IRISDB_HUB]
--GO
--/****** Object:  StoredProcedure [dbo].[usp706WebConsoleGetProductName]    Script Date: 12/26/2024 2:03:32 PM ******/
--SET ANSI_NULLS ON
--GO
--SET QUOTED_IDENTIFIER ON
--GO
CREATE Procedure [dbo].[uspWebFetchSettingValue]
as
begin
    select [Setting Value] from s01tblSettingsCommon where [Setting Name] = 'USAGE_CONFIRM_MODE'
end
GO
USE [master]
GO
ALTER DATABASE [MA_SUP_SITENAME_IRISHUBDB] SET  READ_WRITE 
GO
