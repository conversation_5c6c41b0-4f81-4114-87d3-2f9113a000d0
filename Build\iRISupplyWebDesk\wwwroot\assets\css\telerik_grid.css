﻿body {
    padding-top: 50px;
    padding-bottom: 20px;
    background-color: #FFFFFF !important;
}

/* Set padding to keep content from hitting the edges */
.body-content {
    padding-left: 15px;
    padding-right: 15px;
}

/* Override the default bootstrap behavior where horizontal description lists 
   will truncate terms that are too long to fit in the left column 
*/
.dl-horizontal dt {
    white-space: normal;
}

/* Set width on the form input elements since they're 100% wide by default */
input,
select,
textarea {
    max-width: 280px;
}

.k-grid-header {
    color: #23459C !important;
    font-weight: bold !important;
    font-size: 10.67px !important;
    font-family: Verdana, Arial, Helvetica, Geneva, sans-serif !important;
    background-color: #F4FAFF !important;
    border-color: #B4C9D3 !important;
    border-bottom: 1px solid #B4C9D3 !important;
    border-right: 1px solid #B4C9D3 !important;
    border-top: 1px solid #B4C9D3 !important;
    border-left: 1px solid #B4C9D3 !important;
}

.k-master-row {
    FONT-FAMILY: Verdana,Arial,Helvetica,Geneva,sans-serif !important;
    /*COLOR: #333333;*/
    FONT-SIZE: 8pt !important;
    FONT-WEIGHT: normal !important;
    TEXT-DECORATION: none !important;
    height: 4px !important;
    
}

.k-grid .k-alt{
    /*background-color: #FFF !important;*/
}

.k-grid td {
    border-Bottom: 1px solid #B4C9D3 !important;
    /*border-right: 1px solid #B4C9D3 !important;*/
    /*background-color: #ffffff !important;*/
}
.k-grid-pager{
    border:0px !important;
}


.k-toolbar .k-button {
    background-color: #006699 !important;
    color: white !important;
    font-weight: bold !important;
    font-family: Verdana,Arial,Helvetica,Geneva,sans-serif !important;
    font-size: 11px !important;
    padding-top:0px !important;
    padding-bottom:0px !important;
}

.k-toolbar .k-button:nth-child(1) {
    margin-right: 10px;
}

.k-icon .k-i-check{
    color: white !important;
}
#Grid .k-i-close::before {
    content: "" !important;
}

#Grid .k-command-cell .k-icon{
    display: none !important;
}

#Grid .k-grid-delete {
    background-color: transparent !important;
    border: 0px solid white !important;
    color: #23459C !important;
    font-size: 11px !important;
    font-family: Verdana,Arial,Helvetica,Geneva,sans-serif !important;
}
.k-pager-info.k-label {
    font-size: 11px !important;
    font-family: Verdana,Arial,Helvetica,Geneva,sans-serif !important;
}
 .k-grid-toolbar {
    padding-top: 0px !important;
    padding-bottom:2px !important;
}

.k-grid {
    /*border-top: 1px !important;
    border-left: 1px !important;*/
}
.k-i-more-vertical::before {
    padding-top: 7px !important;
}
.k-pager-numbers .k-link.k-state-selected, .k-link .k-pager-nav {
    background-color: #006699 !important;
    color: white !important;
    /*font-weight: bold !important;*/
    font-family: Verdana,Arial,Helvetica,Geneva,sans-serif !important;
    font-size: 10px !important;
    border-color: #006699 !important;
}
.k-pager-numbers .k-link {
    color: #006699 !important;
    /*font-weight: bold !important;*/
    font-family: Verdana,Arial,Helvetica,Geneva,sans-serif !important;
    font-size: 10px !important;
    border-color: #006699 !important;
}
.k-i-arrow-60-right::before, .k-i-arrow-60-down::before, .k-i-arrow-end-right::before, .k-i-arrow-60-left::before, .k-i-arrow-end-right::before {
    color: #006699 !important;
    border-color: #006699 !important;
}
.k-pager-nav.k-link, .k-pager-refresh {
    color: #006699 !important;
    border-color: #006699 !important;
}
.k-button-primary, .k-button.k-primary {
    background-color: #006699 !important;
    color: white !important;
    font-weight: bold !important;
    font-family: Verdana,Arial,Helvetica,Geneva,sans-serif !important;
    font-size: 11px !important;
    padding-top: 0px !important;
    padding-bottom: 0px !important;
}
.blue_btn {
    background-color: #006699 !important;
    border-width : 0px !important;
    color: white !important;
    font-weight: bold !important;
    font-family: Verdana,Arial,Helvetica,Geneva,sans-serif !important;
    font-size: 11px !important;
    padding-top: 0px !important;
    padding-bottom: 0px !important;
    height: 19px;
    margin-top: 3px;
    margin-right: 5px;
}
.fieldHeader {
    font-family: Verdana,Arial,Helvetica,Geneva,sans-serif !important;
    font-size: 11px !important;
}
.fieldvalue {
    font-family: Verdana,Arial,Helvetica,Geneva,sans-serif !important;
    font-size: 11px !important;
}





    

