CREATE TABLE [dbo].[tblProductsExtn](
	[ProductExtensionID] [int] IDENTITY(1,1) NOT NULL,
	[ProductID] [int] NOT NULL,
	[ModelNumber] [varchar](200) NOT NULL,
	[isSerialNoReq] [varchar](10) NULL,
	[isLotNoReq] [varchar](10) NULL,
	[isImplant] [varchar](10) NULL,
	[isExpirationDateReq] [varchar](10) NULL,
	[isDiscontiued] [varchar](10) NULL,
	[isDiscontiueDate] [varchar](10) NULL,
	[LastActivityDate] [datetime] NULL,
	[CreatedDate] [datetime] NOT NULL,
PRIMARY KEY CLUSTERED 
(
	[ProductExtensionID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
CREATE VIEW [dbo].[qryItemsOneTimeUse]
AS
SELECT	I.ItemID, I.ItemStatusID, IST.[Item Status], PR.[Cat No], PR.NDC, 
	PR.[Description 1], PR.Manufacturer, I.[Lot No], I.[Expired Date], I.[Serial No], 
	P.PatientID, P.[Patient Name], PS.AppointmentID, PS.PhysicianID, 
	PS.[Physician Name], I.[Use Cycle], S.[Supplier Name], PR.[Ref Price], 
	U2.[User Name] AS [Added By], I.[Date Added], U1.[User Name] AS [Removed By], I.[Date Removed], 
	I.[Act Price], U3.[User Name] AS [Entered By], I.[Entered Date], I.CompartmentID, I.SupplierID, 
	I.AddUserID, I.RmUserID, I.[Domain], I.[Computer Name], I.RFID, I.MAPatientID, 
	I.MAPhysicianID, I.MAScheduleID, PR.ProductID, I.LastActivityDate, I.ICDM, 
	PR.PCDM, PR.MMID, PR.ProductCategoryID, PR.ProductSubCategoryID, 
	PR.ProductGroupID, I.Consignment, I.Misc1, I.Misc2, I.Misc3, C.ClusterID, 
	C.[Cluster Name], C.[Cabinet Name], C.[Cabinet Type], C.[Compartment Name], PS.ProcedureCode, 
	PS.ProcedureDesc, PR.QtyPerPkg, PR.UOMCode, I.Qty, PR.ProdUnitID, 
	C.LocationTypeID, C.[Cluster Location], I.EntUserID, PS.[Account Number], 
	PS.AltAcctNo, PS.MAVisitID, C.LocationID, PR.ProductTypeID, PR.[Notify Interval],
	ProductCategory, Size,LR.department
	--10/23/22 Jerome added fields for tissue verification report
	,PR.[ID 1]
	,S.[Contact Address]
	,S.Number
	,PR.[Brand Name]
FROM tblItems I 
		LEFT OUTER JOIN qryProducts PR ON I.ProductID = PR.ProductID
		LEFT OUTER JOIN tblSuppliers S ON I.SupplierID = S.SupplierID
		LEFT OUTER JOIN	qryCabinets C ON IIF(I.CompartmentID IS NULL, IIF(ISNUMERIC(I.Domain)=1, I.Domain, 120), I.CompartmentID) = C.CompartmentID
		LEFT OUTER JOIN tblLocationRoom LR ON C.LocationID = LR.RoomID
		LEFT OUTER JOIN qryPatientSchedules PS ON I.MAScheduleID = PS.MAScheduleID
		LEFT OUTER JOIN qryUsers U3 ON I.EntUserID = U3.UserID
		LEFT OUTER JOIN	qryPatients P ON I.MAPatientID = P.MAPatientID
		LEFT OUTER JOIN	qryUsers U2 ON I.AddUserID = U2.UserID
		LEFT OUTER JOIN	qryUsers U1 ON I.RmUserID = U1.UserID
		LEFT OUTER JOIN	tblItemStatus IST ON I.ItemStatusID = IST.ItemStatusID
GO