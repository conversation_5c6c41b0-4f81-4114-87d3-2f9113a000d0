﻿###########################################################################################################################################################################
# HEADER TEMPLATE

param (
    $OutputPath ="",
    $SQLScriptFielPath =  "",
    $ServerName="",
    $SiteName="" ,
    $synonymDataBaseName = "" ,
    $DBUsername = "",
    $DBPassword = "",
    $synonymServerName = ""
    
   
)

if($OutputPath.Length -gt 0)
    { $FileName=Split-Path -Leaf $OutputPath
        If($FileName.IndexOf('.') -gt 0)
            {$LogFile=$OutputPath}        
        else
            {$LogFile=Join-Path $OutputPath $MyInvocation.MyCommand.Name.Replace('.ps1','.log')}
    }
else
    {
       if ($MyInvocation.PSCommandPath.Length -eq 0)
            {$LogFile=$PSCommandPath.Replace('.ps1',(Get-Date -format "_yyyyMMdd_hhmmss") +".log")}
        else 
            {$LogFile=$MyInvocation.PSCommandPath.Replace('.ps1','') + (Get-Date -format "_yyyyMMdd_hhmmss") + "_" + $MyInvocation.MyCommand.Name.Replace('.ps1','.log') }

 }

start-transcript -Path $LogFile -IncludeInvocationHeader -Append

"PSScriptRoot=$PSScriptRoot"
"PSCommandPath=$PSCommandPath"
"ScriptDir=$ScriptDir"
"LogFile=$LogFile"
"MyInvocation.MyCommand.Name=" + $MyInvocation.MyCommand.Name
""
""
###########################################################################################################################################################################
# INSERT YOUR SCRIPT IN THIS SECTION

#Find and Replace the DatabaseName 
(Get-Content $SQLScriptFielPath).replace('SITENAME', $SiteName).replace('SYNONYMDATABASENAME', $synonymDataBaseName) | Set-Content $SQLScriptFielPath

#Insatll SQl script 
Invoke-Sqlcmd -ServerInstance $ServerName -Database "master" -U $DBUsername -P $DBPassword -InputFile $SQLScriptFielPath 

#Revert back DatabaseName
(Get-Content $SQLScriptFielPath).replace($SiteName, 'SITENAME') | Set-Content $SQLScriptFielPath

###########################################################################################################################################################################
# FOOTER TEMPLATE
""
"Process Complete."
""
stop-transcript

if(((get-content -path $LogFile) -like "*error*").length -gt 0){Ren $LogFile ($LogFile.Replace(".log","") +'_ERROR.log') }

###########################################################################################################################################################################

# SIG # Begin signature block
# MIIpqgYJKoZIhvcNAQcCoIIpmzCCKZcCAQExDzANBglghkgBZQMEAgEFADB5Bgor
# BgEEAYI3AgEEoGswaTA0BgorBgEEAYI3AgEeMCYCAwEAAAQQH8w7YFlLCE63JNLG
# KX7zUQIBAAIBAAIBAAIBAAIBADAxMA0GCWCGSAFlAwQCAQUABCB+Z0EcRevn4LYu
# RGywVA0gjsJJLWsTCyBGP2dPhnhlhaCCEpcwggVvMIIEV6ADAgECAhBI/JO0YFWU
# jTanyYqJ1pQWMA0GCSqGSIb3DQEBDAUAMHsxCzAJBgNVBAYTAkdCMRswGQYDVQQI
# DBJHcmVhdGVyIE1hbmNoZXN0ZXIxEDAOBgNVBAcMB1NhbGZvcmQxGjAYBgNVBAoM
# EUNvbW9kbyBDQSBMaW1pdGVkMSEwHwYDVQQDDBhBQUEgQ2VydGlmaWNhdGUgU2Vy
# dmljZXMwHhcNMjEwNTI1MDAwMDAwWhcNMjgxMjMxMjM1OTU5WjBWMQswCQYDVQQG
# EwJHQjEYMBYGA1UEChMPU2VjdGlnbyBMaW1pdGVkMS0wKwYDVQQDEyRTZWN0aWdv
# IFB1YmxpYyBDb2RlIFNpZ25pbmcgUm9vdCBSNDYwggIiMA0GCSqGSIb3DQEBAQUA
# A4ICDwAwggIKAoICAQCN55QSIgQkdC7/FiMCkoq2rjaFrEfUI5ErPtx94jGgUW+s
# hJHjUoq14pbe0IdjJImK/+8Skzt9u7aKvb0Ffyeba2XTpQxpsbxJOZrxbW6q5KCD
# J9qaDStQ6Utbs7hkNqR+Sj2pcaths3OzPAsM79szV+W+NDfjlxtd/R8SPYIDdub7
# P2bSlDFp+m2zNKzBenjcklDyZMeqLQSrw2rq4C+np9xu1+j/2iGrQL+57g2extme
# me/G3h+pDHazJyCh1rr9gOcB0u/rgimVcI3/uxXP/tEPNqIuTzKQdEZrRzUTdwUz
# T2MuuC3hv2WnBGsY2HH6zAjybYmZELGt2z4s5KoYsMYHAXVn3m3pY2MeNn9pib6q
# RT5uWl+PoVvLnTCGMOgDs0DGDQ84zWeoU4j6uDBl+m/H5x2xg3RpPqzEaDux5mcz
# mrYI4IAFSEDu9oJkRqj1c7AGlfJsZZ+/VVscnFcax3hGfHCqlBuCF6yH6bbJDoEc
# QNYWFyn8XJwYK+pF9e+91WdPKF4F7pBMeufG9ND8+s0+MkYTIDaKBOq3qgdGnA2T
# OglmmVhcKaO5DKYwODzQRjY1fJy67sPV+Qp2+n4FG0DKkjXp1XrRtX8ArqmQqsV/
# AZwQsRb8zG4Y3G9i/qZQp7h7uJ0VP/4gDHXIIloTlRmQAOka1cKG8eOO7F/05QID
# AQABo4IBEjCCAQ4wHwYDVR0jBBgwFoAUoBEKIz6W8Qfs4q8p74Klf9AwpLQwHQYD
# VR0OBBYEFDLrkpr/NZZILyhAQnAgNpFcF4XmMA4GA1UdDwEB/wQEAwIBhjAPBgNV
# HRMBAf8EBTADAQH/MBMGA1UdJQQMMAoGCCsGAQUFBwMDMBsGA1UdIAQUMBIwBgYE
# VR0gADAIBgZngQwBBAEwQwYDVR0fBDwwOjA4oDagNIYyaHR0cDovL2NybC5jb21v
# ZG9jYS5jb20vQUFBQ2VydGlmaWNhdGVTZXJ2aWNlcy5jcmwwNAYIKwYBBQUHAQEE
# KDAmMCQGCCsGAQUFBzABhhhodHRwOi8vb2NzcC5jb21vZG9jYS5jb20wDQYJKoZI
# hvcNAQEMBQADggEBABK/oe+LdJqYRLhpRrWrJAoMpIpnuDqBv0WKfVIHqI0fTiGF
# OaNrXi0ghr8QuK55O1PNtPvYRL4G2VxjZ9RAFodEhnIq1jIV9RKDwvnhXRFAZ/ZC
# J3LFI+ICOBpMIOLbAffNRk8monxmwFE2tokCVMf8WPtsAO7+mKYulaEMUykfb9gZ
# pk+e96wJ6l2CxouvgKe9gUhShDHaMuwV5KZMPWw5c9QLhTkg4IUaaOGnSDip0TYl
# d8GNGRbFiExmfS9jzpjoad+sPKhdnckcW67Y8y90z7h+9teDnRGWYpquRRPaf9xH
# +9/DUp/mBlXpnYzyOmJRvOwkDynUWICE5EV7WtgwggYcMIIEBKADAgECAhAz1wio
# kUBTGeKlu9M5ua1uMA0GCSqGSIb3DQEBDAUAMFYxCzAJBgNVBAYTAkdCMRgwFgYD
# VQQKEw9TZWN0aWdvIExpbWl0ZWQxLTArBgNVBAMTJFNlY3RpZ28gUHVibGljIENv
# ZGUgU2lnbmluZyBSb290IFI0NjAeFw0yMTAzMjIwMDAwMDBaFw0zNjAzMjEyMzU5
# NTlaMFcxCzAJBgNVBAYTAkdCMRgwFgYDVQQKEw9TZWN0aWdvIExpbWl0ZWQxLjAs
# BgNVBAMTJVNlY3RpZ28gUHVibGljIENvZGUgU2lnbmluZyBDQSBFViBSMzYwggGi
# MA0GCSqGSIb3DQEBAQUAA4IBjwAwggGKAoIBgQC70f4et0JbePWQp64sg/GNIdMw
# hoV739PN2RZLrIXFuwHP4owoEXIEdiyBxasSekBKxRDogRQ5G19PB/YwMDB/NSXl
# wHM9QAmU6Kj46zkLVdW2DIseJ/jePiLBv+9l7nPuZd0o3bsffZsyf7eZVReqskmo
# PBBqOsMhspmoQ9c7gqgZYbU+alpduLyeE9AKnvVbj2k4aOqlH1vKI+4L7bzQHkND
# brBTjMJzKkQxbr6PuMYC9ruCBBV5DFIg6JgncWHvL+T4AvszWbX0w1Xn3/YIIq62
# 0QlZ7AGfc4m3Q0/V8tm9VlkJ3bcX9sR0gLqHRqwG29sEDdVOuu6MCTQZlRvmcBME
# Jd+PuNeEM4xspgzraLqVT3xE6NRpjSV5wyHxNXf4T7YSVZXQVugYAtXueciGoWnx
# G06UE2oHYvDQa5mll1CeHDOhHu5hiwVoHI717iaQg9b+cYWnmvINFD42tRKtd3V6
# zOdGNmqQU8vGlHHeBzoh+dYyZ+CcblSGoGSgg8sCAwEAAaOCAWMwggFfMB8GA1Ud
# IwQYMBaAFDLrkpr/NZZILyhAQnAgNpFcF4XmMB0GA1UdDgQWBBSBMpJBKyjNRsjE
# osYqORLsSKk/FDAOBgNVHQ8BAf8EBAMCAYYwEgYDVR0TAQH/BAgwBgEB/wIBADAT
# BgNVHSUEDDAKBggrBgEFBQcDAzAaBgNVHSAEEzARMAYGBFUdIAAwBwYFZ4EMAQMw
# SwYDVR0fBEQwQjBAoD6gPIY6aHR0cDovL2NybC5zZWN0aWdvLmNvbS9TZWN0aWdv
# UHVibGljQ29kZVNpZ25pbmdSb290UjQ2LmNybDB7BggrBgEFBQcBAQRvMG0wRgYI
# KwYBBQUHMAKGOmh0dHA6Ly9jcnQuc2VjdGlnby5jb20vU2VjdGlnb1B1YmxpY0Nv
# ZGVTaWduaW5nUm9vdFI0Ni5wN2MwIwYIKwYBBQUHMAGGF2h0dHA6Ly9vY3NwLnNl
# Y3RpZ28uY29tMA0GCSqGSIb3DQEBDAUAA4ICAQBfNqz7+fZyWhS38Asd3tj9lwHS
# /QHumS2G6Pa38Dn/1oFKWqdCSgotFZ3mlP3FaUqy10vxFhJM9r6QZmWLLXTUqwj3
# ahEDCHd8vmnhsNufJIkD1t5cpOCy1rTP4zjVuW3MJ9bOZBHoEHJ20/ng6SyJ6UnT
# s5eWBgrh9grIQZqRXYHYNneYyoBBl6j4kT9jn6rNVFRLgOr1F2bTlHH9nv1HMePp
# GoYd074g0j+xUl+yk72MlQmYco+VAfSYQ6VK+xQmqp02v3Kw/Ny9hA3s7TSoXpUr
# OBZjBXXZ9jEuFWvilLIq0nQ1tZiao/74Ky+2F0snbFrmuXZe2obdq2TWauqDGIgb
# MYL1iLOUJcAhLwhpAuNMu0wqETDrgXkG4UGVKtQg9guT5Hx2DJ0dJmtfhAH2KpnN
# r97H8OQYok6bLyoMZqaSdSa+2UA1E2+upjcaeuitHFFjBypWBmztfhj24+xkc6Zt
# CDaLrw+ZrnVrFyvCTWrDUUZBVumPwo3/E3Gb2u2e05+r5UWmEsUUWlJBl6MGAAjF
# 5hzqJ4I8O9vmRsTvLQA1E802fZ3lqicIBczOwDYOSxlP0GOabb/FKVMxItt1UHeG
# 0PL4au5rBhs+hSMrl8h+eplBDN1Yfw6owxI9OjWb4J0sjBeBVESoeh2YnZZ/WVim
# VGX/UUIL+Efrz/jlvzCCBwAwggVooAMCAQICEQCHABqwx1IDoBa/pq5PBMtdMA0G
# CSqGSIb3DQEBCwUAMFcxCzAJBgNVBAYTAkdCMRgwFgYDVQQKEw9TZWN0aWdvIExp
# bWl0ZWQxLjAsBgNVBAMTJVNlY3RpZ28gUHVibGljIENvZGUgU2lnbmluZyBDQSBF
# ViBSMzYwHhcNMjIwNTEzMDAwMDAwWhcNMjMwNTEzMjM1OTU5WjCBxTEQMA4GA1UE
# BRMHMjkzMzA2NTETMBEGCysGAQQBgjc8AgEDEwJVUzEdMBsGCysGAQQBgjc8AgEC
# EwxQZW5uc3lsdmFuaWExHTAbBgNVBA8TFFByaXZhdGUgT3JnYW5pemF0aW9uMQsw
# CQYDVQQGEwJVUzEVMBMGA1UECAwMUGVubnN5bHZhbmlhMRwwGgYDVQQKDBNNb2Jp
# bGUgQXNwZWN0cywgSW5jMRwwGgYDVQQDDBNNb2JpbGUgQXNwZWN0cywgSW5jMIIC
# IjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEAmGPmAgzqoLOQQDRQ8a3XAEtm
# dPYhwhwrk8gNYWKb32Gky4cTauz3FgmZbw67CVWLjvynXi0S4jsYxfLJoGhxhUtP
# z8rU91cX4fv6UPGeL0R7wmNMeo7yQp6DdcMl6Y8raxXrvU9oJ3zycdi9oPAdlv2P
# TXdud42Z9KzJnux6jRruKaRDiccbzp2G9ccg+foRifAfHOZmZrnjkzLM+ztmC2C/
# M8FLWpOMgCV8LIfAa6ZrZiSASLvIKcLl2mCLtOvcv3jx5+ZAMMcBdUbpGdd8oR/w
# Orc2bvuwDBMG0M2iHRiIDMdhg5Gu5cqLB5Pmt0iBN39+Gt0ohXdh4OOqXRGb7lfu
# lgTTsvyoLzg2JHn890p0jg7SU6vimXCsfo2GDlJuhGSNiJcF3fkQZDCSVjG0xJJt
# gzZxTBkrAyGyEzjtXCO24awS2X9fGqa79EBJ78DxuxIfVawzlEL91XsPQvAaYWAH
# twQUXcnI6Kob6iDMmqqx6iQBdtGnktm4gENZrqlKBzct0HJU95X4X8POO43DTcJ1
# XtoPT2R6cqNKGYMqgnJD2SPXBNMEMaWP2RmWxYmSohKsa+fEU4vcuYaQqZk+9SW5
# a4BPQ66JFOHpYj6L0aUK2pKAnBtDerh48OUZI16C3ROsKZ696hXXyGghfRtbgS/x
# niUdzYmtnd0twHhOgWsCAwEAAaOCAdYwggHSMB8GA1UdIwQYMBaAFIEykkErKM1G
# yMSixio5EuxIqT8UMB0GA1UdDgQWBBRYZfuJW//OuukFmwkB5s8w3lxWVDAOBgNV
# HQ8BAf8EBAMCB4AwDAYDVR0TAQH/BAIwADATBgNVHSUEDDAKBggrBgEFBQcDAzBJ
# BgNVHSAEQjBAMDUGDCsGAQQBsjEBAgEGATAlMCMGCCsGAQUFBwIBFhdodHRwczov
# L3NlY3RpZ28uY29tL0NQUzAHBgVngQwBAzBLBgNVHR8ERDBCMECgPqA8hjpodHRw
# Oi8vY3JsLnNlY3RpZ28uY29tL1NlY3RpZ29QdWJsaWNDb2RlU2lnbmluZ0NBRVZS
# MzYuY3JsMHsGCCsGAQUFBwEBBG8wbTBGBggrBgEFBQcwAoY6aHR0cDovL2NydC5z
# ZWN0aWdvLmNvbS9TZWN0aWdvUHVibGljQ29kZVNpZ25pbmdDQUVWUjM2LmNydDAj
# BggrBgEFBQcwAYYXaHR0cDovL29jc3Auc2VjdGlnby5jb20wSAYDVR0RBEEwP6An
# BggrBgEFBQcIA6AbMBkMF1VTLVBFTk5TWUxWQU5JQS0yOTMzMDY1gRRpdEBtb2Jp
# bGVhc3BlY3RzLmNvbTANBgkqhkiG9w0BAQsFAAOCAYEAbrUSkAsNnVPFZ2F6Js+4
# pL0/mzEf86842ENmk5nlFZJYfeLNTkOSnsRA34qitApnyKOuLgdwIYlw/PB53hdb
# U8clwmdriqcDWefpHf6SxDdwj3P3140vLEYVHfMou1yJwzBHuR/h5qKKrlv9AVex
# kIGy9SbMe6Sf5llS2AUq/SiWbhpElkLoL/JX9Co6N+y/GRgRN7VX5f1gJj++svV9
# aCyUauQYYnXrhMJx4Prw+DKYOLdh/tDvV8OB40KytvTx7xWz6dck+KwA9UUoakiU
# K2Qusz4YWHGDGkmherfF8x5BfZTCGLS72kvX1RqkWWF/TgYb4m9ZRP4VuFnZ7Kui
# +TneCRUvakqM4bRa1lnrZDLi83kE658PSiPzaqHhct9Pfl6dnAxKpAh6Rsbv1V0E
# 2Oc/BjF9r/4uy7BAlakKmoJKKSupmEyNRf1Ptl1EEVy/JPm9NmlzZb4YgpvsF/oQ
# 5WfRPV+RyZmOjuu4eAQmtNdlglip347gM/1F8spPidanMYIWaTCCFmUCAQEwbDBX
# MQswCQYDVQQGEwJHQjEYMBYGA1UEChMPU2VjdGlnbyBMaW1pdGVkMS4wLAYDVQQD
# EyVTZWN0aWdvIFB1YmxpYyBDb2RlIFNpZ25pbmcgQ0EgRVYgUjM2AhEAhwAasMdS
# A6AWv6auTwTLXTANBglghkgBZQMEAgEFAKB8MBAGCisGAQQBgjcCAQwxAjAAMBkG
# CSqGSIb3DQEJAzEMBgorBgEEAYI3AgEEMBwGCisGAQQBgjcCAQsxDjAMBgorBgEE
# AYI3AgEVMC8GCSqGSIb3DQEJBDEiBCC3v95BadMMUPM/o+JrM6DFaXPfTbiPKVgt
# Gf3+VymtHjANBgkqhkiG9w0BAQEFAASCAgBk1pC9pOtHnKsHF9/ejGLlWg4TGs8c
# 8eA/UPvXpsWFrSeSLX3b+vTXOK11fzvwLm+9VB8jxP1FG1GeBRY0u6AwrSuJksKg
# g2mHUkbHiSjruUMzlz9XMmDaGp6V0X1MicEEnrfZB/oyqlQdyMtXfg560wJ0q4/w
# LjW+BRblQ6uYKCVDqE4l3OksUNZNUOkjVrkijCzbQ0k43IRuWS1C82loTu//cWLm
# wX1tub711Y0s0qFUjCibLj7/xZWaXQkZjnHwvv8MBi6DkYuIE97n6DxjVAe7xL2u
# O9D09dgxg6EucLHrZcpTZXg1VCfSB1MpauHYRyqEvgJICydXHTgdFiPwXeMmS3u4
# iwyuW4xGMjHOkis47LzTXR2QfSybYDkcHZjnXpSmvCKvAy/sbVry/socZK/c2M+w
# lSyL9G/uRUNGqBXiFTVXdk1e3jF2yCE/Ej8uiEMlCzthFeFPtrs0c7Jn5zM70riz
# 84xVWsbBpagRFzlbImNiqtQdKwXmMVh7gbwL0arIb5UhwW6c+eutlFuHNXeCmAGE
# 8SknssStGCogCZhrDzODIICWkmAaHmVxsMKdSiAkvnMvwPPWJ4pkgMIit0iiP00g
# w2za3YgyiAagMSWvs6Gq5kIfiH82+2v5QaTE1efkrQLKNJmJWp/W16k3THHGb0ac
# pQAObG1tNTuS3aGCE1AwghNMBgorBgEEAYI3AwMBMYITPDCCEzgGCSqGSIb3DQEH
# AqCCEykwghMlAgEDMQ8wDQYJYIZIAWUDBAICBQAwge8GCyqGSIb3DQEJEAEEoIHf
# BIHcMIHZAgEBBgorBgEEAbIxAgEBMDEwDQYJYIZIAWUDBAIBBQAEIKgkS+qlVs0U
# XP8YKzCqyopidOsWnPXaWBqNAqv8HRZMAhR3UlSCa1u4lMMFaaYkdUVwvsgooxgP
# MjAyMjA2MjMwNjMyMTVaoG6kbDBqMQswCQYDVQQGEwJHQjETMBEGA1UECBMKTWFu
# Y2hlc3RlcjEYMBYGA1UEChMPU2VjdGlnbyBMaW1pdGVkMSwwKgYDVQQDDCNTZWN0
# aWdvIFJTQSBUaW1lIFN0YW1waW5nIFNpZ25lciAjM6CCDeowggb2MIIE3qADAgEC
# AhEAkDl/mtJKOhPyvZFfCDipQzANBgkqhkiG9w0BAQwFADB9MQswCQYDVQQGEwJH
# QjEbMBkGA1UECBMSR3JlYXRlciBNYW5jaGVzdGVyMRAwDgYDVQQHEwdTYWxmb3Jk
# MRgwFgYDVQQKEw9TZWN0aWdvIExpbWl0ZWQxJTAjBgNVBAMTHFNlY3RpZ28gUlNB
# IFRpbWUgU3RhbXBpbmcgQ0EwHhcNMjIwNTExMDAwMDAwWhcNMzMwODEwMjM1OTU5
# WjBqMQswCQYDVQQGEwJHQjETMBEGA1UECBMKTWFuY2hlc3RlcjEYMBYGA1UEChMP
# U2VjdGlnbyBMaW1pdGVkMSwwKgYDVQQDDCNTZWN0aWdvIFJTQSBUaW1lIFN0YW1w
# aW5nIFNpZ25lciAjMzCCAiIwDQYJKoZIhvcNAQEBBQADggIPADCCAgoCggIBAJCy
# cT954dS5ihfMw5fCkJRy7Vo6bwFDf3NaKJ8kfKA1QAb6lK8KoYO2E+RLFQZeaoog
# NHF7uyWtP1sKpB8vbH0uYVHQjFk3PqZd8R5dgLbYH2DjzRJqiB/G/hjLk0NWesfO
# A9YAZChWIrFLGdLwlslEHzldnLCW7VpJjX5y5ENrf8mgP2xKrdUAT70KuIPFvZgs
# B3YBcEXew/BCaer/JswDRB8WKOFqdLacRfq2Os6U0R+9jGWq/fzDPOgNnDhm1fx9
# HptZjJFaQldVUBYNS3Ry7qAqMfwmAjT5ZBtZ/eM61Oi4QSl0AT8N4BN3KxE8+z3N
# 0Ofhl1tV9yoDbdXNYtrOnB786nB95n1LaM5aKWHToFwls6UnaKNY/fUta8pfZMdr
# KAzarHhB3pLvD8Xsq98tbxpUUWwzs41ZYOff6Bcio3lBYs/8e/OS2q7gPE8PWsxu
# 3x+8Iq+3OBCaNKcL//4dXqTz7hY4Kz+sdpRBnWQd+oD9AOH++DrUw167aU1ymeXx
# Mi1R+mGtTeomjm38qUiYPvJGDWmxt270BdtBBcYYwFDk+K3+rGNhR5G8RrVGU2zF
# 9OGGJ5OEOWx14B0MelmLLsv0ZCxCR/RUWIU35cdpp9Ili5a/xq3gvbE39x/fQnuq
# 6xzp6z1a3fjSkNVJmjodgxpXfxwBws4cfcz7lhXFAgMBAAGjggGCMIIBfjAfBgNV
# HSMEGDAWgBQaofhhGSAPw0F3RSiO0TVfBhIEVTAdBgNVHQ4EFgQUJS5oPGuaKyQU
# qR+i3yY6zxSm8eAwDgYDVR0PAQH/BAQDAgbAMAwGA1UdEwEB/wQCMAAwFgYDVR0l
# AQH/BAwwCgYIKwYBBQUHAwgwSgYDVR0gBEMwQTA1BgwrBgEEAbIxAQIBAwgwJTAj
# BggrBgEFBQcCARYXaHR0cHM6Ly9zZWN0aWdvLmNvbS9DUFMwCAYGZ4EMAQQCMEQG
# A1UdHwQ9MDswOaA3oDWGM2h0dHA6Ly9jcmwuc2VjdGlnby5jb20vU2VjdGlnb1JT
# QVRpbWVTdGFtcGluZ0NBLmNybDB0BggrBgEFBQcBAQRoMGYwPwYIKwYBBQUHMAKG
# M2h0dHA6Ly9jcnQuc2VjdGlnby5jb20vU2VjdGlnb1JTQVRpbWVTdGFtcGluZ0NB
# LmNydDAjBggrBgEFBQcwAYYXaHR0cDovL29jc3Auc2VjdGlnby5jb20wDQYJKoZI
# hvcNAQEMBQADggIBAHPa7Whyy8K5QKExu7QDoy0UeyTntFsVfajp/a3Rkg18PTag
# adnzmjDarGnWdFckP34PPNn1w3klbCbojWiTzvF3iTl/qAQF2jTDFOqfCFSr/8R+
# lmwr05TrtGzgRU0ssvc7O1q1wfvXiXVtmHJy9vcHKPPTstDrGb4VLHjvzUWgAOT4
# BHa7V8WQvndUkHSeC09NxKoTj5evATUry5sReOny+YkEPE7jghJi67REDHVBwg80
# uIidyCLxE2rbGC9ueK3EBbTohAiTB/l9g/5omDTkd+WxzoyUbNsDbSgFR36bLvBk
# +9ukAzEQfBr7PBmA0QtwuVVfR745ZM632iNUMuNGsjLY0imGyRVdgJWvAvu00S6d
# OHw14A8c7RtHSJwialWC2fK6CGUD5fEp80iKCQFMpnnyorYamZTrlyjhvn0boXzt
# VoCm9CIzkOSEU/wq+sCnl6jqtY16zuTgS6Ezqwt2oNVpFreOZr9f+h/EqH+noUgU
# kQ2C/L1Nme3J5mw2/ndDmbhpLXxhL+2jsEn+W75pJJH/k/xXaZJL2QU/bYZy06LQ
# wGTSOkLBGgP70O2aIbg/r6ayUVTVTMXKHxKNV8Y57Vz/7J8mdq1kZmfoqjDg0q23
# fbFqQSduA4qjdOCKCYJuv+P2t7yeCykYaIGhnD9uFllLFAkJmuauv2AV3Yb1MIIG
# 7DCCBNSgAwIBAgIQMA9vrN1mmHR8qUY2p3gtuTANBgkqhkiG9w0BAQwFADCBiDEL
# MAkGA1UEBhMCVVMxEzARBgNVBAgTCk5ldyBKZXJzZXkxFDASBgNVBAcTC0plcnNl
# eSBDaXR5MR4wHAYDVQQKExVUaGUgVVNFUlRSVVNUIE5ldHdvcmsxLjAsBgNVBAMT
# JVVTRVJUcnVzdCBSU0EgQ2VydGlmaWNhdGlvbiBBdXRob3JpdHkwHhcNMTkwNTAy
# MDAwMDAwWhcNMzgwMTE4MjM1OTU5WjB9MQswCQYDVQQGEwJHQjEbMBkGA1UECBMS
# R3JlYXRlciBNYW5jaGVzdGVyMRAwDgYDVQQHEwdTYWxmb3JkMRgwFgYDVQQKEw9T
# ZWN0aWdvIExpbWl0ZWQxJTAjBgNVBAMTHFNlY3RpZ28gUlNBIFRpbWUgU3RhbXBp
# bmcgQ0EwggIiMA0GCSqGSIb3DQEBAQUAA4ICDwAwggIKAoICAQDIGwGv2Sx+iJl9
# AZg/IJC9nIAhVJO5z6A+U++zWsB21hoEpc5Hg7XrxMxJNMvzRWW5+adkFiYJ+9Uy
# UnkuyWPCE5u2hj8BBZJmbyGr1XEQeYf0RirNxFrJ29ddSU1yVg/cyeNTmDoqHvzO
# WEnTv/M5u7mkI0Ks0BXDf56iXNc48RaycNOjxN+zxXKsLgp3/A2UUrf8H5VzJD0B
# KLwPDU+zkQGObp0ndVXRFzs0IXuXAZSvf4DP0REKV4TJf1bgvUacgr6Unb+0ILBg
# frhN9Q0/29DqhYyKVnHRLZRMyIw80xSinL0m/9NTIMdgaZtYClT0Bef9Maz5yIUX
# x7gpGaQpL0bj3duRX58/Nj4OMGcrRrc1r5a+2kxgzKi7nw0U1BjEMJh0giHPYla1
# IXMSHv2qyghYh3ekFesZVf/QOVQtJu5FGjpvzdeE8NfwKMVPZIMC1Pvi3vG8Aij0
# bdonigbSlofe6GsO8Ft96XZpkyAcSpcsdxkrk5WYnJee647BeFbGRCXfBhKaBi2f
# A179g6JTZ8qx+o2hZMmIklnLqEbAyfKm/31X2xJ2+opBJNQb/HKlFKLUrUMcpEmL
# QTkUAx4p+hulIq6lw02C0I3aa7fb9xhAV3PwcaP7Sn1FNsH3jYL6uckNU4B9+rY5
# WDLvbxhQiddPnTO9GrWdod6VQXqngwIDAQABo4IBWjCCAVYwHwYDVR0jBBgwFoAU
# U3m/WqorSs9UgOHYm8Cd8rIDZsswHQYDVR0OBBYEFBqh+GEZIA/DQXdFKI7RNV8G
# EgRVMA4GA1UdDwEB/wQEAwIBhjASBgNVHRMBAf8ECDAGAQH/AgEAMBMGA1UdJQQM
# MAoGCCsGAQUFBwMIMBEGA1UdIAQKMAgwBgYEVR0gADBQBgNVHR8ESTBHMEWgQ6BB
# hj9odHRwOi8vY3JsLnVzZXJ0cnVzdC5jb20vVVNFUlRydXN0UlNBQ2VydGlmaWNh
# dGlvbkF1dGhvcml0eS5jcmwwdgYIKwYBBQUHAQEEajBoMD8GCCsGAQUFBzAChjNo
# dHRwOi8vY3J0LnVzZXJ0cnVzdC5jb20vVVNFUlRydXN0UlNBQWRkVHJ1c3RDQS5j
# cnQwJQYIKwYBBQUHMAGGGWh0dHA6Ly9vY3NwLnVzZXJ0cnVzdC5jb20wDQYJKoZI
# hvcNAQEMBQADggIBAG1UgaUzXRbhtVOBkXXfA3oyCy0lhBGysNsqfSoF9bw7J/Ra
# oLlJWZApbGHLtVDb4n35nwDvQMOt0+LkVvlYQc/xQuUQff+wdB+PxlwJ+TNe6qAc
# Jlhc87QRD9XVw+K81Vh4v0h24URnbY+wQxAPjeT5OGK/EwHFhaNMxcyyUzCVpNb0
# llYIuM1cfwGWvnJSajtCN3wWeDmTk5SbsdyybUFtZ83Jb5A9f0VywRsj1sJVhGbk
# s8VmBvbz1kteraMrQoohkv6ob1olcGKBc2NeoLvY3NdK0z2vgwY4Eh0khy3k/ALW
# PncEvAQ2ted3y5wujSMYuaPCRx3wXdahc1cFaJqnyTdlHb7qvNhCg0MFpYumCf/R
# oZSmTqo9CfUFbLfSZFrYKiLCS53xOV5M3kg9mzSWmglfjv33sVKRzj+J9hyhtal1
# H3G/W0NdZT1QgW6r8NDT/LKzH7aZlib0PHmLXGTMze4nmuWgwAxyh8FuTVrTHurw
# ROYybxzrF06Uw3hlIDsPQaof6aFBnf6xuKBlKjTg3qj5PObBMLvAoGMs/FwWAKjQ
# xH/qEZ0eBsambTJdtDgJK0kHqv3sMNrxpy/Pt/360KOE2See+wFmd7lWEOEgbsau
# sfm2usg1XTN2jvF8IAwqd661ogKGuinutFoAsYyr4/kKyVRd1LlqdJ69SK6YMYIE
# LTCCBCkCAQEwgZIwfTELMAkGA1UEBhMCR0IxGzAZBgNVBAgTEkdyZWF0ZXIgTWFu
# Y2hlc3RlcjEQMA4GA1UEBxMHU2FsZm9yZDEYMBYGA1UEChMPU2VjdGlnbyBMaW1p
# dGVkMSUwIwYDVQQDExxTZWN0aWdvIFJTQSBUaW1lIFN0YW1waW5nIENBAhEAkDl/
# mtJKOhPyvZFfCDipQzANBglghkgBZQMEAgIFAKCCAWswGgYJKoZIhvcNAQkDMQ0G
# CyqGSIb3DQEJEAEEMBwGCSqGSIb3DQEJBTEPFw0yMjA2MjMwNjMyMTVaMD8GCSqG
# SIb3DQEJBDEyBDD9mD3MkGbFLRtVTBr8VLSfSc+at3hYyCCH94MPwwFev28YcIfU
# Idl9FqwomG1p1qIwge0GCyqGSIb3DQEJEAIMMYHdMIHaMIHXMBYEFKs0ATqsQJcx
# nwga8LMY4YP4D3iBMIG8BBQC1luV4oNwwVcAlfqI+SPdk3+tjzCBozCBjqSBizCB
# iDELMAkGA1UEBhMCVVMxEzARBgNVBAgTCk5ldyBKZXJzZXkxFDASBgNVBAcTC0pl
# cnNleSBDaXR5MR4wHAYDVQQKExVUaGUgVVNFUlRSVVNUIE5ldHdvcmsxLjAsBgNV
# BAMTJVVTRVJUcnVzdCBSU0EgQ2VydGlmaWNhdGlvbiBBdXRob3JpdHkCEDAPb6zd
# Zph0fKlGNqd4LbkwDQYJKoZIhvcNAQEBBQAEggIAMBkn0L/dJKZeVebB/CC4E7xL
# n1MO9t8hT0oi9NJCYtYcLnczZGqSWHoH3hN+OctF1cL4I+OSj9HxjUPLP78eaXcx
# wiTLmNwjjjK8vPKs865Tf/rYP1Dd62M0CXmM9AUeHjGfGqIPVxRZQ1KxI61uxwkR
# Igls552pW4cNFT8LSWUVbInzHLZl3MhdjHiqBShF/Z+WADckGEHtAlK4/pQautox
# 0uohmZy7jyhy+h034r86h5Fs1V2Uwbw6srX9MBUlMGiG+I0TWVuV1g0ErIYm3P7c
# RHuiDV3Ysr+GeK3n1zqw1JZe0SjOmt6Hve7PO/E1fGIlug0H9nunbvj6zxmUBiru
# BIpD0mSofo0qR3th79Y7+HAZ16b6b9Qsyd6wge+x4xkwhkKILiH0j+l6zuO6zA96
# nssUk3lXbGF3tMczeeDjk6USovM2YUcPOAOaWMvYmmEZ9HZf5PyX2rqFCmEVKwXW
# y/MLRDCjwaXS/p+gb2SHK+OB6ddI+ZyK9iCVJK46it5JIzvhjpKFCqINEHteOuNe
# c3ZGdS0hOkCxg/GULvqAt5xe1brzQMx2ZfWxrgNJc/Kt/lX+mDA9E4x2IhNbIrnr
# fuPI19mVYJoM43p7sIipAgIxeU3EBow+nOkH1r8Bt8f0LxHJuYyy7sOzJwvWCXmW
# WgoP3AqFK6mcm8WlDek=
# SIG # End signature block
